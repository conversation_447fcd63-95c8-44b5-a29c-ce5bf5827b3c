DROP VIEW IF EXISTS payroll_tax_summary_data2;

CREATE OR REPLACE VIEW payroll_tax_summary_data2 AS
with wl as
(
    select distinct Line1, Line2, City,State, postalcode, symmetrycitycode, companyid, row_number() over (partition by companyid order by symmetrycitycode desc) as instanceNo from worklocation where archivedat is null
), t as
(
    select
        'MTCSV01-PTS' as format_id,
        '' as company_group_name,
        c.id as payroll_code,
        min(to_char(paydate, 'YYYYMMDD')) as check_date,
        case when wl.symmetrycitycode in ('1558347', '1542678', '1555154', '1548994') -- Charleston = 1558347, Madison = 1542678, Montgomery = 1555154, Wheeling = 1548994
            then
                max(case pp.payperiodtype
                    when 'WEEKLY' then 'W'
                    when 'BI_WEEKLY' then 'B'
                    when 'SEMI_MONTHLY' then 'S'
                    when 'MONTHLY' then 'M'
                    else ''
                end)
            else
                ''
        end as payroll_frequency, -- The problem with this is that we have AD_HOC, CUSTOM, IMPORT, DAILY, PAY_ON_DEMAND, and PAY_ON_DEMAND_REMAINDER pay periods as well. I am going to leave those blank for now
        m.accountidentifier as tax_code,
        coalesce(c.federalein, '') as ein,
        '' as wc_class_code,
        case when sum(pt.subjectwages) = 0 then 0 else round(sum(pt.amount)/sum(pt.subjectwages)*100, 2) end as tax_rate,
        sum(pt.amount) as tax,
        count(distinct p.employeeid) as employee_count,
        sum(pt.subjectwages) as taxable_wages,
        sum(grossearnings) as gross_wages,
        0 as exempt_wages,
        '' as liability_trace_id,
        '' as user_field_1,
        '' as user_field_2,
        '' as user_field_3,
        '' as user_field_4,
        '' as user_field_5,
        '' as user_field_6,
        '' as user_field_7,
        '' as user_field_8,
        paidat AS paid_at,
        c.democompany as demo_company,
        count(*) as number_records
    from
        company c join companyaddress ca on c.id = ca.companyId and now() between ca.startdate and coalesce(ca.enddate, '2100-12-31')
        left join wl on c.id = wl.companyid and lower(replace(replace(wl.line1, '.', ''), ' ', '')) = lower(replace(replace(ca.line1, '.', ''), ' ', '')) and lower(replace(wl.city, ' ', '')) = lower(replace(ca.city, ' ', '')) and wl.state = ca.state and left(replace(wl.postalcode, ' ', ''), 5) = left(replace(ca.postalcode, ' ', ''), 5) and instanceNo = 1
        join payment p on c.id = p.companyId
        join payperiod pp on p.payperiodid = pp.id
        join paymenttax pt on p.id = pt.paymentid and pt.amount > 0
        join taxjurisdictionmastertaxaccountmapping m on pt.jurisdictiontaxId = m.taxjurisdictionid and accountidentifier <> '?' -- This is temporary because a ? got into a tax mapping
    where
         p.status in ('PAID', 'IMPORTED')
    group by
        c.id, c.federalein, m.accountidentifier, wl.symmetrycitycode, pt.jurisdictiontaxId, paydate, paidat, demo_company
)
select * from t

    commit;
