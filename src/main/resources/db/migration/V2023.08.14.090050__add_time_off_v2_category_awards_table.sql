
-- add TimeOffPolicyCategoryAwardConfig table
CREATE TABLE IF NOT EXISTS TimeOffPolicyCategoryAwardConfig (
    id              BIGSERIAL PRIMARY KEY,
    createdAt       TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt       TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    timeOffPolicyCategoryId     BIGINT REFERENCES TimeOffPolicyCategory (id) ON UPDATE CASCADE ON DELETE CASCADE,
    employedYears               INTEGER NOT NULL DEFAULT 0,
    frontLoadedHours            INTEGER NOT NULL DEFAULT 0,
    accrualRatioEarnedHours     INTEGER NOT NULL DEFAULT 0,
    maxBalanceHours             INTEGER NOT NULL DEFAULT 0,
    maxRollOverHours            INTEGER NOT NULL DEFAULT 0,
    maxNegativeBalanceHours     INTEGER NOT NULL DEFAULT 0
);
CREATE INDEX IF NOT EXISTS TimeOffPolicyCategoryAwardConfig_policyCategoryId_idx ON TimeOffPolicyCategoryAwardConfig (timeOffPolicyCategoryId);
