-- We should keep this whole script together!
--
--  `box122023`
--    - added order by to string_agg
--
drop view if exists w2exportdata2023; -- BJ report
drop view if exists w2localdata2023;
drop view if exists w2nonfedtaxdata2023;
drop view if exists w2scorpdata2023;
drop view if exists box122023;
drop view if exists w2data2023;

-- w2data2023
create or replace view w2data2023
            (id, userid, companyid, year, controlnumber, grossearnings, fitwages, fit, ficawages,
             fica, mediwages, medi, sstips, ffcrasick, ffcracaregiver, ffcrafmla, alloctips, dra,
             nqplans, erisa401k, erisa403b, hsa, roth401k, healthcoverage, statutoryemployee,
             retirementplan, thirdpartysickpay)
as
SELECT
    to_hex(VARIADIC ARRAY [
        e.userid,
           CASE
               WHEN e.statutoryemployee THEN 1
               ELSE 0
               END::bigint,
           p.companyid,
           date_part('year'::text, p.paydate)::bigint])                  							        AS id,
    e.userid																					        AS userid,
    p.companyid																					        AS companyid,
    date_part('year'::text, p.paydate)                                                       	        AS year,
    ''::text                                                                                  	        AS controlnumber,
    sum(p.grossearnings)                                                                      	        AS grossearnings,
    sum(ptfit.subjectwages)                                                                   	        AS fitwages,
    sum(ptfit.amount)                                                                         	        AS fit,
    COALESCE(sum(ptfica.subjectwages), 0::numeric) - COALESCE(sum(petips.amount), 0::numeric)  	        AS ficawages,
    sum(ptfica.amount)                                                                        	        AS fica,
    GREATEST(COALESCE(sum(ptmedi.subjectwages) , 0::numeric), COALESCE(sum(ptmedi2.subjectwages), 0::numeric))  AS mediwages,
    COALESCE(sum(ptmedi.amount), 0::numeric) + COALESCE(sum(ptmedi2.amount), 0::numeric)      	        AS medi,
    COALESCE(sum(petips.amount), 0::numeric)                                                  	        AS sstips,
    COALESCE(sum(peffcrasick.amount), 0::numeric)                                             	        AS ffcrasick,
    0::numeric                                                                                	        AS ffcracaregiver,
    COALESCE(sum(peffcrafmla.amount), 0::numeric)                                             	        AS ffcrafmla,
    0::numeric                                                                                	        AS alloctips,
    (SELECT COALESCE(SUM(pcd.amountee) + SUM(pcd.amounter), 0)
     FROM paymentcontributiondeduction pcd
              JOIN payment p2 on p2.id = pcd.paymentid
              JOIN employee e2 on e2.id = p2.employeeid
     WHERE e2.userid = e.userid AND e2.companyid=p.companyid
       AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
       AND date_part('year'::text, p2.paydate) = 2023
       AND pcd.type = ANY (ARRAY['DRA']))														        AS dra,
    0::numeric                                                                                	        AS nqplans,
    (SELECT COALESCE(SUM(pcd.amountee), 0)
     FROM paymentcontributiondeduction pcd
              JOIN payment p2 on p2.id = pcd.paymentid
              JOIN employee e2 on e2.id = p2.employeeid
     WHERE e2.userid = e.userid AND e2.companyid=p.companyid
       AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
       AND date_part('year'::text, p2.paydate) = 2023
       AND pcd.type = ANY (ARRAY['ERISA_401K']))												        AS erisa401k,
    (SELECT COALESCE(SUM(pcd.amountee), 0)
     FROM paymentcontributiondeduction pcd
              JOIN payment p2 on p2.id = pcd.paymentid
              JOIN employee e2 on e2.id = p2.employeeid
     WHERE e2.userid = e.userid AND e2.companyid=p.companyid
       AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
       AND date_part('year'::text, p2.paydate) = 2023
       AND pcd.type = ANY (ARRAY['ERISA_403B']))												        AS erisa403b,
    (SELECT COALESCE(SUM(pcd.amountee) + SUM(pcd.amounter), 0)
     FROM paymentcontributiondeduction pcd
              JOIN payment p2 on p2.id = pcd.paymentid
              JOIN employee e2 on e2.id = p2.employeeid
     WHERE e2.userid = e.userid AND e2.companyid=p.companyid
       AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
       AND date_part('year'::text, p2.paydate) = 2023
       AND pcd.type = ANY (ARRAY['HSA']))	                         							        AS hsa,
    (SELECT COALESCE(SUM(pcd.amountee), 0)
     FROM paymentcontributiondeduction pcd
              JOIN payment p2 on p2.id = pcd.paymentid
              JOIN employee e2 on e2.id = p2.employeeid
     WHERE e2.userid = e.userid AND e2.companyid=p.companyid
       AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
       AND date_part('year'::text, p2.paydate) = 2023
       AND pcd.type = ANY (ARRAY['ROTH_401K'])) 												        AS roth401k,
    (SELECT COALESCE(SUM(pcd.amountee) + SUM(pcd.amounter), 0)
     FROM paymentcontributiondeduction pcd
              JOIN payment p2 on p2.id = pcd.paymentid
              JOIN employee e2 on e2.id = p2.employeeid
     WHERE e2.userid = e.userid AND e2.companyid=p.companyid
       AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
       AND date_part('year'::text, p2.paydate) = 2023
       AND pcd.type = ANY (ARRAY[
         'MEDICAL_INSURANCE',
         'DENTAL_INSURANCE',
         'VISION_INSURANCE',
         'PRESCRIPTION_PLAN',
         'HRA',
         'HOSPITAL_INDEMNITY_INSURANCE']))													            AS healthcoverage,
    CASE WHEN e.statutoryemployee = true THEN 'On'::text ELSE 'Off'::text END                	        AS statutoryemployee,
    CASE WHEN (
                  (SELECT COALESCE(SUM(pcd.amountee) + SUM(pcd.amounter), 0)
                   FROM paymentcontributiondeduction pcd
                            JOIN payment p2 on p2.id = pcd.paymentid
                            JOIN employee e2 on e2.id = p2.employeeid
                   WHERE e2.userid = e.userid AND e2.companyid=p.companyid
                     AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
                     AND date_part('year'::text, p2.paydate) = 2023
                     AND pcd.type = ANY (ARRAY['ERISA_401K', 'ERISA_403B', 'ROTH_401K']))) > 0::numeric
             THEN 'On'::text ELSE 'Off'::text END 													    AS retirementplan,
    'Off'::text                                                                              	        AS thirdpartysickpay
FROM payment p
         JOIN employee e ON p.employeeid = e.id AND e.employmenttype = 'EMPLOYEE'::text
         LEFT JOIN paymenttax ptfit ON ptfit.paymentid = p.id AND ptfit.type = 'FIT'::text
         LEFT JOIN paymenttax ptfica ON ptfica.paymentid = p.id AND ptfica.type = 'FICA'::text
         LEFT JOIN paymenttax ptmedi ON ptmedi.paymentid = p.id AND ptmedi.type = 'MEDI'::text
         LEFT JOIN paymenttax ptmedi2 ON ptmedi2.paymentid = p.id AND ptmedi2.type = 'MEDI2'::text
         LEFT JOIN LATERAL(
    select
        sum(amount) as amount
    from paymentearning pe
    where
            pe.paymentid = p.id
      AND pe.type in ('TIPS'::text, 'PREVIOUSLY_PAID_TIPS'::text)
    limit 1
    ) as petips on true
         LEFT JOIN paymentearning pereimbursement
                   ON pereimbursement.paymentid = p.id AND pereimbursement.type = 'REIMBURSEMENT'::text AND pereimbursement.amount > 0::numeric
         LEFT JOIN paymentearning peffcrasick ON peffcrasick.paymentid = p.id AND
                                                 peffcrasick.type = 'EMERGENCY_FFCRA_SICK'::text AND
                                                 peffcrasick.amount > 0::numeric
         LEFT JOIN paymentearning peffcrafmla ON peffcrafmla.paymentid = p.id AND
                                                 peffcrafmla.type = 'EMERGENCY_FFCRA_FMLA'::text AND
                                                 peffcrafmla.amount > 0::numeric
WHERE (p.status = ANY (ARRAY ['PAID'::text, 'IMPORTED'::text]))
  AND date_part('year'::text, p.paydate) = 2023
  AND p.grossearnings <> 0
  AND (p.grossearnings - coalesce(pereimbursement.amount, 0.00) <> 0)
GROUP BY e.userid, e.statutoryemployee, p.companyid, date_part('year'::text, p.paydate);


-- box122023 (No java entity)
create or replace view box122023(companyid, userid, description) as
SELECT b.companyid,
       b.userid,
       string_agg((b.code || '_'::text) || b.amount, ','::text order by b.code) AS description
FROM (SELECT w2data2023.companyid,
             w2data2023.userid,
             'D'::text            AS code,
              w2data2023.erisa401k AS amount
      FROM w2data2023
      WHERE w2data2023.erisa401k > 0::numeric
        AND w2data2023.year = 2023::double precision
      UNION
      SELECT w2data2023.companyid,
          w2data2023.userid,
          'E'::text            AS code,
          w2data2023.erisa403b AS amount
      FROM w2data2023
      WHERE w2data2023.erisa403b > 0::numeric
        AND w2data2023.year = 2023::double precision
      UNION
      SELECT w2data2023.companyid,
          w2data2023.userid,
          'W'::text      AS code,
          w2data2023.hsa AS amount
      FROM w2data2023
      WHERE w2data2023.hsa > 0::numeric
        AND w2data2023.year = 2023::double precision
      UNION
      SELECT w2data2023.companyid,
          w2data2023.userid,
          'AA'::text          AS code,
          w2data2023.roth401k AS amount
      FROM w2data2023
      WHERE w2data2023.roth401k > 0::numeric
        AND w2data2023.year = 2023::double precision
      UNION
      SELECT w2data2023.companyid,
          w2data2023.userid,
          'DD'::text                AS code,
          w2data2023.healthcoverage AS amount
      FROM w2data2023
      WHERE w2data2023.healthcoverage > 0::numeric
        AND w2data2023.year = 2023::double precision) b
GROUP BY b.companyid, b.userid;


-- w2scorpdata2023 (No java entity)
create or replace view w2scorpdata2023
            (id, userid, statutoryemployee, companyid, year, controlnumber, type, "group",
             jurisdictiontaxid, description, subjectwages, amount)
as
SELECT to_hex(VARIADIC
                  ARRAY [e.userid, date_part('year'::text, p.paydate)::bigint, max(p.id), '1'::bigint]) AS id,
       e.userid,
       e.statutoryemployee,
       p.companyid,
       date_part('year'::text, p.paydate)                                                             AS year,
       ''::text                                                                                       AS controlnumber,
       NULL::text                                                                                     AS type,
       'OTHER'::text                                                                                  AS "group",
       NULL::text                                                                                     AS jurisdictiontaxid,
       'SCorp MP'::text                                                                               AS description,
       sum(p.grossearnings)                                                                           AS subjectwages,
       (SELECT COALESCE(SUM(pcd.amountee) + SUM(pcd.amounter), 0)
        FROM paymentcontributiondeduction pcd
                 JOIN payment p2 on p2.id = pcd.paymentid
                 JOIN employee e2 on e2.id = p2.employeeid
        WHERE e2.userid = e.userid
          AND p2.status = ANY (ARRAY['PAID', 'IMPORTED'])
          AND date_part('year'::text, p2.paydate) = 2023
          AND pcd.type = ANY (ARRAY['MEDICAL_INSURANCE']))										      AS amount
FROM payment p
    JOIN employee e ON p.employeeid = e.id
WHERE (p.status = ANY (ARRAY ['PAID'::text, 'IMPORTED'::text]))
  AND date_part('year'::text, p.paydate) = 2023
  AND (p.employeeid = ANY (ARRAY [2807::bigint, 1347::bigint]))
GROUP BY e.userid, e.statutoryemployee, p.companyid, (date_part('year'::text, p.paydate));


-- w2nonfedtaxdata2023
create or replace view w2nonfedtaxdata2023
            (id, userid, statutoryemployee, companyid, year, controlnumber, type, "group",
             jurisdictiontaxid, w2Description, description, subjectwages, amount)
as
SELECT to_hex(VARIADIC ARRAY [e.userid,
              CASE
                  WHEN e.statutoryemployee THEN 1
                  ELSE 0
                  END::bigint, p.companyid, date_part('year'::text, p.paydate)::bigint, max(pt.id)]) AS id,
       e.userid,
       e.statutoryemployee,
       p.companyid,
       date_part('year'::text, p.paydate)                                                  AS year,
       ''::text                                                                            AS controlnumber,
       pt.type,
       CASE
           WHEN pt.type = 'SIT'::text THEN 'STATE'::text
           WHEN pt.type = ANY (ARRAY['CITY'::text, 'SCHL'::text, 'SST'::text, 'CNTY'::text, 'LST'::text, 'EIT'::text]) THEN 'LOCAL'::text
           ELSE 'OTHER'::text
END                                                                             AS "group",
       pt.jurisdictiontaxid,
       case
        when pt.jurisdictiontaxid='18-001-0000-CNTY-000' then 'IN - CNTY 01'
        when pt.jurisdictiontaxid='18-003-0000-CNTY-000' then 'IN - CNTY 02'
        when pt.jurisdictiontaxid='18-005-0000-CNTY-000' then 'IN - CNTY 03'
        when pt.jurisdictiontaxid='18-007-0000-CNTY-000' then 'IN - CNTY 04'
        when pt.jurisdictiontaxid='18-009-0000-CNTY-000' then 'IN - CNTY 05'
        when pt.jurisdictiontaxid='18-011-0000-CNTY-000' then 'IN - CNTY 06'
        when pt.jurisdictiontaxid='18-013-0000-CNTY-000' then 'IN - CNTY 07'
        when pt.jurisdictiontaxid='18-015-0000-CNTY-000' then 'IN - CNTY 08'
        when pt.jurisdictiontaxid='18-017-0000-CNTY-000' then 'IN - CNTY 09'
        when pt.jurisdictiontaxid='18-019-0000-CNTY-000' then 'IN - CNTY 10'
        when pt.jurisdictiontaxid='18-021-0000-CNTY-000' then 'IN - CNTY 11'
        when pt.jurisdictiontaxid='18-023-0000-CNTY-000' then 'IN - CNTY 12'
        when pt.jurisdictiontaxid='18-025-0000-CNTY-000' then 'IN - CNTY 13'
        when pt.jurisdictiontaxid='18-027-0000-CNTY-000' then 'IN - CNTY 14'
        when pt.jurisdictiontaxid='18-029-0000-CNTY-000' then 'IN - CNTY 15'
        when pt.jurisdictiontaxid='18-031-0000-CNTY-000' then 'IN - CNTY 16'
        when pt.jurisdictiontaxid='18-033-0000-CNTY-000' then 'IN - CNTY 17'
        when pt.jurisdictiontaxid='18-035-0000-CNTY-000' then 'IN - CNTY 18'
        when pt.jurisdictiontaxid='18-037-0000-CNTY-000' then 'IN - CNTY 19'
        when pt.jurisdictiontaxid='18-039-0000-CNTY-000' then 'IN - CNTY 20'
        when pt.jurisdictiontaxid='18-041-0000-CNTY-000' then 'IN - CNTY 21'
        when pt.jurisdictiontaxid='18-043-0000-CNTY-000' then 'IN - CNTY 22'
        when pt.jurisdictiontaxid='18-045-0000-CNTY-000' then 'IN - CNTY 23'
        when pt.jurisdictiontaxid='18-047-0000-CNTY-000' then 'IN - CNTY 24'
        when pt.jurisdictiontaxid='18-049-0000-CNTY-000' then 'IN - CNTY 25'
        when pt.jurisdictiontaxid='18-051-0000-CNTY-000' then 'IN - CNTY 26'
        when pt.jurisdictiontaxid='18-053-0000-CNTY-000' then 'IN - CNTY 27'
        when pt.jurisdictiontaxid='18-055-0000-CNTY-000' then 'IN - CNTY 28'
        when pt.jurisdictiontaxid='18-057-0000-CNTY-000' then 'IN - CNTY 29'
        when pt.jurisdictiontaxid='18-059-0000-CNTY-000' then 'IN - CNTY 30'
        when pt.jurisdictiontaxid='18-061-0000-CNTY-000' then 'IN - CNTY 31'
        when pt.jurisdictiontaxid='18-063-0000-CNTY-000' then 'IN - CNTY 32'
        when pt.jurisdictiontaxid='18-065-0000-CNTY-000' then 'IN - CNTY 33'
        when pt.jurisdictiontaxid='18-067-0000-CNTY-000' then 'IN - CNTY 34'
        when pt.jurisdictiontaxid='18-069-0000-CNTY-000' then 'IN - CNTY 35'
        when pt.jurisdictiontaxid='18-071-0000-CNTY-000' then 'IN - CNTY 36'
        when pt.jurisdictiontaxid='18-073-0000-CNTY-000' then 'IN - CNTY 37'
        when pt.jurisdictiontaxid='18-075-0000-CNTY-000' then 'IN - CNTY 38'
        when pt.jurisdictiontaxid='18-077-0000-CNTY-000' then 'IN - CNTY 39'
        when pt.jurisdictiontaxid='18-079-0000-CNTY-000' then 'IN - CNTY 40'
        when pt.jurisdictiontaxid='18-081-0000-CNTY-000' then 'IN - CNTY 41'
        when pt.jurisdictiontaxid='18-083-0000-CNTY-000' then 'IN - CNTY 42'
        when pt.jurisdictiontaxid='18-085-0000-CNTY-000' then 'IN - CNTY 43'
        when pt.jurisdictiontaxid='18-087-0000-CNTY-000' then 'IN - CNTY 44'
        when pt.jurisdictiontaxid='18-089-0000-CNTY-000' then 'IN - CNTY 45'
        when pt.jurisdictiontaxid='18-091-0000-CNTY-000' then 'IN - CNTY 46'
        when pt.jurisdictiontaxid='18-093-0000-CNTY-000' then 'IN - CNTY 47'
        when pt.jurisdictiontaxid='18-095-0000-CNTY-000' then 'IN - CNTY 48'
        when pt.jurisdictiontaxid='18-097-0000-CNTY-000' then 'IN - CNTY 49'
        when pt.jurisdictiontaxid='18-099-0000-CNTY-000' then 'IN - CNTY 50'
        when pt.jurisdictiontaxid='18-101-0000-CNTY-000' then 'IN - CNTY 51'
        when pt.jurisdictiontaxid='18-103-0000-CNTY-000' then 'IN - CNTY 52'
        when pt.jurisdictiontaxid='18-105-0000-CNTY-000' then 'IN - CNTY 53'
        when pt.jurisdictiontaxid='18-107-0000-CNTY-000' then 'IN - CNTY 54'
        when pt.jurisdictiontaxid='18-109-0000-CNTY-000' then 'IN - CNTY 55'
        when pt.jurisdictiontaxid='18-111-0000-CNTY-000' then 'IN - CNTY 56'
        when pt.jurisdictiontaxid='18-113-0000-CNTY-000' then 'IN - CNTY 57'
        when pt.jurisdictiontaxid='18-115-0000-CNTY-000' then 'IN - CNTY 58'
        when pt.jurisdictiontaxid='18-117-0000-CNTY-000' then 'IN - CNTY 59'
        when pt.jurisdictiontaxid='18-119-0000-CNTY-000' then 'IN - CNTY 60'
        when pt.jurisdictiontaxid='18-121-0000-CNTY-000' then 'IN - CNTY 61'
        when pt.jurisdictiontaxid='18-123-0000-CNTY-000' then 'IN - CNTY 62'
        when pt.jurisdictiontaxid='18-125-0000-CNTY-000' then 'IN - CNTY 63'
        when pt.jurisdictiontaxid='18-127-0000-CNTY-000' then 'IN - CNTY 64'
        when pt.jurisdictiontaxid='18-129-0000-CNTY-000' then 'IN - CNTY 65'
        when pt.jurisdictiontaxid='18-131-0000-CNTY-000' then 'IN - CNTY 66'
        when pt.jurisdictiontaxid='18-133-0000-CNTY-000' then 'IN - CNTY 67'
        when pt.jurisdictiontaxid='18-135-0000-CNTY-000' then 'IN - CNTY 68'
        when pt.jurisdictiontaxid='18-137-0000-CNTY-000' then 'IN - CNTY 69'
        when pt.jurisdictiontaxid='18-139-0000-CNTY-000' then 'IN - CNTY 70'
        when pt.jurisdictiontaxid='18-141-0000-CNTY-000' then 'IN - CNTY 71'
        when pt.jurisdictiontaxid='18-143-0000-CNTY-000' then 'IN - CNTY 72'
        when pt.jurisdictiontaxid='18-145-0000-CNTY-000' then 'IN - CNTY 73'
        when pt.jurisdictiontaxid='18-147-0000-CNTY-000' then 'IN - CNTY 74'
        when pt.jurisdictiontaxid='18-149-0000-CNTY-000' then 'IN - CNTY 75'
        when pt.jurisdictiontaxid='18-151-0000-CNTY-000' then 'IN - CNTY 76'
        when pt.jurisdictiontaxid='18-153-0000-CNTY-000' then 'IN - CNTY 77'
        when pt.jurisdictiontaxid='18-155-0000-CNTY-000' then 'IN - CNTY 78'
        when pt.jurisdictiontaxid='18-157-0000-CNTY-000' then 'IN - CNTY 79'
        when pt.jurisdictiontaxid='18-159-0000-CNTY-000' then 'IN - CNTY 80'
        when pt.jurisdictiontaxid='18-161-0000-CNTY-000' then 'IN - CNTY 81'
        when pt.jurisdictiontaxid='18-163-0000-CNTY-000' then 'IN - CNTY 82'
        when pt.jurisdictiontaxid='18-165-0000-CNTY-000' then 'IN - CNTY 83'
        when pt.jurisdictiontaxid='18-167-0000-CNTY-000' then 'IN - CNTY 84'
        when pt.jurisdictiontaxid='18-169-0000-CNTY-000' then 'IN - CNTY 85'
        when pt.jurisdictiontaxid='18-171-0000-CNTY-000' then 'IN - CNTY 86'
        when pt.jurisdictiontaxid='18-173-0000-CNTY-000' then 'IN - CNTY 87'
        when pt.jurisdictiontaxid='18-175-0000-CNTY-000' then 'IN - CNTY 88'
        when pt.jurisdictiontaxid='18-177-0000-CNTY-000' then 'IN - CNTY 89'
        when pt.jurisdictiontaxid='18-179-0000-CNTY-000' then 'IN - CNTY 90'
        when pt.jurisdictiontaxid='18-181-0000-CNTY-000' then 'IN - CNTY 91'
        when pt.jurisdictiontaxid='18-183-0000-CNTY-000' then 'IN - CNTY 92'
        when pt.jurisdictiontaxid like '41-%-TRANS-000' then 'OR - ORSTT W/H'
        else null end                                                                      AS w2Description,
       max(pt.description)                                                                 AS description,
       sum(pt.subjectwages)                                                                AS subjectwages,
       sum(pt.amount)                                                                      AS amount
FROM payment p
         JOIN employee e ON p.employeeid = e.id AND e.employmenttype = 'EMPLOYEE'::text
         LEFT JOIN paymenttax pt ON pt.paymentid = p.id AND pt.payertype = 'EMPLOYEE'::text AND
                                    (pt.type <> ALL
                                     (ARRAY ['FIT'::text, 'FICA'::text, 'MEDI'::text, 'MEDI2'::text, 'SUI'::text, 'WC'::text]))
WHERE (p.status = ANY (ARRAY ['PAID'::text, 'IMPORTED'::text]))
  AND p.paydate >= '2023-01-01'::date
  AND p.paydate <= '2023-12-31'::date
GROUP BY e.userid, e.statutoryemployee, p.companyid, (date_part('year'::text, p.paydate)), pt.type,
         pt.jurisdictiontaxid
UNION
SELECT w2scorpdata2023.id,
       w2scorpdata2023.userid,
       w2scorpdata2023.statutoryemployee,
       w2scorpdata2023.companyid,
       w2scorpdata2023.year,
       w2scorpdata2023.controlnumber,
       w2scorpdata2023.type,
       w2scorpdata2023."group",
       w2scorpdata2023.jurisdictiontaxid,
       null as w2Description,
       w2scorpdata2023.description,
       w2scorpdata2023.subjectwages,
       w2scorpdata2023.amount
FROM w2scorpdata2023;


-- w2localdata2023
create or replace view w2localdata2023
            (state, rownum, id, userid, companyid, year, controlnumber, type, "group",
             jurisdictiontaxid, description, subjectwages, amount)
as
SELECT sc.state,
       row_number()
                                                   OVER (PARTITION BY d.companyid, d.userid, d."group" ORDER BY d.jurisdictiontaxid) AS rownum,
        d.id,
       d.userid,
       d.companyid,
       d.year,
       d.controlnumber,
       d.type,
       d."group",
       d.jurisdictiontaxid,
       coalesce(d.w2Description, d.description) as description,
       d.subjectwages,
       d.amount
FROM w2nonfedtaxdata2023 d
         LEFT JOIN statecodes sc ON sc.code::text = split_part(d.jurisdictiontaxid, '-'::text, 1)
WHERE d.year = 2023::double precision
  AND (d.subjectwages > 0::numeric OR d.amount > 0::numeric);


-- w2exportdata2023
CREATE OR REPLACE VIEW w2exportdata2023
AS
SELECT w2.id                                            AS id,
       c.federalein                                     AS employeridentificationnumber,
       c.legalentityname                                AS employersnameline1,
       ''::text                                         AS employersnameline2optional,
        ''::text                                         AS employersdisregardedentity,
        ca.line1                                         AS employersaddressline1,
       COALESCE(ca.line2, ''::text)                     AS employersaddressline2optional,
       ca.city                                          AS employerscity,
       ca.state                                         AS employersstate,
       ca.postalcode                                    AS employerszipcode,
       'US'::text                                       AS employerscountry,
        c.email                                          AS employersemailoptional,
       c.phone                                          AS employersphonenumber,
       CASE
           WHEN c.closeddate >= '2023-01-01'::date AND c.closeddate <= '2023-12-31'::date THEN TRUE
           ELSE FALSE
END                                          AS businesswasterminatedduringthistaxyearoptional,
       COALESCE(u.taxpayeridentifier,
                u.unverifiedtaxpayeridentifier)         AS employeessocialsecuritynumber,
       u.firstname                                      AS employeesfirstname,
       COALESCE(u.middlename, ''::text)                 AS employeesmiddlenameoptional,
       u.lastname                                       AS employeeslastname,
       ''::text                                         AS employeessuffixoptional,
       ua.line1                                         AS employeesaddressline1,
       COALESCE(ua.line2, ''::text)                     AS employeesaddressline2optional,
       ua.city                                          AS employeescity,
       ua.state                                         AS employeesstate,
       ua.postalcode                                    AS employeeszipcode,
       'US'::text                                       AS employeescountry,
       COALESCE(u.email, u.unverifiedemail)             AS employeesemailaddressoptional,
       COALESCE(u.phonenumber, u.unverifiedphonenumber) AS employeesphonenumberoptional,
       NULLIF(w2.fitwages, 0)                           AS wagestipsandothercompensationbox1,
       NULLIF(w2.fit, 0)                                AS federalincometaxwithheldbox2,
       NULLIF(w2.ficawages, 0)                          AS socialsecuritywagesbox3,
       NULLIF(w2.fica, 0)                               AS socialsecuritytaxwithheldbox4,
       NULLIF(w2.mediwages, 0)                          AS medicarewagesandtipsbox5,
       NULLIF(w2.medi, 0)                               AS medicaretaxwithheldbox6,
       NULLIF(w2.sstips, 0)                             AS socialsecuritytipsbox7,
       NULLIF(w2.alloctips, 0)                          AS allocatedtipsbox8,
       NULL::numeric                                    AS dependentcarebenefitsbox10,
       NULLIF(w2.nqplans, 0)                            AS nonqualifiedplansbox11,
       COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 1), '_'::text, 1)::text,
                ''::text)                               AS box12acodebox12a,
       NULLIF(COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 1), '_'::text, 2)::text,
                       ''::text), '')::numeric          AS box12aamountbox12a,
       COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 2), '_'::text, 1)::text,
                ''::text)                               AS box12bcodebox12b,
       NULLIF(COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 2), '_'::text, 2)::text,
                       ''::text), '')::numeric          AS box12bamountbox12b,
       COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 3), '_'::text, 1)::text,
                ''::text)                               AS box12ccodebox12c,
       NULLIF(COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 3), '_'::text, 2)::text,
                       ''::text), '')::numeric          AS box12camountbox12c,
       COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 4), '_'::text, 1)::text,
                ''::text)                               AS box12dcodebox12d,
       NULLIF(COALESCE(SPLIT_PART(SPLIT_PART(box12.description, ','::text, 4), '_'::text, 2)::text,
                       ''::text), '')::numeric          AS box12damountbox12d,
       CASE
           WHEN w2.statutoryemployee = 'On'::text THEN TRUE
           ELSE FALSE
END                                          AS statutoryemployeebox13,
       CASE
           WHEN w2.retirementplan = 'On'::text THEN TRUE
           ELSE FALSE
END                                          AS retirementplanbox13,
       CASE
           WHEN w2.thirdpartysickpay = 'On'::text THEN TRUE
           ELSE FALSE
END                                          AS thirdpartysickpaybox13,
       CASE
           WHEN o1.amount > 0::numeric THEN (
                                                    CASE
                                                        WHEN o1.type IS NULL AND o1.jurisdictiontaxid IS NULL
                                                            THEN o1.description
                                                        ELSE (COALESCE(o1.state, ' '::character varying)::text ||
                                                              ' '::text) || o1.type
                                                        END || ' $'::text) || o1.amount
           WHEN peffcrasick.userid IS NOT NULL THEN peffcrasick.description
           ELSE ''::text
END                                          AS otherline1box14,
       CASE
           WHEN o2.amount > 0::numeric THEN (
                                                    CASE
                                                        WHEN o2.type IS NULL AND o2.jurisdictiontaxid IS NULL
                                                            THEN o2.description
                                                        ELSE (COALESCE(o2.state, ' '::character varying)::text ||
                                                              ' '::text) || o2.type
                                                        END || ' $'::text) || o2.amount
           WHEN o1.amount > 0::numeric AND peffcrasick.userid IS NOT NULL
               THEN peffcrasick.description
           ELSE ''::text
END                                          AS otherline2box14,
       CASE
           WHEN o3.amount > 0::numeric THEN (
                                                    CASE
                                                        WHEN o3.type IS NULL AND o3.jurisdictiontaxid IS NULL
                                                            THEN o3.description
                                                        ELSE (COALESCE(o3.state, ' '::character varying)::text ||
                                                              ' '::text) || o3.type
                                                        END || ' $'::text) || o3.amount
           WHEN o2.amount > 0::numeric AND peffcrasick.userid IS NOT NULL
               THEN peffcrasick.description
           ELSE ''::text
END                                          AS otherline3box14,
       CASE
           WHEN o3.amount > 0::numeric AND peffcrasick.userid IS NOT NULL
               THEN peffcrasick.description
           ELSE ''::text
END                                          AS otherline4box14,
       ''::text                                         AS otherline5box14,
       ''::text                                         AS otherline6box14,
       ''::text                                         AS otherline7box14,
       ''::text                                         AS otherline8box14,
       COALESCE(s1.state, ''::character varying)        AS statename1box15,
       COALESCE(s2.state, ''::character varying)        AS statename2box15,
       COALESCE(s3.state, ''::character varying)        AS statename3box15,
       COALESCE(s4.state, ''::character varying)        AS statename4box15,
       COALESCE(csi1.accountnumber, ''::text)           AS stateid1box15,
       COALESCE(csi2.accountnumber, ''::text)           AS stateid2box15,
       COALESCE(csi3.accountnumber, ''::text)           AS stateid3box15,
       COALESCE(csi4.accountnumber, ''::text)           AS stateid4box15,
       s1.subjectwages                                  AS statewagestipsetc1box16,
       s2.subjectwages                                  AS statewagestipsetc2box16,
       s3.subjectwages                                  AS statewagestipsetc3box16,
       s4.subjectwages                                  AS statewagestipsetc4box16,
       s1.amount                                        AS stateincometax1box17,
       s2.amount                                        AS stateincometax2box17,
       s3.amount                                        AS stateincometax3box17,
       s4.amount                                        AS stateincometax4box17,
       l1.subjectwages                                  AS localwagestipsetc1box18,
       l2.subjectwages                                  AS localwagestipsetc2box18,
       l3.subjectwages                                  AS localwagestipsetc3box18,
       l4.subjectwages                                  AS localwagestipsetc4box18,
       l1.amount                                        AS localincometax1box19,
       l2.amount                                        AS localincometax2box19,
       l3.amount                                        AS localincometax3box19,
       l4.amount                                        AS localincometax4box19,
       CASE
           WHEN l1.type = 'SCHL'::text THEN 'SD '::text ||
                                            SPLIT_PART(l1.jurisdictiontaxid, '-'::text, 5)
           ELSE COALESCE(SPLIT_PART(l1.description, '- '::text, 2), ''::text)
END                                          AS localname1box20,
       CASE
           WHEN l2.type = 'SCHL'::text THEN 'SD '::text ||
                                            SPLIT_PART(l2.jurisdictiontaxid, '-'::text, 5)
           ELSE COALESCE(SPLIT_PART(l2.description, '- '::text, 2), ''::text)
END                                          AS localname2box20,
       CASE
           WHEN l3.type = 'SCHL'::text THEN 'SD '::text ||
                                            SPLIT_PART(l3.jurisdictiontaxid, '-'::text, 5)
           ELSE COALESCE(SPLIT_PART(l3.description, '- '::text, 2), ''::text)
END                                          AS localname3box20,
       CASE
           WHEN l4.type = 'SCHL'::text THEN 'SD '::text ||
                                            SPLIT_PART(l4.jurisdictiontaxid, '-'::text, 5)
           ELSE COALESCE(SPLIT_PART(l4.description, '- '::text, 2), ''::text)
END                                          AS localname4box20,
       c.id                                             AS companyid,
       u.id                                             AS userid
FROM w2data2023 w2
         JOIN company c ON c.id = w2.companyid AND c.democompany = FALSE
         JOIN appuser u ON u.id = w2.userid
         LEFT JOIN useraddress ua ON ua.userid = u.id AND ua.enddate IS NULL
         LEFT JOIN companyaddress ca ON ca.companyid = c.id AND ca.enddate IS NULL
         LEFT JOIN w2localdata2023 l1
                   ON l1.rownum = 1 AND l1.companyid = w2.companyid AND l1.userid = w2.userid AND
                      l1."group" = 'LOCAL'::text
         LEFT JOIN w2localdata2023 l2 ON l2.companyid = l1.companyid AND l2.userid = l1.userid AND
                                         l2."group" = l1."group" AND l2.rownum = 2
         LEFT JOIN w2localdata2023 l3 ON l3.companyid = l1.companyid AND l3.userid = l1.userid AND
                                         l3."group" = l1."group" AND l3.rownum = 3
         LEFT JOIN w2localdata2023 l4 ON l4.companyid = l1.companyid AND l4.userid = l1.userid AND
                                         l4."group" = l1."group" AND l4.rownum = 4
         LEFT JOIN w2localdata2023 s1
                   ON s1.rownum = 1 AND s1.companyid = w2.companyid AND s1.userid = w2.userid AND
                      s1."group" = 'STATE'::text
         LEFT JOIN w2localdata2023 s2 ON s2.companyid = s1.companyid AND s2.userid = s1.userid AND
                                         s2."group" = s1."group" AND s2.rownum = 2
         LEFT JOIN w2localdata2023 s3 ON s3.companyid = s1.companyid AND s3.userid = s1.userid AND
                                         s3."group" = s1."group" AND s3.rownum = 3
         LEFT JOIN w2localdata2023 s4 ON s4.companyid = s1.companyid AND s4.userid = s1.userid AND
                                         s4."group" = s1."group" AND s4.rownum = 4
         LEFT JOIN w2localdata2023 o1
                   ON o1.rownum = 1 AND o1.companyid = w2.companyid AND o1.userid = w2.userid AND
                      o1."group" = 'OTHER'::text
         LEFT JOIN w2localdata2023 o2 ON o2.companyid = o1.companyid AND o2.userid = o1.userid AND
                                         o2."group" = o1."group" AND o2.rownum = 2
         LEFT JOIN w2localdata2023 o3 ON o3.companyid = o1.companyid AND o3.userid = o1.userid AND
                                         o3."group" = o1."group" AND o2.rownum = 3
         LEFT JOIN (SELECT e.userid,
                           u_1.taxpayeridentifier,
                           ('$'::text || SUM(pe.amount)) ||
                           ' FFCRA Sick ($511/day)'::text AS description
                    FROM paymentearning pe
                             JOIN payment p ON p.id = pe.paymentid
                             JOIN employee e ON e.id = p.employeeid
                             JOIN appuser u_1 ON u_1.id = e.userid
                    WHERE p.paydate >= '2023-01-01'::date
                      AND p.paydate <= '2023-12-31'::date
                      AND (p.status = ANY (ARRAY ['PAID'::text, 'IMPORTED'::text]))
                      AND pe.type = 'EMERGENCY_FFCRA_SICK'::text
                      AND pe.amount <> 0::numeric
                    GROUP BY e.userid, u_1.taxpayeridentifier) peffcrasick
                   ON peffcrasick.userid = w2.userid
         LEFT JOIN companytaxjurisdiction csi1
                   ON csi1.state = s1.state::text AND csi1.companyid = s1.companyid AND
                      csi1.taxtype = 'SIT' AND csi1.active = TRUE
         LEFT JOIN companytaxjurisdiction csi2
                   ON csi2.state = s2.state::text AND csi2.companyid = s2.companyid AND
                      csi2.taxtype = 'SIT' AND csi2.active = TRUE
         LEFT JOIN companytaxjurisdiction csi3
                   ON csi3.state = s3.state::text AND csi3.companyid = s3.companyid AND
                      csi3.taxtype = 'SIT' AND csi3.active = TRUE
         LEFT JOIN companytaxjurisdiction csi4
                   ON csi4.state = s4.state::text AND csi4.companyid = s4.companyid AND
                      csi4.taxtype = 'SIT' AND csi4.active = TRUE
         LEFT JOIN box122023 box12 ON box12.companyid = w2.companyid AND box12.userid = w2.userid
WHERE w2.year = 2023::double precision
  AND (c.enddate IS NULL OR c.enddate >= '2023-12-31'::date)
  AND c.democompany = FALSE
  AND c.status IN ('LIVE', 'ONBOARDING', 'READ_ONLY');


CREATE OR REPLACE VIEW annualfw3
(
  year,
  companyid,
  wagestipsandothercompensation,
  federalincometaxwithheld,
  federalincometaxwages,
  socialsecuritywages,
  socialsecuritytaxwithheld,
  medicarewagesandtips,
  medicaretaxwithheld,
  socialsecuritytips,
  countofemployees
)
AS
SELECT paymentagg.year,
       paymentagg.companyid,
       ( SELECT sum(tax1.subjectwages) AS sum
FROM paymenttax tax1
WHERE (tax1.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND tax1.type = 'FIT'::text) AS wagestipsandothercompensation,
    ( SELECT sum(tax2.amount) AS sum
FROM paymenttax tax2
WHERE (tax2.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND tax2.type = 'FIT'::text) AS federalincometaxwithheld,
    ( SELECT sum(tax2a.subjectwages) AS sum
FROM paymenttax tax2a
WHERE (tax2a.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND tax2a.type = 'FIT'::text) AS federalincometaxwages,
    (( SELECT sum(tax3a.subjectwages) AS earningtypes
    FROM paymenttax tax3a
    WHERE tax3a.type = 'FICA'::text AND (tax3a.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))) - COALESCE(( SELECT sum(earning3b.amount) AS sum
    FROM paymenttax tax3b
    JOIN paymentearning earning3b ON earning3b.paymentid = tax3b.paymentid AND earning3b.type = 'TIPS'::text
    WHERE tax3b.type = 'FICA'::text AND (tax3b.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest))), 0::numeric) AS socialsecuritywages,
    ( SELECT sum(tax4.amount) AS sum
FROM paymenttax tax4
WHERE (tax4.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND tax4.type = 'FICA'::text) AS socialsecuritytaxwithheld,
    ( SELECT sum(tax5.subjectwages) AS sum
FROM paymenttax tax5
WHERE (tax5.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax5.type = ANY (ARRAY['MEDI'::text]))) AS medicarewagesandtips,
    ( SELECT sum(tax6.amount) AS sum
FROM paymenttax tax6
WHERE (tax6.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax6.type = ANY (ARRAY['MEDI'::text, 'MEDI2'::text]))) AS medicaretaxwithheld,
    ( SELECT sum(earning7.amount) AS sum
FROM paymenttax tax7
    JOIN paymentearning earning7 ON earning7.paymentid = tax7.paymentid AND earning7.type = 'TIPS'::text
WHERE tax7.type = 'FICA'::text AND (tax7.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest))) AS socialsecuritytips,
    ( SELECT count(DISTINCT e.userid) AS count
FROM payment payment9
    JOIN employee e ON e.id = payment9.employeeid AND e.employmenttype = 'EMPLOYEE'::text
WHERE (payment9.id IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND payment9.grossearnings > 0::numeric) AS countofemployees
FROM employeepaymentidsbyyearcompany paymentagg;
