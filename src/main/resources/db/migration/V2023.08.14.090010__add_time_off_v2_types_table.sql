
-- add TimeOffType table
CREATE TABLE IF NOT EXISTS TimeOffType (
    id              BIGSERIAL PRIMARY KEY,
    createdAt       TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt       TIMES<PERSON>MP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    companyId       BIGINT REFERENCES Company (id) ON UPDATE CASCADE ON DELETE CASCADE,
    name            TEXT NOT NULL
);
CREATE UNIQUE INDEX TimeOffType_companyId_name_idx ON TimeOffType (companyId, name) WHERE companyId IS NOT NULL;
CREATE UNIQUE INDEX TimeOffType_defaults_name_idx ON TimeOffType (name) WHERE companyId IS NULL;
