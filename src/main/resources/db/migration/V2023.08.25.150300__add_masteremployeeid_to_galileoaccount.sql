-- add column
alter table galileoaccount add column masteremployeeid bigint null;

-- populate masteremployeeid column
-- user could not generate an account without an employee record, and we
-- don't delete employee records, so this should not fail.
-- Using max employee id to satisfy the constraint and can one-off fix ambiguous cases 
update
	galileoaccount g
set
	masteremployeeid = (
	select
		min(id)
	from
		employee e
	where
		e.userid = g.userid);
	
-- add foreign key constraint
alter table galileoaccount add constraint galileoaccount_masteremployeeid_fkey foreign key (masteremployeeid) references employee(id);
alter table galileoaccount alter column masteremployeeid set not null;
