ALTER TABLE stateunemploymentrate
    ADD COLUMN IF NOT EXISTS type VARCHAR(10) NOT NULL default 'SUIT';

ALTER TABLE stateunemploymentrate DROP CONSTRAINT IF EXISTS
    stateunemploymentrate_deferred_phasing_check;

ALTER TABLE stateunemploymentrate ADD CONSTRAINT
    stateunemploymentrate_deferred_phasing_check
        EXCLUDE USING gist (companyid WITH =, residentstate WITH =, type WITH  = , daterange((startdate - '1 day'::INTERVAL)::DATE, enddate) WITH &&)
        DEFERRABLE INITIALLY DEFERRED
