CREATE OR REPLACE VIEW employeepaymentidsbyyearstatecompany AS
 SELECT date_part('year'::text, payment.paydate) AS year,
    payment.companyid,
    COALESCE(split_part(sit.jurisdictiontaxid, '-'::text, 1), split_part(suta.jurisdictiontaxid, '-'::text, 1), (sc.code)::text) AS recipientstatecode,
    array_agg(DISTINCT payment.id ORDER BY payment.id) AS paymentids
   FROM ((((payment payment
     JOIN employee e ON (((payment.employeeid = e.id) AND (e.employmenttype = 'EMPLOYEE'::text))))
     JOIN statecodes sc ON (((sc.state)::text = payment.residentstate)))
     LEFT JOIN paymenttax sit ON (((sit.paymentid = payment.id) AND ((sit.amount > (0)::numeric) OR (sit.subjectwages > (0)::numeric) OR (sit.uncappedsubjectwages > (0)::numeric)) AND (sit.type = 'SIT'::text))))
     LEFT JOIN paymenttax suta ON (((suta.paymentid = payment.id) AND ((suta.amount > (0)::numeric) OR (suta.subjectwages > (0)::numeric) OR (suta.uncappedsubjectwages > (0)::numeric)) AND (suta.type = 'ER_SUTA'::text))))
     LEFT JOIN paymentearning ri ON (((ri.paymentid = payment.id) AND ((ri.amount > (0)::numeric) AND (ri.type = 'REIMBURSEMENT'::text))))
  WHERE ((payment.status = ANY (ARRAY['PAID'::text, 'IMPORTED'::text])) AND ((payment.grossearnings - coalesce(ri.amount, 0.00) <> (0)::numeric) OR (payment.netearnings - coalesce(ri.amount, 0.00) <> (0)::numeric)))
  GROUP BY (date_part('year'::text, payment.paydate)), payment.companyid, COALESCE(split_part(sit.jurisdictiontaxid, '-'::text, 1), split_part(suta.jurisdictiontaxid, '-'::text, 1), (sc.code)::text);

CREATE OR REPLACE VIEW annualfw3state AS
 SELECT paymentagg.year,
    paymentagg.companyid,
    paymentagg.recipientstatecode,
    ( SELECT sum(tax1.subjectwages) AS sum
           FROM paymenttax tax1
          WHERE ((tax1.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax1.type = 'FIT'::text))) AS wagestipsandothercompensation,
    ( SELECT sum(tax2.amount) AS sum
           FROM paymenttax tax2
          WHERE ((tax2.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax2.type = 'FIT'::text))) AS federalincometaxwithheld,
    ( SELECT sum(tax2a.subjectwages) AS sum
           FROM paymenttax tax2a
          WHERE ((tax2a.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax2a.type = 'FIT'::text))) AS federalincometaxwages,
    (( SELECT sum(tax3a.subjectwages) AS earningtypes
           FROM paymenttax tax3a
          WHERE ((tax3a.type = 'FICA'::text) AND (tax3a.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))) - COALESCE(( SELECT sum(earning3b.amount) AS sum
           FROM (paymenttax tax3b
             JOIN paymentearning earning3b ON (((earning3b.paymentid = tax3b.paymentid) AND (earning3b.type = 'TIPS'::text))))
          WHERE ((tax3b.type = 'FICA'::text) AND (tax3b.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))), (0)::numeric)) AS socialsecuritywages,
    ( SELECT sum(tax4.amount) AS sum
           FROM paymenttax tax4
          WHERE ((tax4.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax4.type = 'FICA'::text))) AS socialsecuritytaxwithheld,
    ( SELECT sum(tax5.subjectwages) AS sum
           FROM paymenttax tax5
          WHERE ((tax5.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax5.type = ANY (ARRAY['MEDI'::text, 'MEDI2'::text])))) AS medicarewagesandtips,
    ( SELECT sum(tax6.amount) AS sum
           FROM paymenttax tax6
          WHERE ((tax6.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax6.type = ANY (ARRAY['MEDI'::text, 'MEDI2'::text])))) AS medicaretaxwithheld,
    ( SELECT sum(earning7.amount) AS sum
           FROM (paymenttax tax7
             JOIN paymentearning earning7 ON (((earning7.paymentid = tax7.paymentid) AND (earning7.type = 'TIPS'::text))))
          WHERE ((tax7.type = 'FICA'::text) AND (tax7.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))) AS socialsecuritytips,
    ( SELECT sum(tax8.subjectwages) AS sum
           FROM paymenttax tax8
          WHERE ((tax8.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax8.type = 'SIT'::text) AND (split_part(tax8.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS statewagestipsetc,
    ( SELECT sum(tax9.amount) AS sum
           FROM paymenttax tax9
          WHERE ((tax9.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax9.type = 'SIT'::text) AND (split_part(tax9.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS stateincometaxwithheld,
    ( SELECT sum(tax10.subjectwages) AS sum
           FROM paymenttax tax10
          WHERE ((tax10.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax10.type = ANY (ARRAY['CITY'::text, 'SCHL'::text])) AND (split_part(tax10.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS localwagestipsetc,
    ( SELECT sum(tax11.amount) AS sum
           FROM paymenttax tax11
          WHERE ((tax11.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax11.type = ANY (ARRAY['CITY'::text, 'SCHL'::text])) AND (split_part(tax11.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS localincometaxwithheld,
    ( SELECT count(DISTINCT payment12.employeeid) AS count
           FROM payment payment12
          WHERE (payment12.id IN ( SELECT unnest(paymentagg.paymentids) AS unnest))) AS countofemployees
   FROM employeepaymentidsbyyearstatecompany paymentagg;
   
   
CREATE OR REPLACE VIEW annualstatef940equivalent AS
 SELECT paymentagg.year,
    paymentagg.companyid,
    paymentagg.recipientstatecode,
    ( SELECT sum(tax1.subjectwages) AS sum
           FROM paymenttax tax1
          WHERE ((tax1.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax1.type = 'FIT'::text))) AS wagestipsandothercompensation,
    ( SELECT sum(tax2.amount) AS sum
           FROM paymenttax tax2
          WHERE ((tax2.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax2.type = 'FIT'::text))) AS federalincometaxwithheld,
    ( SELECT sum(tax2a.subjectwages) AS sum
           FROM paymenttax tax2a
          WHERE ((tax2a.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax2a.type = 'FIT'::text))) AS federalincometaxwages,
    (( SELECT sum(tax3a.subjectwages) AS earningtypes
           FROM paymenttax tax3a
          WHERE ((tax3a.type = 'FICA'::text) AND (tax3a.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))) - COALESCE(( SELECT sum(earning3b.amount) AS sum
           FROM (paymenttax tax3b
             JOIN paymentearning earning3b ON (((earning3b.paymentid = tax3b.paymentid) AND (earning3b.type = 'TIPS'::text))))
          WHERE ((tax3b.type = 'FICA'::text) AND (tax3b.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))), (0)::numeric)) AS socialsecuritywages,
    ( SELECT sum(tax4.amount) AS sum
           FROM paymenttax tax4
          WHERE ((tax4.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax4.type = 'FICA'::text))) AS socialsecuritytaxwithheld,
    ( SELECT sum(tax5.subjectwages) AS sum
           FROM paymenttax tax5
          WHERE ((tax5.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax5.type = ANY (ARRAY['MEDI'::text, 'MEDI2'::text])))) AS medicarewagesandtips,
    ( SELECT sum(tax6.amount) AS sum
           FROM paymenttax tax6
          WHERE ((tax6.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax6.type = ANY (ARRAY['MEDI'::text, 'MEDI2'::text])))) AS medicaretaxwithheld,
    ( SELECT sum(earning7.amount) AS sum
           FROM (paymenttax tax7
             JOIN paymentearning earning7 ON (((earning7.paymentid = tax7.paymentid) AND (earning7.type = 'TIPS'::text))))
          WHERE ((tax7.type = 'FICA'::text) AND (tax7.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)))) AS socialsecuritytips,
    ( SELECT sum(tax8.subjectwages) AS sum
           FROM paymenttax tax8
          WHERE ((tax8.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax8.type = 'SIT'::text) AND (split_part(tax8.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS statewagestipsetc,
    ( SELECT sum(tax9.amount) AS sum
           FROM paymenttax tax9
          WHERE ((tax9.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax9.type = 'SIT'::text) AND (split_part(tax9.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS stateincometaxwithheld,
    ( SELECT sum(tax10.subjectwages) AS sum
           FROM paymenttax tax10
          WHERE ((tax10.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax10.type = ANY (ARRAY['CITY'::text, 'SCHL'::text])) AND (split_part(tax10.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS localwagestipsetc,
    ( SELECT sum(tax11.amount) AS sum
           FROM paymenttax tax11
          WHERE ((tax11.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax11.type = ANY (ARRAY['CITY'::text, 'SCHL'::text])) AND (split_part(tax11.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS localincometaxwithheld,
    ( SELECT count(DISTINCT payment12.employeeid) AS count
           FROM payment payment12
          WHERE (payment12.id IN ( SELECT unnest(paymentagg.paymentids) AS unnest))) AS countofemployees,
    ( SELECT sum(tax12.amount) AS sum
           FROM paymenttax tax12
          WHERE ((tax12.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax12.type = 'ER_SUTA'::text) AND (split_part(tax12.jurisdictiontaxid, '-'::text, 1) = paymentagg.recipientstatecode))) AS stateunemploymentwithheld,
    ( SELECT sum(tax13.amount) AS sum
           FROM paymenttax tax13
          WHERE ((tax13.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax13.type = 'ER_FUTA'::text))) AS federalunemploymentwithheld,
    ( SELECT sum(payment14.grossearnings) AS sum
           FROM payment payment14
          WHERE (payment14.id IN ( SELECT unnest(paymentagg.paymentids) AS unnest))) AS totalgrossearningstoallemployees
   FROM employeepaymentidsbyyearstatecompany paymentagg;

CREATE OR REPLACE VIEW annualf940scheduleastate AS
 SELECT paymentagg.year,
    paymentagg.companyid,
    paymentagg.recipientstatecode,
     ( SELECT sum(payment.grossearnings) AS sum
           FROM payment
          WHERE (payment.id IN ( SELECT unnest(paymentagg.paymentids) AS unnest))) AS totalpaymentstoallemployees,
     ( SELECT sum(tax1.amount) AS sum
           FROM paymenttax tax1
          WHERE ((tax1.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax1.type = 'ER_FUTA'::text))) AS taxamount,
    ( SELECT sum(tax2.subjectwages) AS sum
           FROM paymenttax tax2
          WHERE ((tax2.paymentid IN ( SELECT unnest(paymentagg.paymentids) AS unnest)) AND (tax2.type = 'ER_FUTA'::text))) AS taxsubjectwages
   FROM employeepaymentidsbyyearstatecompany paymentagg;
