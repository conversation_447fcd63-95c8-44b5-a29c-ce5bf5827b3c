ALTER TABLE public.payment 
    DROP CONSTRAINT IF EXISTS payment_errortype_enum_ck,
    ADD CONSTRAINT payment_errortype_enum_ck 
        CHECK (
            (
                (errortype)::text = ANY (
                    ARRAY[
                        ('MISSING_OR_INVALID_ADDRESS'::character varying)::text, 
                        ('MISSING_BANK_ACCOUNT'::character varying)::text, 
                        ('INVALID_BANK_ACCOUNT'::character varying)::text, 
                        ('MISSING_WITHHOLDINGS'::character varying)::text, 
                        ('MISSING_EMPLOYEE_POSITION'::character varying)::text, 
                        ('MISSING_TAX_PAYER_IDENTIFIER'::character varying)::text, 
                        ('MISSING_OR_INVALID_PAY_PERIOD'::character varying)::text, 
                        ('MISSING_UNEMPLOYMENT_RATES'::character varying)::text, 
                        ('MISSING_CONTRIBUTION_DEDUCTION_LIMITS'::character varying)::text, 
                        ('FAILED_VALIDATION'::character varying)::text, 
                        ('NEGATIVE_NET'::character varying)::text, 
                        ('FUTURE_FINALIZED'::character varying)::text, 
                        ('FUNDING_FAILED'::character varying)::text, 
                        ('PREVIOUS_NOT_FINALIZED'::character varying)::text, 
                        ('UNEXPECTED'::character varying)::text, 
                        ('MISSING_PAY_CARD'::character varying)::text, 
                        ('INSUFFICIENT_NET_FOR_ACH_FEE'::character varying)::text, 
                        ('ANOTHER_PAYMENT_CALCULATION_IN_PROGRESS'::character varying)::text, 
                        ('MISSING_TAX_JURISDICTION_DATA'::character varying)::text, 
                        ('TAX_AMOUNT_LARGER_THAN_WAGES'::character varying)::text, 
                        ('MISSING_MARITAL_STATUS_ABBREVIATION'::character varying)::text, 
                        ('CONFLICTING_OTP_EARNING_TAX_CALCULATION_METHODS'::character varying)::text, 
                        ('CONFLICTING_OTP_TAX_CALCULATION_METHODS'::character varying)::text, 
                        ('FAILED_LOADING_PAYMENT_DETAILS'::character varying)::text, 
                        ('NEGATIVE_XTD'::character varying)::text
                    ]
                )
            )
        )