-- Creating a composite index on paymentearning for filtering and grouping by paymentId and type
CREATE INDEX IF NOT EXISTS paymentearning_paymentid_type_idx
    ON paymentearning (paymentId, type);

-- Dropping the old single-column index on paymentId from paymentearning as it is now redundant
DROP INDEX IF EXISTS paymentearning_payment_idx;

-- Creating an index on paymentwarning to improve EXISTS subquery performance on paymentId
CREATE INDEX IF NOT EXISTS paymentwarning_paymentid_idx
    ON paymentwarning (paymentId);