DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'payrollcalendar') THEN

        -- Add the archivedat column, if it does not already exist
        ALTER TABLE payrollcalendar
        ADD COLUMN IF NOT EXISTS archivedat TIMESTAMP WITHOUT TIME ZONE NULL;


        -- Add the constraint to ensure archivedat is set if status is 'ARCHIVED'
        ALTER TABLE payrollcalendar
        DROP CONSTRAINT IF EXISTS payrollcalendar_archivedat_ck;

        ALTER TABLE payrollcalendar
        ADD CONSTRAINT payrollcalendar_archivedat_ck
        CHECK (status <> 'ARCHIVED' OR (status = 'ARCHIVED' AND archivedat IS NOT NULL));


       -- Recreate the check constraint for status including 'ARCHIVED'
        ALTER TABLE payrollcalendar
        DROP CONSTRAINT IF EXISTS payrollcalendar_status_ck;

        ALTER TABLE payrollcalendar
        ADD CONSTRAINT payrollcalendar_status_ck
        CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED'));

    END IF;
END $$;