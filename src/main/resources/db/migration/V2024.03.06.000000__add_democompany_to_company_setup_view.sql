drop view if exists company_setup_view;
CREATE OR REPLACE VIEW company_setup_view AS
WITH t AS (
    SELECT
        'MTCSV01-COMPANY' AS format_id,
        '' AS company_group_name,
        c.id AS payroll_code,
        COALESCE(wl.name, '') AS payroll_description,
        LEFT(TRIM(CASE WHEN c.legalentityname LIKE '%|%' THEN SPLIT_PART(c.legalentityname, '|', 2) ELSE c.legalentityname END), 40) AS company_name,
        '' AS reporting_payroll_code,
        'DYN3' AS name_control,
        TO_CHAR(COALESCE((SELECT MIN(achfile.createdat) FROM achfile JOIN company c ON achfile.companyid = c.id), NOW()), 'YYYYMMDD') AS company_start_date,
        'Y' AS quarterly_wage_reporting_flag,
        'Y' AS worksite_reporting_flag,
        'Y' AS yearend_employee_filing_flag,
        'Y' AS wage_attachment_flag,
        '' AS cash_service_level,
        'N' AS kind_of_employer,
        '' AS naics_code,
        'N' AS fein_type,
        COALESCE(c.federalein, '') AS fein,
        '' AS reserved,
        'N' AS Nine44_filer,
        CASE WHEN c.status = 'LIVE' THEN 'A' ELSE 'I' END AS company_status,
        'F' AS service_level,
        '' AS in_care_of,
        COALESCE(a.line1, '') AS address_line_1,
        COALESCE(a.line2, '') AS address_line_2,
        COALESCE(a.city, '') AS city,
        COALESCE(a.state, '') AS state,
        COALESCE(a.postalcode, '') AS zip_code,
        'US' AS country_code,
        COALESCE(wl.symmetrypsdcode, '') AS psd_code,
        COALESCE(c.firstname, '') AS first_name,
        '' AS middle_initial,
        COALESCE(c.lastname, '') AS last_name,
        CASE WHEN LENGTH(phone) = 10 THEN SUBSTRING(phone, 1, 3) ELSE '' END AS phone_area_code,
        CASE WHEN LENGTH(phone) = 10 THEN SUBSTRING(phone, 4, 3) || '-' || SUBSTRING(phone, 7, 4) ELSE '' END AS phone_number,
        '' AS phone_extension,
        '' AS fax_area_code,
        '' AS fax_number,
        COALESCE(email, '') AS email_address,
        COALESCE(ba.accountname, '') AS bank_account_name,
        COALESCE(ba.routingnumber, '') AS transit_routing_number,
        COALESCE(ba.accountnumber, '') AS bank_account_number,
        SUBSTRING(COALESCE(ba.accounttype, ''), 1, 1) AS bank_account_type,
        1 AS draft_days,
        c.democompany as demo_company
    FROM
        company c
            JOIN companyaddress a ON c.id = a.companyid AND NOW() BETWEEN a.startdate AND COALESCE(a.enddate, '12/31/2100')
            JOIN worklocation wl ON c.id = wl.companyid AND wl.archivedAt IS NULL
            LEFT JOIN companybankaccount ba ON c.id = ba.companyid
    WHERE
        c.status = 'LIVE'
)
SELECT
    format_id, company_group_name, payroll_code, payroll_description, company_name, reporting_payroll_code, name_control, company_start_date, quarterly_wage_reporting_flag, worksite_reporting_flag,
    yearend_employee_filing_flag, wage_attachment_flag, cash_service_level, kind_of_employer, naics_code, company_start_date AS effective_date, fein_type, fein, reserved, Nine44_filer, company_status, service_level,
    company_name AS company_dba_name, in_care_of, address_line_1, address_line_2, city, state, zip_code, country_code, psd_code, first_name, middle_initial, last_name, phone_area_code, phone_number, phone_extension,
    fax_area_code, fax_number, email_address, bank_account_name, transit_routing_number, bank_account_number, bank_account_type, draft_days, demo_company
FROM t;

commit;
