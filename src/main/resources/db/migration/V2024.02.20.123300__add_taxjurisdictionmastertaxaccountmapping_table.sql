-- Create sequence and new table if it does not exist
CREATE SEQUENCE IF NOT EXISTS public.taxjurisdictionmastertaxaccountmapping_id_seq;

CREATE TABLE IF NOT EXISTS public.taxjurisdictionmastertaxaccountmapping
(
    id bigint NOT NULL DEFAULT nextval('taxjurisdictionmastertaxaccountmapping_id_seq'::regclass),
    accountidentifier character varying (20) COLLATE pg_catalog."default" NOT NULL,
    taxjurisdictionid character varying COLLATE pg_catalog."default" NOT NULL,
    createdat timestamp without time zone NOT NULL,
    updatedat timestamp without time zone NOT NULL,
    CONSTRAINT taxjurisdictionmastertaxaccountmapping_pkey PRIMARY KEY (id),
    CONSTRAINT taxjurisdictionmastertaxaccountmapping_taxjurisdiction_fk FOREIGN KEY (taxjurisdictionid)
        REFERENCES public.taxjurisdiction (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)

TABLESPACE pg_default;

create UNIQUE index IF NOT EXISTS taxjurisdictionmastertaxaccountmapping_taxjurisdiction_idx on taxjurisdictionmastertaxaccountmapping(taxjurisdictionid);
create UNIQUE index IF NOT EXISTS taxjurisdictionmastertaxaccountmapping_accountidentifier_idx on taxjurisdictionmastertaxaccountmapping(accountidentifier);
