-- add new column to shift table
ALTER TABLE bulktaxformfilinginstance ADD COLUMN IF NOT EXISTS taxfilingformdefinitionid bigint  references taxfilingformdefinition(id);

-- Typically do this in a second script, but this table is empty in prod (and will be when it gets released) so just doing it in the same script
update bulktaxformfilinginstance set taxfilingformdefinitionid = (select id from taxfilingformdefinition where taxformname='Utah Unemployment Quarterly Return' and enddate is null) where taxfilingformdefinitionid is null;

ALTER TABLE bulktaxformfilinginstance ALTER COLUMN taxfilingformdefinitionid SET NOT NULL;
