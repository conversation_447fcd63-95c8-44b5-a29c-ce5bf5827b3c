DO
$$
    DECLARE
max_id INT;
BEGIN
EXECUTE 'ALTER TABLE payrun ALTER COLUMN id DROP DEFAULT';

EXECUTE 'DROP SEQUENCE IF EXISTS basepayrun_id_seq';

SELECT COALESCE(MAX(id), 0) INTO max_id FROM payrun;

EXECUTE 'CREATE SEQUENCE basepayrun_id_seq ' ||
        'START WITH ' || (max_id + 1) ||
        ' INCREMENT BY 200';

EXECUTE 'SELECT setval(''basepayrun_id_seq'', ' || (max_id + 1) || ', true)';
END
$$;
