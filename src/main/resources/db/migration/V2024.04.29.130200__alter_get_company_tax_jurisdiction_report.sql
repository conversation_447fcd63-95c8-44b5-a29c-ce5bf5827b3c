DROP FUNCTION IF EXISTS get_company_tax_jurisdiction_report(start_date TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
                                                  end_date TIMES<PERSON>MP WITHOUT TIME ZONE, page_number INTEGER,
                                                  page_size INTEGER, include_demo_companies BOOLEAN);
CREATE OR REPLACE FUNCTION get_company_tax_jurisdiction_report(start_date TIMESTAMP WITHOUT TIME ZONE,
                                                    end_date TIMESTAMP WITHOUT TIME ZONE, page_number INTEGER,
                                                    page_size INTEGER, include_demo_companies BOOLEAN)
    RETURNS TABLE
            (
                id              CHARACTER VARYING,
                companyid       BIGINT,
                companyname     TEXT,
                name            CHARACTER VARYING,
                state           CHARACTER VARYING,
                taxtype         CHARACTER VARYING,
                filingfrequency TEXT,
                accountnumber   CHARACTER VARYING,
                active          BOOLEAN,
                addedby         TEXT,
                autoadded       BOOLEAN,
                createdat       TIMESTAMP WITHOUT TIME ZONE,
                updatedat       TIMESTAMP WITHOUT TIME ZONE,
                grossearnings   NUMERIC,
                subjectwages    NUMERIC
            )
    LANGUAGE plpgsql
AS
$$
BEGIN
    RETURN QUERY
        SELECT ctj.taxjurisdictionid                                          AS id,
               c.id                                                           AS companyId,
               c.displayname                                                  AS companyname,
               tj.name                                                        AS name,
               ctj.state,
               ctj.taxType,
               ctjf.filingfrequency,
               ctj.accountNumber,
               ctj.active,
               CASE WHEN ctj.createdbyuserid IS NOT NULL THEN au.username END AS addedby,
               ctj.auto                                                       AS autoadded,
               ctj.createdAt,
               ctj.updatedAt,
               (SELECT COALESCE(SUM(p.grossearnings), 0)  -- added distinct and fixed join
                FROM (SELECT DISTINCT p.* FROM payment p
                                                   JOIN paymenttax pt ON p.id = pt.paymentid
                      WHERE p.companyid = ctj.companyid
                        AND pt.jurisdictiontaxid = ctj.taxjurisdictionid
                        AND p.status = 'PAID'
                        AND p.paydate BETWEEN start_date AND end_date) AS p)              AS grossearnings,
               (SELECT COALESCE(SUM(pt.subjectwages), 0)  -- added distinct and fixed join
                FROM (SELECT DISTINCT pt.* FROM paymenttax pt
                                                    JOIN payment p ON p.id = pt.paymentid
                      WHERE p.companyid = ctj.companyid
                        AND pt.jurisdictiontaxid = ctj.taxjurisdictionid
                        AND p.status = 'PAID'
                        AND p.paydate BETWEEN start_date AND end_date) AS pt )             AS subjectwages
        FROM companytaxjurisdiction ctj
                 JOIN
             company c ON ctj.companyId = c.id AND (include_demo_companies OR c.democompany = FALSE)
                 JOIN
             taxjurisdiction tj ON ctj.taxJurisdictionId = tj.id
                 LEFT JOIN
             companytaxjurisdictionfilingfrequency ctjf ON ctjf.companytaxjurisdictionid = ctj.id
                 LEFT JOIN
             appuser au ON ctj.createdbyuserid = au.id
        WHERE ctj.createdAt BETWEEN start_date AND end_date
        ORDER BY ctj.createdAt DESC
        OFFSET (page_number) * page_size LIMIT page_size;
END;
$$;
