CREATE TABLE IF NOT EXISTS JobWorkersCompClass
(
    Id                     bigserial primary key,
    WorkersCompClassId     BIGINT NOT NULL REFERENCES WorkersCompClass (id) ON UPDATE CASCADE ON DELETE CASCADE,
    ExternalJobId          VARCHAR(36) NOT NULL,
    StartDate              DATE NOT NULL,
    EndDate                DATE NULL,
    CreatedAt              TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt              TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS JobWorkersCompClass_ExternalJobId ON JobWorkersCompClass (ExternalJobId);
