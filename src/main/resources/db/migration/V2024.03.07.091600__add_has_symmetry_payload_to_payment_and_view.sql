DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1
                       FROM information_schema.columns
                       WHERE table_name = 'payment'
                         AND column_name = 'hassymmetrypayload') THEN
            ALTER TABLE payment
                ADD COLUMN hassymmetrypayload bool default false;
        END IF;

        drop view if exists receivablepaymenttotalbycompany;
        drop view if exists receivablepayment;
        drop view if exists decoratedpayment;

        -- Re Create views
        create view decoratedpayment
        as
        SELECT p.*,
               (SELECT "position".id
                FROM employeeposition "position"
                WHERE "position".employeeid = p.employeeid
                  AND p.fordate >= "position".startdate
                  AND p.fordate <= COALESCE("position".enddate::timestamp without time zone,
                                            'infinity'::timestamp without time zone)) AS employeepositionid,
               CASE
                   WHEN p.status = 'DELETED'::text THEN 'DELETED'::text
                   WHEN p.status = 'ERRORED'::text AND (p.errortype::text = ANY
                                                        (ARRAY ['MISSING_OR_INVALID_ADDRESS'::text, 'MISSING_BANK_ACCOUNT'::text, 'MISSING_WITHHOLDINGS'::text, 'MISSING_EMPLOYEE_POSITION'::text, 'MISSING_UNEMPLOYMENT_RATES'::text, 'INVALID_BANK_ACCOUNT'::text, 'MISSING_TAX_PAYER_IDENTIFIER'::text, 'MISSING_PAY_CARD'::text]))
                       THEN 'UNPAYABLE_WORKER'::text
                   WHEN p.status = 'ERRORED'::text THEN 'ERROR'::text
                   WHEN p.status = ANY (ARRAY ['SUBMITTED'::text, 'PAID'::text, 'IMPORTED'::text]) THEN 'PAID'::text
                   WHEN p.status = ANY (ARRAY ['STARTED'::text, 'AWAITING_CALCULATION'::text, 'AWAITING_PRE_CALCULATION'::text])
                       THEN 'PROCESSING'::text
                   WHEN p.status = ANY (ARRAY ['APPROVED'::text, 'FUNDED'::text]) THEN 'PENDING_PAYMENT'::text
                   WHEN p.status = ANY (ARRAY ['PREPARED_FOR_FUNDING_REQUEST'::text, 'FUNDING_REQUESTED'::text])
                       THEN 'PENDING_FUNDING'::text
                   WHEN (SELECT every(c.verificationenabled) = true AND COALESCE(every(s.verified), true) = false
                         FROM effectiveshift s
                                  JOIN companytimetrackingconfiguration c ON c.companyid = p.companyid
                                  JOIN payperiod pp ON pp.id = p.payperiodid
                         WHERE s.employeeid = p.employeeid
                           AND s.worklocationeffectivepunchoutdate >= pp.startdate
                           AND s.worklocationeffectivepunchoutdate <= pp.enddate
                           AND p.type = 'PAYROLL'::text) THEN 'PENDING_VERIFICATION'::text
                   WHEN (p.status = ANY (ARRAY ['PRE_CALCULATED'::text, 'CALCULATED'::text])) AND p.scheduledate IS NOT NULL AND
                        p.scheduleapprovedbyuserid IS NOT NULL THEN 'SCHEDULED'::text
                   WHEN (p.status = ANY (ARRAY ['PRE_CALCULATED'::text, 'CALCULATED'::text])) AND p.scheduledate IS NOT NULL AND
                        p.schedulesource = 'AUTO_PAYROLL'::text THEN 'AUTO_APPROVED'::text
                   WHEN p.status = 'PRE_CALCULATED'::text THEN 'READY_TO_CALCULATE'::text
                   WHEN p.status = 'CALCULATED'::text THEN 'PENDING_APPROVAL'::text
                   ELSE NULL::text
                   END                                                                AS querystatus
        FROM payment p;

        create view receivablepayment
        as
        WITH earliestfundingrecord AS (SELECT file.companyid,
                                              min(file.createdat) AS fundingtimestamp
                                       FROM achfilerecord record
                                                JOIN achfile file ON record.achfileid = file.id
                                       WHERE record.achfilerecordtype = 'FUNDING_DEBIT'::text
                                         AND file.status = 'PAID'::text
                                       GROUP BY file.companyid),
             timeframe AS (SELECT company.id                                                          AS companyid,
                                  COALESCE(earliestfundingrecord.fundingtimestamp, company.createdat) AS earliesttimestamp
                           FROM company
                                    LEFT JOIN earliestfundingrecord ON earliestfundingrecord.companyid = company.id)
        SELECT p.*,
               pp.startdate AS payperiodstartdate,
               pp.enddate   AS payperiodenddate,
               u.firstname,
               u.middlename,
               u.lastname,
               e.employmenttype,
               ag.name      AS approvalgroupname
        FROM decoratedpayment p
                 JOIN timeframe ON timeframe.companyid = p.companyid
                 JOIN company c ON c.id = p.companyid
                 JOIN payperiod pp ON pp.id = p.payperiodid
                 JOIN employee e ON e.id = p.employeeid
                 JOIN appuser u ON u.id = e.userid
                 LEFT JOIN approvalgroup ag ON ag.id = e.approvalgroupid
        WHERE p.status = 'PAID'::text
          AND (p.grossearnings - COALESCE(p.previouslypaidearnings, 0::numeric)) > 0::numeric
          AND p.approvedat > timeframe.earliesttimestamp
          AND p.fundingachfilerecordid IS NULL;

        create view receivablepaymenttotalbycompany
        as
        SELECT c.id                        AS companyid,
               count(p.*)                  AS paymentcount,
               sum(p.grossearnings)        AS totalgrossearnings,
               sum(p.netearnings)          AS totalnetearnings,
               sum(p.totaltaxesee)         AS totaltaxesee,
               sum(p.totaltaxeser)         AS totaltaxeser,
               sum(p.totalcontributionser) AS totalcontributionser,
               sum(p.totalexpense)         AS totalexpense,
               sum(p.totaltaxfunding)      AS totaltaxfunding,
               sum(p.totalfunding)         AS totalfunding
        FROM company c
                 LEFT JOIN receivablepayment p ON p.companyid = c.id
        GROUP BY c.id, p.companyid;

    END
$$;



