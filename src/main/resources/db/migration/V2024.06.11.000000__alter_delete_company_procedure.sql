CREATE OR R<PERSON>LACE PROCEDURE delete_company_data(company_id INT, include_company BOOLEAN)
    LANGUAGE plpgsql
AS
$$
BEGIN
    DELETE
    FROM accounttransaction at
        USING account a
    WHERE at.accountid = a.id
      AND a.companyid = company_id;

    DELETE
    FROM account
    WHERE companyid = company_id;

    DELETE
    FROM employeeposition ep
        USING employee e
    WHERE ep.employeeid = e.id
      AND e.companyid = company_id;

    DELETE
    FROM paymentdeductionoverride pdo
        USING employeecontributiondeduction ecd
    WHERE pdo.employeecontributiondeductionid = ecd.id
      AND ecd.companyid = company_id;

    DELETE
    FROM employeecontributiondeductionamountdetail ecdad
        USING employeecontributiondeduction ecd
    WHERE ecdad.employeecontributiondeductionid = ecd.id
      AND ecd.companyid = company_id;

    DELETE
    FROM paymentcontributiondeduction pcd
        USING employeecontributiondeduction ecd
    WHERE pcd.employeecontributiondeductionid = ecd.id
      AND ecd.companyid = company_id;

    DELETE
    FROM employeecontributiondeduction
    WHERE companyid = company_id;

    DELETE
    FROM paymentrequestrecipient pr
        USING payment p
    WHERE pr.paymentid = p.id
      AND p.companyid = company_id;

    DELETE
    FROM achfilerecord afr
        USING achfile af
    WHERE afr.achfileid = af.id
      AND af.companyid = company_id;

    DELETE
    FROM paymentwarning pw
        USING payment p
    WHERE pw.paymentid = p.id
      AND p.companyid = company_id;

    DELETE
    FROM payment
    WHERE companyid = company_id;

    DELETE
    FROM workerlegalworklocation wlwl
    WHERE wlwl.employeeid IN (SELECT e.id
                              FROM employee e
                              WHERE e.companyid = company_id)
       OR wlwl.worklocationid IN (SELECT wl.id FROM worklocation wl WHERE companyid = company_id)
       OR wlwl.companyid = company_id;

    DELETE
    FROM workeronboarding wo
        USING employee e
    WHERE wo.employeeid = e.id
      AND e.companyid = company_id;

    DELETE
    FROM employeedocument ed
        USING folder f
    WHERE ed.companyid = company_id
       OR (ed.folderid = f.id AND f.companyid = company_id);

    DELETE
    FROM achfilebatch afb
        USING payperiod pp
    WHERE afb.payperiodid = pp.id
      AND pp.companyid = company_id;

    DELETE
    FROM companyfunding
    WHERE companyid = company_id;

    DELETE
    FROM paymentrequest
    WHERE companyid = company_id;

    DELETE
    FROM payperiod
    WHERE companyid = company_id;

    DELETE
    FROM payperiodpreference
    WHERE companyid = company_id;

    DELETE
    FROM timeoffrequest
    WHERE companyid = company_id;

    DELETE
    FROM ptobalanceadjustment ptoba
        USING employee e
    WHERE ptoba.employeeid = e.id
      AND e.companyid = company_id;

    DELETE
    FROM grossearning ge
    WHERE companyid = company_id
       OR ge.worklocationid IN (SELECT wl.id FROM worklocation wl WHERE companyid = company_id)
       OR ge.workerroleid IN (SELECT wr.id FROM workerrole wr WHERE companyid = company_id);

    DELETE
    FROM scheduledshiftrequest
    WHERE companyid = company_id;

    DELETE
    FROM scheduledshift
    WHERE companyid = company_id;

    DELETE
    FROM scheduleemployeeavailability
    WHERE companyid = company_id;

    DELETE
    FROM shift
    WHERE companyid = company_id;

    DELETE
    FROM projecttime pt USING employee e
    WHERE pt.employeeid = e.id
      AND e.companyid = company_id;

    DELETE
    FROM galileoaccount g USING employee e
    WHERE g.masteremployeeid = e.id
      AND e.companyid = company_id;

    DELETE
    FROM employee
    WHERE companyid = company_id;

    DELETE
    FROM companyautopayrollsettings
    WHERE companyid = company_id;

    DELETE
    FROM companybankaccount
    WHERE companyid = company_id;

    DELETE
    FROM companybillingconfiguration
    WHERE companyid = company_id;

    DELETE
    FROM companycontributiondeductiontypeconfig
    WHERE companyid = company_id;

    DELETE
    FROM companycreditdetails
    WHERE companyid = company_id;

    DELETE
    FROM companydistribution
    WHERE companyid = company_id;

    DELETE
    FROM companyduediligencedetails
    WHERE companyid = company_id;

    DELETE
    FROM companyprojecttrackingconfiguration
    WHERE companyid = company_id;

    DELETE
    FROM companyrole
    WHERE companyid = company_id;

    DELETE
    FROM companytimetrackingconfiguration
    WHERE companyid = company_id;

    DELETE
    FROM federalunemploymentoverride
    WHERE companyid = company_id;

    DELETE
    FROM companyschedulingconfiguration
    WHERE companyid = company_id;

    DELETE
    FROM stateunemploymentrate
    WHERE companyid = company_id;

    DELETE
    FROM webhookevent
    WHERE companyid = company_id;

    DELETE
    FROM approvalgroup
    WHERE companyid = company_id;

    DELETE
    FROM companyaddress
    WHERE companyid = company_id;

    DELETE
    FROM companydocument
    WHERE companyid = company_id;

    DELETE
    FROM companyemployeeimportjob
    WHERE companyid = company_id;

    DELETE
    FROM enabledholiday
    WHERE companyid = company_id;

    DELETE
    FROM companysetting
    WHERE companyid = company_id;

    DELETE
    FROM folder
    WHERE companyid = company_id;

    DELETE
    FROM form
    WHERE companyid = company_id;

    DELETE
    FROM holidaypolicyadditionalhours hpah
        USING holidaypolicy hp
    WHERE hpah.policyid = hp.id
      AND hp.companyid = company_id;

    DELETE
    FROM holidaypolicyeligibility hpe
        USING holidaypolicy hp
    WHERE hpe.policyid = hp.id
      AND hp.companyid = company_id;

    DELETE
    FROM holidaypolicymultiplier hpm
        USING holidaypolicy hp
    WHERE hpm.policyid = hp.id
      AND hp.companyid = company_id;

    DELETE
    FROM holidaypolicy
    WHERE companyid = company_id;


    DELETE
    FROM ptopolicy
    WHERE companyid = company_id;

    DELETE
    FROM taxtypecreditaccumulation ttca
        USING taxtypeaccumulation tta
    WHERE ttca.taxtypeaccumulationid = tta.id
      AND tta.companyid = company_id;


    DELETE
    FROM taxtypeaccumulation
    WHERE companyid = company_id;


    DELETE
    FROM tenantapikey
    WHERE companyid = company_id;


    DELETE
    FROM workerrole
    WHERE companyid = company_id;

    DELETE
    FROM workerscompclass
    WHERE companyid = company_id;

    DELETE
    FROM worklocationhours wlh
        USING worklocation wl
    WHERE wlh.worklocationid = wl.id
      AND wl.companyid = company_id;

    DELETE
    FROM terminalaccesscode
    WHERE companyid = company_id;

    DELETE
    FROM worklocation
    WHERE companyid = company_id;

    DELETE
    FROM task
    WHERE companyid = company_id;

    DELETE
    FROM project
    WHERE companyid = company_id;

    DELETE FROM achfilebatch afb -- ADD ACH FILE BATCH DELETION
        USING achfile af
    WHERE afb.achfileid = af.id
      AND af.companyid = company_id;

    DELETE FROM journalentry je -- ADD JOURNAL ENTRY DELETION
        USING achfile af
    WHERE je.achfileid = af.id
      AND af.companyid = company_id;

    DELETE -- ADD ACH FILE DELETION
    FROM achfile a
    WHERE a.companyid = company_id;

    DELETE
    FROM companytaxjurisdiction ctj -- ADD CTJ DELETION
    WHERE ctj.companyid = company_id;

    DELETE
    FROM companyembedcomponentsettings cecs -- ADD CECS DELETION
    WHERE cecs.companyid = company_id;

    -- If the boolean flag is true, also delete the company record
    IF include_company THEN
        DELETE
        FROM company
        WHERE id = company_id;
    END IF;
END;
$$
