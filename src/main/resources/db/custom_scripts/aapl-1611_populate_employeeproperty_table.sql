-- Delete any records that have not changed from the staging table
delete from
    r365employeeproperty_staging s
using
    employee e,
    employeeproperty p,
	company c
where
    s.companyid = c.id and
    s.payrollemployeeid = e.integrationid and
    e.id = p.employeeid and
    (
        (p.enddate is null) or
        (p.enddate = e.enddate)
    ) and
    (
        (s.gender is not null and p.fieldtype = 'gender' and p.fieldvalue = s.gender) or
        (s.employmenttype is not null and p.fieldtype = 'employmenttype' and p.fieldvalue = s.employmenttype)
    );

-- Update the end date of existing records (if any) where the value is changing
update
    employeeproperty p set enddate = s.importdate + interval '-1 day'
from
    r365employeeproperty_staging s
        join employee e on s.payrollemployeeid = e.integrationid
        join company c on s.companyid = c.id
where
    e.id = p.employeeid and
    p.enddate is null and
    (
        (s.gender is not null and p.fieldtype = 'gender' and p.fieldvalue <> s.gender) or
        (s.employmenttype is not null and p.fieldtype = 'employmenttype' and p.fieldvalue <> s.employmenttype)
    );

-- Update the end date for terminated employees
update
    employeeproperty p set enddate = e.enddate
from
    r365employeeproperty_staging s
        join employee e on s.payrollemployeeid = e.integrationid
        join company c on s.companyid = c.id
where
    e.id = p.employeeid and
    e.enddate is not null and
    p.enddate is null;

-- Insert the new records
insert into
    employeeproperty (employeeid, startdate, fieldtype, fieldvalue)
select
    e.id,
    coalesce(ep.enddate + interval '1 day', e.startdate),
    'gender',
    s.gender
from
    r365employeeproperty_staging s
        join employee e on s.payrollemployeeid = e.integrationid
        join company c on s.companyid = c.id
        left join employeeproperty ep on e.id = ep.employeeid and
        (
            ((s.gender is not null or s.gender <> ep.fieldvalue) and ep.fieldtype = 'gender')
        );

insert into
    employeeproperty (employeeid, startdate, fieldtype, fieldvalue)
select
    e.id,
    coalesce(ep.enddate + interval '1 day', e.startdate),
    'employmenttype',
    s.employmenttype
from
    r365employeeproperty_staging s
        join employee e on s.payrollemployeeid = e.integrationid
        join company c on s.companyid = c.id
        left join employeeproperty ep on e.id = ep.employeeid and
        (
            ((s.employmenttype is not null or s.employmenttype <> ep.fieldvalue) and ep.fieldtype = 'employmenttype')
        );

-- Finally, delete all remaining staging records
truncate table r365employeeproperty_staging;

