-- Company Setup
with wl as
    (
        select distinct Line1, Line2, City,State, postalcode, symmetrypsdcode, companyid from worklocation where archivedat is null
    ), t as
    (
        select
            'MTCSV01-COMPANY' as format_id,
            '' as company_group_name,
            c.id as payroll_code,
            '' as payroll_description,
    left(trim(case when c.legalentityname like '%|%' then split_part(c.legalentityname,'|', 2) else c.legalentityname end), 40) as company_name, -- Need consistency in naming pattern here. Sometimes using '|' to delineate instance and legal name. Sometimes legal name makes no sense (id 389 for example)
    '' as reporting_payroll_code,
    'DYN3' as name_control,
    case when coalesce((select min(achfile.createdat) from achfile join company c on achfile.companyid = c.id), now()) > '2024-01-01' then to_char(coalesce((select min(achfile.createdat) from achfile join company c on achfile.companyid = c.id), now()), 'YYYYMMDD') else '20240101' end as company_start_date, -- This is being set as our go live date, but the actual date we need to send should be when we went live with Mastertax with the company
        'Y' as quarterly_wage_reporting_flag,
        'Y' as worksite_reporting_flag,
        'Y' as yearend_employee_filing_flag,
        'Y' as wage_attachment_flag,
        '' as cash_service_level,
        'N' as kind_of_employer,
        '' as naics_code, -- Will need to get this for 2024 as we will be using W2 module
        case when c.status = 'LIVE' then '20240101' else to_char(greatest(coalesce(c.enddate, now()), c.startdate), 'YYYYMMDD') end as effective_date,
        'N' as fein_type, -- Is it possible we have some that should be marked as common payer parent?
        coalesce(c.federalein, '') as fein,
        '' as reserved,
        'N' as Nine44_filer, -- Should this be 'Y'?
        case when c.status = 'LIVE' then 'A' else 'I' end as company_status,
        'F' as service_level, -- Will this always be full service?
        '' as in_care_of,
        coalesce(a.line1, '') as address_line_1,
        coalesce(a.line2, '') as address_line_2,
        coalesce(a.city, '') as city,
        coalesce(a.state, '') as state,
        coalesce(a.postalcode, '') as zip_code,
        'US' as country_code,
        coalesce(wl.symmetrypsdcode, '') as psd_code,
        coalesce(c.firstname, '') as first_name,
        '' as middle_initial,
        coalesce(c.lastname, '') as last_name,
        case when length(phone) = 10 then SUBSTRING(phone, 1, 3) else '' end as phone_area_code,
        case when length(phone) = 10 then SUBSTRING(phone, 4, 3) || '-' || SUBSTRING(phone, 7, 4) else '' end as phone_number,
        '' as phone_extension,
        '' as fax_area_code,
        '' as fax_number,
        coalesce(email, '') as email_address,
        coalesce(ba.accountname, '') as bank_account_name,
        coalesce(ba.routingnumber, '') as transit_routing_number,
        coalesce(ba.accountnumber, '') as bank_account_number,
        substring(coalesce(ba.accounttype, ''), 1, 1) as bank_account_type,
        1 as draft_days
    from
        company c join companyaddress a on c.id = a.companyId and now() between a.startdate and greatest(coalesce(a.enddate, '12/31/2100'), c.startdate)
        left join wl on c.id = wl.companyid and lower(replace(replace(wl.line1, '.', ''), ' ', '')) = lower(replace(replace(a.line1, '.', ''), ' ', '')) and lower(replace(wl.city, ' ', '')) = lower(replace(a.city, ' ', '')) and wl.state = a.state and left(replace(wl.postalcode, ' ', ''), 5) = left(replace(a.postalcode, ' ', ''), 5)
        left join companybankaccount ba on c.id = ba.companyid
    where
        c.democompany = False and (c.status = 'LIVE' or (c.status in ('READ_ONLY', 'DISABLED') and c.enddate < now() + interval '30 days')) and greatest(coalesce(c.enddate, '2024-01-01'), c.startdate) >= '2024-01-01'
), r as (
select
    format_id,
    left(company_group_name, 40) as company_group_name,
    payroll_code,
    left(payroll_description, 40) as payroll_description,
    left(company_name, 40) as company_name,
    reporting_payroll_code,
    name_control,
    company_start_date,
    quarterly_wage_reporting_flag,
    worksite_reporting_flag,
    yearend_employee_filing_flag,
    wage_attachment_flag,
    cash_service_level,
    kind_of_employer,
    naics_code,
    effective_date,
    fein_type,
    fein,
    reserved,
    Nine44_filer,
    company_status,
    service_level,
    left(company_name, 40) as company_dba_name,
    left(in_care_of, 40) as in_care_of,
    left(address_line_1, 40) as address_line_1,
    left(address_line_2, 40) as address_line_2,
    left(city, 25) as city,
    state,
    zip_code,
    country_code,
    psd_code,
    left(first_name, 40) as first_name,
    middle_initial,
    left(last_name, 40) as last_name,
    phone_area_code,
    phone_number,
    phone_extension,
    fax_area_code,
    fax_number,
    left(email_address, 40) as email_address,
    left(bank_account_name, 23) as bank_account_name,
    left(transit_routing_number, 9) as transit_routing_number,
    left(bank_account_number, 17) as bank_account_number,
    bank_account_type,
    draft_days,
    case when length(company_group_name) > 40 then length(company_group_name) else 0 end as company_group_name_length_40,
    case when length(payroll_description) > 40 then length(payroll_description) else 0 end as payroll_description_length_40,
    case when length(company_name) > 40 then length(company_name) else 0 end as company_name_length_40,
    case when length(in_care_of) > 40 then length(in_care_of) else 0 end as in_care_of_length_40,
    case when length(address_line_1) > 40 then length(address_line_1) else 0 end as address_line_1_length_40,
    case when length(address_line_2) > 40 then length(address_line_2) else 0 end as address_line_2_length_40,
    case when length(city) > 25 then length(city) else 0 end as city_length_25,
    case when length(first_name) > 40 then length(first_name) else 0 end as first_name_length_40,
    case when length(last_name) > 40 then length(last_name) else 0 end as last_name_length_40,
    case when length(email_address) > 40 then length(email_address) else 0 end as email_address_length_40,
    case when length(bank_account_name) > 23 then length(bank_account_name) else 0 end as bank_account_name_length_23,
    case when length(transit_routing_number) > 9 then length(transit_routing_number) else 0 end as transit_routing_number_length_9,
    case when length(bank_account_number) > 17 then length(bank_account_number) else 0 end as bank_account_number_length_17
from t
)
select
    format_id, company_group_name, payroll_code, payroll_description, company_name,	reporting_payroll_code, name_control, company_start_date, quarterly_wage_reporting_flag, worksite_reporting_flag,
    yearend_employee_filing_flag, wage_attachment_flag, cash_service_level, kind_of_employer, naics_code,effective_date, fein_type, fein, reserved, Nine44_filer, company_status, service_level,
    company_dba_name, in_care_of, address_line_1, address_line_2, city, state, zip_code, country_code, psd_code, first_name, middle_initial, last_name, phone_area_code, phone_number, phone_extension,
    fax_area_code, fax_number, email_address, bank_account_name, transit_routing_number, bank_account_number, bank_account_type, draft_days
from
    r
