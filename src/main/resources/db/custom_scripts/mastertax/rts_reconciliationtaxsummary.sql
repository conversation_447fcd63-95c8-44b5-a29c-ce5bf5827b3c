-- Reconciliation Tax Summary Data run for a quarter at a time
-- explain analyze
with t as 
(
    select
        p.employeeid,
        'MTCSV01-RTS' as format_id,
        '' as company_group_name,
        c.id as payroll_code,
        CAST(date_trunc('quarter', p.paydate) + interval '3 months' - interval '1 day' AS date) as quarter_end_date,
        tjm.accountidentifier as tax_code,
        pt.jurisdictiontaxId as taxjurisdictionid,
        case when split_part(pt.jurisdictiontaxId, '-', 1)::int > 0 and split_part(pt.jurisdictiontaxId, '-', 3)::int > 0 then true else false end as is_local_tax,
        case when split_part(pt.jurisdictiontaxId, '-', 1)::int > 0 and split_part(pt.jurisdictiontaxId, '-', 2)::int = 0 then true else false end as is_state_tax,
        case when split_part(pt.jurisdictiontaxId, '-', 4) in ('SUI', 'ER_SUTA', 'ER_SUTA_SC') then true else false end as is_state_unemployment,
        sum(pt.amount) as qtd_tax,
        case when tjm.accountidentifier = 'KY0015-138' and sum(pt.subjectwages) > 25000 then 25000 else sum(pt.subjectwages) end as qtd_taxable_wages,
        sum(p.grossearnings) as qtd_gross_wages,
        max(pt.ytdamount) as ytd_tax,
        max(pt.ytdsubjectwages) as ytd_taxable_wages,
        max(p.ytdgrossearnings) as ytd_gross_wages,
        case when tjm.accountidentifier = 'KY0015-138' and sum(pt.subjectwages) > 25000 then sum(pt.subjectwages) - 25000 else 0 end as qtd_taxable_wages_exceeding_limit,
        case when tjm.accountidentifier = 'KY0015-138' and sum(p.grossearnings) > 25000 then sum(p.grossearnings) - 25000 else 0 end as qtd_gross_wages_exceeding_limit,
        case when split_part(pt.jurisdictiontaxId, '-', 1)::int = 0 then sum(pt.amount) else 0 end as qtd_federal_tax_withheld,
        -- max(case when (select count(1) from employeetaxjurisdiction ejt where ejt.taxjurisdictionid = pt.jurisdictiontaxid and ejt.employeeid = p.employeeid and p.fordate between ejt.startdate and coalesce(ejt.enddate, '2100-12-31') and isexempt = true) > 0 then 1 else 0 end) tax_exempt_count,
        max(case when etjresides.isexempt or etjworks.isexempt then 1 else 0 end) as is_tax_exempt,
        max(case when etjresides.id is not null then 1 else 0 end) as resides_in_jurisdiction,
        max(case when etjworks.id is not null then 1 else 0 end) as works_in_jurisdiction,
        max(case when CAST(date_trunc('quarter', p.paydate) AS date) = CAST(date_trunc('month', p.paydate) AS date) then 1 else 0 end) as employee_paid_first_month, -- Paid in first month?
        max(case when CAST(date_trunc('quarter', p.paydate) + interval '1 month' AS date) = CAST(date_trunc('month', p.paydate) AS date) then 1 else 0 end) as employee_paid_second_month, -- Paid in second month?
        max(case when CAST(date_trunc('quarter', p.paydate) + interval '2 month' AS date) = CAST(date_trunc('month', p.paydate) AS date) then 1 else 0 end) as employee_paid_third_month, -- Paid in third month?
        max(case when CAST(date_trunc('quarter', p.paydate) + interval '11 day' AS date) between pp.startdate and pp.enddate then 1 else 0 end) as employee_paid_for_12th_of_first_month, -- Paid for a period that includes 12th day of 1st month?
        max(case when CAST(date_trunc('quarter', p.paydate) + interval '1 month' + interval '11 day' AS date) between pp.startdate and pp.enddate then 1 else 0 end) as employee_paid_for_12th_of_second_month, -- Paid for a period that includes 12th day of 2nd month?
        max(case when CAST(date_trunc('quarter', p.paydate) + interval '2 month' + interval '11 day' AS date) between pp.startdate and pp.enddate then 1 else 0 end) as employee_paid_for_12th_of_third_month, -- Paid for a period that includes 12th day of 3rd month?
        max(case when date_part('month', p.paydate) between 10 and 12 then 1 else 0 end) as has_date_in_fourth_quarter,
        max(p.grossearnings - pt.ytdsubjectwages) as total_exempt_wages
    from
        company c join companyaddress ca on c.id = ca.companyId and now() between ca.startdate and coalesce(ca.enddate, '2100-12-31')
        left join worklocation wl on c.id = wl.companyid and wl.archivedat is null and lower(replace(replace(wl.line1, '.', ''), ' ', '')) = lower(replace(replace(ca.line1, '.', ''), ' ', '')) and lower(replace(wl.city, ' ', '')) = lower(replace(ca.city, ' ', '')) and wl.state = ca.state and left(replace(wl.postalcode, ' ', ''), 5) = left(replace(ca.postalcode, ' ', ''), 5)
        join payment p on c.id = p.companyId and p.status in ('PAID','IMPORTED')
        join payperiod pp on p.payperiodid = pp.id
        join paymenttax pt on p.id = pt.paymentid
        left join employeetaxjurisdiction etjresides on etjresides.taxjurisdictionid = pt.jurisdictiontaxid and etjresides.employeeid = p.employeeid and p.fordate between etjresides.startdate and coalesce(etjresides.enddate, '2100-12-31') and etjresides.isresident = true
        left join employeetaxjurisdiction etjworks on etjworks.taxjurisdictionid = pt.jurisdictiontaxid and etjworks.employeeid = p.employeeid and p.fordate between etjworks.startdate and coalesce(etjworks.enddate, '2100-12-31') and etjworks.isresident = false
        left join taxjurisdictionmastertaxaccountmapping tjm on pt.jurisdictiontaxId = tjm.taxjurisdictionid
    where
        c.democompany = False 
        and (c.status = 'LIVE' or (c.status in ('READ_ONLY', 'DISABLED') and c.enddate < now() + interval '30 days')) and coalesce(c.enddate, '2024-01-01') >= '2024-01-01'
        and p.paydate between '2024-01-01' and '2024-03-31' 
        -- and c.id = 137
        -- and c.id < 500
        -- and c.id between 500 and 999
        -- and c.id between 1000 and 1499
        -- and c.id between 1500 and 2500
    group by
        c.id, c.federalein, tjm.accountidentifier, pt.jurisdictiontaxId, CAST(date_trunc('quarter', p.paydate) + interval '3 months' - interval '1 day' AS date), p.employeeId
), agg as
(
    select 
        format_id,
        company_group_name,
        payroll_code,
        to_char(quarter_end_date, 'YYYYMMDD') as quarter_end_date,
        tax_code,
        taxjurisdictionid,
        is_local_tax,
        is_state_tax,
        is_state_unemployment,
        sum(qtd_tax) as qtd_tax,
        sum(qtd_taxable_wages) as qtd_taxable_wages,
        sum(qtd_gross_wages) as qtd_gross_wages,
        sum(ytd_tax) as ytd_tax,
        sum(ytd_taxable_wages) as ytd_taxable_wages,
        sum(ytd_gross_wages) as ytd_gross_wages,
        sum(qtd_taxable_wages_exceeding_limit) as qtd_taxable_wages_exceeding_limit,
        sum(qtd_gross_wages_exceeding_limit) as qtd_gross_wages_exceeding_limit,
        sum(employee_paid_first_month) as employees_paid_first_month,
        sum(employee_paid_second_month) as employees_paid_second_month,
        sum(employee_paid_third_month) as employees_paid_third_month,
        sum(employee_paid_for_12th_of_first_month) as employees_paid_for_12th_of_first_month,
        sum(employee_paid_for_12th_of_second_month) as employees_paid_for_12th_of_second_month,
        sum(employee_paid_for_12th_of_third_month) as employees_paid_for_12th_of_third_month,
        sum(qtd_federal_tax_withheld) as qtd_federal_tax_withheld,
        sum(is_tax_exempt) as employees_exempt_from_tax,
        sum(resides_in_jurisdiction) as employees_residing_in_jurisdiction,
        sum(works_in_jurisdiction) as employees_working_in_jurisdiction,
        sum(total_exempt_wages) as total_exempt_wages,
        case when max(has_date_in_fourth_quarter) > 0 then true else false end as is_fourth_quarter,
        count(1) as total_employees_in_quarter
    from 
        t 
    where 
        tax_code is not null
    group by
        format_id,
        company_group_name,
        payroll_code,
        quarter_end_date,
        tax_code,
        taxjurisdictionid,
        is_local_tax,
        is_state_unemployment,
        is_state_tax
)
select 
    format_id,
    company_group_name,
    payroll_code,
    quarter_end_date,
    tax_code,
    qtd_tax,
    qtd_taxable_wages,
    qtd_gross_wages,
    ytd_tax,
    ytd_taxable_wages,
    ytd_gross_wages,
    qtd_taxable_wages_exceeding_limit,
    qtd_gross_wages_exceeding_limit,
    -- Statistic 1 = Qualified sick wages subject to social security tax for FE0000-116, FE0000-216, and FE0000-316 for the quarter
    --               or, qualified family leave wages subject to social security tax for FE0000-117, FE0000-217, and FE0000-317 for the quarter
    --               or, number of employees earning wages on the 12th day of the 1st month in the quarter for state unemployment
    --               and for all other non-federal tax jurisdictions, the total number of employees receiving pay in the first month of the quarter
    case when tax_code in ('FE0000-116', 'FE0000-216', 'FE0000-316') then
        0 -- Don't know the answer yet on getting sick wages
    else
        case when tax_code in ('FE0000-117', 'FE0000-217', 'FE0000-317') then
            0 -- Don't know the answer yet on getting family leave wages
        else
            case when is_state_unemployment then
                employees_paid_for_12th_of_first_month
            else
                case when is_local_tax then
                    employees_paid_first_month
                else
                    0
                end
            end
        end
    end as statistic_1,
    -- Statistic 2 = Qualified sick wages subject to social security tax for FE0000-116, FE0000-216, and FE0000-316 for the entire year to date
    --               or, qualified family leave wages subject to social security tax for FE0000-117, FE0000-217, and FE0000-317 for the entire year to date
    --               or, number of employees earning wages on the 12th day of the 1st month in the quarter for state unemployment
    --               and for all other non-federal tax jurisdictions, the total number of employees receiving pay in the first month of the quarter
    case when tax_code in ('FE0000-116', 'FE0000-216', 'FE0000-316') then
        0 -- Don't know the answer yet on getting sick wages
    else
        case when tax_code in ('FE0000-117', 'FE0000-217', 'FE0000-317') then
            0 -- Don't know the answer yet on getting family leave wages
        else
            case when is_state_unemployment then
                employees_paid_for_12th_of_second_month
            else
                case when is_local_tax then
                    employees_paid_second_month
                else
                    0
                end
            end
        end
    end as statistic_2,
    -- Statistic 3 = Number of part time employees (presumably active, whether worked or not???) for VT0000-001
    --               or, number of employees earning wages on the 12th day of the 1st month in the quarter for state unemployment
    --               and for all other non-federal tax jurisdictions, the total number of employees receiving pay in the first month of the quarter
    case when tax_code in ('VT0000-001') then
        0 -- Don't know the answer yet on getting sick wages
    else
        case when is_state_unemployment or tax_code in ('FE0000-001','FE0000-203','FE0000-303','FE0000-100') then
            employees_paid_for_12th_of_third_month
        else
            case when is_local_tax then
                employees_paid_third_month
            else
                0
            end
        end
    end as statistic_3,
    -- Statistic 4 = Number of female employees earning wages for pay periods including the 12th day of the first month for ME0000-010 and ME0000-016
    --               or, total quarter to date federal tax withheld for UT0000-001 (meaning )
    --               and for all others, the total number of employees earning wages in the quarter
    case when tax_code in ('ME0000-010', 'ME0000-016') then
        0 -- Don't have the data for the female employees...exists in R365 hcmEmployee.gender, but is not being filled in. It also has a "Choose not to identify" which may not fly with government responses. 
    else
        case when tax_code = 'UT0000-001' then
            qtd_federal_tax_withheld
        else
            total_employees_in_quarter
        end
    end as statistic_4,
    -- Statistic 5 = Number of employees exempt from the taxes that are like PA____-051 and PA____-147 and PA9905-LST
    --               or, number of female employees earning wages for pay periods including the 12th day of the second month for ME0000-010 and ME0000-016
    case when tax_code like 'PA____-051' or tax_code like 'PA____-147' or tax_code = 'PA9905-LST' or tax_code = 'WV9006-001' then
        employees_exempt_from_tax
    else
        case when tax_code in ('ME0000-010', 'ME0000-016') then
            0 -- Don't have the data for the female employees...exists in R365 hcmEmployee.gender, but is not being filled in. It also has a "Choose not to identify" which may not fly with government responses. 
        else
            0
        end
    end as statistic_5,
    -- Statistic 6 = Number of employees exempt from the taxes that are like PA____-051 and PA____-147 and PA9905-LST
    --               or, number of female employees earning wages for pay periods including the 12th day of the third month for ME0000-010, ME0000-016, and VT0000-010
    case when tax_code in ('KY0048-138', 'PA2049-001') then
        employees_working_in_jurisdiction
    else
        case when tax_code in ('NJ9001-001') then
            employees_residing_in_jurisdiction
        else
            case when tax_code in ('ME0000-010','ME0000-016', 'VT0000-010') then
                0 -- Don't have the data for the female employees...exists in R365 hcmEmployee.gender, but is not being filled in. It also has a "Choose not to identify" which may not fly with government responses. 
            else
                0
            end
        end
    end as statistic_6,
    case when is_fourth_quarter then 0 else 0 end as w2_count,
    case when is_fourth_quarter and is_state_tax then 0 else 0 end as total_employee_count,
    case when is_fourth_quarter and tax_code = 'FE0000-010' then total_exempt_wages else 0 end as ytd_futa_exempt_wages,
    '' as trace_id
from 
    agg
where payroll_code = 1311
order by 
    payroll_code, tax_code


/* 
select * from company where id = 1301

select count(*) from payment where paidat between '2024-03-01' and '2024-04-01' and status = 'PAID'
select distinct status from employee

select * from appuser limit 10

select sum(p.grossearnings), date_part('month', paydate), p.employeeid, u.firstname, u.lastname from paymenttax pt join payment p on p.id = pt.paymentid and p.status in ('PAID', 'IMPORTED') and p.paydate between '2024-01-01' and '2024-03-31' and pt.jurisdictiontaxId = '48-000-0000-ER_SUTA-000' and p.companyid = 1301
join employee e on p.employeeid = e.id join appuser u on e.userid = u.id
group by p.employeeid, date_part('month', paydate), u.firstname, u.lastname order by 5, 2

select sum(p.grossearnings), date_part('month', paydate), p.employeeid, u.firstname, u.lastname from paymenttax pt join payment p on p.id = pt.paymentid and p.status in ('PAID', 'IMPORTED') and p.paydate between '2024-01-01' and '2024-03-31' and pt.jurisdictiontaxId = '48-000-0000-ER_SUTA-000' and p.companyid = 1301
join employee e on p.employeeid = e.id join appuser u on e.userid = u.id join payperiod pp on p.payperiodid = pp.id
group by p.employeeid, date_part('month', paydate), u.firstname, u.lastname order by 5, 2


select 3.57 + 3.81 +

1161020

select paydate, paidat, fordate, * from payment where id in (1070079, 1161020, 1117186)

select paydate, * from taxjurisdictionmastertaxaccountmapping where accountidentifier = 'TX0000-010'

48-000-0000-ER_SUTA-000

select * from payment where paidat is null and paydate is not null limit 100;
select * from paymenttax limit 10;
select * from employeetaxjurisdiction limit 10;

select p.employeeid, pt.jurisdictiontaxid, count(*) from payment p join paymenttax pt on p.id = pt.paymentid join employeetaxjurisdiction ejt on p.employeeid = ejt.employeeid and p.fordate between ejt.startdate and coalesce(ejt.enddate, '2100-12-31') and pt.jurisdictiontaxid = ejt.taxjurisdictionid where p.paydate > '2024-01-01' group by p.employeeid, pt.jurisdictiontaxid having count(*) > 1

select employeeid, taxjurisdictionid, count(*) from Employeetaxjurisdiction group by employeeid, taxjurisdictionid having count(*) > 1
select * from employeetaxjurisdiction where employeeid = 85234 and taxjurisdictionid = '39-000-0000-SIT-000'

select * from taxjurisdiction limit 100

select * from taxjurisdictionmastertaxaccountmapping limit 100
select * From information_schema.columns where column_name ilike '%w2%'
select * From information_schema.tables where table_name ilike '%w2%'
select * from w2exportdata2023 limit 10
-- This is an example of more employees paid on the 12th of the month than were paid in the month. In this case the 1 extra employee was actually paid on the first of the following month on a pay period that include the 12th of the current month
select distinct employeeid from payment where paidat between '2023-10-01' and '2023-11-01' and companyid = 154
select distinct p.employeeid from payment p join payperiod pp on p.payperiodid = pp.id where '2023-10-12' between pp.startdate and pp.enddate and p.companyid = 154 and p.employeeid not in (select employeeid from payment where paidat between '2023-10-01' and '2023-11-01' and companyid = 154)
select * from payment p join payperiod pp on p.payperiodid = pp.id where '2023-10-12' between pp.startdate and pp.enddate and p.companyid = 154 and p.employeeid = 58812

select distinct(taxtype) from taxjurisdiction where split_part(id, '-', 1)::int > 0 and split_part(id, '-', 3)::int = 0
select * from taxjurisdiction where taxtype = 'ER_SUTA_SC' and id = '34-000-0000-ER_SUTA_SC-048'

select * from W2NonFedTaxData limit 10

select * from taxjurisdictionmastertaxaccountmapping where accountidentifier = 'VT0000-001'
select * from companytaxjurisdiction where taxjurisdictionid = '50-000-0000-SIT-000'
select * from company where id in (330,1543,1541,1876,1878,1954) and democompany = false and status = 'LIVE'

with t as (select id, split_part(id, '-', 1)::int as part1, split_part(id, '-', 2)::int as part2, split_part(id, '-', 3)::int as part3,split_part(id, '-', 5)::int as part5, name, state, taxtype from taxjurisdiction)
select distinct(taxtype) from t where part1 <> 0 and part2 = 0

select * from INFORMATION_SCHEMA.columns where column_name like '%gender%'


    select
        p.*, pt.*
    from
        company c join companyaddress ca on c.id = ca.companyId and now() between ca.startdate and coalesce(ca.enddate, '2100-12-31')
        left join worklocation wl on c.id = wl.companyid and wl.archivedat is null and lower(replace(replace(wl.line1, '.', ''), ' ', '')) = lower(replace(replace(ca.line1, '.', ''), ' ', '')) and lower(replace(wl.city, ' ', '')) = lower(replace(ca.city, ' ', '')) and wl.state = ca.state and left(replace(wl.postalcode, ' ', ''), 5) = left(replace(ca.postalcode, ' ', ''), 5)
        join payment p on c.id = p.companyId
        join payperiod pp on p.payperiodid = pp.id
        join paymenttax pt on p.id = pt.paymentid and pt.amount > 0
        left join employeetaxjurisdiction etjresides on etjresides.taxjurisdictionid = pt.jurisdictiontaxid and etjresides.employeeid = p.employeeid and p.fordate between etjresides.startdate and coalesce(etjresides.enddate, '2100-12-31') and etjresides.isresident = true
        left join employeetaxjurisdiction etjworks on etjworks.taxjurisdictionid = pt.jurisdictiontaxid and etjworks.employeeid = p.employeeid and p.fordate between etjworks.startdate and coalesce(etjworks.enddate, '2100-12-31') and etjworks.isresident = false
        -- left join taxjurisdictionmastertaxaccountmapping tjm on pt.jurisdictiontaxId = tjm.taxjurisdictionid
    where
        c.democompany = False 
        and c.status = 'LIVE'
        and p.paidat between '2024-01-01' and '2024-04-01' 
        and c.id = 137
        and p.employeeid = 3950

select * from r365employeeproperty_staging
*/