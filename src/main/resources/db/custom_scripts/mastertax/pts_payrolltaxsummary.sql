-- Payroll Tax Summary Data
WITH args(start_range, end_range) AS
         (
             VALUES ('2024-04-23'::date, '2024-04-23'::date) -- Change me to yesterday (or Friday) and run. You can run multiple days, but do not rerun the same days previously run
         ), wl as
         (
             select distinct Line1, Line2, City,State, postalcode, symmetrycitycode, companyid from worklocation where archivedat is null
         ), t as
         (
             select
                 'MTCSV01-PTS' as format_id,
                 '' as company_group_name,
                 c.id as payroll_code,
                 min(to_char(paydate, 'YYYYMMDD')) as check_date,
                 case when wl.symmetrycitycode in ('1558347', '1542678', '1555154', '1548994') -- Charleston = 1558347, Madison = 1542678, Montgomery = 1555154, Wheeling = 1548994
                          then
                          max(case pp.payperiodtype
                                  when 'WEEKLY' then 'W'
                                  when 'BI_WEEKLY' then 'B'
                                  when 'SEMI_MONTHLY' then 'S'
                                  when 'MONTHLY' then 'M'
                                  else ''
                              end)
                      else
                          ''
                     end as payroll_frequency, -- The problem with this is that we have AD_HOC, CUSTOM, IMPORT, DAILY, PAY_ON_DEMAND, and PAY_ON_DEMAND_REMAINDER pay periods as well. I am going to leave those blank for now
                 m.accountidentifier as tax_code,
                 coalesce(c.federalein, '') as ein,
                 '' as wc_class_code,
                 case when sum(pt.subjectwages) = 0 then 0 else round(sum(pt.amount)/sum(pt.subjectwages)*100, 2) end as tax_rate,
                 sum(pt.amount) as tax,
                 count(distinct p.employeeid) as employee_count,
                 sum(pt.subjectwages) as taxable_wages,
                 sum(grossearnings) as gross_wages,
                 0 as exempt_wages,
                 '' as liability_trace_id,
                 '' as user_field_1,
                 '' as user_field_2,
                 '' as user_field_3,
                 '' as user_field_4,
                 '' as user_field_5,
                 '' as user_field_6,
                 '' as user_field_7,
                 '' as user_field_8
             from
                 company c join companyaddress ca on c.id = ca.companyId and now() between ca.startdate and coalesce(ca.enddate, '2100-12-31')
                           cross join args
                           left join wl on c.id = wl.companyid and lower(replace(replace(wl.line1, '.', ''), ' ', '')) = lower(replace(replace(ca.line1, '.', ''), ' ', '')) and lower(replace(wl.city, ' ', '')) = lower(replace(ca.city, ' ', '')) and wl.state = ca.state and left(replace(wl.postalcode, ' ', ''), 5) = left(replace(ca.postalcode, ' ', ''), 5)
    join payment p on c.id = p.companyId
    join payperiod pp on p.payperiodid = pp.id
    join paymenttax pt on p.id = pt.paymentid and pt.amount > 0
    join taxjurisdictionmastertaxaccountmapping m on pt.jurisdictiontaxId = m.taxjurisdictionid and accountidentifier <> '?' -- This is temporary because a ? got into a tax mapping
where
    c.democompany = False
  and p.paydate between args.start_range and args.end_range
  and p.status in ('PAID', 'IMPORTED')
group by
    c.id, c.federalein, m.accountidentifier, wl.symmetrycitycode, pt.jurisdictiontaxId, paydate
    )
select * from t
order by 3, 4
