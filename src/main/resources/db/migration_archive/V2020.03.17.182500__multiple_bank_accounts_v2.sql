ALTER TABLE userbankaccount DROP CONSTRAINT IF EXISTS userbankaccount_user_ruleorder_uk;

-- Create PaymentDeposit table
ALTER TABLE achfilerecord DROP CONSTRAINT IF EXISTS achfilerecord_paymentdeposit_fk;
DROP TABLE IF EXISTS paymentdeposit;

CREATE TABLE paymentdeposit
(
    id BIGSERIAL NOT NULL,
    paymentid BIGINT NOT NULL,
    bankname TEXT NOT NULL,
    accountname TEXT NOT NULL,
    accountnumber TEXT NOT NULL,
    routingnumber TEXT NOT NULL,
    accounttype TEXT NOT NULL,
    ruleamount NUMERIC NOT NULL,
    ruleamounttype TEXT NOT NULL,
    amount numeric NOT NULL,
    mtdamount numeric NOT NULL,
    qtdamount numeric NOT NULL,
    ytdamount numeric NOT NULL,
    createdat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updatedat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id)
);

ALTER TABLE paymentdeposit ADD CONSTRAINT paymentdeposit_payment_fk FOREIGN KEY (paymentid) REFERENCES payment;
ALTER TABLE paymentdeposit ADD CONSTRAINT paymentdeposit_payment_accountnumber_uk UNIQUE (paymentid, accountnumber);
CREATE INDEX paymentdeposit_payment_idx ON paymentdeposit (paymentid ASC);

ALTER TABLE userbankaccount DROP CONSTRAINT IF EXISTS userbankaccount_user_accountnumber_uk;
ALTER TABLE userbankaccount ADD CONSTRAINT userbankaccount_user_accountnumber_uk UNIQUE (userid, accountnumber);

ALTER TABLE company ADD COLUMN IF NOT EXISTS hourpredictionenabled BOOLEAN;
update company set hourpredictionenabled = false;
ALTER TABLE company ALTER COLUMN hourpredictionenabled SET NOT NULL;

ALTER TABLE achfilerecord ADD COLUMN IF NOT EXISTS hourpredictionenabled BOOLEAN;
ALTER TABLE achfilerecord ADD COLUMN IF NOT EXISTS paymentdepositid bigint;
ALTER TABLE achfilerecord ADD CONSTRAINT achfilerecord_paymentdeposit_fk FOREIGN KEY
    (paymentdepositid) REFERENCES paymentdeposit;

-- Backfill payment deposits without xtd values
delete from paymentdeposit;

insert into paymentdeposit (
    paymentid, amount, mtdamount, qtdamount, ytdamount, accountname, accountnumber,
    accounttype, bankname, routingnumber, ruleamount,ruleamounttype, createdat, updatedat)
(select p.id, p.netearnings, 0, 0, 0, b.accountname, b.accountnumber, b.accounttype, b.bankname,
    b.routingnumber, b.ruleamount, b.ruleamounttype, current_timestamp, current_timestamp
  from payment p
  join employee e on e.id = p.employeeid
  join userbankaccount b on b.userid = e.userid and b.ruleorder = 0
 where p.status in ('CALCULATED', 'APPROVED', 'SUBMITTED', 'PAID'));

-- Now update xtd values
with paymentstoupdate as (
    select * from payment
),
paymentdepositxtd as (
    select
        ptu.id as paymentid,
        ptu.paydate,
        ptu.employeeid,
        pd.accountnumber,
        coalesce(sum(case
            when extract(month from p.paydate) = extract(month from ptu.paydate)
            then pd.amount end), 0) as mtd,
        coalesce(sum(case
            when extract(quarter from p.paydate) = extract(quarter from ptu.paydate)
            then pd.amount end), 0) as qtd,
        coalesce(sum(pd.amount), 0) as ytd
      from paymentstoupdate ptu
      join payment p on p.employeeid = ptu.employeeid
      join paymentdeposit pd on pd.paymentid = p.id
     where 1=1
       and p.status not in ('ERRORED')
       and extract(year from p.paydate) = extract(year from ptu.paydate)
       and (p.fordate < ptu.fordate or (p.fordate = ptu.fordate and p.startedat <= ptu.startedat))
       and pd.accountnumber is not null
     group by ptu.id, ptu.paydate, ptu.employeeid, pd.accountnumber
     order by ptu.employeeid, ptu.id
)
update paymentdeposit as pd set
    mtdamount = xtd.mtd,
    qtdamount = xtd.qtd,
    ytdamount = xtd.ytd
  from paymentdepositxtd xtd
 where pd.paymentid = xtd.paymentid
   and pd.accountnumber = xtd.accountnumber;
