UPDATE payperiodpreference
   SET weeklyStartDay = 'SUNDAY'
WHERE weeklyStartDay IS NULL
  AND payperiodtype IN ('WEEKLY', 'BI_WEEKLY');

-- Add monthlyStartDay and firstSemiMonthlyStartDay columns; drop secondmonthlystartday
ALTER TABLE payperiodpreference ADD COLUMN IF NOT EXISTS monthlystartday integer;
ALTER TABLE payperiodpreference RENAME COLUMN firstmonthlystartday TO firstsemimonthlystartday;
ALTER TABLE payperiodpreference DROP COLUMN IF EXISTS secondmonthlystartday;

-- Copy firstSemiMonthlyStartDay to monthlyStartDay when "MONTHLY"
UPDATE payperiodpreference
  SET monthlystartday = COALESCE(firstsemimonthlystartday, 1)
WHERE payperiodtype = 'MONTHLY';

-- Reset firstSemiMonthlyStartDay to null when "MONTHLY"
UPDATE payperiodpreference
SET firstsemimonthlystartday = NULL
WHERE firstsemimonthlystartday IS NOT NULL
  AND payperiodtype = 'MONTHLY';

-- Initialize firstSemiMonthlyStartDay to 1 when "SEMI_MONTHLY"
UPDATE payperiodpreference
  SET firstsemimonthlystartday = 1
WHERE firstsemimonthlystartday IS NULL
  AND payperiodtype = 'SEMI_MONTHLY';

-- Create trigger to for advanced checking
CREATE OR REPLACE FUNCTION payperiodpreference_startday_procedure() RETURNS TRIGGER
  LANGUAGE plpgsql
  AS $$
  BEGIN
    -- if company pay period preference and for specific pay period types
    -- default the start day and check for valid value
    IF (NEW.employeeid IS NULL) THEN
      IF (NEW.payperiodtype IN ('BI_WEEKLY', 'WEEKLY')) THEN
        NEW.weeklystartday = COALESCE(NEW.weeklystartday, 'SUNDAY');
        IF (NEW.weeklystartday NOT IN ('MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY')) THEN
          RAISE EXCEPTION 'weeklystartday not valid';
        END IF;
      ELSIF (NEW.payperiodtype = 'SEMI_MONTHLY') THEN
        NEW.firstsemimonthlystartday = COALESCE(NEW.firstsemimonthlystartday, 1);
        IF (NEW.firstsemimonthlystartday NOT BETWEEN 1 AND 13) THEN
          RAISE EXCEPTION 'firstsemimonthlystartday not valid';
        END IF;
      ELSIF (NEW.payperiodtype = 'MONTHLY') THEN
        NEW.monthlystartday = COALESCE(NEW.monthlystartday, 1);
        IF (NEW.monthlystartday NOT BETWEEN 1 AND 28) THEN
          RAISE EXCEPTION 'monthlystartday not valid';
        END IF;
      END IF;
    END IF;
    RETURN NEW;
  END;
$$;

DROP TRIGGER IF EXISTS payperiodpreference_startday_trigger ON payperiodpreference;
CREATE TRIGGER payperiodpreference_startday_trigger
  BEFORE INSERT OR UPDATE ON payperiodpreference
  FOR EACH ROW
  EXECUTE PROCEDURE payperiodpreference_startday_procedure();
