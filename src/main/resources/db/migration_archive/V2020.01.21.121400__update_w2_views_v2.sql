drop view if exists w2data2019;

create or replace view w2data2019 as
    select to_hex(p.employeeid, extract(year from p.paydate)::bigint) as id,
           p.employeeid as employeeid,
           e.userid as userid,
           p.companyid as companyid,
           extract(year from p.paydate) as year,
           '' as controlnumber,
           sum(p.grossearnings) as grossearnings,
           sum(ptfit.subjectwages) as fitwages,
           sum(ptfit.amount) as fit,
           sum(ptfica.subjectwages) - coalesce(sum(petips.amount), 0) as ficawages,
           sum(ptfica.amount) as fica,
           sum(ptmedi.subjectwages) as mediwages,
           sum(ptmedi.amount) + sum(ptmedi2.amount) as medi,
           coalesce(sum(petips.amount), 0::numeric) as sstips,
           0::numeric as alloctips,
           coalesce(sum(pcddra.amountee) + sum(pcddra.amounter), 0::numeric) as dra,
           0::numeric as nqplans,
           sum(pcd401k.amountee) as erisa401k,
           sum(pcd403b.amountee) as erisa403b,
           sum(pcdhsa.amountee) + sum(pcdhsa.amounter) as hsa,
           sum(pcdroth.amountee) as roth401k,
           coalesce(sum(pcdmedical.amountee) + sum(pcdmedical.amounter), 0::numeric) +
             coalesce(sum(pcddental.amountee) + sum(pcddental.amounter), 0::numeric) +
             coalesce(sum(pcdvision.amountee) + sum(pcdvision.amounter), 0::numeric) +
             coalesce(sum(pcdprescription.amountee) + sum(pcdprescription.amounter), 0::numeric) +
             coalesce(sum(pcdhra.amountee) + sum(pcdhra.amounter), 0::numeric) +
             coalesce(sum(pcdhopital.amountee) + sum(pcdhopital.amounter), 0::numeric) as healthcoverage,
           'Off' as statutoryemployee,
           case when (coalesce(sum(pcd401k.amountee) + sum(pcd401k.amounter), 0) +
                      coalesce(sum(pcd403b.amountee) + sum(pcd403b.amounter), 0) +
                      coalesce(sum(pcdroth.amountee) + sum(pcdroth.amounter), 0)) > 0 then 'On' else 'Off' end as retirementplan,
           'Off' as thirdPartySickPay
    from payment p
    inner join employee e on p.employeeid = e.id
    left outer join paymenttax ptfit on ptfit.paymentid = p.id and ptfit.type = 'FIT'
    left outer join paymenttax ptfica on ptfica.paymentid = p.id and ptfica.type = 'FICA'
    left outer join paymenttax ptmedi on ptmedi.paymentid = p.id and ptmedi.type = 'MEDI'
    left outer join paymenttax ptmedi2 on ptmedi2.paymentid = p.id and ptmedi2.type = 'MEDI2'
    left outer join paymentcontributiondeduction pcddra on pcddra.paymentid = p.id and pcddra.type = 'DRA'
    left outer join paymentearning petips on petips.paymentid = p.id and petips.type = 'TIPS'
    left outer join paymentcontributiondeduction pcd401k on pcd401k.paymentid = p.id and pcd401k.type = 'ERISA_401K'
    left outer join paymentcontributiondeduction pcd403b on pcd403b.paymentid = p.id and pcd403b.type = 'ERISA_403B'
    left outer join paymentcontributiondeduction pcdhsa on pcdhsa.paymentid = p.id and pcdhsa.type = 'HSA'
    left outer join paymentcontributiondeduction pcdroth on pcdroth.paymentid = p.id and pcdroth.type = 'ROTH_401K'
    left outer join paymentcontributiondeduction pcdmedical on pcdmedical.paymentid = p.id and pcdmedical.type = 'MEDICAL_INSURANCE'
    left outer join paymentcontributiondeduction pcddental on pcddental.paymentid = p.id and pcddental.type = 'DENTAL_INSURANCE'
    left outer join paymentcontributiondeduction pcdvision on pcdvision.paymentid = p.id and pcdvision.type = 'VISION_INSURANCE'
    left outer join paymentcontributiondeduction pcdprescription on pcdprescription.paymentid = p.id and pcdprescription.type = 'PRESCRIPTION_PLAN'
    left outer join paymentcontributiondeduction pcdhra on pcdhra.paymentid = p.id and pcdhra.type = 'HRA'
    left outer join paymentcontributiondeduction pcdhopital on pcdhopital.paymentid = p.id and pcdhopital.type = 'HOSPITAL_INDEMNITY_INSURANCE'
    where p.status in ('PAID', 'IMPORTED')
    group by p.employeeid, e.userid, p.companyid, extract(year from p.paydate);

drop view if exists w2nonfedtaxdata2019;

create or replace view w2nonfedtaxdata2019 as
    select to_hex(p.employeeid, extract(year from p.paydate)::bigint, max(pt.id)) as id,
           p.employeeid as employeeid,
           e.userid as userid,
           p.companyid as companyid,
           extract(year from p.paydate) as year,
           '' as controlnumber,
           pt.type as type,
           case
             when pt.type in ('SIT') then 'STATE'
             when pt.type in ('CITY', 'SCHL') then 'LOCAL'
             else 'OTHER' end as group,
           pt.jurisdictiontaxid as jurisdictiontaxid,
           max(pt.description) as description,
           sum(pt.subjectwages) as subjectwages,
           sum(pt.amount) as amount
    from payment p
    inner join employee e on p.employeeid = e.id
    left outer join paymenttax pt on pt.paymentid = p.id and pt.payertype = 'EMPLOYEE' and pt.type not in ('FIT', 'FICA', 'MEDI', 'MEDI2')
    where p.status in ('PAID', 'IMPORTED')
      and amount > 0
    group by p.employeeid, e.userid, p.companyid, extract(year from p.paydate), pt.type, pt.jurisdictiontaxid;
