
CREATE OR REPLACE FUNCTION shift_update_sync_timestamps()
 RET<PERSON><PERSON> trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    startAtInclusiveChanged         BOOLEAN;
    overrideStartAtInclusiveChanged BOOLEAN;
    endAtExclusiveChanged           BOOLEAN;
    overrideEndAtExclusiveChanged   BOOLEAN;
    punchInAtChanged                BOOLEAN;
    overridePunchInAtChanged        BOOLEAN;
    punchOutAtChanged               BOOLEAN;
    overridePunchOutAtChanged       BOOLEAN;
BEGIN
    SELECT (NEW.startAtInclusive IS NOT NULL AND OLD.startAtInclusive IS NOT NULL AND
            NEW.startAtInclusive <> OLD.startAtInclusive)
               OR (NEW.startAtInclusive IS NOT NULL AND OLD.startAtInclusive IS NULL)
               OR (NEW.startAtInclusive IS NULL AND OLD.startAtInclusive IS NOT NULL)
    INTO startAtInclusiveChanged;

    SELECT (NEW.overrideStartAtInclusive IS NOT NULL AND
            OLD.overrideStartAtInclusive IS NOT NULL AND
            NEW.overrideStartAtInclusive <> OLD.overrideStartAtInclusive)
               OR
           (NEW.overrideStartAtInclusive IS NOT NULL AND OLD.overrideStartAtInclusive IS NULL)
               OR
           (NEW.overrideStartAtInclusive IS NULL AND OLD.overrideStartAtInclusive IS NOT NULL)
    INTO overrideStartAtInclusiveChanged;

    SELECT (NEW.endAtExclusive IS NOT NULL AND OLD.endAtExclusive IS NOT NULL AND
            NEW.endAtExclusive <> OLD.endAtExclusive)
               OR (NEW.endAtExclusive IS NOT NULL AND OLD.endAtExclusive IS NULL)
               OR (NEW.endAtExclusive IS NULL AND OLD.endAtExclusive IS NOT NULL)
    INTO endAtExclusiveChanged;

    SELECT (NEW.overrideEndAtExclusive IS NOT NULL AND OLD.overrideEndAtExclusive IS NOT NULL AND
            NEW.overrideEndAtExclusive <> OLD.overrideEndAtExclusive)
               OR (NEW.overrideEndAtExclusive IS NOT NULL AND OLD.overrideEndAtExclusive IS NULL)
               OR (NEW.overrideEndAtExclusive IS NULL AND OLD.overrideEndAtExclusive IS NOT NULL)
    INTO overrideEndAtExclusiveChanged;

    -- if incoming data contains zoned punches: copy into legacy punches
    IF startAtInclusiveChanged
        OR overrideStartAtInclusiveChanged
        OR endAtExclusiveChanged
        OR overrideEndAtExclusiveChanged
    THEN
				NEW.punchInAt          = TIMEZONE('Etc/UTC'::TEXT, NEW.startAtInclusive);
        NEW.overridePunchInAt  = TIMEZONE('Etc/UTC'::TEXT, NEW.overrideStartAtInclusive);
        NEW.punchOutAt         = TIMEZONE('Etc/UTC'::TEXT, NEW.endAtExclusive);
        NEW.overridePunchOutAt = TIMEZONE('Etc/UTC'::TEXT, NEW.overrideEndAtExclusive);

        RETURN NEW;
    END IF;

    SELECT (NEW.punchInAt IS NOT NULL AND OLD.punchInAt IS NOT NULL AND
            NEW.punchInAt <> OLD.punchInAt)
               OR (NEW.punchInAt IS NOT NULL AND OLD.punchInAt IS NULL)
               OR (NEW.punchInAt IS NULL AND OLD.punchInAt IS NOT NULL)
    INTO punchInAtChanged;
    SELECT (NEW.overridePunchInAt IS NOT NULL AND OLD.overridePunchInAt IS NOT NULL AND
            NEW.overridePunchInAt <> OLD.overridePunchInAt)
               OR (NEW.overridePunchInAt IS NOT NULL AND OLD.overridePunchInAt IS NULL)
               OR (NEW.overridePunchInAt IS NULL AND OLD.overridePunchInAt IS NOT NULL)
    INTO overridePunchInAtChanged;
    SELECT (NEW.punchOutAt IS NOT NULL AND OLD.punchOutAt IS NOT NULL AND
            NEW.punchOutAt <> OLD.punchOutAt)
               OR (NEW.punchOutAt IS NOT NULL AND OLD.punchOutAt IS NULL)
               OR (NEW.punchOutAt IS NULL AND OLD.punchOutAt IS NOT NULL)
    INTO punchOutAtChanged;
    SELECT (NEW.overridePunchOutAt IS NOT NULL AND OLD.overridePunchOutAt IS NOT NULL AND
            NEW.overridePunchOutAt <> OLD.overridePunchOutAt)
               OR (NEW.overridePunchOutAt IS NOT NULL AND OLD.overridePunchOutAt IS NULL)
               OR (NEW.overridePunchOutAt IS NULL AND OLD.overridePunchOutAt IS NOT NULL)
    INTO overridePunchOutAtChanged;

    -- incoming data needs to contain legacy punch in; copy into zoned punches
    IF punchInAtChanged
        OR overridePunchInAtChanged
        OR punchOutAtChanged
        OR overridePunchOutAtChanged
    THEN
				NEW.startAtInclusive         = TIMEZONE('Etc/UTC'::TEXT, NEW.punchInAt);
        NEW.overrideStartAtInclusive = TIMEZONE('Etc/UTC'::TEXT, NEW.overridePunchInAt);
        NEW.endAtExclusive           = TIMEZONE('Etc/UTC'::TEXT, NEW.punchOutAt);
        NEW.overrideEndAtExclusive   = TIMEZONE('Etc/UTC'::TEXT, NEW.overridePunchOutAt);

        RETURN NEW;
    END IF;

    -- no change in timestamps detected
    RETURN NEW;
END;
$function$;

DROP TRIGGER IF EXISTS shift_update_sync_timestamps ON Shift;
CREATE TRIGGER shift_update_sync_timestamps
    BEFORE UPDATE ON Shift
    FOR EACH ROW
EXECUTE FUNCTION shift_update_sync_timestamps();
