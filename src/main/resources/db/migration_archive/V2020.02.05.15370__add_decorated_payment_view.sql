drop view if exists decoratedpayment;

create or replace view decoratedpayment as
    select p.*
      , case
            when p.status in ('ERRORED') then 'ERROR'
            when p.status in ('SUBMITTED', 'PAID', 'IMPORTED') then 'PAID'
            when p.status in ('STARTED', 'AWAITING_RECALCULATION') then 'PROCESSING'
            when p.status in ('APPROVED') then 'PENDING_PAYMENT'
            when
                (select every(c.shiftverificationenabled) = true and coalesce(every(verified), true) = false
                   from effectiveshift s
                   join company c on c.id = p.companyid
                   join payperiod pp on pp.id = p.payperiodid
                  where s.employeeid = p.employeeid
                    and s.worklocationeffectivepunchoutdate between pp.startdate and pp.enddate)
                then 'PENDING_VERIFICATION'
            when p.status in ('CALCULATED') then 'PENDING_APPROVAL'
        end as querystatus
      from payment p;
