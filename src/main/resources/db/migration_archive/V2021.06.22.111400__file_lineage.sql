CREATE OR REPLACE VIEW filelineage AS
 WITH RECURSIVE
 parents AS
 (
      SELECT
        t.id,
        t.name,
        t.parentfolderid,
        null AS formState,
        'FOLDER' AS type,
        t.companyid
      FROM folder t
      WHERE parentfolderid IS null
      UNION ALL
      SELECT
        ed.id,
        ed.filename AS name,
        ed.folderid AS parentfolderid,
        ef.formState,
        CASE WHEN ed.employeeformid IS null THEN 'EMPLOYEE_DOCUMENT' ELSE 'EMPLOYEE_FORM' END AS type,
        e.companyid
      FROM employeedocument AS ed
      JOIN employee e ON e.id=ed.employeeid
      LEFT JOIN employeeform ef ON ef.id=ed.employeeformid
      WHERE deletedat IS null AND folderid IS null
      UNION ALL
      SELECT
        cd.id,
        cd.filename AS name,
        cd.folderid AS parentfolderid,
        null AS formstate,
        'COMPANY_DOCUMENT' AS type,
        cd.companyid
      FROM companydocument AS cd
      WHERE deletedat IS null AND folderid is null
 ),
 children AS
 (
      SELECT
        t.id,
        t.name,
        t.parentfolderid,
        null AS formState,
        'FOLDER' AS type,
        t.companyid
      FROM folder t
      WHERE parentfolderid IS NOT null
      UNION ALL
      SELECT
        ed.id,
        ed.filename AS name,
        ed.folderid AS parentfolderid,
        ef.formState,
        CASE WHEN ed.employeeformid IS null THEN 'EMPLOYEE_DOCUMENT' ELSE 'EMPLOYEE_FORM' END AS type,
        e.companyid
      FROM employeedocument AS ed
      JOIN employee e ON e.id=ed.employeeid
      LEFT JOIN employeeform ef ON ef.id=ed.employeeformid
      WHERE deletedat IS null AND folderid IS NOT null
      UNION ALL
      SELECT
        cd.id,
        cd.filename AS name,
        cd.folderid AS parentfolderid,
        null AS formstate,
        'COMPANY_DOCUMENT' AS type,
        cd.companyid
      FROM companydocument AS cd
      WHERE deletedat IS null AND folderid IS NOT null
 ),
 tree AS
 (
    SELECT
        id,
        id::text AS branch,
        0 AS layer,
        parentfolderid,
        type, formState, companyid
    FROM parents
    UNION ALL
    SELECT
        c.id,
        t.branch || '.' || c.id::text AS branch,
        t.layer + 1 AS layer,
        c.parentfolderid,
        c.type, c.formState, c.companyid
    FROM children AS c, tree AS t
    WHERE c.parentfolderid = t.id AND t.type='FOLDER'
 )
 SELECT * FROM tree ORDER BY branch;
