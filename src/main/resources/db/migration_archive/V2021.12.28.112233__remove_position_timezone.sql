
DROP VIEW ptobalance;
DROP VIEW ptopolicycoveredemployee;
DROP TRIGGER IF EXISTS employeeposition_set_timestamps ON employeeposition;
DROP FUNCTION employeeposition_set_timestamps;

CREATE OR REPLACE VIEW ptopolicycoveredemployee AS
SELECT to_hex(VARIADIC ARRAY[ep.id, pp.id]) AS id,
    e.companyid,
    e.id AS employeeid,
    ep.id AS positionid,
    pp.id AS policyid,
    pp.type AS policytype,
    COALESCE(e.overrideptoaccrualrate, pp.defaultaccrualrate) AS effectiveptoaccrualrate,
    GREATEST(pp.startdate, ep.startdate) AS startdate,
    LEAST(pp.enddate, ep.enddate) AS enddate,
    COALESCE(e.overrideptomaxhours, pp.defaultptomaxhours) AS effectiveptomaxhours
	FROM employee e
		JOIN company c ON e.companyid = c.id
		JOIN ptopolicy pp ON e.companyid = pp.companyid
		JOIN employeeposition ep ON e.id = ep.employeeid
  WHERE overlaps(timezone(c.timezone, pp.startdate), timezone(c.timezone, COALESCE(pp.enddate, 'infinity'::date)), timezone(c.timezone, ep.startdate), timezone(c.timezone, COALESCE(ep.enddate, 'infinity'::date)))
  	AND e.employmenttype = 'EMPLOYEE'::text
  	AND (pp.salaryeligible = true AND ep.wagetype = 'SALARY'::text
  		OR pp.fulltimehourlyeligible = true AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND ep.wagetype = 'HOURLY'::text
  		OR pp.parttimehourlyeligible = true AND ep.wagetype = 'HOURLY'::text)
  GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate, ep.id, ep.startdate, ep.enddate;

 CREATE OR REPLACE VIEW ptobalance AS
 SELECT to_hex(VARIADIC ARRAY[e.id::text, COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text)]) AS id,
    e.companyid,
    e.id AS employeeid,
    COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text) AS timeofftype,
    COALESCE(sum(
        CASE
            WHEN p.type = 'FLEXIBLE'::text AND t.finalized = true THEN t.hours
            ELSE NULL::numeric
        END), 0::numeric) AS currenthours,
    COALESCE(sum(
        CASE
            WHEN p.type = 'FLEXIBLE'::text AND t.finalized = false THEN t.hours
            ELSE NULL::numeric
        END), 0::numeric) AS pendinghours,
    COALESCE(sum(
        CASE
            WHEN p.type = 'FLEXIBLE'::text THEN t.hours
            ELSE NULL::numeric
        END), 0::numeric) AS availablehours,
    (EXISTS ( SELECT 1
           FROM ptopolicycoveredemployee ce
          WHERE ce.employeeid = e.id AND ce.policytype = 'UNLIMITED'::text AND ce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE AND (ce.enddate IS NULL OR ce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))) AS unlimited,
    ( SELECT ce.effectiveptoaccrualrate
           FROM ptopolicycoveredemployee ce
          WHERE ce.employeeid = e.id AND ce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE AND (ce.enddate IS NULL OR ce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE)) AS effectiveptoaccrualrate,
    ( SELECT ce.effectiveptomaxhours
           FROM ptopolicycoveredemployee ce
          WHERE ce.employeeid = e.id AND ce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE AND (ce.enddate IS NULL OR ce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE)) AS effectiveptomaxhours
   FROM employee e
   	 JOIN company c ON c.id=e.companyid
     LEFT JOIN ptotransaction t ON t.employeeid = e.id
     LEFT JOIN ptopolicy p ON p.id = t.policyid
  GROUP BY c.id, e.id, (COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text));

ALTER TABLE employeeposition
	DROP COLUMN worklocationtimezone,
	DROP COLUMN startsat,
	DROP COLUMN endsat;
