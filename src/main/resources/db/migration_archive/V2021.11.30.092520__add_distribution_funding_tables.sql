CREATE TABLE IF NOT EXISTS companyfunding (
  id                BIGSERIAL PRIMARY KEY,
  createdat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  companyid         BIGINT NOT NULL REFERENCES company (id),
  fundingdate       DATE NOT NULL,

  type              TEXT NOT NULL,
  amount            numeric NOT NULL DEFAULT 0.00,

  status            TEXT NOT NULL,

  companybankaccountid    BIGINT NOT NULL,
  accountnumber           TEXT NOT NULL,
  routingnumber           TEXT NOT NULL,
  accountname             TEXT NOT NULL,
  bankname                TEXT NOT NULL,
  accounttype             TEXT,

  targetachbankaccountid           BIGINT NOT NULL REFERENCES achbankaccount (id),
  achfileid                        BIGINT REFERENCES achfile (id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX companyfunding_company_type_started_uk ON companyfunding (status, companyId, type) WHERE (status='STARTED');

CREATE TABLE IF NOT EXISTS paymentfunding (
  id                BIGSERIAL PRIMARY KEY,
  createdat         TIMES<PERSON>MP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  paymentid         BIGINT NOT NULL REFERENCES payment (id) ON DELETE CASCADE,

  type              TEXT NOT NULL,
  amount            numeric NOT NULL DEFAULT 0.00,
  fundingdate       DATE NOT NULL,

  status            TEXT NOT NULL,

  attemptedat       TIMESTAMP WITHOUT TIME ZONE,
  prevfundingid     BIGINT,
  companyfundingid  BIGINT REFERENCES companyfunding (id) ON DELETE SET NULL
);
ALTER TABLE paymentfunding ADD CONSTRAINT paymentfunding_type_unique EXCLUDE (type WITH =, paymentid WITH =, (CASE WHEN status <> 'CREATED_NEW_FUNDING' THEN TRUE END) WITH =) DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX IF NOT EXISTS paymentfunding_payment_idx ON paymentfunding USING BTREE (paymentid);
CREATE INDEX IF NOT EXISTS paymentfunding_status_fundingdate_idx ON paymentfunding USING BTREE (status, fundingdate);

CREATE TABLE IF NOT EXISTS companydistribution (
  id                BIGSERIAL PRIMARY KEY,
  createdat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  companyid         BIGINT NOT NULL REFERENCES company (id),
  distributiondate  DATE NOT NULL,

  type              TEXT NOT NULL,
  amount            numeric NOT NULL DEFAULT 0.00,

  status            TEXT NOT NULL,
  achfileid         BIGINT REFERENCES achfile (id) ON DELETE CASCADE,
  taxAuthoritySubmissionId BIGINT REFERENCES taxauthoritysubmission (id) ON DELETE CASCADE,
  sourceachbankaccountid BIGINT REFERENCES achbankaccount (id)
);

CREATE UNIQUE INDEX companydistribution_company_type_started_uk ON companydistribution (status, companyId, type) WHERE (status='STARTED');

CREATE TABLE IF NOT EXISTS paymentdistribution (
  id                BIGSERIAL PRIMARY KEY,
  createdat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  paymentid         BIGINT NOT NULL REFERENCES payment (id) ON DELETE CASCADE,

  type              TEXT NOT NULL,
  typeinfo          TEXT,
  amount            numeric NOT NULL DEFAULT 0.00,
  mtdAmount         numeric NOT NULL DEFAULT 0.00,
  qtdAmount         numeric NOT NULL DEFAULT 0.00,
  ytdAmount         numeric NOT NULL DEFAULT 0.00,
  distributiondate  DATE NOT NULL,

  status            TEXT NOT NULL,

  attemptedat            TIMESTAMP WITHOUT TIME ZONE,
  prevdistributionid     BIGINT,
  companydistributionid  BIGINT REFERENCES companydistribution (id) ON DELETE SET NULL,

  userbankaccountid       BIGINT,
  accountnumber           TEXT,
  routingnumber           TEXT,
  accountname             TEXT,
  bankname                TEXT,
  accounttype             TEXT,
  taxtypeaccumulationid   BIGINT REFERENCES taxtypeaccumulation (id) ON DELETE SET NULL
);

ALTER TABLE paymentdistribution ADD CONSTRAINT paymentdistribution_type_unique EXCLUDE (paymentid WITH =, type WITH =, typeinfo WITH =, (CASE WHEN status <> 'CREATED_NEW_DISTRIBUTION' THEN status END) WITH =) DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX IF NOT EXISTS paymentdistribution_payment_idx ON paymentdistribution USING BTREE (paymentid);
CREATE INDEX IF NOT EXISTS paymentdistribution_type_status_fundingdate_idx ON paymentdistribution USING BTREE (type, typeinfo, status, distributiondate);
CREATE INDEX IF NOT EXISTS paymentdistribution_taxaccumulation_idx ON paymentdistribution USING BTREE (taxtypeaccumulationid);


alter table company add column if not exists weekendandholidaypayenabled boolean;
UPDATE company SET weekendandholidaypayenabled=FALSE WHERE weekendandholidaypayenabled is null;
ALTER TABLE company ALTER COLUMN weekendandholidaypayenabled SET NOT NULL;
ALTER TABLE company ALTER COLUMN weekendandholidaypayenabled SET DEFAULT FALSE;

with paymentIds as (
select p.id as paymentid from payment p left join paymentdistribution pd on pd.paymentid=p.id and pd.type='ACH' where pd.id is null group by p.id
)
insert into paymentdistribution (paymentid, type, typeinfo, amount, mtdamount, qtdamount, ytdamount, distributiondate, status, attemptedat,
                            userbankaccountid, accountnumber, routingnumber, accountname, bankname, accounttype)
select
    p.id, 'ACH', pd.routingnumber||'_'||pd.accountnumber, sum(pd.amount), sum(pd.mtdamount), sum(pd.qtdamount), sum(pd.ytdamount), max(p.paydate),
    case when pd.status='DEPOSITED' then 'DISTRIBUTED' when pd.status='READY_TO_DEPOSIT' then 'APPROVED_FOR_DISTRIBUTION' when pd.status='FAILED' then 'FAILED' else 'CALCULATED' end,
    case when pd.status='DEPOSITED' then max(pd.updatedat) else null end,
    max(pd.bankAccountId),
    pd.accountnumber,
    pd.routingnumber,
    max(pd.accountname),
    max(pd.bankname),
    max(pd.accounttype)
from
    payment p
    join paymentIds on p.id=paymentIds.paymentId
    join paymentdeposit pd on pd.paymentid=p.id and pd.amount > 0
group by
    p.id,
    pd.status,
    pd.routingnumber,
    pd.accountnumber;

