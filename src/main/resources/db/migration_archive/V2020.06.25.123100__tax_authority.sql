CREATE TABLE taxauthority (
    id bigserial NOT NULL,
    name character varying NOT NULL,
    state character varying,
    submissionfrequency character varying,
    submissionUrl character varying,
    autoGenerated BOOLEAN default false,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT taxauthority_pkey PRIMARY KEY (id)
);

CREATE TABLE taxauthoritysubmission (
    id bigserial NOT NULL,
    taxauthorityid bigint NOT NULL,
    fromdate date NOT NULL,
    todate date NOT NULL,
    note character varying,
    status character varying NOT NULL,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT taxauthoritysubmission_pkey PRIMARY KEY (id),
    CONSTRAINT taxauthoritysubmission_taxauthority_fk FOREIGN KEY (taxauthorityid) REFERENCES taxauthority (id)
);

CREATE TABLE taxjurisdiction (
    id character varying NOT NULL,
    name character varying,
    state character varying,
    taxtype character varying,
    taxauthorityid bigint,
    autoGenerated <PERSON><PERSON><PERSON><PERSON><PERSON> default false,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT taxjurisdiction_pkey PRIMARY KEY (id),
    CONSTRAINT taxjurisdiction_taxauthority_fk FOREIGN KEY (taxauthorityid) REFERENCES taxauthority (id)
);

ALTER TABLE taxtypeaccumulation ADD taxauthoritysubmissionid bigint;

CREATE TABLE achbankaccountqboaccountmapping (
    id bigserial NOT NULL,
    accountidentifier character varying NOT NULL,
    accountname character varying NOT NULL,
    araccountidentifier character varying,
    araccountname character varying,
    locaccountidentifier character varying,
    locaccountname character varying,
    achbankaccountid bigint NOT NULL,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT achbankaccountqboaccountmapping_pkey PRIMARY KEY (id),
    CONSTRAINT achbankaccountqboaccountmapping_achbankaccount_fk FOREIGN KEY (achbankaccountid) REFERENCES achbankaccount (id)
);

CREATE TABLE taxauthorityqbovendormapping (
    id bigserial NOT NULL,
    vendoridentifier character varying NOT NULL,
    vendorname character varying NOT     NULL,
    taxauthorityid bigint NOT NULL,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT taxauthorityqbovendormapping_pkey PRIMARY KEY (id),
    CONSTRAINT taxauthorityqbovendormapping_taxauthority_fk FOREIGN KEY (taxauthorityid) REFERENCES taxauthority (id)
);

CREATE TABLE taxjurisdictionqboaccountmapping (
    id bigserial NOT NULL,
    accountidentifier character varying NOT NULL,
    accountname character varying NOT NULL,
    taxjurisdictionid character varying NOT NULL,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT taxjurisdictionqboaccountmapping_pkey PRIMARY KEY (id),
    CONSTRAINT taxjurisdictionqboaccountmapping_taxjurisdiction_fk FOREIGN KEY (taxjurisdictionid) REFERENCES taxjurisdiction (id)
);


CREATE TABLE companyqbocustomermapping (
    id bigserial NOT NULL,
    customeridentifier character varying NOT NULL,
    customername character varying NOT NULL,
    companyid bigint NOT NULL,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT companyqbocustomermapping_pkey PRIMARY KEY (id),
    CONSTRAINT companyqbocustomermapping_company_fk FOREIGN KEY (companyid) REFERENCES company (id)
);

ALTER TABLE journalentryline ALTER COLUMN expensetypeaccountmappingid DROP NOT NULL;
ALTER TABLE journalentryline
    ADD COLUMN bankAccountMappingId bigint,
    ADD COLUMN taxJurisdictionAccountMappingId bigint,
    ADD COLUMN arAccountMappingId bigint,
    ADD COLUMN locAccountMappingId bigint,
    ADD COLUMN companyCustomerMappingId bigint,
    ADD COLUMN taxAuthorityVendorMappingId bigint,
    ADD CONSTRAINT journalentryline_bankAccountMapping_fk FOREIGN KEY (bankAccountMappingId) REFERENCES achbankaccountqboaccountmapping (id),
    ADD CONSTRAINT journalentryline_taxJurisdictionAccountMapping_fk FOREIGN KEY (taxJurisdictionAccountMappingId) REFERENCES taxjurisdictionqboaccountmapping (id),
    ADD CONSTRAINT journalentryline_arAccountMapping_fk FOREIGN KEY (arAccountMappingId) REFERENCES achbankaccountqboaccountmapping (id),
    ADD CONSTRAINT journalentryline_companyCustomerMapping_fk FOREIGN KEY (companyCustomerMappingId) REFERENCES companyqbocustomermapping (id),
    ADD CONSTRAINT journalentryline_taxAuthorityVendorMapping_fk FOREIGN KEY (taxAuthorityVendorMappingId) REFERENCES taxauthorityqbovendormapping (id)
    ;
ALTER TABLE journalentry
    ADD COLUMN name character varying,
    ADD COLUMN evereeAccountingRecord BOOLEAN;

ALTER TABLE company ADD COLUMN accountingExportToEvereeEnabled BOOLEAN;
update company set accountingExportToEvereeEnabled = false;
ALTER TABLE company ALTER COLUMN accountingExportToEvereeEnabled SET NOT NULL;

INSERT INTO taxjurisdiction (id, name, state, taxtype, createdat, updatedat)
SELECT
    jurisdictiontaxid,
    max(description),
    case left(jurisdictiontaxid,2)
                when '01' then 'AL'
                when '02' then 'AK'
                when '04' then 'AZ'
                when '05' then 'AR'
                when '06' then 'CA'
                when '08' then 'CO'
                when '09' then 'CT'
                when '10' then 'DE'
                when '11' then 'DC'
                when '12' then 'FL'
                when '13' then 'GA'
                when '15' then 'HI'
                when '16' then 'ID'
                when '17' then 'IL'
                when '18' then 'IN'
                when '19' then 'IA'
                when '20' then 'KS'
                when '21' then 'KY'
                when '22' then 'LA'
                when '23' then 'ME'
                when '24' then 'MD'
                when '25' then 'MA'
                when '26' then 'MI'
                when '27' then 'MN'
                when '28' then 'MS'
                when '29' then 'MO'
                when '30' then 'MT'
                when '31' then 'NE'
                when '32' then 'NV'
                when '33' then 'NH'
                when '34' then 'NJ'
                when '35' then 'NM'
                when '36' then 'NY'
                when '37' then 'NC'
                when '38' then 'ND'
                when '39' then 'OH'
                when '40' then 'OK'
                when '41' then 'OR'
                when '42' then 'PA'
                when '72' then 'PR'
                when '44' then 'RI'
                when '45' then 'SC'
                when '46' then 'SD'
                when '47' then 'TN'
                when '48' then 'TX'
                when '49' then 'UT'
                when '50' then 'VT'
                when '51' then 'VA'
                when '53' then 'WA'
                when '54' then 'WV'
                when '55' then 'WI'
                when '56' then 'WY'
            end,
    split_part(jurisdictiontaxid,'-', 4),
    now(),
    now()
FROM
    paymenttax
GROUP BY jurisdictiontaxid;


INSERT INTO taxauthority (name, state, createdat, updatedat) VALUES
    ('Alabama Department of Revenue - Individual and Corporate Tax', 'AL', now(), now()),
    ('Arizona Department of Economic Security- AZ SUTA', 'AZ', now(), now()),
    ('Arizona Department of Revenue- AZ SIT', 'AZ', now(), now()),
    ('Arkansas Dept. of Finance- AR SIT', 'AR', now(), now()),
    ('Arkansas Division of Workforce Services- AR SUTA', 'AR', now(), now()),
    ('Colorado Dept. of Labor and Employment- CO SUTA', 'CO', now(), now()),
    ('Colorado Dept. of Revenue- CO SIT', 'CO', now(), now()),
    ('Connecticut State Dept of Revenue Services- CT SIT', 'CT', now(), now()),
    ('Denver Treasury Division- CO City Tax Denver', 'CO', now(), now()),
    ('Department of the Treasury - Internal Revenue Service', null, now(), now()),
    ('California Employment Development Dept.', 'CA', now(), now()), --ETT, SDI, SUTA
    ('Employer Compensation Expense Program- NY Comp Expense', 'NY', now(), now()),
    ('FICA - United States', null, now(), now()),
    ('Franchise Tax Board- CA SIT', 'CA', now(), now()),
    ('FUTA - United States', null, now(), now()),
    ('Idaho Dept. of Labor- ID SUTA', 'ID', now(), now()),
    ('Idaho State Tax Commission- ID SIT', 'ID', now(), now()),
    ('Lakewood Municipal Income Tax Division- OH City Tax Lakewood', 'OH', now(), now()),
    ('Louisiana Tax Commission- LA SIT', 'LA', now(), now()),
    ('Louisiana Workforce Unemployment Insurance- LA SUTA', 'LA', now(), now()),
    ('New Jersey Business Services', 'NJ', now(), now()),
    ('New York City Dept of Finance- NY City Tax', 'NY', now(), now()),
    ('New York Paid Family Leave- NY FLI', 'NY', now(), now()),
    ('New York State Dept of Labor- NY SUTA', 'NY', now(), now()),
    ('New York State Dept of Taxation and Finance- NY SIT', 'NY', now(), now()),
    ('North Carolina Dept of Revenue- NC SIT', 'NC', now(), now()),
    ('North Carolina Division of Employment Security- NC SUTA', 'NC', now(), now()),
    ('NYSIF Disability Benefits- NY SDI', 'NY', now(), now()),
    ('Office of Unemployment Insurance Operations- OH SUTA', 'OH', now(), now()),
    ('Ohio Dept. of Taxation', 'OH', now(), now()), -- Schoool District, SIT
    ('Oklahoma Employment Security Commission- OK SUTA', 'OK', now(), now()),
    ('Oklahoma Tax Commission- OK SIT', 'OK', now(), now()),
    ('Oregon Dept. of Revenue', 'OR', now(), now()), --SIT, Transit, SUTA
    ('Regional Income Tax Agency- OH City Tax', 'OH', now(), now()),
    ('State of Nevada Unemployment Insurance- NV SUTA', 'NV', now(), now()),
    ('Tennessee Dept of Labor & Workforce Development- TN SUTA', 'TN', now(), now()),
    ('Texas Workforce Commission- TX SUTA', 'TX', now(), now()),
    ('Unemployment Compensation Trust Fund- FL SUTA', 'FL', now(), now()),
    ('Utah State Tax Commission- UT SIT', 'UT', now(), now()),
    ('Washington Department of Revenue', 'WA', now(), now()),
    ('Paid Family & Medical Leave', 'WA', now(), now()), --FLI
    ('Employment Security Department Washington State', 'WA', now(), now()), --SUTA
    ('Workforce Services - UT SUTA', 'UT', now(), now());
