create or replace view ptobalance as
select to_hex(e.id::text, coalesce(t.timeofftype, 'PAID_TIME_OFF')) as id,
       e.id as employeeid,
       e.companyid as companyid,
       coalesce(bool_or(p.type = 'UNLIMITED' and current_timestamp at time zone 'UTC' <@ tsrange(ce.startsat, coalesce(ce.endsat, 'infinity'))), false) as unlimited,
       coalesce(t.timeofftype, 'PAID_TIME_OFF') as timeofftype,
       coalesce(sum(case t.finalized when true then t.hours end), 0)::numeric as currenthours,
       coalesce(sum(case t.finalized when false then t.hours end), 0)::numeric as pendinghours,
       coalesce(sum(t.hours), 0)::numeric as availablehours
from employee e
         left outer join ptopolicycoveredemployee ce on ce.employeeid = e.id
         left outer join ptopolicy p on p.id = ce.policyid
         left outer join ptotransaction t on t.employeeid = ce.employeeid and t.policyid = ce.policyid
group by e.id, e.companyid, t.timeofftype;
