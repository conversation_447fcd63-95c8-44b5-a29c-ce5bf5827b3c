ALTER TABLE userbankaccount ADD COLUMN IF NOT EXISTS depositsblocked BOOLEAN DEFAULT FALSE;

ALTER TABLE paymentdeposit ADD COLUMN IF NOT EXISTS status text;
UPDATE paymentdeposit SET status='DEPOSITED' WHERE status IS null AND paymentid IN (select id from payment where status='PAID');
UPDATE paymentdeposit SET status='READY_TO_DEPOSIT' WHERE status IS null AND paymentid IN (select id from payment where status in ('APPROVED', 'FUNDING_REQUESTED', 'FUNDED', 'SUBMITTED', 'PREPARED_FOR_FUNDING_REQUEST'));
UPDATE paymentdeposit SET status='NONE' WHERE status IS null;
ALTER TABLE paymentdeposit ALTER COLUMN status SET NOT NULL;

ALTER TABLE paymentdeposit ADD COLUMN IF NOT EXISTS depositnote text;

ALTER TABLE payment ADD COLUMN IF NOT EXISTS depositstatus text;
UPDATE payment SET depositstatus='DEPOSITED' WHERE depositstatus IS null AND status='PAID';
UPDATE payment SET depositstatus='READY_TO_DEPOSIT' WHERE depositstatus IS null AND status in ('APPROVED', 'FUNDING_REQUESTED', 'FUNDED', 'SUBMITTED', 'PREPARED_FOR_FUNDING_REQUEST');
UPDATE payment SET depositstatus='NONE' WHERE depositstatus IS null;
ALTER TABLE payment ALTER COLUMN depositstatus SET NOT NULL;
CREATE INDEX IF NOT EXISTS "payment_company_employee_depositstatus_idx" ON payment USING BTREE ("companyid", "employeeid","depositstatus");
CREATE INDEX IF NOT EXISTS "payment_status_depositstatus_idx" ON payment USING BTREE ("status", "depositstatus");

DROP VIEW if exists receivablepaymenttotalbycompany;
DROP VIEW if exists receivablepayment;
DROP VIEW if exists decoratedpayment;

CREATE OR REPLACE VIEW decoratedpayment AS  SELECT
    p.*,
    (SELECT
        position.id
    FROM employeeposition position
    WHERE
        position.employeeid = p.employeeid
        AND p.fordate >= position.startdate
        AND p.fordate <= coalesce(position.enddate, 'infinity'::timestamp)
    ) AS employeepositionid,
        CASE
            WHEN p.status = 'DELETED'::text THEN 'DELETED'::text
            WHEN p.status = 'ERRORED'::text THEN 'ERROR'::text
            WHEN p.status = ANY (ARRAY['SUBMITTED'::text, 'PAID'::text, 'IMPORTED'::text]) THEN 'PAID'::text
            WHEN p.status = ANY (ARRAY['STARTED'::text, 'AWAITING_RECALCULATION'::text]) THEN 'PROCESSING'::text
            WHEN p.status = ANY (ARRAY['APPROVED'::text , 'FUNDED'::text]) THEN 'PENDING_PAYMENT'::text
            WHEN p.status = ANY (ARRAY['PREPARED_FOR_FUNDING_REQUEST'::text, 'FUNDING_REQUESTED'::text]) THEN 'PENDING_FUNDING'::text
            WHEN ( SELECT every(c.shiftverificationenabled) = true AND COALESCE(every(s.verified), true) = false
               FROM effectiveshift s
                 JOIN company c ON c.id = p.companyid
                 JOIN payperiod pp ON pp.id = p.payperiodid
              WHERE s.employeeid = p.employeeid AND s.worklocationeffectivepunchoutdate >= pp.startdate AND s.worklocationeffectivepunchoutdate <= pp.enddate AND p.type = 'PAYROLL'::text) THEN 'PENDING_VERIFICATION'::text
            WHEN p.status = 'CALCULATED'::text THEN 'PENDING_APPROVAL'::text
            ELSE NULL::text
        END AS querystatus
   FROM payment p;

CREATE VIEW receivablepayment AS  WITH earliestfundingrecord AS (
         SELECT file.companyid,
            min(file.createdat) AS fundingtimestamp
           FROM achfilerecord record
             JOIN achfile file ON record.achfileid = file.id
          WHERE record.achfilerecordtype = 'FUNDING_DEBIT'::text AND file.status = 'PAID'::text
          GROUP BY file.companyid
        ), timeframe AS (
         SELECT company.id AS companyid,
            COALESCE(earliestfundingrecord.fundingtimestamp, company.createdat) AS earliesttimestamp
           FROM company
             LEFT JOIN earliestfundingrecord ON earliestfundingrecord.companyid = company.id
        )
 SELECT
    p.*,
    pp.startdate AS payperiodstartdate,
    pp.enddate AS payperiodenddate,
    u.firstname,
    u.middlename,
    u.lastname,
    e.employmenttype,
    t.name AS teamname
   FROM decoratedpayment p
     JOIN timeframe ON timeframe.companyid = p.companyid
     JOIN company c ON c.id = p.companyid
     JOIN payperiod pp ON pp.id = p.payperiodid
     JOIN employee e ON e.id = p.employeeid
     JOIN appuser u ON u.id = e.userid
     JOIN team t ON t.id = e.teamid
  WHERE
    p.status = 'PAID'::text
    AND p.grossearnings > 0
    AND p.approvedat > timeframe.earliesttimestamp
    AND p.fundingachfilerecordid IS NULL;

CREATE VIEW receivablepaymenttotalbycompany AS  SELECT c.id AS companyid,
    count(p.*) AS paymentcount,
    sum(p.grossearnings) AS totalgrossearnings,
    sum(p.netearnings) AS totalnetearnings,
    sum(p.totaltaxesee) AS totaltaxesee,
    sum(p.totaltaxeser) AS totaltaxeser,
    sum(p.totalcontributionser) AS totalcontributionser,
    sum(p.totalexpense) AS totalexpense,
    sum(p.totaltaxfunding) AS totaltaxfunding,
    sum(p.totalfunding) AS totalfunding
   FROM company c
     LEFT JOIN receivablepayment p ON p.companyid = c.id
  GROUP BY c.id, p.companyid;
