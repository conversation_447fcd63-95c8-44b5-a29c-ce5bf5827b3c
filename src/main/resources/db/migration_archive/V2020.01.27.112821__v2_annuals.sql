begin;

drop view IF EXISTS
  annualF940Quarter,
  annualF940,
  annualFW3,
  paymentIdsByYearStateCompany,
  annualStateF940Equivalent,
  annualF940ScheduleAState,
  annualPaymentByEmployeeByStateByType,
  annualF1096,
  annualFW3State,
  totalPaymentsToEmployee
;

create or replace view totalPaymentsToEmployee as
  select extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    payment.employeeId as employeeId,
    sum(payment.grossearnings) as totalPayment,
    (sum(payment.grossearnings)
      - sum(payment.deferredCompensation)) as totalPaymentLessDeferred,
    (sum(payment.grossearnings)
      - sum(payment.preTaxDeductions)) as totalPaymentLessPreTax,
    (sum(payment.grossearnings)
      - sum(payment.preTaxDeductions)
      - sum(payment.deferredCompensation)) as totalPaymentLessPreTaxLessDeferred
    FROM Payment payment
    where payment.status in ('PAID', 'IMPORTED')
    GROUP BY 1, 2, 3
;

create or replace view annualF940 as
  select
    *,
    round(annual940InnerTable4.question7TotalTaxableFutaWages * 0.006, 2) as question8FutaTaxBeforeAdjustments
  from (
    select
      *,
      (annual940InnerTable3.totalPaymentsToAllEmployees - annual940InnerTable3.question6Subtotal) as question7TotalTaxableFutaWages
    from (
      select
        *,
        (annual940InnerTable2.totalPaymentsToEachEmployeeInExcessOf7000) as question6Subtotal
      from (
        select
          *,
          (annual940InnerTable1.countOfEmployees * 42) as maxFutaAmount,
          (select sum(totalPaymentsToEmployee.totalPaymentLessPreTax)
            FROM TotalPaymentsToEmployee totalPaymentsToEmployee
            WHERE totalPaymentsToEmployee.companyId = annual940InnerTable1.companyId
            AND totalPaymentsToEmployee.year = annual940InnerTable1.year) as totalPaymentsToAllEmployees,
          (select sum(case when totalPaymentsToEmployee.totalPaymentLessPreTax > 7000
              then totalPaymentsToEmployee.totalPaymentLessPreTax - 7000
              else 0 end)
            FROM TotalPaymentsToEmployee totalPaymentsToEmployee
            WHERE totalPaymentsToEmployee.companyId = annual940InnerTable1.companyId
            AND totalPaymentsToEmployee.year = annual940InnerTable1.year) as totalPaymentsToEachEmployeeInExcessOf7000
        from (
          select
            extract(year from payment.paydate) as year,
            payment.companyId as companyId,
            count(DISTINCT payment.employeeId) as countOfEmployees
          from Payment payment
          inner join Company company on payment.companyId = company.id
          where payment.status in ('PAID', 'IMPORTED')
          group by 1, 2
        ) as annual940InnerTable1
      ) as annual940InnerTable2
    ) as annual940InnerTable3
  ) as annual940InnerTable4
;

create or replace view annualF940Quarter as
  SELECT *,
     (annualF940QuarterInnerTable1.countOfEmployees * 42) as maxFutaAmount
  FROM (
    SELECT (extract (year from payment.payDate)) as year,
      ('Q' || extract (quarter from payment.payDate)) as quarter,
      payment.companyId,
      SUM(tax.subjectWages) as futaSubjectWages,
      SUM(tax.amount) as futaDeposit,
      COUNT(DISTINCT payment.employeeId) as countOfEmployees
    FROM Payment payment
    INNER JOIN PaymentTax tax
    ON payment.id = tax.paymentId
    WHERE tax.type IN ('ER_FUTA')
    AND  payment.status IN ('PAID', 'IMPORTED')
    GROUP BY 1, 2, 3
    ORDER BY 3, 1, 2
 	) as annualF940QuarterInnerTable1
;

CREATE OR REPLACE VIEW annualF1096 AS (
  SELECT companyTaxYear.*,
      COUNT(Distinct(employee.id)) as countofcontractors,
      sum(payment.grossearnings) as totalAmountReported,
      sum(tax.amount) as federalIncomeTaxWithheld
    FROM (
      SELECT companyYear.companyId,
        CAST(generate_series(companyYear.startYear,
        companyYear.endYear) as FLOAT) as taxYear
      FROM(
        SELECT company.id as companyId,
        extract(year from company.startDate)::bigint as startYear,
        extract(year from coalesce(company.enddate, now()))::bigint as endYear
        FROM Company company
      ) as companyYear
    ) as companyTaxYear
    LEFT JOIN Employee employee
      INNER JOIN Payment payment ON employee.id = payment.employeeId
      inner join PaymentTax tax on tax.paymentId = payment.id
      ON (companyTaxYear.companyId = employee.companyId
      AND companyTaxYear.taxYear >= extract(year from employee.startDate)
      AND (employee.endDate IS NULL OR companyTaxYear.taxYear <= extract(year from employee.endDate))
      AND employee.employmentType = 'CONTRACTOR'
      AND tax.type = 'FIT'
      AND payment.status IN ('PAID', 'IMPORTED')
      )
  GROUP BY 1, 2
);

create or replace view annualF940ScheduleAState as
  SELECT
    extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    (SELECT SPLIT_PART(paymentTax2.jurisdictionTaxId, '-', 1)
       from PaymentTax paymentTax2
       WHERE payment.id = paymentTax2.paymentId
       and paymentTax2.type IN ('SUI', 'ER_SUTA', 'SIT')
       LIMIT 1
       ) as recipientStateCode,
    sum(payment.grossearnings) as totalPaymentsToAllEmployees,
    sum(paymentTax1.amount) as taxAmount,
    sum(paymentTax1.subjectWages) as taxSubjectWages
  from Payment payment
  inner join PaymentTax paymentTax1
    on ( payment.id = paymentTax1.paymentId
      and paymentTax1.type = 'ER_FUTA')
  WHERE payment.status IN ('PAID', 'IMPORTED')
  GROUP BY 1, 2, 3
;

create or replace view annualPaymentByEmployeeByStateByType as
  SELECT
    extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    payment.employeeId AS employeeId,
    SPLIT_PART(paymentTax2.jurisdictionTaxId, '-', 1) as recipientStateCode,
    paymentTax.type as taxType,
    sum(payment.grossearnings) as totalPaymentsToAllEmployees,
    sum(paymentTax.amount) as taxAmount,
    sum(paymentTax.subjectWages) as taxSubjectWages
  from Payment payment
  inner join PaymentTax paymentTax
    on ( payment.id = paymentTax.paymentId)
  left join PaymentTax paymentTax2
    on ( payment.id = paymentTax2.paymentId
      and paymentTax2.type IN ('SUI', 'ER_SUTA', 'SIT'))
  WHERE payment.status IN ('PAID', 'IMPORTED')
  GROUP BY 1, 2, 3, 4, 5
;

create or replace view paymentIdsByYearStateCompany AS
  SELECT
    extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    SPLIT_PART(paymentTax.jurisdictionTaxId, '-', 1) as recipientStateCode,
    array_agg(DISTINCT(payment.id) ORDER BY payment.id) as paymentIds
  from Payment payment
  inner join PaymentTax paymentTax
    on (payment.id = paymentTax.paymentId
    AND paymentTax.type IN ('SUI', 'ER_SUTA', 'SIT'))
  WHERE payment.status IN ('PAID', 'IMPORTED')
  GROUP BY 1, 2, 3
;

create or replace view annualStateF940Equivalent AS
  SELECT paymentAgg.year,
    paymentAgg.companyId,
    paymentAgg.recipientStateCode,
    (SELECT sum(tax1.subjectWages)
      FROM PaymentTax tax1
      WHERE tax1.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax1.type = 'FIT') as wagesTipsAndOtherCompensation,
    (SELECT sum(tax2.amount)
      FROM PaymentTax tax2
      WHERE tax2.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax2.type = 'FIT') as federalIncomeTaxWithheld,
    (SELECT sum(tax2a.subjectWages)
      FROM PaymentTax tax2a
      WHERE tax2a.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax2a.type = 'FIT') as federalIncomeTaxWages,
    ((
      SELECT sum(tax3a.subjectWages) as earningTypes FROM PaymentTax tax3a
        WHERE tax3a.type = 'FICA'
        AND tax3a.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      ) - coalesce((
      SELECT sum(earning3b.amount) FROM PaymentTax tax3b
        INNER JOIN PaymentEarning earning3b
        ON (earning3b.paymentId = tax3b.paymentId AND earning3b.type = 'TIPS')
        WHERE tax3b.type = 'FICA'
        AND tax3b.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    ), 0)) as socialSecurityWages,
    (SELECT sum(tax4.amount)
      FROM PaymentTax tax4
      WHERE tax4.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax4.type = 'FICA') as socialSecurityTaxWithheld,
    (SELECT sum(tax5.subjectWages)
      FROM PaymentTax tax5
      WHERE tax5.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax5.type IN ('MEDI', 'MEDI2')) as medicareWagesAndTips,
    (SELECT sum(tax6.amount)
      FROM PaymentTax tax6
      WHERE tax6.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax6.type IN ('MEDI', 'MEDI2')) as medicareTaxWithheld,
    (SELECT sum(earning7.amount) FROM PaymentTax tax7
      INNER JOIN PaymentEarning earning7
      ON (earning7.paymentId = tax7.paymentId AND earning7.type = 'TIPS')
      WHERE tax7.type = 'FICA'
      AND tax7.paymentId IN (select(unnest(paymentAgg.paymentIds)))) as socialSecurityTips,
    (SELECT sum(tax8.subjectWages)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateWagesTipsEtc,
    (SELECT sum(tax9.amount)
      FROM PaymentTax tax9
      WHERE tax9.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax9.type = 'SIT') as stateIncomeTaxWithheld,
    (SELECT sum(tax10.subjectWages)
      FROM PaymentTax tax10
      WHERE tax10.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax10.type in ('CITY', 'SCHL')) as localWagesTipsEtc,
    (SELECT sum(tax11.amount)
      FROM PaymentTax tax11
      WHERE tax11.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax11.type in ('CITY', 'SCHL')) as localIncomeTaxWithheld,
    (SELECT count(distinct(payment12.employeeId))
      FROM Payment payment12
      WHERE payment12.id IN (select(unnest(paymentAgg.paymentIds)))) as countOfEmployees,
    (SELECT sum(tax12.amount)
      FROM PaymentTax tax12
      WHERE tax12.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax12.type IN ('ER_SUTA')) as stateUnemploymentWithheld,
    (SELECT sum(tax13.amount)
      FROM PaymentTax tax13
      WHERE tax13.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax13.type IN ('ER_FUTA')) as federalUnemploymentWithheld,
    (SELECT sum(payment14.grossearnings)
      FROM Payment payment14
      WHERE payment14.id IN (select(unnest(paymentAgg.paymentIds)))) as totalGrossEarningsToAllEmployees
  FROM paymentIdsByYearStateCompany paymentAgg
;

create or replace view paymentIdsByYearCompany AS
  SELECT
    extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    array_agg(DISTINCT(payment.id) ORDER BY payment.id) as paymentIds
  from Payment payment
  WHERE payment.status IN ('PAID', 'IMPORTED')
  GROUP BY 1, 2
;

create or replace view annualFW3 AS
  SELECT paymentAgg.year,
  paymentAgg.companyId,
  (SELECT sum(tax1.subjectWages)
    FROM PaymentTax tax1
    WHERE tax1.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax1.type = 'FIT') as wagesTipsAndOtherCompensation,
  (SELECT sum(tax2.amount)
    FROM PaymentTax tax2
    WHERE tax2.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax2.type = 'FIT') as federalIncomeTaxWithheld,
  (SELECT sum(tax2a.subjectWages)
    FROM PaymentTax tax2a
    WHERE tax2a.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax2a.type = 'FIT') as federalIncomeTaxWages,
  ((
  	SELECT sum(tax3a.subjectWages) as earningTypes FROM PaymentTax tax3a
      WHERE tax3a.type = 'FICA'
      AND tax3a.paymentId IN (select(unnest(paymentAgg.paymentIds)))
  	) - coalesce((
	  SELECT sum(earning3b.amount) FROM PaymentTax tax3b
	    INNER JOIN PaymentEarning earning3b
	    ON (earning3b.paymentId = tax3b.paymentId AND earning3b.type = 'TIPS')
	    WHERE tax3b.type = 'FICA'
	    AND tax3b.paymentId IN (select(unnest(paymentAgg.paymentIds)))
	), 0)) as socialSecurityWages,
  (SELECT sum(tax4.amount)
    FROM PaymentTax tax4
    WHERE tax4.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax4.type = 'FICA') as socialSecurityTaxWithheld,
  (SELECT sum(tax5.subjectWages)
    FROM PaymentTax tax5
    WHERE tax5.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax5.type IN ('MEDI', 'MEDI2')) as medicareWagesAndTips,
  (SELECT sum(tax6.amount)
    FROM PaymentTax tax6
    WHERE tax6.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax6.type IN ('MEDI', 'MEDI2')) as medicareTaxWithheld,
  (SELECT sum(earning7.amount) FROM PaymentTax tax7
    INNER JOIN PaymentEarning earning7
    ON (earning7.paymentId = tax7.paymentId AND earning7.type = 'TIPS')
    WHERE tax7.type = 'FICA'
    AND tax7.paymentId IN (select(unnest(paymentAgg.paymentIds)))) as socialSecurityTips,
  (SELECT count(distinct(payment9.employeeId))
    FROM Payment payment9
    WHERE payment9.id IN (select(unnest(paymentAgg.paymentIds)))) as countOfEmployees
  FROM paymentIdsByYearCompany paymentAgg
;

create or replace view annualFW3State AS
  SELECT paymentAgg.year,
  paymentAgg.companyId,
  paymentAgg.recipientStateCode,
  (SELECT sum(tax1.subjectWages)
    FROM PaymentTax tax1
    WHERE tax1.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax1.type = 'FIT') as wagesTipsAndOtherCompensation,
  (SELECT sum(tax2.amount)
    FROM PaymentTax tax2
    WHERE tax2.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax2.type = 'FIT') as federalIncomeTaxWithheld,
  (SELECT sum(tax2a.subjectWages)
    FROM PaymentTax tax2a
    WHERE tax2a.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax2a.type = 'FIT') as federalIncomeTaxWages,
  ((
  	SELECT sum(tax3a.subjectWages) as earningTypes FROM PaymentTax tax3a
      WHERE tax3a.type = 'FICA'
      AND tax3a.paymentId IN (select(unnest(paymentAgg.paymentIds)))
  	) - coalesce((
	  SELECT sum(earning3b.amount) FROM PaymentTax tax3b
	    INNER JOIN PaymentEarning earning3b
	    ON (earning3b.paymentId = tax3b.paymentId AND earning3b.type = 'TIPS')
	    WHERE tax3b.type = 'FICA'
	    AND tax3b.paymentId IN (select(unnest(paymentAgg.paymentIds)))
	), 0)) as socialSecurityWages,
  (SELECT sum(tax4.amount)
    FROM PaymentTax tax4
    WHERE tax4.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax4.type = 'FICA') as socialSecurityTaxWithheld,
  (SELECT sum(tax5.subjectWages)
    FROM PaymentTax tax5
    WHERE tax5.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax5.type IN ('MEDI', 'MEDI2')) as medicareWagesAndTips,
  (SELECT sum(tax6.amount)
    FROM PaymentTax tax6
    WHERE tax6.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax6.type IN ('MEDI', 'MEDI2')) as medicareTaxWithheld,
  (SELECT sum(earning7.amount) FROM PaymentTax tax7
    INNER JOIN PaymentEarning earning7
    ON (earning7.paymentId = tax7.paymentId AND earning7.type = 'TIPS')
    WHERE tax7.type = 'FICA'
    AND tax7.paymentId IN (select(unnest(paymentAgg.paymentIds)))) as socialSecurityTips,
  (SELECT sum(tax8.subjectWages)
    FROM PaymentTax tax8
    WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax8.type = 'SIT') as stateWagesTipsEtc,
  (SELECT sum(tax9.amount)
    FROM PaymentTax tax9
    WHERE tax9.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax9.type = 'SIT') as stateIncomeTaxWithheld,
  (SELECT sum(tax10.subjectWages)
    FROM PaymentTax tax10
    WHERE tax10.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax10.type in ('CITY', 'SCHL')) as localWagesTipsEtc,
  (SELECT sum(tax11.amount)
    FROM PaymentTax tax11
    WHERE tax11.paymentId IN (select(unnest(paymentAgg.paymentIds)))
    AND tax11.type in ('CITY', 'SCHL')) as localIncomeTaxWithheld,
  (SELECT count(distinct(payment12.employeeId))
    FROM Payment payment12
    WHERE payment12.id IN (select(unnest(paymentAgg.paymentIds)))) as countOfEmployees
  FROM paymentIdsByYearStateCompany paymentAgg
;

commit;
