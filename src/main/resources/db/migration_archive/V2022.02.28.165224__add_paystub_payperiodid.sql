drop view paystub;

create view paystub as
SELECT to_hex(VARIADIC ARRAY [e.userid, p.companyid, pp.id])                AS id,
       pp.enddate                                                           AS date,
       date_part('year'::text, pp.enddate)::integer                         AS year,
       date_part('month'::text, pp.enddate)::integer                        AS month,
       e.userid,
       p.companyid,
       p.employeeid,
       pp.id                                                                AS payperiodid,
       sum(p.grossearnings)                                                 AS grossearnings,
       max(p.ytdgrossearnings)                                              AS ytdgrossearnings,
       sum(p.totaltaxesee)                                                  AS totaltaxesee,
       sum(p.pretaxdeductions)                                              AS pretaxdeductions,
       max(p.ytdpretaxdeductions)                                           AS ytdpretaxdeductions,
       sum(p.posttaxdeductions)                                             AS posttaxdeductions,
       max(p.ytdposttaxdeductions)                                          AS ytdposttaxdeductions,
       sum(p.deferredcompensation)                                          AS deferredcompensation,
       max(p.ytddeferredcompensation)                                       AS ytddeferredcompensation,
       sum(p.netearnings)                                                   AS netearnings,
       max(p.ytdnetearnings)                                                AS ytdnetearnings
FROM payment p
         JOIN employee e ON p.employeeid = e.id
         JOIN payperiod pp ON p.paystubpayperiodid = pp.id
WHERE p.status = 'PAID'::text
  AND p.paystubpayperiodid is not null
GROUP BY p.companyid, p.employeeid, e.userid, pp.id, pp.startdate, pp.enddate;
