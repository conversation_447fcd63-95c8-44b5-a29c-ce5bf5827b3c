drop view if exists paycardCddEntry;
create view paycardCddEntry as
select
  to_hex(e.id, c.id) as id,
  ga.productid as subProgramId,
  ga.pmtrefno as accountNumber,
  c.legalEntityName as contractedPartyName,
  c.displayName as contractedPartyDba,
  ca.line1 as physicalAddress1,
  ca.line2 as physicalAddress2,
  ca.city as physicalAddressCity,
  ca.state as physicalAddressState,
  ca.postalcode as physicalAddressZip,
  'USA' as physicalAddressCountry,
  DATE_PART('year', AGE(current_date, ca.startdate)) as numberOfYearsAtPhysicalLocation,
  c.federalEin as ein,
  ccd.websiteNote as companyWebsite,
  cdd.stockSymbol as stockTicker,
  cdd.stockExchange as stockExchange,
  null as fdicNumber,
  cru.firstname || ' ' || cru.lastname as primaryContactFullName,
  cr.type as primaryContactTitle,
  null as primaryContactDepartment,
  coalesce(cru.phonenumber, cru.unverifiedphonenumber) as primarycontactdirectphone,
  null as primaryContactPhoneExt,
  null as primaryContactFax,
  null as primaryContactCell,
  coalesce(cru.email, cru.unverifiedemail) as primarycontactemailaddress,
  cdd.typeOfIncorporation,
  cdd.ceoFullName,
  cdd.cfoFullName,
  cdd.cooFullName,
  ca.line1 as principalAddress1,
  ca.line2 as principalAddress2,
  ca.city as principalAddressCity,
  ca.state as principalAddressState,
  ca.postalcode as principalAddressZip,
  'USA' as principalAddressCountry,
  cdd.stateOfIncorporationRegistrationName,
  ca.line1 as stateOfIncorporationAddress1,
  ca.line2 as stateOfIncorporationAddress2,
  ca.city as stateOfIncorporationAddressCity,
  ca.state as stateOfIncorporationAddressState,
  ca.postalcode as stateOfIncorporationAddressZip,
  cdd.annualRevenue,
  cdd.typesOfProductsOffered,
  cdd.numberOfWorkers,
  cdd.percentOfRevenueFromOutsideUS,
  cdd.estimatedLoadAverages,
  '2' as cardUsage,
  'N' as cardOutsideUs,
  cast(0 as float) as percentageOfCardsToBeSentOutsideTheUs,
  null as whereCardsWillBeSentOutsideUs,
  cdd.naicsCode,
  'Y' as cashWithdrawlAvailable,
  2 as intendedUseForCard,
  'N' as coBrander,
  0.00 as expectedAchActivityVolume,
  0.00 as expectedWireTransferVolume,
  0.00 as expectedCheckActivityVolume,
  0.00 as expectedCashActivityVolume,
  0.00 as expectedRemoteDepositActivityVolume,
  e.id as employeeid,
  c.id as companyid
from
  employee e
  join galileoaccount ga on ga.userid=e.userid
  join company c on c.id=e.companyid and c.paycardstatus<>'DISABLED'
  join companyduediligencedetails cdd on cdd.companyid=c.id
  join companycreditdetails ccd on ccd.companyid=c.id
  left join companyaddress ca on ca.companyid=c.id and ca.enddate is null
  left join (select companyid, min(id) as id from companyrole where type='OWNER' group by companyid) crid on crid.companyid=c.id
  left join companyrole cr on cr.id=crid.id
  left join appuser cru on cru.id=cr.userid
