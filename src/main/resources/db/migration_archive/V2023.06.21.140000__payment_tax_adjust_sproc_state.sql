-- call assign_tta_to_paymenttax(null, null, 123, '04-000-0000-ER_SUTA-000');
-- call payment_tax_adjustment('48-000-0000-ER_SUTA-000', '49-000-0000-ER_SUTA-000', 85141, null);


DROP PROCEDURE IF EXISTS assign_tta_to_paymenttax;
DROP procedure IF EXISTS payment_tax_adjustment_for_employee;
DROP PROCEDURE IF EXISTS payment_tax_adjustment;

CREATE OR REPLACE PROCEDURE assign_tta_to_paymenttax(
    arg_paymenttaxid bigint DEFAULT null,
    arg_paymentId bigint DEFAULT null,
    arg_employeeId bigint DEFAULT null,
    arg_jurisdictionTaxId text DEFAULT NULL)
 	LANGUAGE plpgsql
AS $$
BEGIN
    if arg_paymenttaxid is null AND arg_paymentId is null AND arg_employeeId is null THEN
        RAISE EXCEPTION 'arg_paymenttaxid OR arg_paymentId OR arg_employeeId should not be null to assign tax type accumulation ids to paymenttax';
    END IF;

    with companydistributionIdsForPaymentIds as(
      select
          min(pd.companydistributionid) companydistributionid, pt.jurisdictiontaxid, pd.paymentid
      from paymentdistribution pd
      JOIN payment p on p.id=pd.paymentid
      join paymenttax pt on pt.paymentid=p.id
      WHERE
          pd.status in ('DISTRIBUTED', 'SUBMITTED')
          and pd.type in ('PTPC','PAY_CARD')
          and p.status in ('PAID', 'IMPORTED', 'SUBMITTED')
          and pd.amount <> 0
          and pt.taxtypeaccumulationid is null
          and case when arg_paymenttaxid is not null then pt.id = arg_paymenttaxid else 1 = 1 end
          and case when arg_employeeId is not null then p.employeeid = arg_employeeId else 1 = 1 end
          and case when arg_paymentId is not null then p.id = arg_paymentId else 1 = 1 end
          and case when arg_jurisdictionTaxId is not null then pt.jurisdictiontaxid = arg_jurisdictionTaxId else 1 = 1 end
      group by pd.paymentid, pt.jurisdictiontaxid
  ), lockboxitems as (
      select
          ids.*, cdl.id as companydistributionlockboxitemid
      from companydistributionlockboxitem cdl
      join companydistributionIdsForPaymentIds ids on ids.companydistributionid=cdl.companydistributionid
  ), ttas as (
    select
        pt.id as paymenttaxid,
        tta.id as taxtypeaccumulationid,
        tta.companydistributionlockboxitemid,
        lb.companydistributionid,
        lb.paymentid,
        lb.jurisdictiontaxid
     from taxtypeaccumulation tta
     join lockboxitems lb
        on lb.companydistributionlockboxitemid=tta.companydistributionlockboxitemid
        and lb.jurisdictiontaxid=tta.jurisdictiontaxid
     join paymenttax pt
        on pt.jurisdictiontaxid=lb.jurisdictiontaxid
        and lb.paymentid=pt.paymentid
  ) -- select * from ttas
  update paymenttax as pt
    set taxtypeaccumulationid=ttas.taxtypeaccumulationid
    from ttas
    where ttas.paymenttaxid=pt.id;


    with paymenttaxes as (
      select p.achfileid, fr.achfileid as fundingachfileid, p.paydate,
             pt.jurisdictiontaxid, pt.amount,
        case when p.fundingtype='PRE_FUNDED' then 'PRE'
             when p.fundingtype='OPTIMISTIC_FUNDED' then 'REGULAR'
             else 'PYW' end as ttatype,
             pt.id as paymenttaxid
        from payment p
        join paymenttax pt on p.id = pt.paymentid
        left join achfilerecord fr on fr.id = p.fundingachfilerecordid
       where 1 = 1
       and p.status in ('PAID', 'IMPORTED')
          and pt.taxtypeaccumulationid is null
          and case when arg_paymenttaxid is not null then pt.id = arg_paymenttaxid else 1 = 1 end
          and case when arg_employeeId is not null then p.employeeid = arg_employeeId else 1 = 1 end
          and case when arg_paymentId is not null then p.id = arg_paymentId else 1 = 1 end
          and case when arg_jurisdictionTaxId is not null then pt.jurisdictiontaxid = arg_jurisdictionTaxId else 1 = 1 end
  ) --select  * from paymenttaxes
  , ttastopaymenttaxids as (
      select tta.id as taxtypeaccumulationid, tta.jurisdictiontaxid, pr.paymenttaxid
        from taxtypeaccumulation tta
        join paymenttaxes pr on pr.paydate = tta.fordate
         and pr.ttatype = tta.paymenttype
         and pr.jurisdictiontaxid = tta.jurisdictiontaxid
         and pr.achfileid = tta.achfileid
         and ((pr.fundingachfileid is NULL AND tta.fundingachfileid is null) OR (pr.fundingachfileid = tta.fundingachfileid) OR (tta.fundingachfileid is null and pr.fundingachfileid is not null))
  ) --select * from ttastopaymenttaxids
  update paymenttax as pt
    set taxtypeaccumulationid=ttas.taxtypeaccumulationid
    from ttastopaymenttaxids ttas
    where ttas.paymenttaxid=pt.id;
END;
$$;


CREATE OR REPLACE PROCEDURE payment_tax_adjustment_for_employee(
    arg_jurisdictionTaxId text,
    arg_newJurisdictionTaxId text DEFAULT NULL,
    arg_employeeId bigint DEFAULT NULL)
 	LANGUAGE plpgsql
AS $$
DECLARE
BEGIN
call payment_tax_adjustment(arg_jurisdictionTaxId, arg_newJurisdictionTaxId, arg_employeeId, null);
END;
$$;

CREATE OR REPLACE PROCEDURE remove_payment_tax_for_employee(
    arg_jurisdictionTaxId text,
    arg_employeeId bigint)
 	LANGUAGE plpgsql
AS $$
DECLARE
BEGIN
call payment_tax_adjustment(arg_jurisdictionTaxId, null, arg_employeeId, null);
END;
$$;

CREATE OR REPLACE PROCEDURE payment_tax_adjustment(
    arg_jurisdictionTaxId text,
    arg_newJurisdictionTaxId text DEFAULT NULL,
    arg_employeeId bigint DEFAULT null,
    arg_paymentId bigint DEFAULT null)
 	LANGUAGE plpgsql
AS $$
DECLARE
result int;
_employee_ids bigint[];
_tax_years int[];
_paymenttax_ids_added int[];
_tax_year int;
_employee_id int;
_timestamp timestamp without time zone;

BEGIN
if arg_jurisdictionTaxId is null OR (arg_employeeId is null AND arg_paymentId is null) THEN
    RAISE EXCEPTION 'arg_jurisdictionTaxId and one of arg_employeeId OR arg_paymentId should not be null';
END IF;

select current_timestamp into _timestamp;

with paymenttaxestorevert as (
    select pt.taxtypeaccumulationid,
        pt.amount,
        pt.subjectwages,
        pt.uncappedsubjectwages,
        pt.id,
        pt.paymentid,
        pt.jurisdictiontaxid,
        arg_newJurisdictionTaxId as newTaxJurisdictionId,
        p.employeeid,
        extract(year from p.paydate) as taxyear,
        newtta.id as newTaxTypeAccumulationId,
        ptnext.id as newPaymentTaxId,
        case when pt.type like 'ER_%' then pt.amount else 0.00 end as amountER,
        case when pt.type not like 'ER_%' then pt.amount else 0.00 end as amountEE
    from
        payment p
        join paymenttax pt on p.id=pt.paymentid
        left join taxtypeaccumulation tta on tta.id=pt.taxtypeaccumulationid
        left join taxtypeaccumulation newtta
            on newtta.achfileid=tta.achfileid
            and newtta.fordate=tta.fordate
            --and newtta.companyDistributionLockboxItemId=tta.companyDistributionLockboxItemId
            and newtta.paymentType=tta.paymentType
            and newtta.jurisdictionTaxId = arg_newJurisdictionTaxId
        left join paymenttax ptnext
            on ptnext.paymentid=pt.paymentid
            AND ptnext.jurisdictiontaxid = arg_newJurisdictionTaxId
    where
        1=1
        and case when arg_employeeId is not null then p.employeeid = arg_employeeId else 1 = 1 end
        and case when arg_paymentId is not null then p.id = arg_paymentId else 1 = 1 end
        and pt.jurisdictiontaxid =  arg_jurisdictionTaxId
) --select * from paymenttaxestorevert
, totals_by_tta as (
    select taxtypeaccumulationid,
        newTaxTypeAccumulationId,
        sum(amount) as amount
    from
        paymenttaxestorevert
    where
        taxtypeaccumulationid is not null
    group by taxtypeaccumulationid, newTaxTypeAccumulationId
) -- select * from totals_by_tta
, taxtotalstoupdate as (
    select tta.id, tta.jurisdictiontaxid, tta.gross as prevgross, tta.gross - pr.amount as gross, tta.net as prevnet, tta.net - pr.amount as net, tta.taxauthoritysubmissionid
    from taxtypeaccumulation tta
    join totals_by_tta pr on pr.taxtypeaccumulationid=tta.id
    where pr.amount<>0
    union
    -- if moving to a new tax type, increase existing tta's
    select newtta.id, newtta.jurisdictiontaxid, newtta.gross as prevgross, newtta.gross + pr.amount as gross, newtta.net as prevnet, newtta.net + pr.amount as net, newtta.taxauthoritysubmissionid
    from taxtypeaccumulation newtta
    join totals_by_tta pr on pr.newTaxTypeAccumulationId=newtta.id
    where pr.amount<>0
) --select * from taxtotalstoupdate;
, updated_payment_tax_values as (
    select pt.id, pt.amount as prevamount, 0.00 as amount, 0.00 as subjectwages, 0.00 as uncappedsubjectwages, pt.jurisdictiontaxid
    from paymenttax pt
    join paymenttaxestorevert pr on pr.id=pt.id
    union
    -- if moving to a new tax type, increase existing pts
    select pt.id, pt.amount as prevamount, pt.amount + pr.amount as amount, pt.subjectwages + pr.subjectwages as subjectwages, pt.uncappedsubjectwages + pr.uncappedsubjectwages as uncappedsubjectwages, pt.jurisdictiontaxid
    from paymenttax pt
    join paymenttaxestorevert pr on pr.newPaymentTaxId=pt.id
) --select * from updated_payment_tax_values;
, to_insert_ttas as (
    select
        tta.companyid, tta.fordate,
        split_part(ptr.newTaxJurisdictionId, '-', 4),
        ptr.newTaxJurisdictionId,
        tta.achfileid,
        tta.paymenttype,
        tta.status,
        sc.state,
        split_part(ptr.newTaxJurisdictionId, '-', 2),
        split_part(ptr.newTaxJurisdictionId, '-', 3),
        split_part(ptr.newTaxJurisdictionId, '-', 5),
        ptr.amount,
        ptr.amount,
        tta.deferred,
        0.00,
        0.00,
        0.00,
        _timestamp,
        _timestamp,
        tta.fundingachfileid,
        tta.companydistributionlockboxitemid
    from taxtypeaccumulation tta
    join paymenttaxestorevert ptr on ptr.taxtypeaccumulationid = tta.id
    LEFT JOIN statecodes sc on sc.code=split_part(ptr.newTaxJurisdictionId, '-', 1)
    where ptr.newTaxJurisdictionId is not null AND ptr.newTaxTypeAccumulationId is null
) --select * from to_insert_ttas
, to_insert_pts as (
  select
        pt.paymentid,
        pt.payertype,
        split_part(ptr.newTaxJurisdictionId, '-', 4),
        ptr.newTaxJurisdictionId,
        ptr.newTaxJurisdictionId,
        '',
        ptr.amount, 0.00, 0.00, 0.00,
        ptr.subjectwages, 0.00, 0.00, 0.00,
        ptr.uncappedsubjectwages,
        _timestamp, _timestamp
    from paymenttax pt
    join paymenttaxestorevert ptr on ptr.id = pt.id
    where ptr.newTaxJurisdictionId is not null AND ptr.newPaymentTaxId is null
) --select * from to_insert_pts
, updated_tax_totals as (
    update taxtypeaccumulation as tta
        set gross=tu.gross, net=tu.gross
    from taxtotalstoupdate tu
    where tu.id=tta.id
    returning *
)
, inserted_ttas as (
  insert into taxtypeaccumulation(companyid, fordate, taxtype, jurisdictiontaxid, achfileid, paymenttype, status, state, countycode, featureidcode, schooldistrictcode, gross, net, deferred, grosscredit, appliedcredit, unappliedcredit, createdat, updatedat, fundingachfileid, companydistributionlockboxitemid)
    select * from to_insert_ttas
)
, updated_payment_taxes as (
    update paymenttax as pt
        set amount=tu.amount
            , subjectwages=tu.subjectwages
            , uncappedsubjectwages=tu.uncappedsubjectwages
    from updated_payment_tax_values tu
    where tu.id=pt.id
    returning *
)
, inserted_pts as (
    insert into paymenttax(paymentid, payertype, type, qualifiedtype, jurisdictiontaxid, description, amount, mtdamount, qtdamount, ytdamount, subjectwages, mtdsubjectwages, qtdsubjectwages, ytdsubjectwages, uncappedsubjectwages, createdat, updatedat)
    select * from to_insert_pts
    returning id
)
, updated_payments as (
    update payment as p
        set totalTaxesEE = totalTaxesEE-tu.amountEE,
            totalTaxesER = totalTaxesER-tu.amountER,
            totalTaxFunding = totalTaxFunding - tu.amount,
            netEarnings = netEarnings + tu.amount,
            worklocationstate = coalesce(sc.state, worklocationstate)
    from paymenttaxestorevert tu
    LEFT JOIN statecodes sc on sc.code=split_part(tu.newTaxJurisdictionId, '-', 1)
    where tu.paymentid=p.id
        AND tu.newTaxJurisdictionId is null
    returning p.id
)
select array(select distinct employeeid from paymenttaxestorevert), array(select distinct taxyear from paymenttaxestorevert), array(select * from inserted_pts) into _employee_ids, _tax_years, _paymenttax_ids_added;

IF cardinality(_paymenttax_ids_added) > 0 THEN
    -- assign tta to paymenttax records that were added
   FOREACH _employee_id IN ARRAY _employee_ids
   LOOP
      CALL assign_tta_to_paymenttax(null, null, _employee_id, arg_newJurisdictionTaxId);
   END LOOP;

END IF;


FOREACH _tax_year IN ARRAY _tax_years
   LOOP
      CALL payment_xtd_calc_by_employeeids_and_year(_employee_ids, _tax_year);
   END LOOP;

select count(_employee_ids) into result;

END;
$$;
