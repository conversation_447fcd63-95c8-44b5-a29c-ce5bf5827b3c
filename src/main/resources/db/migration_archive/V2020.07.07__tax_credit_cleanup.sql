-- Step 1 - generate tax credits
       insert into paymenttaxcredit (paymentid, credittype, amount, mtdamount, qtdamount, ytdamount, createdat, updatedat)
        select
                pe.paymentid as paymentid,
                'FFCRA' as credittype,
                pe.amount + pt.amount as amount,
                0.00 as mtdamount,
                0.00 as qtdamount,
                0.00 as ytdamount,
                now() as createdat,
                now() as updatedat
        from paymentearning pe
        join payment p on p.id = pe.paymentid
        left join paymenttaxcredit tc on tc.paymentid=p.id
        left join paymenttax pt on pt.paymentid=p.id and pt.type='ER_MEDI'
        where pe.type in ('EMERGENCY_FFCRA_SICK', 'EMERGENCY_FFCRA_FMLA')
        and pe.amount > 0
        and tc.id is null;

-- Step 2 - insert tax credit accumulations
       insert into taxtypecreditaccumulation (taxTypeAccumulationId, credittype, gross, applied, unapplied, createdat, updatedat)
       select
        fica_tta.id as taxTypeAccumulationId,
        'FFCRA' as credittype,
        fica_tta.grosscredit + taxcredits.unappliedcredit as gross,
        0.00 as applied,
        fica_tta.unappliedcredit + taxcredits.unappliedcredit as unapplied,
        now() as createdat,
        now() as updatedat
       from (
               select
                tta.id,
                tta.taxType,
                tta.grossCredit,
                tta.unappliedCredit,
                tta.achfileid,
                tta.companyid,
                tta.fordate
               from
                TaxTypeAccumulation tta
               where
                tta.taxtype='ER_FICA'
        ) fica_tta
        join (
               select
                p.paydate as fordate,
                p.companyid as companyid,
                p.achfileid as achfileid,
                sum(tc.amount) as unappliedCredit
               from
                paymenttaxcredit tc
               join payment p
                on p.id=tc.paymentid
               where
                tc.ytdamount=0
                and tc.amount > 0
                and p.achfileid is not null
               group by
                p.paydate,
                p.companyid,
                p.achfileid
        ) taxcredits
                on taxcredits.companyid=fica_tta.companyid
                and taxcredits.achfileid=fica_tta.achfileid
                and taxcredits.fordate=fica_tta.fordate;

-- Step 3 - update tax accumulations
        UPDATE taxtypeaccumulation as tta
        SET
                grosscredit = tta.grosscredit + ttca.unapplied,
                unappliedcredit = tta.unappliedcredit + ttca.unapplied
        FROM
                taxtypecreditaccumulation ttca
        where
                tta.id=ttca.taxtypeaccumulationid
                AND tta.unappliedcredit <> ttca.unapplied;

-- Step 4 - fix ytd, qtd and mtd amounts for paymenttaxcredit
        with paymentstocheck as (
            select * from payment --where employeeid = 32-- and id = 37718
        ),
        xtd as (
            select
                ptc.id as paymentid,
                ptc.paydate,
                ptc.employeeid,
                pc.credittype,
                coalesce(sum(case
                    when extract(month from p.paydate) = extract(month from ptc.paydate)
                    then pc.amount end), 0) as mtdamount,
                coalesce(sum(case
                    when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
                    then pc.amount end), 0) as qtdamount,
                coalesce(sum(pc.amount), 0) as ytdamount
              from paymentstocheck ptc
              join payment p on p.employeeid = ptc.employeeid
              join paymenttaxcredit pc on pc.paymentid = p.id
             where 1=1
               and ptc.status not in ('ERRORED')
               and p.status not in ('ERRORED')
               and extract(year from p.paydate) = extract(year from ptc.paydate)
               and (p.fordate < ptc.fordate or (p.fordate = ptc.fordate and p.startedat <= ptc.startedat))
             group by ptc.id, ptc.paydate, ptc.employeeid, pc.credittype
             order by ptc.employeeid, ptc.id
        ),
        toupdate as (
            select pc.id, xtd.mtdamount, xtd.qtdamount, xtd.ytdamount
              from paymenttaxcredit pc
              join xtd on pc.paymentid = xtd.paymentid
               and pc.credittype = xtd.credittype
               and (not pc.mtdamount = xtd.mtdamount
                    or not pc.qtdamount = xtd.qtdamount
                    or not pc.ytdamount = xtd.ytdamount)
        )
        -- select * from toupdate;
        update paymenttaxcredit as pd
           set mtdamount = tu.mtdamount,
               qtdamount = tu.qtdamount,
               ytdamount = tu.ytdamount
          from toupdate tu
        where pd.id = tu.id
        returning pd.*;

-- Step 5 - Cleanup data from achfiles that were missed as part of the first backfill
delete from taxtypecreditaccumulation where taxtypeaccumulationid in (
        select id from taxtypeaccumulation where fordate < '2020-06-16'
);

delete from taxtypeaccumulation where fordate < '2020-06-16';

--- Gross Credit Summary
with grosscreditsummary as (
    select
        p.companyid companyid,
        p.paydate fordate,
        pt.type taxtype,
        pt.jurisdictiontaxid jurisdictiontaxid,
        p.achfileid achfileid,
        case when b.achfilebatchtype = 'REGULAR_FUNDING' then 'REGULAR' else 'PYW' end paymenttype,
        max(pt.description) description,
        'FINALIZED' status,
        case when (left(max(pt.jurisdictiontaxid),2) = '00') then null else max(p.residentstate) end state,
        SPLIT_PART(pt.jurisdictiontaxid, '-', 2) countyCode,
        SPLIT_PART(pt.jurisdictiontaxid, '-', 3) featureidcode,
        SPLIT_PART(pt.jurisdictiontaxid, '-', 5) schooldistrictcode,
        sum(pt.amount) gross,
        sum(coalesce(case when pt.type = 'ER_FICA' then ptc.amount else 0.00 end, 0.00)) grosscredit
    from
        payment p
    join
        paymenttax pt
                on pt.paymentid=p.id
    join
        paymenttaxcredit ptc
                on ptc.paymentid=p.id
    join
        achfile a
                on a.id=p.achfileid
    join
        achfilerecord r
            on r.id = p.fundingachfilerecordid
    join
        achfilebatch b
            on b.id = r.achfilebatchid
    where a.status in ('PAID', 'SUBMITTED')
    and p.paydate < '2020-06-16'
    group by
        p.achfileid,
        p.companyid,
        p.paydate,
        pt.type,
        pt.jurisdictiontaxid,
        b.achfilebatchtype
),
toupdate as (
    select
        companyid,
        fordate,
        taxtype,
        jurisdictiontaxid,
        achfileid,
        paymenttype,
        description,
        status,
        state,
        countycode,
        featureidcode,
        schooldistrictcode,
        gross,
        grosscredit,
        least(grosscredit, gross) appliedcredit,
        greatest(0.00, grosscredit-gross) unappliedcredit,
        (gross - least(grossCredit, gross)) net,
        0.00 as deferred
    from
        grosscreditsummary
)


insert into taxtypeaccumulation(
        companyid,
        fordate,
        taxtype,
        jurisdictiontaxid,
        achfileid,
        paymenttype,
        description,
        status,
        state,
        countycode,
        featureidcode,
        schooldistrictcode,
        gross,
        grosscredit,
        appliedcredit,
        unappliedcredit,
        net,
        deferred,
        createdat,
        updatedat
)
select
        companyid,
        fordate,
        taxtype,
        jurisdictiontaxid,
        achfileid,
        paymenttype,
        description,
        status,
        state,
        countycode,
        featureidcode,
        schooldistrictcode,
        gross,
        grosscredit,
        appliedcredit,
        unappliedcredit,
        net,
        deferred,
        now(),
        now()
 from toupdate;

 insert into taxtypecreditaccumulation (taxtypeaccumulationid, credittype, gross, applied, unapplied, createdat, updatedat)
 select id, 'FFCRA', grosscredit, appliedcredit, unappliedcredit, createdat, updatedat from taxtypeaccumulation where fordate < '2020-06-16';

