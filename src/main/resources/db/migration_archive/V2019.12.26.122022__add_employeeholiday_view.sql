create view employeeholiday as
select to_hex(ce.employeeid, eh.id) as id,
       ce.companyid as companyid,
       ce.employeeid as employeeid,
       eh.policyid as policyid,
       eh.id as holidayid,
       eh.date as date,
       eh.type as type,
       eh.label as label,
       (case ep.wagetype when 'SALARY' then ep.expectedweeklyhours / 5 * 3600 end)::bigint as paidtimeoffduration,
       (case ep.wagetype when 'HOURLY' then ce.multiplier end) as timeworkedmultiplier
from holidaypolicycoveredemployee ce
join enabledholiday eh on ce.policyid = eh.policyid
join employeeposition ep on ep.employeeid = ce.employeeid
where date >= ep.startdate and date <= coalesce(ep.enddate, 'infinity'::date);
