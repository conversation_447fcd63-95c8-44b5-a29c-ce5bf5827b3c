DROP VIEW IF EXISTS employeeaccountbalance;
ALTER TABLE employeeaccounttransaction
    DROP COLUMN IF EXISTS fundingamount,
    ADD COLUMN IF NOT EXISTS fundingtype text NOT NULL DEFAULT 'NONE',
    ADD COLUMN IF NOT EXISTS pretaxliabilityamount NUMERIC NOT NULL DEFAULT 0.00;


CREATE OR REPLACE VIEW employeeaccountbalance AS
    SELECT
        et.companyid,
        c.displayname as companyname,
        et.employeeid,
        u.lastname as lastname,
        u.firstname as firstname,
        u.middlename as middlename,
        et.employeecontributiondeductionguid,
        ecd.type as employeecontributiondeductiontype,
        ecd.name as employeecontributiondeductionname,
        SUM(CASE WHEN paymentfinalized then assetamount else 0.00 end) as assetamount,
        SUM(CASE WHEN paymentfinalized then liabilityamount else 0.00 end) as liabilityamount,
        SUM(CASE WHEN paymentfinalized then pretaxliabilityamount else 0.00 end) as pretaxliabilityamount,
        SUM(CASE WHEN paymentfinalized and fundingtype='LIABILITY' then liabilityamount  WHEN paymentfinalized and fundingtype='ASSET' then assetamount else 0.00 end) as fundingamount,
        SUM(CASE WHEN paymentfinalized then liabilityamount else 0.00 end) - SUM(CASE WHEN paymentfinalized then assetamount else 0.00 end) as balance,
        SUM(CASE WHEN paymentfinalized then liabilityamount else 0.00 end) - SUM(CASE WHEN paymentfinalized then pretaxliabilityamount else 0.00 end) as pretaxbalance,
        SUM(CASE WHEN paymentfinalized then 0.00 else assetamount end) as pendingassetamount,
        SUM(CASE WHEN paymentfinalized then 0.00 else liabilityamount end) as pendingliabilityamount,
        SUM(CASE WHEN paymentfinalized then 0.00 else liabilityamount end) - SUM(CASE WHEN paymentfinalized then 0.00 else assetamount end) as pendingbalance,
        SUM(CASE WHEN paymentfinalized OR fundingtype='NONE' then 0.00 WHEN fundingtype='LIABILITY' then liabilityamount ELSE assetamount end) as pendingfundingamount
    FROM (
        SELECT
            et.*,
            p.status is null OR  p.status IN
                ('APPROVED', 'SUBMITTED', 'PAID', 'IMPORTED','PREPARED_FOR_FUNDING_REQUEST', 'FUNDING_REQUESTED','FUNDED')
            --then true else false end
            as paymentfinalized
        FROM
            employeeaccounttransaction et
        LEFT JOIN payment p on p.id=et.paymentid
        WHERE et.paymentid is null or p.status <> 'DELETED'
    ) et
    JOIN company c on c.id=et.companyid
    JOIN employee e on e.id=et.employeeid
    JOIN appuser u on u.id=e.userid
    LEFT JOIN (
        select id::text as guid,
            type,
            name
        from employeecontributiondeduction
        group by id, type, name
    ) ecd ON ecd.guid=et.employeecontributiondeductionguid
    GROUP BY
        et.companyid,
        c.displayname,
        u.firstname,
        u.lastname,
        u.middlename,
        et.employeeid,
        et.employeecontributiondeductionguid,
        ecd.type,
        ecd.name;

ALTER TABLE paymentcontributiondeduction
    ADD COLUMN IF NOT EXISTS pretaxamountee NUMERIC,
    ADD COLUMN IF NOT EXISTS mtdpretaxamountee NUMERIC,
    ADD COLUMN IF NOT EXISTS qtdpretaxamountee NUMERIC,
    ADD COLUMN IF NOT EXISTS ytdpretaxamountee NUMERIC;


-- Will need to run this before the release so it doesn't kill the deploy
UPDATE paymentcontributiondeduction
    SET
        pretaxamountee=amountee,
        mtdpretaxamountee=mtdamountee,
        qtdpretaxamountee=qtdamountee,
        ytdpretaxamountee=ytdamountee
    WHERE
        pretaxamountee IS NULL;

ALTER TABLE paymentcontributiondeduction
    ALTER COLUMN pretaxamountee SET NOT NULL,
    ALTER COLUMN mtdpretaxamountee SET NOT NULL,
    ALTER COLUMN qtdpretaxamountee SET NOT NULL,
    ALTER COLUMN ytdpretaxamountee SET NOT NULL;
