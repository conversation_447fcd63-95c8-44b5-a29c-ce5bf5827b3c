ALTER TABLE ptopolicy
    ADD COLUMN defaultptomaxhours numeric;

ALTER TABLE employee
    ADD COLUMN overrideptomaxhours numeric;

CREATE OR REPLACE VIEW ptopolicycoveredemployee AS
    SELECT to_hex(VARIADIC ARRAY[ep.id, pp.id]) AS id,
    e.companyid,
    e.id AS employeeid,
    ep.id AS positionid,
    pp.id AS policyid,
    COALESCE(e.overrideptoaccrualrate, pp.defaultaccrualrate) AS effectiveptoaccrualrate,
    GREATEST(pp.startdate, ep.startdate) AS startdate,
    LEAST(pp.enddate, ep.enddate) AS enddate,
    ep.worklocationtimezone AS timezone,
    timezone(ep.worklocationtimezone, GREATEST(pp.startdate, ep.startdate)::timestamp with time zone) AS startsat,
    timezone(ep.worklocationtimezone, LEAST(pp.enddate, ep.enddate)::timestamp with time zone) + '1 day'::interval AS endsat,
    COALESCE(e.overrideptomaxhours, pp.defaultptomaxhours) AS effectiveptomaxhours
   FROM employee e
     JOIN company c ON e.companyid = c.id
     JOIN ptopolicy pp ON e.companyid = pp.companyid
     JOIN employeeposition ep ON e.id = ep.employeeid
  WHERE "overlaps"(pp.startdate::timestamp with time zone, COALESCE(pp.enddate, 'infinity'::date)::timestamp with time zone, ep.startdate::timestamp with time zone, COALESCE(ep.enddate, 'infinity'::date)::timestamp with time zone) AND e.employmenttype = 'EMPLOYEE'::text AND (pp.salaryeligible = true AND ep.wagetype = 'SALARY'::text OR pp.fulltimehourlyeligible = true AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND ep.wagetype = 'HOURLY'::text OR pp.parttimehourlyeligible = true AND ep.wagetype = 'HOURLY'::text)
  GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate, ep.id, ep.startdate, ep.enddate, ep.worklocationtimezone;
