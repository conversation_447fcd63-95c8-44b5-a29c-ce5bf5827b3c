CREATE OR REPLACE VIEW ptopolicycoveredemployee AS  SELECT encode(convert_to(concat(e.id, '.', pp.id), 'utf-8'::name), 'base64'::text) AS id,
    e.companyid,
    pp.id AS policyid,
    e.id AS employeeid,
    pp.startdate,
    pp.enddate
   FROM employee e
     JOIN company c ON e.companyid = c.id
     JOIN ptopolicy pp ON e.companyid = pp.companyid
     JOIN ( SELECT * FROM employeeposition
          WHERE employeeposition.startdate <= CURRENT_DATE AND (employeeposition.enddate IS NULL OR employeeposition.enddate >= CURRENT_DATE)) ep ON e.id = ep.employeeid
  WHERE (pp.fulltimehourlyeligible = true AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND ep.wagetype = 'HOURLY'::text OR pp.salaryeligible = true AND ep.wagetype = 'SALARY'::text OR pp.parttimehourlyeligible = true AND ep.wagetype = 'HOURLY'::text) AND e.employmenttype = 'EMPLOYEE'::text
  GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate;


CREATE OR REPLACE VIEW holidaypolicycoveredemployee AS  SELECT encode(convert_to(concat(e.id, '.', hp.id), 'utf-8'::name), 'base64'::text) AS id,
    e.companyid,
    hp.id AS policyid,
    e.id AS employeeid,
    hp.startdate,
    hp.enddate,
    hp.multiplier
   FROM employee e
     JOIN company c ON e.companyid = c.id
     JOIN holidaypolicy hp ON e.companyid = hp.companyid
     JOIN ( SELECT * FROM employeeposition
          WHERE employeeposition.startdate <= CURRENT_DATE AND (employeeposition.enddate IS NULL OR employeeposition.enddate >= CURRENT_DATE)) ep ON e.id = ep.employeeid
  WHERE (hp.fulltimehourlyeligible = true AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND ep.wagetype = 'HOURLY'::text OR hp.salaryeligible = true AND ep.wagetype = 'SALARY'::text OR hp.parttimehourlyeligible = true AND ep.wagetype = 'HOURLY'::text) AND e.employmenttype = 'EMPLOYEE'::text
  GROUP BY e.companyid, e.id, hp.id, hp.startdate, hp.enddate;
