drop view IF EXISTS
  annualF940Quarter,
  annualF940,
  annualFW3
;

create or replace view annualF940 as
  select
    *,
    round(annual940InnerTable4.question7TotalTaxableFutaWages * 0.006, 2) as question8FutaTaxBeforeAdjustments,
    round(annual940InnerTable4.question7TotalTaxableFutaWages * 0.054, 2) as question9
  from (
    select
      *,
      (annual940InnerTable3.totalPaymentsToAllEmployees - annual940InnerTable3.question6Subtotal) as question7TotalTaxableFutaWages
    from (
      select
        *,
        (annual940InnerTable2.amountNotSubjectToFuta + annual940InnerTable2.totalPaymentsToEachEmployeeInExcessOf7000) as question6Subtotal
      from (
        select
          *,
          (select (annualPaymentByType.totalpaymentstoallemployees - annualPaymentByType.taxSubjectWages) as amountNotSubjectToFuta
            from AnnualPaymentByType annualPaymentByType
            where annualPaymentByType.taxType = 'ER_FUTA'
              and annualPaymentByType.companyId = annual940InnerTable1.companyId
              and annualPaymentByType.year = annual940InnerTable1.year),
          (select (annualPaymentByType.totalpaymentstoallemployees - annualPaymentByType.taxSubjectWages) as amountNotSubjectToSuta
            from AnnualPaymentByType annualPaymentByType
            where annualPaymentByType.taxType = 'ER_SUTA'
              and annualPaymentByType.companyId = annual940InnerTable1.companyId
              and annualPaymentByType.year = annual940InnerTable1.year)
        from (
          select
            extract(year from payment.paydate) as year,
            payment.companyId as companyId,
            company.federalEin as federalEin,
            company.displayName as displayName,
            company.legalEntityName as legalEntityName,
            company.state as state,
            company.zip as zip,
            sum(payment.grossearnings) as totalPaymentsToAllEmployees,
            sum(case when payment.grossearnings > 7000 then payment.grossearnings else 0 end) as totalPaymentsToEachEmployeeInExcessOf7000
          from Payment payment
          inner join Company company on payment.companyId = company.id
          where payment.status in ('PAID', 'IMPORT')
          group by 1, 2, 3, 4, 5, 6, 7
        ) as annual940InnerTable1
      ) as annual940InnerTable2
    ) as annual940InnerTable3
  ) as annual940InnerTable4
;

create or replace view annualF940Quarter as
  select
  *,
  round(annual940QuarterInnerTable4.question7TotalTaxableFutaWages * 0.006, 2) as question8FutaTaxBeforeAdjustments,
  round(annual940QuarterInnerTable4.question7TotalTaxableFutaWages * 0.054, 2) as question9
  from (
    select
      *,
      (annual940QuarterInnerTable3.totalPaymentsToAllEmployees - annual940QuarterInnerTable3.question6Subtotal) as question7TotalTaxableFutaWages
    from (
      select
        *,
        (annual940QuarterInnerTable2.amountNotSubjectToFuta + annual940QuarterInnerTable2.totalPaymentsToEachEmployeeInExcessOf7000) as question6Subtotal
      from (
        select
          *,
          (select (annualPaymentByType.totalpaymentstoallemployees - annualPaymentByType.taxSubjectWages) as amountNotSubjectToFuta
            from AnnualPaymentByType annualPaymentByType
            where annualPaymentByType.taxType = 'ER_FUTA'
              and annualPaymentByType.companyId = annualF940QuarterInnerTable1.companyId
              and annualPaymentByType.year = annualF940QuarterInnerTable1.year),
          (select (annualPaymentByType.totalpaymentstoallemployees - annualPaymentByType.taxSubjectWages) as amountNotSubjectToSuta
            from AnnualPaymentByType annualPaymentByType
            where annualPaymentByType.taxType = 'ER_SUTA'
              and annualPaymentByType.companyId = annualF940QuarterInnerTable1.companyId
              and annualPaymentByType.year = annualF940QuarterInnerTable1.year)
        from (
          select
            extract(year from payment.paydate) as year,
            concat('Q', extract(quarter from payment.paydate)) as quarter,
            payment.companyId as companyId,
            sum(payment.grossearnings) as totalPaymentsToAllEmployees,
            sum(CASE WHEN payment.grossearnings > 7000 THEN payment.grossearnings ELSE 0 END) as totalPaymentsToEachEmployeeInExcessOf7000
          from Payment payment
          inner join Company company on payment.companyId = company.id
          WHERE payment.status IN ('PAID', 'IMPORT')
          group by 1, 2, 3
      ) as annualF940QuarterInnerTable1
    ) as annual940QuarterInnerTable2
  ) as annual940QuarterInnerTable3
) as annual940QuarterInnerTable4
;

create or replace view annualFW3 AS
  SELECT paymentAgg.year, paymentAgg.companyId, paymentAgg.recipientState,
    (SELECT (SUM(payment1.grossearnings)
        - SUM(payment1.deferredcompensation)
        - SUM(payment1.pretaxdeductions))
      FROM Payment payment1
      WHERE payment1.id IN (select(unnest(paymentAgg.paymentIds)))) as wagesTipsAndOtherCompensation,
    (SELECT sum(tax2.amount)
      FROM PaymentTax tax2
      WHERE tax2.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax2.type = 'FIT') as federalIncomeTaxWithheld,
    (SELECT sum(tax3.subjectWages) FROM Payment payment3
      INNER JOIN PaymentTax tax3 ON tax3.paymentId = payment3.id
      INNER JOIN PaymentEarning earning3 ON earning3.paymentId = payment3.id
      WHERE tax3.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax3.type IN ('FICA', 'ER_FICA')
      AND earning3.type != 'TIPS') as socialSecurityWages,
    (SELECT sum(tax4.amount)
      FROM PaymentTax tax4
      WHERE tax4.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax4.type IN ('FICA', 'ER_FICA')) as socialSecurityTaxWithheld,
    (SELECT sum(tax5.subjectWages)
      FROM PaymentTax tax5
      WHERE tax5.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax5.type IN ('MEDI', 'ER_MEDI', 'MEDI2')) as medicareWagesAndTips,
    (SELECT sum(tax6.amount)
      FROM PaymentTax tax6
      WHERE tax6.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax6.type IN ('MEDI', 'ER_MEDI', 'MEDI2')) as medicareTaxWithheld,
    (SELECT sum(tax7.subjectWages) FROM Payment payment3
      INNER JOIN PaymentTax tax7 ON tax7.paymentId = payment3.id
      INNER JOIN PaymentEarning earning3 ON earning3.paymentId = payment3.id
      WHERE tax7.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax7.type IN ('FICA', 'ER_FICA')
      AND earning3.type = 'TIPS') as socialSecurityTips,
    (SELECT sum(tax8.subjectWages)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateWagesTipsEtc,
    (SELECT sum(tax8.amount)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateIncomeTaxWithheld,
    (SELECT count(distinct(payment12.employeeId))
      FROM Payment payment12
      WHERE payment12.id IN (select(unnest(paymentAgg.paymentIds)))) as countOfEmployees
    FROM paymentIdsByYearStateCompany paymentAgg
;
