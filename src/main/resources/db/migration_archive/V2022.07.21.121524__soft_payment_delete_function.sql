CREATE OR R<PERSON>LACE FUNCTION payment_soft_delete(func_paymentid bigint, func_userid bigint, func_note text DEFAULT NULL::text) RETURNS bigint
    LANGUAGE plpgsql
    AS $$
DECLARE
  current_status text;
  isPOD boolean;
  isPODRemainder boolean;
  podPeriodId bigint;
/*

    % SELECT payment_soft_delete(
        paymentid   := 123,
        userid      := 123,
        note        := 'An optional note'
    );
     payment_soft_delete
    ─────────────
     t

Soft delete the payment

paymentid
: The payment id to "delete"

userid
: The user id performing the "delete"

note
: Optional note as to why the payment was deleted

Returns the deleted payment.

*/
    BEGIN

        SELECT status INTO current_status FROM payment WHERE id = func_paymentid;

        IF current_status in ('APPROVED', 'SUBMITTED', 'PAID', 'IMPORTED', 'PREPARED_FOR_FUNDING_REQUEST', 'FUNDING_REQUESTED', 'FUNDED') THEN
            RAISE EXCEPTION 'payment (%) is already finalized', func_paymentid;
        END IF;

        SELECT case when pp.payPeriodType='PAY_ON_DEMAND_REMAINDER' then true else false end INTO isPODRemainder FROM payment p JOIN payperiod pp ON pp.id=p.payperiodid WHERE p.id = func_paymentid;

        IF isPODRemainder THEN
            RAISE EXCEPTION 'payment (%) is a POD remainder and cannot be deleted.', func_paymentid;
        END IF;

        SELECT case when pp.payPeriodType='PAY_ON_DEMAND' then true else false end INTO isPOD FROM payment p JOIN payperiod pp ON pp.id=p.payperiodid WHERE p.id = func_paymentid;

        DELETE FROM paymentrequestrecipient prr
        WHERE prr.paymentid = func_paymentid;

        DELETE from payondemandrequest podr
        WHERE podr.paymentid = func_paymentid;

        UPDATE payment
           SET prevpaymentid = null
        WHERE prevpaymentid=func_paymentid;

        UPDATE employee
           SET severancepaymentid = null
        WHERE severancepaymentid = func_paymentid;

    ---- Leave this here so we can determine where it was deleted from
    --    UPDATE payment
    --       SET prevpaymentid = null
    --    WHERE prevpaymentid = func_paymentid;

        IF isPOD THEN
            SELECT payperiodid INTO podPeriodId FROM payment p WHERE id=func_paymentid;
            UPDATE payment SET payperiodid = (select pp.id from payperiod pp join payperiod oldpp on oldpp.companyid=pp.companyid and oldpp.startdate=pp.startdate join payment p on oldpp.id=p.payperiodid where pp.employeeid is null and pp.payperiodtype NOT IN ('IMPORT', 'AD_HOC') and p.id=func_paymentid) where id=func_paymentid;
            DELETE FROM payperiod where id = (select r.id from payperiod r join payperiod p on p.employeeid=r.employeeid and r.startdate > p.enddate where p.id=podPeriodId and r.payperiodtype='PAY_ON_DEMAND_REMAINDER');
            DELETE from payperiod where id = podPeriodId;
        END IF;


        UPDATE payment
           SET status           = 'DELETED',
               deletedat        = COALESCE(payment.deletedat, now()),
               deletedbyuserid  = COALESCE(payment.deletedbyuserid, func_userid),
               deletednote      = COALESCE(payment.deletednote, func_note),
               updatedat        = NOW()
         WHERE payment.id = func_paymentid;

       RETURN func_paymentid;
    END;
$$;
