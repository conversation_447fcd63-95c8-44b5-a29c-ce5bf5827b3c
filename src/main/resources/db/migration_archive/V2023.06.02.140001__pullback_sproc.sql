-- call payment_pullback(ARRAY[1415208, 1418881], 123, 'https://ticket-url');

DROP PROCEDURE IF EXISTS payment_pullback;

CREATE OR REPLACE PROCEDURE payment_pullback(
    arg_paymentids bigint[],
    arg_userId bigint,
    arg_note text,
    arg_pullbackByCompany boolean)
 	LANGUAGE plpgsql
AS $$
DECLARE
result int;
_employee_ids bigint[];
_tax_years int[];
_tax_year int;

BEGIN
with paymentstocheck as (
	select p.id, p.employeeid, p.paydate, extract(year from p.paydate) as taxyear, arg_note as note
	  from payment p
	 where 1 = 1
      and case when arg_paymentids is not null then p.id = ANY (arg_paymentids) else 1 = 1 end
), paymentdelete as (
	update payment as p
	   set status = 'DELETED',
	       deletedat = COALESCE(p.deletedat, now()),
	       deletedbyuserid = COALESCE(p.deletedbyuserid, arg_userId),
	       deletednote = COALESCE(p.deletednote, r.note),
	       deletedreason = 'PULLBACK',
	       pullbackbycompany = arg_pullbackByCompany,
	       updatedat = now()
	  from paymentstocheck r
	 where r.id = p.id
	returning *
), accounttransactiontorevert as (
	insert into employeeaccounttransaction (companyid, employeeid, employeecontributiondeductionguid, liabilityamount, assetamount, description, nonpaymenttransactiondate, createdat, updatedat, employeecontributiondeductiontype, pretaxliabilityamount, fundingtype)
	(select et.companyid, et.employeeid, et.employeecontributiondeductionguid, et.liabilityamount, 0.00, r.note, et.createdat::date, now(), now(), et.employeecontributiondeductiontype, et.pretaxliabilityamount, et.fundingtype from employeeaccounttransaction et join paymentstocheck r on r.id = et.paymentid)
), paymenttaxestorevert as (
    select pt.taxtypeaccumulationid, sum(pt.amount) as amount
      from paymentstocheck p
      join paymenttax pt on p.id = pt.paymentid
      where pt.taxtypeaccumulationid is not null
     group by pt.taxtypeaccumulationid
), taxtotalstoupdate as (
    select tta.id, tta.jurisdictiontaxid, tta.gross as prevgross,
           tta.gross - pr.amount as gross, tta.net as prevnet,
           tta.net - pr.amount as net, tta.taxauthoritysubmissionid
      from taxtypeaccumulation tta
      join paymenttaxestorevert pr on pr.taxtypeaccumulationid = tta.id
), ttatoupdate as (
	update taxtypeaccumulation as tta
	   set gross = tu.gross, net = tu.gross
	  from taxtotalstoupdate tu
	 where tu.id = tta.id
	returning *
)
select array(select distinct employeeid from paymentstocheck), array(select distinct taxyear from paymentstocheck) into _employee_ids, _tax_years;


FOREACH _tax_year IN ARRAY _tax_years
   LOOP
      CALL payment_xtd_calc_by_employeeids_and_year(_employee_ids, _tax_year);
   END LOOP;

select count(_employee_ids) into result;

END;
$$;
