--- Gross Credit Summary
with grosscreditsummary as (
    select
        p.companyid companyid,
        p.paydate fordate,
        pt.type taxtype,
        pt.jurisdictiontaxid jurisdictiontaxid,
        p.achfileid achfileid,
        case when b.achfilebatchtype = 'REGULAR_FUNDING' then 'REGULAR' else 'PYW' end paymenttype,
        max(pt.description) description,
        'FINALIZED' status,
        case when (left(max(pt.jurisdictiontaxid),2) = '00') then null else max(p.residentstate) end state,
        SPLIT_PART(pt.jurisdictiontaxid, '-', 2) countyCode,
        SPLIT_PART(pt.jurisdictiontaxid, '-', 3) featureidcode,
        SPLIT_PART(pt.jurisdictiontaxid, '-', 5) schooldistrictcode,
        sum(pt.amount) gross,
        sum(coalesce(case when pt.type = 'ER_FICA' then p.ffcrataxcredit else 0.00 end, 0.00)) grosscredit
    from
        payment p
    join
        paymenttax pt
                on pt.paymentid=p.id
    join
        achfile a
                on a.id=p.achfileid
    join
        achfilerecord r
            on r.id = p.fundingachfilerecordid
    join
        achfilebatch b
            on b.id = r.achfilebatchid
    where a.status in ('PAID', 'SUBMITTED')
    group by
        p.achfileid,
        p.companyid,
        p.paydate,
        pt.type,
        pt.jurisdictiontaxid,
        b.achfilebatchtype
),
toupdate as (
    select
        companyid,
        fordate,
        taxtype,
        jurisdictiontaxid,
        achfileid,
        paymenttype,
        description,
        status,
        state,
        countycode,
        featureidcode,
        schooldistrictcode,
        gross,
        grosscredit,
        least(grosscredit, gross) appliedcredit,
        greatest(0.00, grosscredit-gross) unappliedcredit,
        (gross - least(grossCredit, gross)) net,
        0.00 as deferred
    from
        grosscreditsummary
)

insert into taxtypeaccumulation(
        companyid,
        fordate,
        taxtype,
        jurisdictiontaxid,
        achfileid,
        paymenttype,
        description,
        status,
        state,
        countycode,
        featureidcode,
        schooldistrictcode,
        gross,
        grosscredit,
        appliedcredit,
        unappliedcredit,
        net,
        deferred,
        createdat,
        updatedat
)
select
        companyid,
        fordate,
        taxtype,
        jurisdictiontaxid,
        achfileid,
        paymenttype,
        description,
        status,
        state,
        countycode,
        featureidcode,
        schooldistrictcode,
        gross,
        grosscredit,
        appliedcredit,
        unappliedcredit,
        net,
        deferred,
        now(),
        now()
 from toupdate;

 insert into taxtypecreditaccumulation (taxtypeaccumulationid, credittype, gross, applied, unapplied, createdat, updatedat)
 select id, 'FFCRA', grosscredit, appliedcredit, unappliedcredit, createdat, updatedat from taxtypeaccumulation;
