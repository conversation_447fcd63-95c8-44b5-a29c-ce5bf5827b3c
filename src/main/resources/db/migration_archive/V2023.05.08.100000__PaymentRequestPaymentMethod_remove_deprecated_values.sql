-- Will need to run this before the release so it doesn't kill the deploy
--WITH cte AS (
--   SELECT id
--   FROM   paymentrequest
--   WHERE  paymentmethod IN ('PAY_CARD', 'DIRECT_DEPOSIT')
--   LIMIT  30000
--   )
--UPDATE paymentrequest s
--SET    paymentmethod='WORKER_PREFERRED'
--FROM   cte
--WHERE  s.id = cte.id;
--
--WITH cte AS (
--   SELECT id
--   FROM   paymentrequestrecipient
--   WHERE  paymentmethod IN ('PAY_CARD', 'DIRECT_DEPOSIT')
--   LIMIT  30000
--   )
--UPDATE paymentrequestrecipient s
--SET    paymentmethod='WORKER_PREFERRED'
--FROM   cte
--WHERE  s.id = cte.id;
UPDATE paymentrequest SET paymentmethod='WORKER_PREFERRED' WHERE paymentmethod IN ('PAY_CARD', 'DIRECT_DEPOSIT');
UPDATE paymentrequestrecipient SET paymentmethod='WORKER_PREFERRED' WHERE paymentmethod IN ('PAY_CARD', 'DIRECT_DEPOSIT');
