CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION paymentrequestrecipient_validate_is_linked_to_payment() RETURNS trigger AS
$fn$
begin
    if (select paymenttimeframe from paymentrequest where id = NEW.paymentrequestid) =
       'WITH_NEXT_PAYROLL' then
        return NEW;
    end if;

    if NEW.paymentid is null then
        RAISE not_null_violation USING MESSAGE =
                'null value in column "paymentid" violates not-null check';
    end if;

    if not exists(select from payment where id = NEW.paymentid) then
        RAISE foreign_key_violation USING MESSAGE =
                'insert or update violates foreign key check on "paymentid"';
    end if;

    return NEW;
end;
$fn$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS paymentrequestrecipient_validate_is_linked_to_payment ON paymentrequestrecipient;

CREATE CONSTRAINT TRIGGER paymentrequestrecipient_validate_is_linked_to_payment
    AFTER INSERT OR UPDATE
    ON paymentrequestrecipient
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
EXECUTE FUNCTION paymentrequestrecipient_validate_is_linked_to_payment();
