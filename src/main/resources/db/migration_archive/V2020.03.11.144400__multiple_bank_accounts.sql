-- Create and migrate UserBankAccount
DROP TABLE IF EXISTS userbankaccount;

CREATE TABLE userbankaccount
(
    id BIGSERIAL NOT NULL,
    userid BIGINT NOT NULL,
    ruleorder INTEGER NOT NULL,
    ruleamount NUMERIC NOT NULL,
    ruleamounttype TEXT NOT NULL,
    accountnumber TEXT NOT NULL,
    routingnumber TEXT NOT NULL,
    bankname TEXT NOT NULL,
    accountname TEXT NOT NULL,
    accounttype TEXT NOT NULL,
    createdat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updatedat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT routingnumber_len CHECK (LENGTH(routingnumber) = 9)
);

ALTER TABLE userbankaccount ADD CONSTRAINT userbankaccount_user_fk FOREIGN KEY (userid) REFERENCES appuser;
ALTER TABLE userbankaccount ADD CONSTRAINT userbankaccount_user_ruleorder_uk UNIQUE (userid, ruleorder);
CREATE INDEX userbankaccount_user_idx ON userbankaccount (userid ASC);

INSERT INTO userbankaccount (userid, ruleorder, ruleamount, ruleamounttype, accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
  select u.id, 0, 1.0, 'PERCENT', a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
    from bankaccounts a inner join appuser u on u.bankaccountid = a.id;

ALTER TABLE appuser DROP CONSTRAINT IF EXISTS appuser_bankaccountid_fkey;
delete from bankaccounts where id in (select bankaccountid from appuser);
ALTER TABLE appuser DROP COLUMN bankaccountid;

-- Create and migrate CompanyBankAccount
DROP TABLE IF EXISTS companybankaccount;

CREATE TABLE companybankaccount
(
    id BIGSERIAL NOT NULL,
    companyid BIGINT NOT NULL,
    accountnumber TEXT NOT NULL,
    routingnumber TEXT NOT NULL,
    bankname TEXT NOT NULL,
    accountname TEXT NOT NULL,
    accounttype TEXT NOT NULL,
    createdat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updatedat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT routingnumber_len CHECK (LENGTH(routingnumber) = 9)
);

ALTER TABLE companybankaccount ADD CONSTRAINT companybankaccount_company_fk FOREIGN KEY (companyid) REFERENCES company;
ALTER TABLE companybankaccount ADD CONSTRAINT companybankaccount_company_uk UNIQUE (companyid);
CREATE INDEX companybankaccount_company_idx ON companybankaccount (companyid ASC);

INSERT INTO companybankaccount (companyid, accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
  select c.id, a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
    from bankaccounts a inner join company c on c.bankaccountid = a.id;

ALTER TABLE company DROP CONSTRAINT company_bankaccountid_fkey;
delete from bankaccounts where id in (select bankaccountid from company);
ALTER TABLE company DROP COLUMN bankaccountid;

-- Create and migrate AchPaymentConfigurationBankAccount
DROP TABLE IF EXISTS achpaymentconfigurationbankaccount;

CREATE TABLE achpaymentconfigurationbankaccount
(
    id BIGSERIAL NOT NULL,
    accountnumber TEXT NOT NULL,
    routingnumber TEXT NOT NULL,
    bankname TEXT NOT NULL,
    accountname TEXT NOT NULL,
    accounttype TEXT NOT NULL,
    createdat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updatedat TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT routingnumber_len CHECK (LENGTH(routingnumber) = 9)
);

ALTER TABLE achpaymentconfiguration DROP CONSTRAINT IF EXISTS "achpaymentconfiguration_achdestinationaccountid_fkey";
ALTER TABLE achpaymentconfiguration DROP CONSTRAINT IF EXISTS "achpaymentconfiguration_achoriginaccountid_fkey";
ALTER TABLE achpaymentconfiguration DROP CONSTRAINT IF EXISTS "achpaymentconfiguration_payrollaccountid_fkey";
ALTER TABLE achpaymentconfiguration DROP CONSTRAINT IF EXISTS "achpaymentconfiguration_payyourwaylockboxaccountid_fkey";
ALTER TABLE achpaymentconfiguration DROP CONSTRAINT IF EXISTS "achpaymentconfiguration_payyourwaypayrollaccountid_fkey";
ALTER TABLE achpaymentconfiguration DROP CONSTRAINT IF EXISTS "achpaymentconfiguration_suiaccountid_fkey";

with achconfig as (
    select c.achdestinationaccountid from achpaymentconfiguration c
),
newid as (
    INSERT INTO achpaymentconfigurationbankaccount (accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
        select a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
            from bankaccounts a where id in (select c.achdestinationaccountid from achpaymentconfiguration c)
    returning id
),
updated as (
    update achpaymentconfiguration set
        achdestinationaccountid = (select id from newid)
    returning id
)
delete from bankaccounts where id in (select * from achconfig);

with achconfig as (
    select c.achoriginaccountid from achpaymentconfiguration c
),
newid as (
    INSERT INTO achpaymentconfigurationbankaccount (accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
        select a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
            from bankaccounts a where id in (select c.achoriginaccountid from achpaymentconfiguration c)
    returning id
),
updated as (
    update achpaymentconfiguration set
        achoriginaccountid = (select id from newid)
    returning id
)
delete from bankaccounts where id in (select * from achconfig);

with achconfig as (
    select c.payrollaccountid from achpaymentconfiguration c
),
newid as (
    INSERT INTO achpaymentconfigurationbankaccount (accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
        select a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
            from bankaccounts a where id in (select c.payrollaccountid from achpaymentconfiguration c)
    returning id
),
updated as (
    update achpaymentconfiguration set
        payrollaccountid = (select id from newid)
    returning id
)
delete from bankaccounts where id in (select * from achconfig);

with achconfig as (
    select c.payyourwaypayrollaccountid from achpaymentconfiguration c
),
newid as (
    INSERT INTO achpaymentconfigurationbankaccount (accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
        select a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
            from bankaccounts a where id in (select c.payyourwaypayrollaccountid from achpaymentconfiguration c)
    returning id
),
updated as (
    update achpaymentconfiguration set
        payyourwaypayrollaccountid = (select id from newid)
    returning id
)
delete from bankaccounts where id in (select * from achconfig);

with achconfig as (
    select c.payyourwaylockboxaccountid from achpaymentconfiguration c
),
newid as (
    INSERT INTO achpaymentconfigurationbankaccount (accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
        select a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
            from bankaccounts a where id in (select c.payyourwaylockboxaccountid from achpaymentconfiguration c)
    returning id
),
updated as (
    update achpaymentconfiguration set
        payyourwaylockboxaccountid = (select id from newid)
    returning id
)
delete from bankaccounts where id in (select * from achconfig);

with achconfig as (
    select c.suiaccountid from achpaymentconfiguration c
),
newid as (
    INSERT INTO achpaymentconfigurationbankaccount (accountnumber, routingnumber, bankname, accountname, accounttype, createdat, updatedat)
        select a.accountnumber, a.routingnumber, a.bankname, a.accountname, a.accounttype, current_timestamp, current_timestamp
            from bankaccounts a where id in (select c.suiaccountid from achpaymentconfiguration c)
    returning id
),
updated as (
    update achpaymentconfiguration set
        suiaccountid = (select id from newid)
    returning id
)
delete from bankaccounts where id in (select * from achconfig);

ALTER TABLE achpaymentconfiguration ADD CONSTRAINT achpaymentconfiguration_achdestinationaccountid_fk FOREIGN KEY (achdestinationaccountid) REFERENCES achpaymentconfigurationbankaccount;
ALTER TABLE achpaymentconfiguration ADD CONSTRAINT achpaymentconfiguration_achoriginaccountid_fk FOREIGN KEY (achoriginaccountid) REFERENCES achpaymentconfigurationbankaccount;
ALTER TABLE achpaymentconfiguration ADD CONSTRAINT achpaymentconfiguration_payrollaccountid_fk FOREIGN KEY (payrollaccountid) REFERENCES achpaymentconfigurationbankaccount;
ALTER TABLE achpaymentconfiguration ADD CONSTRAINT achpaymentconfiguration_payyourwaylockboxaccountid_fk FOREIGN KEY (payyourwaylockboxaccountid) REFERENCES achpaymentconfigurationbankaccount;
ALTER TABLE achpaymentconfiguration ADD CONSTRAINT achpaymentconfiguration_payyourwaypayrollaccountid_fk FOREIGN KEY (payyourwaypayrollaccountid) REFERENCES achpaymentconfigurationbankaccount;
ALTER TABLE achpaymentconfiguration ADD CONSTRAINT achpaymentconfiguration_suiaccountid_fk FOREIGN KEY (suiaccountid) REFERENCES achpaymentconfigurationbankaccount;

DROP TABLE IF EXISTS bankaccounts;

