CREATE OR REPLACE FUNCTION payment_recalculate(func_companyid bigint, func_employeeid bigint, func_year int) RETURNS bigint
    LANGUAGE plpgsql
    AS $$
BEGIN
--commit
--rollback

with paymentstocheck as (
    select p.*, c.contributiondeductioncontextdatetype from payment p
      join company c on c.id = p.companyid
     where 1 = 1
         and extract(year from fordate) = func_year
         and p.companyid = func_companyid
         and p.employeeid = func_employeeid
), --- Payments
paymentsxtd as (
    select 
        ptc.id as id,
        ptc.paydate,
        ptc.employeeid, 
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then p.grossearnings end), 0) as mtdgrossearnings,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then p.grossearnings end), 0) as qtdgrossearnings,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then p.grossearnings end), 0) as ytdgrossearnings,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then p.netearnings end), 0) as mtdnetearnings,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then p.netearnings end), 0) as qtdnetearnings,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then p.netearnings end), 0) as ytdnetearnings,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then p.pretaxdeductions end), 0) as mtdpretaxdeductions,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then p.pretaxdeductions end), 0) as qtdpretaxdeductions,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then p.pretaxdeductions end), 0) as ytdpretaxdeductions,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then p.posttaxdeductions end), 0) as mtdposttaxdeductions,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then p.posttaxdeductions end), 0) as qtdposttaxdeductions,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then p.posttaxdeductions end), 0) as ytdposttaxdeductions,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then p.deferredcompensation end), 0) as mtddeferredcompensation,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then p.deferredcompensation end), 0) as qtddeferredcompensation,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then p.deferredcompensation end), 0) as ytddeferredcompensation
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and extract(year from p.paydate) = extract(year from ptc.paydate)
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
     group by ptc.id, ptc.paydate, ptc.employeeid
     order by ptc.employeeid, ptc.id
), paymentstoupdate as ( 
    select p.id, 
           xtd.mtdgrossearnings, xtd.qtdgrossearnings, xtd.ytdgrossearnings, 
           xtd.mtdnetearnings, xtd.qtdnetearnings, xtd.ytdnetearnings,
           xtd.mtdpretaxdeductions, xtd.qtdpretaxdeductions, xtd.ytdpretaxdeductions,
           xtd.mtdposttaxdeductions, xtd.qtdposttaxdeductions, xtd.ytdposttaxdeductions,
           xtd.mtddeferredcompensation, xtd.qtddeferredcompensation, xtd.ytddeferredcompensation
      from payment p
      join paymentsxtd xtd on p.id = xtd.id
       and (not p.mtdgrossearnings = xtd.mtdgrossearnings
            or not p.qtdgrossearnings = xtd.qtdgrossearnings
            or not p.ytdgrossearnings = xtd.ytdgrossearnings
            or not p.mtdnetearnings = xtd.mtdnetearnings
            or not p.qtdnetearnings = xtd.qtdnetearnings
            or not p.ytdnetearnings = xtd.ytdnetearnings
            or not p.mtdpretaxdeductions = xtd.mtdpretaxdeductions
            or not p.qtdpretaxdeductions = xtd.qtdpretaxdeductions
            or not p.ytdpretaxdeductions = xtd.ytdpretaxdeductions
            or not p.mtdposttaxdeductions = xtd.mtdposttaxdeductions
            or not p.qtdposttaxdeductions = xtd.qtdposttaxdeductions
            or not p.ytdposttaxdeductions = xtd.ytdposttaxdeductions
            or not p.mtddeferredcompensation = xtd.mtddeferredcompensation
            or not p.qtddeferredcompensation = xtd.qtddeferredcompensation
            or not p.ytddeferredcompensation = xtd.ytddeferredcompensation)
), paymentsupdate as (
	update payment as p
	   set mtdgrossearnings = tu.mtdgrossearnings,
	       qtdgrossearnings = tu.qtdgrossearnings,
	       ytdgrossearnings = tu.ytdgrossearnings,
	       mtdnetearnings = tu.mtdnetearnings,
	       qtdnetearnings = tu.qtdnetearnings,
	       ytdnetearnings = tu.ytdnetearnings,
	       mtdpretaxdeductions = tu.mtdpretaxdeductions,
	       qtdpretaxdeductions = tu.qtdpretaxdeductions,
	       ytdpretaxdeductions = tu.ytdpretaxdeductions,
	       mtdposttaxdeductions = tu.mtdposttaxdeductions,
	       qtdposttaxdeductions = tu.qtdposttaxdeductions,
	       ytdposttaxdeductions = tu.ytdposttaxdeductions,
	       mtddeferredcompensation = tu.mtddeferredcompensation,
	       qtddeferredcompensation = tu.qtddeferredcompensation,
	       ytddeferredcompensation = tu.ytddeferredcompensation
	  from paymentstoupdate tu
	 where p.id = tu.id 
	 returning p.*
), --- Payment Earnings
earningsxtd as (
    select 
        ptc.id as paymentid,
        ptc.paydate,
        ptc.employeeid, 
        pe.type,
        pe.unitrate,
        pe.approvalgroupid,
        pe.worklocationid,
        pe.workerroleid,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pe.amount end), 0) as mtdamount,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pe.amount end), 0) as qtdamount,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pe.amount end), 0) as ytdamount
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
      join paymentearning pe on pe.paymentid = p.id
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and extract(year from p.paydate) = extract(year from ptc.paydate)
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
     group by ptc.id, ptc.paydate, ptc.employeeid, pe.type, pe.unitrate, pe.approvalgroupid, pe.worklocationid, pe.workerroleid
     order by ptc.employeeid, ptc.id
), earningstoupdate as ( 
    select pe.id, xtd.mtdamount, xtd.qtdamount, xtd.ytdamount 
      from paymentearning pe
      join earningsxtd xtd on pe.paymentid = xtd.paymentid
       and pe.type = xtd.type
       and coalesce(pe.unitrate, 0.00) = coalesce(xtd.unitrate, 0.00)
       and coalesce(pe.approvalgroupid, 0) = coalesce(xtd.approvalgroupid, 0)
       and coalesce(pe.worklocationid, 0) = coalesce(xtd.worklocationid, 0)
       and coalesce(pe.workerroleid, 0) = coalesce(xtd.workerroleid, 0)
       and (not pe.mtdamount = xtd.mtdamount
            or not pe.qtdamount = xtd.qtdamount
            or not pe.ytdamount = xtd.ytdamount)
), earningsupdate as (
	update paymentearning as pe
	   set mtdamount = tu.mtdamount,
	       qtdamount = tu.qtdamount,
	       ytdamount = tu.ytdamount
	  from earningstoupdate tu
	 where pe.id = tu.id 
	 returning pe.*
), --- Payment Taxes
taxesxtd as (
    select 
        ptc.id as paymentid,
        ptc.paydate,
        ptc.employeeid, 
        pt.jurisdictiontaxid,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pt.amount end), 0) as mtdamount,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pt.amount end), 0) as qtdamount,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pt.amount end), 0) as ytdamount,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pt.subjectwages end), 0) as mtdsubjectwages,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pt.subjectwages end), 0) as qtdsubjectwages,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pt.subjectwages end), 0) as ytdsubjectwages
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
      join paymenttax pt on pt.paymentid = p.id
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and extract(year from p.paydate) = extract(year from ptc.paydate)
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
     group by ptc.id, ptc.paydate, ptc.employeeid, pt.jurisdictiontaxid
     order by ptc.employeeid, ptc.id
), taxestoupdate as ( 
    select pt.id, 
           xtd.mtdamount, xtd.qtdamount, xtd.ytdamount,
           xtd.mtdsubjectwages, xtd.qtdsubjectwages, xtd.ytdsubjectwages 
      from paymenttax pt
      join taxesxtd xtd on pt.paymentid = xtd.paymentid
       and pt.jurisdictiontaxid = xtd.jurisdictiontaxid
       and (not pt.mtdamount = xtd.mtdamount
            or not pt.qtdamount = xtd.qtdamount
            or not pt.ytdamount = xtd.ytdamount
            or not pt.mtdsubjectwages = xtd.mtdsubjectwages
            or not pt.qtdsubjectwages = xtd.qtdsubjectwages
            or not pt.ytdsubjectwages = xtd.ytdsubjectwages)
), taxesupdate as (
	update paymenttax as pt
	   set mtdamount = tu.mtdamount,
	       qtdamount = tu.qtdamount,
	       ytdamount = tu.ytdamount,
	       mtdsubjectwages = tu.mtdsubjectwages,
	       qtdsubjectwages = tu.qtdsubjectwages,
	       ytdsubjectwages = tu.ytdsubjectwages
	  from taxestoupdate tu
	 where pt.id = tu.id 
	 returning pt.*
), --- Payment Contrib/Deduct by PayDate
pcdpaydatextd as (
    select 
        ptc.id as paymentid,
        ptc.paydate,
        ptc.employeeid, 
        pcd.typeguid,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pcd.amountee end), 0) as mtdamountee,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pcd.amountee end), 0) as qtdamountee,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pcd.amountee end), 0) as ytdamountee,
        coalesce(sum(pcd.amountee), 0) as ltdamountee,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pcd.pretaxamountee end), 0) as mtdpretaxamountee,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pcd.pretaxamountee end), 0) as qtdpretaxamountee,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pcd.pretaxamountee end), 0) as ytdpretaxamountee,
        coalesce(sum(pcd.pretaxamountee), 0) as ltdpretaxamountee,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pcd.amounter end), 0) as mtdamounter,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pcd.amounter end), 0) as qtdamounter,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pcd.amounter end), 0) as ytdamounter
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
      join paymentcontributiondeduction pcd on pcd.paymentid = p.id
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
       and ptc.contributiondeductioncontextdatetype = 'PAY_DATE'
     group by ptc.id, ptc.paydate, ptc.employeeid, pcd.typeguid
     order by ptc.employeeid, ptc.id
), pcdpaydatetoupdate as ( 
    select pcd.id, 
           xtd.mtdamountee, xtd.qtdamountee, xtd.ytdamountee, xtd.ltdamountee,
           xtd.mtdpretaxamountee, xtd.qtdpretaxamountee, xtd.ytdpretaxamountee, xtd.ltdpretaxamountee,
           xtd.mtdamounter, xtd.qtdamounter, xtd.ytdamounter 
      from paymentcontributiondeduction pcd
      join pcdpaydatextd xtd on pcd.paymentid = xtd.paymentid
       and pcd.typeguid = xtd.typeguid
       and (not pcd.mtdamountee = xtd.mtdamountee
            or not pcd.qtdamountee = xtd.qtdamountee
            or not pcd.ytdamountee = xtd.ytdamountee
            or not pcd.ltdamountee = xtd.ltdamountee
            or not pcd.mtdpretaxamountee = xtd.mtdpretaxamountee
            or not pcd.qtdpretaxamountee = xtd.qtdpretaxamountee
            or not pcd.ytdpretaxamountee = xtd.ytdpretaxamountee
            or not pcd.ltdpretaxamountee = xtd.ltdpretaxamountee
            or not pcd.mtdamounter = xtd.mtdamounter
            or not pcd.qtdamounter = xtd.qtdamounter
            or not pcd.ytdamounter = xtd.ytdamounter)
), pcdpaydateupdate as (
	update paymentcontributiondeduction as pcd
	   set mtdamountee = tu.mtdamountee,
	       qtdamountee = tu.qtdamountee,
	       ytdamountee = tu.ytdamountee,
	       ltdamountee = tu.ltdamountee,
	       mtdpretaxamountee = tu.mtdpretaxamountee,
	       qtdpretaxamountee = tu.qtdpretaxamountee,
	       ytdpretaxamountee = tu.ytdpretaxamountee,
	       ltdpretaxamountee = tu.ltdpretaxamountee,
	       mtdamounter = tu.mtdamounter,
	       qtdamounter = tu.qtdamounter,
	       ytdamounter = tu.ytdamounter
	  from pcdpaydatetoupdate tu
	 where pcd.id = tu.id 
	 returning pcd.*
), --- Payment Contrib/Deduct by ForDate
pcdfordatextd as (
    select 
        ptc.id as paymentid,
        ptc.fordate,
        ptc.employeeid, 
        pcd.typeguid,
        coalesce(sum(case 
            when extract(month from p.fordate) = extract(month from ptc.fordate)
            then pcd.amountee end), 0) as mtdamountee,
        coalesce(sum(case 
            when extract(quarter from p.fordate) = extract(quarter from ptc.fordate)
            then pcd.amountee end), 0) as qtdamountee,
        coalesce(sum(case 
            when extract(year from p.fordate) = extract(year from ptc.fordate)
            then pcd.amountee end), 0) as ytdamountee,
        coalesce(sum(pcd.amountee), 0) as ltdamountee,
        coalesce(sum(case 
            when extract(month from p.fordate) = extract(month from ptc.fordate)
            then pcd.pretaxamountee end), 0) as mtdpretaxamountee,
        coalesce(sum(case 
            when extract(quarter from p.fordate) = extract(quarter from ptc.fordate)
            then pcd.pretaxamountee end), 0) as qtdpretaxamountee,
        coalesce(sum(case 
            when extract(year from p.fordate) = extract(year from ptc.fordate)
            then pcd.pretaxamountee end), 0) as ytdpretaxamountee,
		coalesce(sum(pcd.pretaxamountee), 0) as ltdpretaxamountee,
        coalesce(sum(case 
            when extract(month from p.fordate) = extract(month from ptc.fordate)
            then pcd.amounter end), 0) as mtdamounter,
        coalesce(sum(case 
            when extract(quarter from p.fordate) = extract(quarter from ptc.fordate)
            then pcd.amounter end), 0) as qtdamounter,
        coalesce(sum(case 
            when extract(year from p.fordate) = extract(year from ptc.fordate)
            then pcd.amounter end), 0) as ytdamounter
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
      join paymentcontributiondeduction pcd on pcd.paymentid = p.id
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
       and ptc.contributiondeductioncontextdatetype = 'FOR_DATE'
     group by ptc.id, ptc.fordate, ptc.employeeid, pcd.typeguid
     order by ptc.employeeid, ptc.id
), pcdfordatetoupdate as ( 
    select pcd.id, 
           xtd.mtdamountee, xtd.qtdamountee, xtd.ytdamountee, xtd.ltdamountee,
           xtd.mtdpretaxamountee, xtd.qtdpretaxamountee, xtd.ytdpretaxamountee, xtd.ltdpretaxamountee,
           xtd.mtdamounter, xtd.qtdamounter, xtd.ytdamounter 
      from paymentcontributiondeduction pcd
      join pcdfordatextd xtd on pcd.paymentid = xtd.paymentid
       and pcd.typeguid = xtd.typeguid
       and (not pcd.mtdamountee = xtd.mtdamountee
            or not pcd.qtdamountee = xtd.qtdamountee
            or not pcd.ytdamountee = xtd.ytdamountee
            or not pcd.ltdamountee = xtd.ltdamountee
            or not pcd.mtdpretaxamountee = xtd.mtdpretaxamountee
            or not pcd.qtdpretaxamountee = xtd.qtdpretaxamountee
            or not pcd.ytdpretaxamountee = xtd.ytdpretaxamountee
            or not pcd.ltdpretaxamountee = xtd.ltdpretaxamountee
            or not pcd.mtdamounter = xtd.mtdamounter
            or not pcd.qtdamounter = xtd.qtdamounter
            or not pcd.ytdamounter = xtd.ytdamounter)
), pcdfordateupdate as (
	update paymentcontributiondeduction as pcd
	   set mtdamountee = tu.mtdamountee,
	       qtdamountee = tu.qtdamountee,
	       ytdamountee = tu.ytdamountee,
	       ltdamountee = tu.ltdamountee,
	       mtdpretaxamountee = tu.mtdpretaxamountee,
	       qtdpretaxamountee = tu.qtdpretaxamountee,
	       ytdpretaxamountee = tu.ytdpretaxamountee,
	       ltdpretaxamountee = tu.ltdpretaxamountee,
	       mtdamounter = tu.mtdamounter,
	       qtdamounter = tu.qtdamounter,
	       ytdamounter = tu.ytdamounter
	  from pcdfordatetoupdate tu
	 where pcd.id = tu.id 
	 returning pcd.*
), --- Payment Deposits
depositsxtd as (
    select 
        ptc.id as paymentid,
        ptc.paydate,
        ptc.employeeid, 
        pd.accountnumber,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pd.amount end), 0) as mtdamount,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pd.amount end), 0) as qtdamount,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pd.amount end), 0) as ytdamount
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
      join paymentdeposit pd on pd.paymentid = p.id
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and extract(year from p.paydate) = extract(year from ptc.paydate)
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
     group by ptc.id, ptc.paydate, ptc.employeeid, pd.accountnumber
     order by ptc.employeeid, ptc.id
), depositstoupdate as ( 
    select pd.id, xtd.mtdamount, xtd.qtdamount, xtd.ytdamount 
      from paymentdeposit pd
      join depositsxtd xtd on pd.paymentid = xtd.paymentid
       and pd.accountnumber = xtd.accountnumber
       and (not (select sum(mtdamount) from paymentdeposit pd1 where pd1.paymentid = xtd.paymentid and pd1.accountnumber = xtd.accountnumber) = xtd.mtdamount
            or not (select sum(qtdamount) from paymentdeposit pd1 where pd1.paymentid = xtd.paymentid and pd1.accountnumber = xtd.accountnumber) = xtd.qtdamount
            or not (select sum(ytdamount) from paymentdeposit pd1 where pd1.paymentid = xtd.paymentid and pd1.accountnumber = xtd.accountnumber) = xtd.ytdamount)
), depositsupdate as (
	update paymentdeposit as pd
	   set mtdamount = tu.mtdamount,
	       qtdamount = tu.qtdamount,
	       ytdamount = tu.ytdamount
	  from depositstoupdate tu
	 where pd.id = tu.id 
	 returning pd.*
), --- Payment Distributions
distributionsxtd as (
    select 
        ptc.id as paymentid,
        ptc.paydate,
        ptc.employeeid, 
        pd.typeinfo,
        coalesce(sum(case 
            when extract(month from p.paydate) = extract(month from ptc.paydate)
            then pd.amount end), 0) as mtdamount,
        coalesce(sum(case 
            when extract(quarter from p.paydate) = extract(quarter from ptc.paydate)
            then pd.amount end), 0) as qtdamount,
        coalesce(sum(case 
            when extract(year from p.paydate) = extract(year from ptc.paydate)
            then pd.amount end), 0) as ytdamount
      from paymentstocheck ptc    
      join payment p on p.employeeid = ptc.employeeid  
      join paymentdistribution pd on pd.paymentid = p.id
     where 1=1
       and ptc.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and p.status not in ('ERRORED', 'DELETED', 'PRE_CALCULATED')
       and extract(year from p.paydate) = extract(year from ptc.paydate)
       and coalesce(p.approvedat, 'infinity'::date) <= coalesce(ptc.approvedat, 'infinity'::date)
     group by ptc.id, ptc.paydate, ptc.employeeid, pd.typeinfo
     order by ptc.employeeid, ptc.id
), distributionstoupdate as ( 
    select pd.id, xtd.mtdamount, xtd.qtdamount, xtd.ytdamount 
      from paymentdistribution pd
      join distributionsxtd xtd on pd.paymentid = xtd.paymentid
       and pd.typeinfo = xtd.typeinfo
       and (not (select sum(mtdamount) from paymentdistribution pd1 where pd1.paymentid = xtd.paymentid and pd1.typeinfo = xtd.typeinfo) = xtd.mtdamount
            or not (select sum(qtdamount) from paymentdistribution pd1 where pd1.paymentid = xtd.paymentid and pd1.typeinfo = xtd.typeinfo) = xtd.qtdamount
            or not (select sum(ytdamount) from paymentdistribution pd1 where pd1.paymentid = xtd.paymentid and pd1.typeinfo = xtd.typeinfo) = xtd.ytdamount)
)
	update paymentdistribution as pd
	   set mtdamount = tu.mtdamount,
	       qtdamount = tu.qtdamount,
	       ytdamount = tu.ytdamount
	  from distributionstoupdate tu
	 where pd.id = tu.id;

RETURN func_employeeid;
END;
$$;
