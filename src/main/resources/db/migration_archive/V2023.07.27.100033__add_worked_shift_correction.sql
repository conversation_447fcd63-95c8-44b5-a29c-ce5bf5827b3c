CREATE TABLE IF NOT EXISTS WorkedShiftCorrection (
    id                  BIGSERIAL PRIMARY KEY,
    createdat           TIMESTA<PERSON> WITHOUT TIME ZONE NOT NULL,
    updatedat           TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    oldWorkedShiftId    BIGINT REFERENCES Shift (id) ON UPDATE CASCADE ON DELETE SET NULL,
    newWorkedShiftId    BIGINT REFERENCES Shift (id) ON UPDATE CASCADE ON DELETE SET NULL,
    createdByUserId     BIGINT NOT NULL REFERENCES AppUser (id) ON UPDATE CASCADE,

    CONSTRAINT unique_old_worked_shift_id UNIQUE (oldWorkedShiftId),
    CONSTRAINT unique_new_worked_shift_id UNIQUE (newWorkedShiftId)
);
