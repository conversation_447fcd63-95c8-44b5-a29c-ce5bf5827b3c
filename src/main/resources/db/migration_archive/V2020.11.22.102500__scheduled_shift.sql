CREATE TABLE IF NOT EXISTS schedulegroup (
    id bigserial NOT NULL,
    companyid bigint NOT NULL,
    locationid bigint NOT NULL,
    name text NOT NULL,
    archivedat TIMESTAMP without TIME zone,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT schedulegroup_pkey PRIMARY KEY (id),
    CONSTRAINT schedulegroup_company_fk FOREIGN KEY (companyid) REFERENCES company (id),
    CONSTRAINT schedulegroup_location_fk FOREIGN KEY (locationid) REFERENCES worklocation (id)
);

CREATE TABLE IF NOT EXISTS scheduledshift (
    id bigserial NOT NULL,
    companyid bigint NOT NULL,
    locationid bigint NOT NULL,
    ownerid bigint NOT NULL,
    startdatetime TIMESTAMP without TIME zone NOT NULL,
    enddatetime TIMESTAMP without TIME zone NOT NULL check (enddatetime > startdatetime),
    regularduration bigint NOT NULL,
    otduration bigint NOT NULL,
    hourlyrate numeric NOT NULL,
    otrate numeric NOT NULL,
    color text NOT NULL,
    publishstate text NOT NULL,
    publisheddatetime TIMESTAMP without TIME zone,
    employeeid bigint,
    schedulegroupid bigint,
    teamid bigint,
    title text,
    note text,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT scheduledshift_pkey PRIMARY KEY (id),
    CONSTRAINT scheduledshift_company_fk FOREIGN KEY (companyid) REFERENCES company (id),
    CONSTRAINT scheduledshift_employee_fk FOREIGN KEY (employeeid) REFERENCES employee (id),
    CONSTRAINT scheduledshift_team_fk FOREIGN KEY (teamid) REFERENCES team (id),
    CONSTRAINT scheduledshift_owner_fk FOREIGN KEY (ownerid) REFERENCES appuser (id),
    CONSTRAINT scheduledshift_location_fk FOREIGN KEY (locationid) REFERENCES worklocation (id),
    CONSTRAINT scheduledshift_schedulegroup_fk FOREIGN KEY (schedulegroupid) REFERENCES schedulegroup (id)
);

CREATE TABLE IF NOT EXISTS scheduledshiftrequest (
    id bigserial NOT NULL,
    "type" text NOT NULL,
    state text NOT NULL,
    companyid bigint NOT NULL,
    locationid bigint NOT NULL,
    offeringshiftid bigint NOT NULL,
    offeringemployeeid bigint NOT NULL,
    approvedbyuserid bigint NOT NULL,
    approveddatetime TIMESTAMP without TIME zone,
    offeringnote text,
    approvernote text,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT scheduledshiftrequest_pkey PRIMARY KEY (id),
    CONSTRAINT scheduledshiftrequest_company_fk FOREIGN KEY (companyid) REFERENCES company (id),
    CONSTRAINT scheduledshiftrequest_employee_fk FOREIGN KEY (offeringemployeeid) REFERENCES employee (id),
    CONSTRAINT scheduledshiftrequest_scheduledshift_fk FOREIGN KEY (offeringshiftid) REFERENCES scheduledshift (id),
    CONSTRAINT scheduledshiftrequest_approvinguser_fk FOREIGN KEY (approvedbyuserid) REFERENCES appuser (id),
    CONSTRAINT scheduledshiftrequest_location_fk FOREIGN KEY (locationid) REFERENCES worklocation (id)
);

CREATE TABLE IF NOT EXISTS scheduledshiftrequestrecipient (
    id bigserial NOT NULL,
    state text NOT NULL,
    scheduledshiftrequestid bigint NOT NULL,
    requestedemployeeid bigint NOT NULL,
    requestedshiftid bigint,
    requestedshifttimediff bigint,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT scheduledshiftrequestrecipient_pkey PRIMARY KEY (id),
    CONSTRAINT scheduledshiftrequestrecipient_shiftrequest_fk FOREIGN KEY (scheduledshiftrequestid) REFERENCES scheduledshiftrequest (id),
    CONSTRAINT scheduledshiftrequestrecipient_employee_fk FOREIGN KEY (requestedemployeeid) REFERENCES employee (id),
    CONSTRAINT scheduledshiftrequestrecipient_scheduledshift_fk FOREIGN KEY (requestedshiftid) REFERENCES scheduledshift (id)
);

CREATE TABLE IF NOT EXISTS scheduledshiftconflict (
    id bigserial NOT NULL,
    "type" text NOT NULL,
    state text NOT NULL,
    companyid bigint NOT NULL,
    locationid bigint NOT NULL,
    approvedbyuserid bigint NOT NULL,
    approveddatetime TIMESTAMP without TIME zone,
    scheduledshiftid bigint,
    conflictingshiftid bigint,
    scheduledshiftrequestid bigint,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT scheduledshiftconflict_pkey PRIMARY KEY (id),
    CONSTRAINT scheduledshiftconflict_company_fk FOREIGN KEY (companyid) REFERENCES company (id),
    CONSTRAINT scheduledshiftconflict_conflictingshift_fk FOREIGN KEY (conflictingshiftid) REFERENCES scheduledshift (id),
    CONSTRAINT scheduledshiftconflict_scheduledshift_fk FOREIGN KEY (scheduledshiftid) REFERENCES scheduledshift (id),
    CONSTRAINT scheduledshiftconflict_scheduledshiftrequest_fk FOREIGN KEY (scheduledshiftrequestid) REFERENCES scheduledshiftrequest (id),
    CONSTRAINT scheduledshiftconflict_approvinguser_fk FOREIGN KEY (approvedbyuserid) REFERENCES appuser (id),
    CONSTRAINT scheduledshiftconflict_location_fk FOREIGN KEY (locationid) REFERENCES worklocation (id),
    CONSTRAINT scheduledshiftconflict_ref_chk CHECK ((COALESCE(scheduledshiftid, scheduledshiftrequestid) IS NOT NULL AND (scheduledshiftrequestid is null OR scheduledshiftid is null)))
);

CREATE TABLE IF NOT EXISTS scheduledshifttemplate (
    id bigserial NOT NULL,
    companyid bigint NOT NULL,
    locationid bigint NOT NULL,
    starttime TIME without TIME zone NOT NULL,
    endtime TIME without TIME zone NOT NULL check (endtime > starttime),
    color text NOT NULL,
    title text NOT NULL,
    note text,
    createdat TIMESTAMP without TIME zone NOT NULL,
    updatedat TIMESTAMP without TIME zone NOT NULL,
    CONSTRAINT scheduledshifttemplate_pkey PRIMARY KEY (id),
    CONSTRAINT scheduledshifttemplate_company_fk FOREIGN KEY (companyid) REFERENCES company (id),
    CONSTRAINT scheduledshifttemplate_location_fk FOREIGN KEY (locationid) REFERENCES worklocation (id)
);

ALTER TABLE company
    ADD COLUMN if not exists schedulehourlyemployees boolean,
    ADD COLUMN if not exists schedulesalaryemployees boolean,
    ADD COLUMN if not exists schedulecontractors boolean;
update company set schedulehourlyemployees=false, schedulesalaryemployees=false, schedulecontractors=false where schedulecontractors is null;
ALTER TABLE company
    ALTER COLUMN schedulehourlyemployees SET NOT NULL,
    ALTER COLUMN schedulesalaryemployees SET NOT NULL,
    ALTER COLUMN schedulecontractors SET NOT NULL;
