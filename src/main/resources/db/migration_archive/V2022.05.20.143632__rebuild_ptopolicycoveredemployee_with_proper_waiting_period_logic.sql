DROP VIEW IF EXISTS ptopolicycoveredemployee;

CREATE OR REPLACE VIEW ptopolicycoveredemployee
AS

  SELECT MD5( VARIADIC ARRAY [ep.id, pp.id] :: Text )                                     AS id,
         e.companyid,
         e.id                                                                             AS employeeid,
         ep.id                                                                            AS positionid,
         pp.id                                                                            AS policyid,
         pp.type                                                                          AS policytype,
         -- Add all applicable tenure bonuses to either the workers overridden values, or the base policy values
         -- or assign effectiveptoaccrualrate to 0:0 if still in policy waiting period
         COALESCE(
                 CASE
                   WHEN ( DATE_PART( 'DAYS', NOW( ) ) - DATE_PART( 'DAYS', e.startdate ) ):: INTEGER < pp.waitingperiod
                     THEN '0.0:0.0'
                 END,
                 CASE
                   WHEN SUM( pt.accrualrateincrease ) IS NULL THEN e.overrideptoaccrualrate
                   ELSE SPLIT_PART( e.overrideptoaccrualrate, ':', 1 ) || ':' ||
                        CAST( SPLIT_PART( e.overrideptoaccrualrate, ':', 2 ) AS NUMERIC ) +
                        SUM( pt.accrualrateincrease )
                 END,
                 CASE
                   WHEN SUM( pt.accrualrateincrease ) IS NULL THEN pp.defaultaccrualrate
                   ELSE
                         SPLIT_PART( pp.defaultaccrualrate, ':', 1 ) || ':' ||
                         CAST( SPLIT_PART( pp.defaultaccrualrate, ':', 2 ) AS NUMERIC ) + SUM( pt.accrualrateincrease )
                 END
           )                                                                              AS effectiveptoaccrualrate,
         DATE( GREATEST( pp.startdate, ( ep.startdate +
                                         MAKE_INTERVAL( 0, 0, 0, pp.waitingperiod ) ) ) ) AS effectiveaccrualdate,
         GREATEST( pp.startdate, ep.startdate )                                           AS startdate,
         LEAST( pp.enddate, ep.enddate )                                                  AS enddate,
         -- Add all tenure bonuses to either the workers overridden values, or the base policy values
         COALESCE(
                 CASE
                   WHEN SUM( pt.maxhoursincrease ) IS NULL THEN e.overrideptomaxhours
                   ELSE e.overrideptomaxhours + SUM( pt.maxhoursincrease )
                 END,
                 CASE
                   WHEN SUM( pt.maxhoursincrease ) IS NULL THEN pp.defaultptomaxhours
                   ELSE pp.defaultptomaxhours + SUM( pt.maxhoursincrease )
                 END
           )                                                                              AS effectiveptomaxhours,
         pp.accrualpolicy,
         ( DATE_PART( 'YEAR', NOW( ) ) - DATE_PART( 'YEAR', e.startdate ) ):: INTEGER     AS yearsemployed,
         pp.defaultaccrualrate                                                            AS baseaccrualrate,
         pp.defaultptomaxhours                                                            AS basemaxhours,
         e.overrideptoaccrualrate                                                         AS individualaccrualrate,
         e.overrideptomaxhours                                                            AS individualmaxhours
  FROM employee e
         JOIN      company c ON e.companyid = c.id
         JOIN      ptopolicy pp ON e.companyid = pp.companyid
         JOIN      employeeposition ep ON e.id = ep.employeeid
                     -- Join on all the PTO Tenure Bonuses that they have reached
         LEFT JOIN ptotenurerule pt ON pt.id IN ( SELECT id
                                                  FROM ptotenurerule inner_pt
                                                  WHERE inner_pt.ptopolicyid = pp.id
                                                    AND DATE_PART( 'YEAR', NOW( ) ) -
                                                        DATE_PART( 'YEAR', e.startdate ) >= inner_pt.years
                                                  ORDER BY inner_pt.years DESC )
  WHERE "overlaps"( timezone( c.timezone, pp.startdate::timestamp WITH TIME ZONE ),
                    timezone( c.timezone, COALESCE( pp.enddate, 'infinity'::date )::timestamp WITH TIME ZONE ),
                    timezone( c.timezone, ep.startdate::timestamp WITH TIME ZONE ),
                    timezone( c.timezone, COALESCE( ep.enddate, 'infinity'::date )::timestamp WITH TIME ZONE ) )
    AND e.employmenttype = 'EMPLOYEE'::text
    AND ( pp.salaryeligible = TRUE AND ep.wagetype = 'SALARY'::text OR
          pp.fulltimehourlyeligible = TRUE AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND
          ep.wagetype = 'HOURLY'::text OR pp.parttimehourlyeligible = TRUE AND ep.wagetype = 'HOURLY'::text )
  GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate, ep.id, ep.startdate, ep.enddate;
