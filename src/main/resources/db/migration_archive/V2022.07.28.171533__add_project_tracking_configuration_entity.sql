CREATE TABLE IF NOT EXISTS companyprojecttrackingconfiguration
(
  companyid BIGINT NOT NULL,
  createdat TIMES<PERSON>MP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedat TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  projectTrackingEnabled BOOLEAN NOT NULL DEFAULT TRUE,
  hourlyEmployeesAllowed BOOLEAN NOT NULL DEFAULT FALSE,
  salaryEmployeesAllowed BOOLEAN NOT NULL DEFAULT FALSE,
  contractorsAllowed BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT companyprojecttrackingconfiguration_pk PRIMARY KEY (companyid),
  CONSTRAINT companyprojecttrackingconfiguration_company_fk FOREIGN KEY (companyid) REFERENCES company (id)
);

INSERT INTO companyprojecttrackingconfiguration
	(companyid,
	 projectTrackingEnabled,
	 hourlyEmployeesAllowed,
	 salaryEmployeesAllowed,
	 contractorsAllowed)
SELECT
	id,
	projectTrackingEnabled,
	projectTrackingEnabledHourly,
  projectTrackingEnabledSalary,
  projectTrackingEnabledContractor
FROM company;
