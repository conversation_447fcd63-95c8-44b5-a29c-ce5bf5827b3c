alter table partner add column if not exists defaultpartner boolean not null default false;

CREATE UNIQUE INDEX partner_defaultpartner_true_ix
    ON partner (defaultpartner) WHERE defaultpartner = true;


insert into partner (createdat, updatedat, legalname, startdate, ein, supportemailaddress, displayname, primarylogolocation, darklogolocation, lightlogolocation, iconlocation, faviconlocation, defaultpartner)
	select current_date, current_date, 'Everee Inc.', '2022-01-01', '*********', '<EMAIL>', 'Everee', 'partners/default_partner/images/primary_logo.svg', 'partners/default_partner/images/dark_logo.png', 'partners/default_partner/images/light_logo.png', 'partners/default_partner/images/icon.png', 'partners/default_partner/images/favicon.ico', true
WHERE NOT EXISTS (select id FROM partner where defaultpartner = true);


update company set partnerid = (select p.id from partner p where p.defaultpartner = true ) where partnerid is null;

ALTER TABLE company ALTER COLUMN partnerid SET NOT NULL;
