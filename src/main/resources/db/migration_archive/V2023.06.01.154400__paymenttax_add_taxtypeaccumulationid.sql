ALTER TABLE paymenttax
    add column if not exists taxtypeaccumulationid bigint REFERENCES taxtypeaccumulation (id) ON DELETE SET NULL;

CREATE INDEX IF NOT EXISTS paymenttax_tta_idx ON paymenttax (taxtypeaccumulationid);

-- Backfill.  Does not need to be run as part of the release since column is nullable, but should run in prod
-- STEP 1: backfill PAY_CARD and PTPC transactions
--with companydistributionIdsForPaymentIds as(
--    select
--        min(pd.companydistributionid) companydistributionid, pt.jurisdictiontaxid, pd.paymentid
--    from paymentdistribution pd
--    JOIN payment p on p.id=pd.paymentid
--    join paymenttax pt on pt.paymentid=p.id
--    WHERE
--        pd.status in ('DISTRIBUTED', 'SUBMITTED')
--        and pd.type in ('PAY_CARD','PTPC')
--        and p.status in ('PAID', 'IMPORTED', 'SUBMITTED')
--        and pd.amount <> 0
--        and pt.taxtypeaccumulationid is null
--    group by pd.paymentid, pt.jurisdictiontaxid
--    limit 10000
--), lockboxitems as (
--    select
--        ids.*, cdl.id as companydistributionlockboxitemid
--    from companydistributionlockboxitem cdl
--    join companydistributionIdsForPaymentIds ids on ids.companydistributionid=cdl.companydistributionid
--), ttas as (
--select
--    pt.id as paymenttaxid,
--    tta.id as taxtypeaccumulationid,
--    tta.companydistributionlockboxitemid,
--    lb.companydistributionid,
--    lb.paymentid,
--    lb.jurisdictiontaxid
-- from taxtypeaccumulation tta
-- join lockboxitems lb
--    on lb.companydistributionlockboxitemid=tta.companydistributionlockboxitemid
--    and lb.jurisdictiontaxid=tta.jurisdictiontaxid
-- join paymenttax pt
--    on pt.jurisdictiontaxid=lb.jurisdictiontaxid
--    and lb.paymentid=pt.paymentid
--) -- select * from ttas
--update paymenttax as pt
--  set taxtypeaccumulationid=ttas.taxtypeaccumulationid
--  from ttas
--  where ttas.paymenttaxid=pt.id

-- STEP 2: Backfill ACH transactions
--with achfileids as (
--  select achfileid from taxtypeaccumulation where achfileid is not null group by achfileid
--), paymenttaxes as (
--    select p.achfileid, fr.achfileid as fundingachfileid, p.paydate,
--           pt.jurisdictiontaxid, pt.amount,
--      case when p.fundingtype='PRE_FUNDED' then 'PRE'
--           when p.fundingtype='OPTIMISTIC_FUNDED' then 'REGULAR'
--           else 'PYW' end as ttatype,
--           pt.id as paymenttaxid
--      from payment p
--      join paymenttax pt on p.id = pt.paymentid
--      left join achfilerecord fr on fr.id = p.fundingachfilerecordid
--      join achfileids a on a.achfileid=p.achfileid
--     where 1 = 1
--     and p.status in ('PAID', 'IMPORTED')
--        and pt.taxtypeaccumulationid is null
--    limit 20000
--) --select  * from paymenttaxes
--, ttastopaymenttaxids as (
--    select tta.id as taxtypeaccumulationid, tta.jurisdictiontaxid, pr.paymenttaxid
--      from taxtypeaccumulation tta
--      join paymenttaxes pr on pr.paydate = tta.fordate
--       and pr.ttatype = tta.paymenttype
--       and pr.jurisdictiontaxid = tta.jurisdictiontaxid
--       and pr.achfileid = tta.achfileid
--       and ((pr.fundingachfileid is NULL AND tta.fundingachfileid is null) OR (pr.fundingachfileid = tta.fundingachfileid))
--) --select * from ttastopaymenttaxids
--update paymenttax as pt
--  set taxtypeaccumulationid=ttas.taxtypeaccumulationid
--  from ttastopaymenttaxids ttas
--  where ttas.paymenttaxid=pt.id

