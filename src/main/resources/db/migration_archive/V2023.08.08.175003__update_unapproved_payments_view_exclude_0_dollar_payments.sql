-- Added this criteria to the view
--      AND ( coalesce(c.autoApproveZeroAmountPayroll, false) = false OR payment.grossearnings <>0.00)
create or replace view unapprovedpaymentsbycompany as
 SELECT payment.companyid,
    count(DISTINCT payment.employeeid) AS numemployees,
    COALESCE(sum(payment.grossearnings), (0)::numeric) AS grossearnings
   FROM (payment
     JOIN company c ON ((c.id = payment.companyid)))
  WHERE ((
    payment.status <> ALL (ARRAY[
        'APPROVED'::text,
        'SUBMITTED'::text,
        'PAID'::text,
        'IMPORTED'::text,
        'DELETED'::text,
        'PREPARED_FOR_FUNDING_REQUEST'::text,
        'FUNDING_REQUESTED'::text,
        'FUNDED'::text
        ]))
    AND (
        (payment.errortype IS NULL)
        OR
        ((payment.errortype)::text <> ALL (ARRAY[
            'MISSING_OR_INVALID_ADDRESS'::text,
            'MISSING_BANK_ACCOUNT'::text,
            'MISSING_WITHHOLDINGS'::text,
            'MISSING_EMPLOYEE_POSITION'::text,
            'MISSING_UNEMPLOYMENT_RATES'::text,
            'INVALID_BANK_ACCOUNT'::text,
            'MISSING_TAX_PAYER_IDENTIFIER'::text
            ]))
    )
    AND (payment.fordate <= CURRENT_DATE)
    AND ((c.enddate IS NULL) OR (c.enddate >= CURRENT_DATE)))
    AND (payment.scheduleapprovedbyuserid is null OR payment.scheduledate < CURRENT_DATE)
    AND ( coalesce(c.autoApproveZeroAmountPayroll, false) = false OR payment.grossearnings <>0.00)
  GROUP BY payment.companyid;
