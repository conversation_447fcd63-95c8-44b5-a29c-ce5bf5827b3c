 CREATE OR REPLACE VIEW employeeaccountbalance AS
 SELECT et.companyid,
    c.displayname AS companyname,
    et.employeeid,
    u.lastname,
    u.firstname,
    u.middlename,
    et.employeecontributiondeductionguid,
    ecd.type AS employeecontributiondeductiontype,
    ecd.name AS employeecontributiondeductionname,
    sum(
        CASE
            WHEN et.paymentfinalized THEN et.assetamount
            ELSE 0.00
        END) AS assetamount,
    sum(
        CASE
            WHEN et.paymentfinalized THEN et.liabilityamount
            ELSE 0.00
        END) AS liabilityamount,
    sum(
        CASE
            WHEN et.paymentfinalized THEN et.pretaxliabilityamount
            ELSE 0.00
        END) AS pretaxliabilityamount,
    sum(
        CASE
            WHEN (et.paymentfinalized AND (et.fundingtype = 'LIABILITY'::text)) THEN et.liabilityamount
            WHEN (et.paymentfinalized AND (et.fundingtype = 'ASSET'::text)) THEN et.assetamount
            ELSE 0.00
        END) AS fundingamount,
    (sum(
        CASE
            WHEN et.paymentfinalized THEN et.liabilityamount
            ELSE 0.00
        END) - sum(
        CASE
            WHEN et.paymentfinalized THEN et.assetamount
            ELSE 0.00
        END)) AS balance,
    (sum(
        CASE
            WHEN et.paymentfinalized THEN et.liabilityamount
            ELSE 0.00
        END) - sum(
        CASE
            WHEN et.paymentfinalized THEN et.pretaxliabilityamount
            ELSE 0.00
        END)) AS pretaxbalance,
    sum(
        CASE
            WHEN et.paymentfinalized THEN 0.00
            ELSE et.assetamount
        END) AS pendingassetamount,
    sum(
        CASE
            WHEN et.paymentfinalized THEN 0.00
            ELSE et.liabilityamount
        END) AS pendingliabilityamount,
    (sum(
        CASE
            WHEN et.paymentfinalized THEN 0.00
            ELSE et.liabilityamount
        END) - sum(
        CASE
            WHEN et.paymentfinalized THEN 0.00
            ELSE et.assetamount
        END)) AS pendingbalance,
    sum(
        CASE
            WHEN (et.paymentfinalized OR (et.fundingtype = 'NONE'::text)) THEN 0.00
            WHEN (et.fundingtype = 'LIABILITY'::text) THEN et.liabilityamount
            ELSE et.assetamount
        END) AS pendingfundingamount
   FROM ((((( SELECT et_1.id,
            et_1.companyid,
            et_1.employeeid,
            et_1.employeecontributiondeductionguid,
            et_1.paymentid,
            et_1.paymentcontributiondeductionid,
            et_1.liabilityamount,
            et_1.assetamount,
            et_1.description,
            et_1.nonpaymenttransactiondate,
            et_1.createdat,
            et_1.updatedat,
            et_1.employeecontributiondeductiontype,
            et_1.fundingtype,
            et_1.pretaxliabilityamount,
            et_1.achfileid,
            ((p.status IS NULL) OR (p.status = ANY (ARRAY['APPROVED'::text, 'SUBMITTED'::text, 'PAID'::text, 'IMPORTED'::text, 'PREPARED_FOR_FUNDING_REQUEST'::text, 'FUNDING_REQUESTED'::text, 'FUNDED'::text]))) AS paymentfinalized
           FROM (employeeaccounttransaction et_1
             LEFT JOIN payment p ON ((p.id = et_1.paymentid)))
          WHERE ((et_1.paymentid IS NULL) OR (p.status <> 'DELETED'::text))) et
     JOIN company c ON ((c.id = et.companyid)))
     JOIN employee e ON ((e.id = et.employeeid)))
     JOIN appuser u ON ((u.id = e.userid)))
     LEFT JOIN ( SELECT employeecontributiondeduction.employeeid,
            employeecontributiondeduction.typeguid,
            employeecontributiondeduction.type,
            employeecontributiondeduction.name
           FROM employeecontributiondeduction
          GROUP BY employeecontributiondeduction.employeeid, employeecontributiondeduction.typeguid, employeecontributiondeduction.type, employeecontributiondeduction.name) ecd
          ON ((ecd.typeguid = et.employeecontributiondeductionguid) AND ecd.employeeid=e.id))
  GROUP BY et.companyid, c.displayname, u.firstname, u.lastname, u.middlename, et.employeeid, et.employeecontributiondeductionguid, ecd.type, ecd.name;


ALTER TABLE paymentcontributiondeduction add column if not exists carryoverpretaxbalanceee numeric;
