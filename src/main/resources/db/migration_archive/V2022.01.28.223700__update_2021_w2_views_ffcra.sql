create or replace view w2data2021 as
  SELECT to_hex(VARIADIC ARRAY[e.userid,
        CASE
            WHEN e.statutoryemployee THEN 1
            ELSE 0
        END::bigint, p.companyid, date_part('year'::text, p.paydate)::bigint]) AS id,
    e.userid,
    p.companyid,
    date_part('year'::text, p.paydate) AS year,
    ''::text AS controlnumber,
    sum(p.grossearnings) AS grossearnings,
    sum(ptfit.subjectwages) AS fitwages,
    sum(ptfit.amount) AS fit,
    COALESCE(sum(ptfica.subjectwages), 0::numeric) - COALESCE(sum(petips.amount), 0::numeric) AS ficawages,
    sum(ptfica.amount) AS fica,
    sum(ptmedi.subjectwages) AS mediwages,
    COALESCE(sum(ptmedi.amount), 0::numeric) + COALESCE(sum(ptmedi2.amount), 0::numeric) AS medi,
    COALESCE(sum(petips.amount), 0::numeric) AS sstips,
    COALESCE(sum(peffcrasick.amount), 0::numeric) AS ffcrasick,
    0::numeric AS ffcracaregiver,
    COALESCE(sum(peffcrafmla.amount), 0::numeric) AS ffcrafmla,
    0::numeric AS alloctips,
    COALESCE(sum(pcddra.amountee) + sum(pcddra.amounter), 0::numeric) AS dra,
    0::numeric AS nqplans,
    sum(pcd401k.amountee) AS erisa401k,
    sum(pcd403b.amountee) AS erisa403b,
    COALESCE(sum(pcdhsa.amountee) + sum(pcdhsa.amounter), 0::numeric) AS hsa,
    sum(pcdroth.amountee) AS roth401k,
    COALESCE(sum(pcdmedical.amountee) + sum(pcdmedical.amounter), 0::numeric) + COALESCE(sum(pcddental.amountee) + sum(pcddental.amounter), 0::numeric) + COALESCE(sum(pcdvision.amountee) + sum(pcdvision.amounter), 0::numeric) + COALESCE(sum(pcdprescription.amountee) + sum(pcdprescription.amounter), 0::numeric) + COALESCE(sum(pcdhra.amountee) + sum(pcdhra.amounter), 0::numeric) + COALESCE(sum(pcdhopital.amountee) + sum(pcdhopital.amounter), 0::numeric) AS healthcoverage,
        CASE
            WHEN e.statutoryemployee = true THEN 'On'::text
            ELSE 'Off'::text
        END AS statutoryemployee,
        CASE
            WHEN (COALESCE(sum(pcd401k.amountee) + sum(pcd401k.amounter), 0::numeric) + COALESCE(sum(pcd403b.amountee) + sum(pcd403b.amounter), 0::numeric) + COALESCE(sum(pcdroth.amountee) + sum(pcdroth.amounter), 0::numeric)) > 0::numeric THEN 'On'::text
            ELSE 'Off'::text
        END AS retirementplan,
    'Off'::text AS thirdpartysickpay
   FROM payment p
     JOIN employee e ON p.employeeid = e.id AND e.employmenttype = 'EMPLOYEE'::text
     LEFT JOIN paymenttax ptfit ON ptfit.paymentid = p.id AND ptfit.type = 'FIT'::text
     LEFT JOIN paymenttax ptfica ON ptfica.paymentid = p.id AND ptfica.type = 'FICA'::text
     LEFT JOIN paymenttax ptmedi ON ptmedi.paymentid = p.id AND ptmedi.type = 'MEDI'::text
     LEFT JOIN paymenttax ptmedi2 ON ptmedi2.paymentid = p.id AND ptmedi2.type = 'MEDI2'::text
     LEFT JOIN paymentcontributiondeduction pcddra ON pcddra.paymentid = p.id AND pcddra.type = 'DRA'::text
     LEFT JOIN paymentearning petips ON petips.paymentid = p.id AND petips.type = 'TIPS'::text AND petips.amount > 0
     LEFT JOIN paymentearning peffcrasick ON peffcrasick.paymentid = p.id AND peffcrasick.type = 'EMERGENCY_FFCRA_SICK'::text AND peffcrasick.amount > 0
     LEFT JOIN paymentearning peffcrafmla ON peffcrafmla.paymentid = p.id AND peffcrafmla.type = 'EMERGENCY_FFCRA_FMLA'::text AND peffcrafmla.amount > 0
     LEFT JOIN paymentcontributiondeduction pcd401k ON pcd401k.paymentid = p.id AND pcd401k.type = 'ERISA_401K'::text
     LEFT JOIN paymentcontributiondeduction pcd403b ON pcd403b.paymentid = p.id AND pcd403b.type = 'ERISA_403B'::text
     LEFT JOIN paymentcontributiondeduction pcdhsa ON pcdhsa.paymentid = p.id AND pcdhsa.type = 'HSA'::text
     LEFT JOIN paymentcontributiondeduction pcdroth ON pcdroth.paymentid = p.id AND pcdroth.type = 'ROTH_401K'::text
     LEFT JOIN paymentcontributiondeduction pcdmedical ON pcdmedical.paymentid = p.id AND pcdmedical.type = 'MEDICAL_INSURANCE'::text
     LEFT JOIN paymentcontributiondeduction pcddental ON pcddental.paymentid = p.id AND pcddental.type = 'DENTAL_INSURANCE'::text
     LEFT JOIN paymentcontributiondeduction pcdvision ON pcdvision.paymentid = p.id AND pcdvision.type = 'VISION_INSURANCE'::text
     LEFT JOIN paymentcontributiondeduction pcdprescription ON pcdprescription.paymentid = p.id AND pcdprescription.type = 'PRESCRIPTION_PLAN'::text
     LEFT JOIN paymentcontributiondeduction pcdhra ON pcdhra.paymentid = p.id AND pcdhra.type = 'HRA'::text
     LEFT JOIN paymentcontributiondeduction pcdhopital ON pcdhopital.paymentid = p.id AND pcdhopital.type = 'HOSPITAL_INDEMNITY_INSURANCE'::text
  WHERE (p.status = ANY (ARRAY['PAID'::text, 'IMPORTED'::text])) AND p.paydate >= '2021-01-01'::date AND p.paydate <= '2021-12-31'::date
  GROUP BY e.userid, e.statutoryemployee, p.companyid, (date_part('year'::text, p.paydate));
