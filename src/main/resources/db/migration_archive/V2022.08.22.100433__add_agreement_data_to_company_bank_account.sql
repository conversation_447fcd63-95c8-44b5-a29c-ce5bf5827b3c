
ALTER TABLE ONLY companybankaccount
 ADD COLUMN IF NOT EXISTS locked BO<PERSON><PERSON>N NOT NULL DEFAULT FALSE,
 ADD COLUMN IF NOT EXISTS authorizationAgreementSignature text,
 ADD COLUMN IF NOT EXISTS authorizationAgreementSignedByUserId bigint REFERENCES appuser (id),
 ADD COLUMN IF NOT EXISTS authorizationAgreementSignedAt TIMESTAMP WITH TIME ZONE,
 DROP CONSTRAINT IF EXISTS companybankaccount_company_uk,
 ADD FOREIGN KEY (plaiditemid) REFERENCES plaiditem (id) ON UPDATE CASCADE ON DELETE SET NULL;
