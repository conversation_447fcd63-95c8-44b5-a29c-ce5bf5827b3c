CREATE TABLE IF NOT EXISTS galileoaccount (
    id                      BIGSERIAL PRIMARY KEY,
    createdat               TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
    updatedat               TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
    userid                  BIGINT NOT NULL REFERENCES appuser (id) ON DELETE CASCADE,
    pmtrefno                TEXT NOT NULL,
    galileoaccountnumber    TEXT,
    productid               TEXT,
    cardid                  TEXT,
    cardnumber              TEXT,
    expirydate              TEXT,
    cardsecuritycode        TEXT
);
