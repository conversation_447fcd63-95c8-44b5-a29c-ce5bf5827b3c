-- adds companyStartDate and companyEndDate
CREATE OR REPLACE VIEW employmentstatuschange AS
 SELECT to_hex(VARIADIC ARRAY[concat(e.id::text, ':', e.startdate, ':HIRE')]) AS id,
    e.id AS employeeid,
    concat_ws(' '::text, u.firstname, u.middlename, u.lastname) AS employeefullname,
    u.taxpayeridentifier,
    u.dateofbirth,
    u.email,
    a.line1,
    a.line2,
    a.city,
    a.state,
    a.postalcode,
    c.id AS companyid,
    c.displayname AS companyname,
    e.employmenttype,
    e.startdate AS statuschangedate,
    'HIRE'::text AS statuschangetype,
    e.recordedhireat AS statuschangerecordedat,
    COALESCE(wl.state, a.state) AS workstate,
    c.startdate AS companyStartDate,
    c.enddate AS companyEndDate
   FROM employee e
     JOIN appuser u ON u.id = e.userid
     LEFT JOIN useraddress a ON u.id = a.userid AND e.startdate >= a.startdate AND e.startdate <= COALESCE(a.enddate, 'infinity'::date)
     LEFT JOIN workerlegalworklocation wlw ON wlw.employeeid = e.id AND e.startdate >= wlw.startdate AND e.startdate <= COALESCE(wlw.enddate, 'infinity'::date)
     LEFT JOIN worklocation wl ON wl.id = wlw.worklocationid
     JOIN company c ON c.id = e.companyid
  WHERE e.startdate >= c.startdate
UNION
 SELECT to_hex(VARIADIC ARRAY[concat(e.id::text, ':', e.enddate, ':SEPARATION')]) AS id,
    e.id AS employeeid,
    concat_ws(' '::text, u.firstname, u.middlename, u.lastname) AS employeefullname,
    u.taxpayeridentifier,
    u.dateofbirth,
    u.email,
    a.line1,
    a.line2,
    a.city,
    a.state,
    a.postalcode,
    c.id AS companyid,
    c.displayname AS companyname,
    e.employmenttype,
    e.enddate AS statuschangedate,
    'SEPARATION'::text AS statuschangetype,
    e.recordedseparationat AS statuschangerecordedat,
    COALESCE(wl.state, a.state) AS workstate,
    c.startdate AS companyStartDate,
    c.enddate AS companyEndDate
   FROM employee e
     JOIN appuser u ON u.id = e.userid
     LEFT JOIN useraddress a ON u.id = a.userid AND e.enddate >= a.startdate AND e.enddate <= COALESCE(a.enddate, 'infinity'::date)
     LEFT JOIN workerlegalworklocation wlw ON wlw.employeeid = e.id AND e.enddate >= wlw.startdate AND e.enddate <= COALESCE(wlw.enddate, 'infinity'::date)
     LEFT JOIN worklocation wl ON wl.id = wlw.worklocationid
     JOIN company c ON c.id = e.companyid
  WHERE e.enddate >= c.startdate;
