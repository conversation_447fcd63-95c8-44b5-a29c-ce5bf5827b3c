--------------------
-- DDL
--------------------
ALTER TABLE payment ADD COLUMN fundingachfilerecordid bigint REFERENCES achfilerecord (id) ON DELETE SET NULL;
ALTER TABLE payment ADD COLUMN fundingtype text;

CREATE VIEW fundingtransaction AS
SELECT
	record.id,
	file.companyid,
	batch.targetsettlementdate,
	payment.fundingtype as type,
	record.amount as amount,
	count(payment.*) as paymentcount,
	sum(payment.grossearnings) as totalgrossearnings,
	sum(payment.netearnings) as totalnetearnings,
	sum(payment.totaltaxesee) as totaltaxesee,
	sum(payment.totaltaxeser) as totaltaxeser,
	sum(payment.totalcontributionser) as totalcontributionser,
	sum(payment.totalexpense) as totalexpense,
	sum(payment.totaltaxfunding) as totaltaxfunding,
	sum(payment.totalfunding) as totalfunding
FROM achfilerecord record
INNER JOIN payment payment ON payment.fundingachfilerecordid = record.id
INNER JOIN achfilebatch batch ON record.achfilebatchid = batch.id
INNER JOIN achfile file ON record.achfileid = file.id
WHERE
	record.achfilerecordtype = 'FUNDING_DEBIT'
	AND file.status = 'PAID'
GROUP BY
	record.id,
	file.companyid,
	batch.targetsettlementdate,
	payment.fundingtype;

CREATE VIEW receivablepayment AS
WITH
	earliestfundingrecord AS (
		SELECT
			file.companyid,
			min(file.createdat) as fundingtimestamp
		FROM achfilerecord record
		INNER JOIN achfile file ON record.achfileid = file.id
		WHERE record.achfilerecordtype = 'FUNDING_DEBIT'
		GROUP BY file.companyid
	)
SELECT
	p.id,
	p.companyid,
	p.fordate,
	p.approvedat,
	p.approvedbyuserid,
	p.status,
	p.payperiodid,
	p.employeeid,
	p.pretaxdeductions,
	p.posttaxdeductions,
	p.deferredcompensation,
	p.grossearnings,
	p.netearnings,
	p.totaltaxesee,
	p.totaltaxeser,
	p.totalexpense,
	p.totalcontributionser,
	p.totaltaxfunding,
	p.totalfunding,
	p.payperiodtype,
	p.paydate,
	p.employeepositionid,
	p.type,
	p.companypayperiodid,
	p.fundingachfilerecordid,
	p.fundingtype,
	pp.startdate as payperiodstartdate,
	pp.enddate as payperiodenddate,
	u.firstname,
	u.middlename,
	u.lastname,
	e.employmenttype,
	ep.wagetype,
	ep.payrate,
	t.name as teamname
FROM payment p
INNER JOIN earliestfundingrecord ON earliestfundingrecord.companyid = p.companyid
INNER JOIN payperiod pp ON pp.id = p.payperiodid
INNER JOIN employee e ON e.id = p.employeeid
INNER JOIN employeeposition ep on ep.id = p.employeepositionid
INNER JOIN appuser u ON u.id = e.userid
INNER JOIN team t ON t.id = e.teamid
WHERE
	p.status = 'PAID'
	AND p.grossearnings > 0
	AND p.approvedat > earliestfundingrecord.fundingtimestamp
	AND p.fundingachfilerecordid IS NULL;

CREATE VIEW receivablepaymenttotalbycompany AS
SELECT
	c.id as companyid,
	count(p.*) as paymentcount,
	sum(p.grossearnings) as totalgrossearnings,
	sum(p.netearnings) as totalnetearnings,
	sum(p.totaltaxesee) as totaltaxesee,
	sum(p.totaltaxeser) as totaltaxeser,
	sum(p.totalcontributionser) as totalcontributionser,
	sum(p.totalexpense) as totalexpense,
	sum(p.totaltaxfunding) as totaltaxfunding,
	sum(p.totalfunding) as totalfunding
FROM company c
LEFT JOIN receivablepayment p on p.companyid = c.id
GROUP BY
  c.id,
  p.companyid;

--------------------
-- DATA BACKFILL
--------------------
-- POSTFUNDED PAYMENTS
UPDATE payment SET
	fundingtype = 'POST_FUNDED'
WHERE
	status IN ('CALCULATED', 'APPROVED')
	AND payperiodtype IN ('DAILY', 'WEEKLY', 'AD_HOC', 'PAY_ON_DEMAND');

WITH
	earliestfundingrecord AS (
		SELECT
			file.companyid,
			min(file.createdat) as fundingtimestamp
		FROM achfilerecord record
		INNER JOIN achfile file ON record.achfileid = file.id
		WHERE record.achfilerecordtype = 'FUNDING_DEBIT'
		GROUP BY file.companyid
	)
UPDATE payment SET
	fundingachfilerecordid = fundingrecord.id,
	fundingtype = 'POST_FUNDED'
FROM
	achfilerecord paymentrecord,
	achfilebatch paymentbatch,
	achfilerecord fundingrecord,
	earliestfundingrecord
WHERE
	paymentrecord.paymentid = payment.id
	AND earliestfundingrecord.companyid = payment.companyid
	AND paymentbatch.id = paymentrecord.achfilebatchid
	AND paymentbatch.achfilebatchtype = 'PYW_PAYMENT'
	AND fundingrecord.id = (
		SELECT record.id
		FROM achfilerecord record
		INNER JOIN achfile file on file.id = record.achfileid
		INNER JOIN achfilebatch batch on record.achfilebatchid = batch.id
		WHERE
			file.createdat > payment.approvedat
			AND file.companyid = payment.companyid
			AND batch.achfilebatchtype = 'PYW_FUNDING'
			AND record.achfilerecordtype = 'FUNDING_DEBIT'
		ORDER BY file.createdat asc
		LIMIT 1
	)
	AND payment.approvedat IS NOT NULL
	AND payment.approvedat > earliestfundingrecord.fundingtimestamp;

-- PREFUNDED PAYMENTS
UPDATE payment SET
	fundingtype = 'PRE_FUNDED'
WHERE
	status IN ('CALCULATED', 'APPROVED')
	AND payperiodtype NOT IN ('DAILY', 'WEEKLY', 'AD_HOC', 'PAY_ON_DEMAND');

WITH
	earliestfundingrecord AS (
		SELECT
			file.companyid,
			min(file.createdat) as fundingtimestamp
		FROM achfilerecord record
		INNER JOIN achfile file ON record.achfileid = file.id
		WHERE record.achfilerecordtype = 'FUNDING_DEBIT'
		GROUP BY file.companyid
	)
UPDATE payment SET
	fundingachfilerecordid = fundingrecord.id,
	fundingtype = 'PRE_FUNDED'
FROM
	achfilerecord paymentrecord,
	achfilebatch paymentbatch,
	achfilerecord fundingrecord,
	earliestfundingrecord
WHERE
	paymentrecord.paymentid = payment.id
	AND earliestfundingrecord.companyid = payment.companyid
	AND paymentbatch.id = paymentrecord.achfilebatchid
	AND paymentbatch.achfilebatchtype = 'REGULAR_PAYMENT'
	AND fundingrecord.id = (
		SELECT record.id
		FROM achfilerecord record
		INNER JOIN achfile file on file.id = record.achfileid
		INNER JOIN achfilebatch batch on record.achfilebatchid = batch.id
		WHERE
			file.createdat > payment.approvedat
			AND file.companyid = payment.companyid
			AND batch.achfilebatchtype = 'REGULAR_FUNDING'
			AND record.achfilerecordtype = 'FUNDING_DEBIT'
		ORDER BY file.createdat asc
		LIMIT 1
	)
	AND payment.approvedat IS NOT NULL
	AND payment.approvedat > earliestfundingrecord.fundingtimestamp;
