CREATE OR REPLACE VIEW decoratedpayment AS
SELECT p.id,
       p.companyid,
       p.fordate,
       p.startedat,
       p.calculatedat,
       p.approvedat,
       p.paidat,
       p.approvalgroupid,
       p.approvedbyuserid,
       p.status,
       p.statusmessage,
       p.payperiodid,
       p.employeeid,
       p.pretaxdeductions,
       p.posttaxdeductions,
       p.deferredcompensation,
       p.grossearnings,
       p.ytdgrossearnings,
       p.netearnings,
       p.ytdnetearnings,
       p.totaltaxesee,
       p.totaltaxeser,
       p.totalexpense,
       p.createdat,
       p.updatedat,
       p.payperiodtype,
       p.ytdpretaxdeductions,
       p.ytddeferredcompensation,
       p.ytdposttaxdeductions,
       p.totalcontributionser,
       p.totaltaxfunding,
       p.totalfunding,
       p.type,
       p.prevpaymentid,
       p.achfileid,
       p.errortype,
       p.paydate,
       p.mtdpretaxdeductions,
       p.qtdpretaxdeductions,
       p.mtdposttaxdeductions,
       p.qtdposttaxdeductions,
       p.mtddeferredcompensation,
       p.qtddeferredcompensation,
       p.mtdgrossearnings,
       p.qtdgrossearnings,
       p.mtdnetearnings,
       p.qtdnetearnings,
       p.companypayperiodid,
       p.fundingachfilerecordid,
       p.fundingtype,
       p.ffcrataxcredit,
       p.ffcracreditabletax,
       p.residentstate,
       p.previouslypaidearnings,
       p.worklocationstate,
       p.deletedat,
       p.deletedbyuserid,
       p.deletednote,
       p.unapprovedat,
       p.unapprovedbyuserid,
       p.percentofmonth,
       p.calculationrequestedat,
       p.depositstatus,
       p.timeframe,
       (SELECT "position".id
        FROM employeeposition "position"
        WHERE "position".employeeid = p.employeeid
          AND p.fordate >= "position".startdate
          AND p.fordate <= COALESCE("position".enddate::timestamp without time zone, 'infinity'::timestamp without time zone)) AS employeepositionid,
       CASE
           WHEN p.status = 'DELETED'::text THEN 'DELETED'::text
           WHEN p.status = 'ERRORED'::text
           AND (p.errortype::text = ANY (ARRAY['MISSING_OR_INVALID_ADDRESS'::text,
                                                        'MISSING_BANK_ACCOUNT'::text,
                                                        'MISSING_WITHHOLDINGS'::text,
                                                        'MISSING_EMPLOYEE_POSITION'::text,
                                                        'MISSING_UNEMPLOYMENT_RATES'::text,
                                                        'INVALID_BANK_ACCOUNT'::text,
                                                        'MISSING_TAX_PAYER_IDENTIFIER'::text])) THEN 'UNPAYABLE_WORKER'::text
            WHEN p.status = 'ERRORED'::text THEN 'ERROR'::text
            WHEN p.status = ANY (ARRAY['SUBMITTED'::text,
                                        'PAID'::text,
                                        'IMPORTED'::text]) THEN 'PAID'::text
            WHEN p.status = ANY (ARRAY['STARTED'::text,
                                        'AWAITING_CALCULATION'::text,
                                        'AWAITING_PRE_CALCULATION'::text]) THEN 'PROCESSING'::text
            WHEN p.status = ANY (ARRAY['APPROVED'::text,
                                        'FUNDED'::text]) THEN 'PENDING_PAYMENT'::text
            WHEN p.status = ANY (ARRAY['PREPARED_FOR_FUNDING_REQUEST'::text,
                                        'FUNDING_REQUESTED'::text]) THEN 'PENDING_FUNDING'::text
            WHEN
                        (SELECT every(c.verificationenabled) = true
                        AND COALESCE(every(s.verified), true) = false
                        FROM effectiveshift s
                        JOIN companytimetrackingconfiguration c ON c.companyid = p.companyid
                        JOIN payperiod pp ON pp.id = p.payperiodid
                        WHERE s.employeeid = p.employeeid
                            AND s.worklocationeffectivepunchoutdate >= pp.startdate
                            AND s.worklocationeffectivepunchoutdate <= pp.enddate
                            AND p.type = 'PAYROLL'::text) THEN 'PENDING_VERIFICATION'::text
            WHEN p.status = 'PRE_CALCULATED'::text THEN 'READY_TO_CALCULATE'::text
            WHEN p.status = 'CALCULATED'::text THEN 'PENDING_APPROVAL'::text
            ELSE NULL::text
END AS querystatus,
    p.externallyPaidCheckEarnings,
    p.paymentDistributionTypeOverride
    FROM payment p;
