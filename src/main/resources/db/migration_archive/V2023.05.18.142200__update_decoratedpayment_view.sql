alter table payment drop constraint if exists payment_errortype_enum_ck;
alter table payment
    add constraint payment_errortype_enum_ck
        check ((errortype)::text = ANY
               (ARRAY [('MISSING_OR_INVALID_ADDRESS'::character varying)::text, ('MISSING_BANK_ACCOUNT'::character varying)::text, ('INVALID_BANK_ACCOUNT'::character varying)::text, ('MISSING_WITHHOLDINGS'::character varying)::text, ('MISSING_EMPLOYEE_POSITION'::character varying)::text, ('MISSING_TAX_PAYER_IDENTIFIER'::character varying)::text, ('MISSING_OR_INVALID_PAY_PERIOD'::character varying)::text, ('MISSING_UNEMPLOYMENT_RATES'::character varying)::text, ('MISSING_CONTRIBUTION_DEDUCTION_LIMITS'::character varying)::text, ('FAILED_VALIDATION'::character varying)::text, ('NEGATIVE_NET'::character varying)::text, ('FUTURE_FINALIZED'::character varying)::text, ('FUNDING_FAILED'::character varying)::text, ('PREVIOUS_NOT_FINALIZED'::character varying)::text, ('UNEXPECTED'::character varying)::text, ('MISSING_PAY_CARD'::character varying)::text]));

create or replace view decoratedpayment as
SELECT p.*,
       (SELECT "position".id
        FROM employeeposition "position"
        WHERE "position".employeeid = p.employeeid
          AND p.fordate >= "position".startdate
          AND p.fordate <= COALESCE("position".enddate::timestamp without time zone,
                                    'infinity'::timestamp without time zone)) AS employeepositionid,
       CASE
           WHEN p.status = 'DELETED'::text THEN 'DELETED'::text
           WHEN p.status = 'ERRORED'::text AND (p.errortype::text = ANY
                                                (ARRAY ['MISSING_OR_INVALID_ADDRESS'::text, 'MISSING_BANK_ACCOUNT'::text, 'MISSING_WITHHOLDINGS'::text, 'MISSING_EMPLOYEE_POSITION'::text, 'MISSING_UNEMPLOYMENT_RATES'::text, 'INVALID_BANK_ACCOUNT'::text, 'MISSING_TAX_PAYER_IDENTIFIER'::text, 'MISSING_PAY_CARD'::text]))
               THEN 'UNPAYABLE_WORKER'::text
           WHEN p.status = 'ERRORED'::text THEN 'ERROR'::text
           WHEN p.status = ANY (ARRAY ['SUBMITTED'::text, 'PAID'::text, 'IMPORTED'::text]) THEN 'PAID'::text
           WHEN p.status = ANY (ARRAY ['STARTED'::text, 'AWAITING_CALCULATION'::text, 'AWAITING_PRE_CALCULATION'::text])
               THEN 'PROCESSING'::text
           WHEN p.status = ANY (ARRAY ['APPROVED'::text, 'FUNDED'::text]) THEN 'PENDING_PAYMENT'::text
           WHEN p.status = ANY (ARRAY ['PREPARED_FOR_FUNDING_REQUEST'::text, 'FUNDING_REQUESTED'::text])
               THEN 'PENDING_FUNDING'::text
           WHEN (SELECT every(c.verificationenabled) = true AND COALESCE(every(s.verified), true) = false
                 FROM effectiveshift s
                          JOIN companytimetrackingconfiguration c ON c.companyid = p.companyid
                          JOIN payperiod pp ON pp.id = p.payperiodid
                 WHERE s.employeeid = p.employeeid
                   AND s.worklocationeffectivepunchoutdate >= pp.startdate
                   AND s.worklocationeffectivepunchoutdate <= pp.enddate
                   AND p.type = 'PAYROLL'::text) THEN 'PENDING_VERIFICATION'::text
           WHEN (p.status = ANY (ARRAY ['PRE_CALCULATED'::text, 'CALCULATED'::text])) AND p.scheduledate IS NOT NULL AND
                p.scheduleapprovedbyuserid IS NOT NULL THEN 'SCHEDULED'::text
           WHEN p.status = 'PRE_CALCULATED'::text THEN 'READY_TO_CALCULATE'::text
           WHEN p.status = 'CALCULATED'::text THEN 'PENDING_APPROVAL'::text
           ELSE NULL::text
           END                                                                AS querystatus
FROM payment p;
