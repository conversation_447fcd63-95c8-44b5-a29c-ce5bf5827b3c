CREATE TABLE IF NOT EXISTS companysettingdefinition
(
  settingid VARCHAR(100) PRIMARY KEY,
  grouplabel VARCHAR(200) NOT NULL,
  label VARCHAR(200) NOT NULL,
  description VARCHAR(1000) NOT NULL,
  defaultvalue VARCHAR(1000) NOT NULL,
  createdat TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedat TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS companysetting
(
  id BIGSERIAL PRIMARY KEY,
  settingid VARCHAR(100) NOT NULL,
  companyid BIGINT NOT NULL,
  value VARCHAR(100) NOT NULL,
  createdat TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedat TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT companysettingcompany_fk FOREIGN KEY (companyid) references company (id),
  CONSTRAINT companysettingdefinition_fk FOREIGN KEY (settingid) references companysettingdefinition (settingid),
  CONSTRAINT companysetting_ukey UNIQUE (companyid, settingid)
);


INSERT INTO companysettingdefinition
  (settingId, grouplabel, label, description, defaultvalue, createdat, updatedat)
VALUES
  ('CO_STATE_TAX_EMPLOYER_ELECTED_PERCENTAGE', 'Colorado Paid Family and Medical Leave - Employer', 'Employer contribution to employee-paid tax', 'Amount of the CPFML EE portion that ER will cover. This applies only when the company is located in Colorado.', '0', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('CO_STATE_TAX_HAS_MET_EMPLOYEE_THRESHOLD', 'Colorado Paid Family and Medical Leave - Employer', 'Number of employees is 10 or more', 'Tax is applicable only if the number of employees is 10 or greater This applies only when the company is located in Colorado.', 'Yes', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;