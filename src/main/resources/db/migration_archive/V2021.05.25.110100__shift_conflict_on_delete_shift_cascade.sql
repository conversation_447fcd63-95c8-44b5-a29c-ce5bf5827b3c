ALTER TABLE scheduledshiftconflict
        DROP CONSTRAINT scheduledshiftconflict_conflictingshift_fk,
        ADD CONSTRAINT scheduledshiftconflict_conflictingshift_fk FOREIGN KEY (conflictingshiftid) REFERENCES scheduledshift (id) ON DELETE CASCADE,
        DROP CONSTRAINT scheduledshiftconflict_scheduledshift_fk,
        ADD CONSTRAINT scheduledshiftconflict_scheduledshift_fk FOREIGN KEY (scheduledshiftid) REFERENCES scheduledshift (id) ON DELETE CASCADE;
