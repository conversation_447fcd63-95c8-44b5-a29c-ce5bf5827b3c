create or replace view ptobalance as
(

with ptotransaction_cte as (
    SELECT p.fordate                                                                                                                                                                                                                 AS date,
           a.policyid,
           'WITHDRAWAL'::text                                                                                                                                                                                                        AS type,
           0::numeric - a.withdrawnhours                                                                                                                                                                                             AS hours,
           p.status = ANY
           (ARRAY ['SUBMITTED'::character varying::text, 'PAID'::character varying::text, 'PREPARED_FOR_FUNDING_REQUEST'::character varying::text, 'FUNDING_REQUESTED'::character varying::text, 'FUNDED'::character varying::text]) AS finalized,
           a.type                                                                                                                                                                                                                    AS timeofftype,
           p.companyid,
           p.employeeid,
           md5(concat('WITHDRAWAL'::text, p.id::text))                                                                                                                                                                               AS id
    FROM paymentptotransaction a
             JOIN payment p ON p.id = a.paymentid AND p.status <> 'DELETED'::text
    UNION ALL
    SELECT a.date,
           a.policyid,
           'ADJUSTMENT'::text                          AS type,
           a.hours,
           true                                        AS finalized,
           a.type                                      AS timeofftype,
           e.companyid,
           a.employeeid,
           md5(concat('ADJUSTMENT'::text, a.id::text)) AS id
    FROM ptobalanceadjustment a
             JOIN employee e ON e.id = a.employeeid
    UNION ALL
    SELECT p.fordate                                                                                                                                                                                                                 AS date,
           a.policyid,
           'ACCRUAL'::text                                                                                                                                                                                                           AS type,
           a.accruedhours                                                                                                                                                                                                            AS hours,
           p.status = ANY
           (ARRAY ['SUBMITTED'::character varying::text, 'PAID'::character varying::text, 'PREPARED_FOR_FUNDING_REQUEST'::character varying::text, 'FUNDING_REQUESTED'::character varying::text, 'FUNDED'::character varying::text]) AS finalized,
           a.type                                                                                                                                                                                                                    AS timeofftype,
           p.companyid,
           p.employeeid,
           md5(concat('ACCRUAL'::text, p.id::text))                                                                                                                                                                                  AS id
    FROM paymentptotransaction a
             JOIN payment p ON p.id = a.paymentid AND p.status <> 'DELETED'::text
),

     ptopolicycoveredemployee_cte as (


SELECT md5(VARIADIC ARRAY [ep.id, pp.id] :: Text)                                       AS id,
       e.companyid,
       e.id                                                                             AS employeeid,
       ep.id                                                                            AS positionid,
       pp.id                                                                            AS policyid,
       pp.type                                                                          AS policytype,
       -- Add tenure bonuses to either the workers overridden values, or the base policy values
       COALESCE(
               CASE
                   WHEN pt.accrualrateincrease IS NULL THEN e.overrideptoaccrualrate
                   ELSE split_part(e.overrideptoaccrualrate, ':', 1) || ':' ||
                        CAST(split_part(e.overrideptoaccrualrate, ':', 2) AS NUMERIC) + pt.accrualrateincrease END,
               CASE
                   WHEN pt.accrualrateincrease IS NULL THEN pp.defaultaccrualrate
                   ELSE
                               split_part(pp.defaultaccrualrate, ':', 1) || ':' ||
                               CAST(split_part(pp.defaultaccrualrate, ':', 2) AS NUMERIC) + pt.accrualrateincrease END
           )                                                                            AS effectiveptoaccrualrate,
       GREATEST(pp.startdate, ep.startdate)                                             AS startdate,
       LEAST(pp.enddate, ep.enddate)                                                    AS enddate,
       -- Add tenure bonuses to either the workers overridden values, or the base policy values
       COALESCE(
               CASE
                   WHEN pt.maxhoursincrease IS NULL THEN e.overrideptomaxhours
                   ELSE e.overrideptomaxhours + pt.maxhoursincrease END,
               CASE
                   WHEN pt.maxhoursincrease IS NULL THEN pp.defaultptomaxhours
                   ELSE pp.defaultptomaxhours + pt.maxhoursincrease END
           )                                                                            AS effectiveptomaxhours,
       pp.accrualpolicy,
       (DATE_PART('YEAR', now()) - date_part('YEAR', e.startdate)):: INTEGER             AS yearsemployed,
       pp.defaultaccrualrate                                                            AS baseaccrualrate,
       pp.defaultptomaxhours                                                            AS basemaxhours,
       e.overrideptoaccrualrate                                                         AS individualaccrualrate,
       e.overrideptomaxhours                                                            AS individualmaxhours,
       pt.accrualrateincrease                                                           AS tenureaccrualrateincrease,
       pt.maxhoursincrease                                                              AS tenuremaxhoursincrease,
       pt.id                                                                            AS tenureid
FROM employee e
         JOIN company c ON e.companyid = c.id
         JOIN ptopolicy pp ON e.companyid = pp.companyid
         JOIN employeeposition ep ON e.id = ep.employeeid
         -- Join on only the highest PTO Tenure an employee has reached
         LEFT JOIN ptotenurerule pt ON pt.id = (
    SELECT id FROM ptotenurerule inner_pt
    WHERE inner_pt.ptopolicyid = pp.id
      AND DATE_PART('YEAR', now()) - date_part('YEAR', e.startdate) >= inner_pt.years
    ORDER BY inner_pt.years DESC
    LIMIT 1
    )
WHERE "overlaps"(timezone(c.timezone, pp.startdate::timestamp with time zone),
    timezone(c.timezone, COALESCE(pp.enddate, 'infinity'::date)::timestamp with time zone),
    timezone(c.timezone, ep.startdate::timestamp with time zone),
    timezone(c.timezone, COALESCE(ep.enddate, 'infinity'::date)::timestamp with time zone))
  AND e.employmenttype = 'EMPLOYEE'::text
  AND (pp.salaryeligible = true AND ep.wagetype = 'SALARY'::text OR
    pp.fulltimehourlyeligible = true AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND
    ep.wagetype = 'HOURLY'::text OR pp.parttimehourlyeligible = true AND ep.wagetype = 'HOURLY'::text)
GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate, ep.id, ep.startdate, ep.enddate, pt.id




     )

SELECT md5(concat(e.id::text, COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text)))       AS id,
       e.companyid,
       e.id                                                                          AS employeeid,


       COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text)                                AS timeofftype,
       COALESCE(sum(
                        CASE
                            WHEN p.type = 'FLEXIBLE'::text AND t.finalized = true THEN t.hours
                            ELSE NULL::numeric
                            END),
                0::numeric)                                                          AS currenthours,
       COALESCE(sum(
                        CASE
                            WHEN p.type = 'FLEXIBLE'::text AND t.finalized = false THEN t.hours
                            ELSE NULL::numeric
                            END),
                0::numeric)                                                          AS pendinghours,
       COALESCE(sum(
                        CASE
                            WHEN p.type = 'FLEXIBLE'::text THEN t.hours
                            ELSE NULL::numeric
                            END),
                0::numeric)                                                          AS availablehours,


       (EXISTS(SELECT 1
               FROM ptopolicycoveredemployee_cte ce
               WHERE ce.employeeid = e.id
                 AND ce.policytype = 'UNLIMITED'::text
                 AND ce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::date
                 AND (ce.enddate IS NULL OR
                      ce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::date))) AS unlimited,


       (SELECT ce.effectiveptoaccrualrate
        FROM ptopolicycoveredemployee_cte ce
        WHERE ce.employeeid = e.id
          AND ce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::date
          AND (ce.enddate IS NULL OR ce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::date))
                                                                                     AS effectiveptoaccrualrate,
       (SELECT ce.effectiveptomaxhours
        FROM ptopolicycoveredemployee_cte ce
        WHERE ce.employeeid = e.id
          AND ce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::date
          AND (ce.enddate IS NULL OR ce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::date))
                                                                                     AS effectiveptomaxhours

FROM employee e
         JOIN company c ON c.id = e.companyid
         LEFT JOIN ptotransaction_cte t ON t.employeeid = e.id
         LEFT JOIN ptopolicy p ON p.id = t.policyid
GROUP BY c.id, e.id, (COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text))

    );
