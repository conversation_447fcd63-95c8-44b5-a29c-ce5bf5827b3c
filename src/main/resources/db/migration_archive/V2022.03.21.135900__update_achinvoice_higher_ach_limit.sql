create or replace view achinvoice as (
    SELECT a.id AS invoicenumber,
    a.id AS achfileid,
    a.companyid,
    a.createdat::date AS achcreationdate,
    'Pay-Your-Way'::text AS terms,
    case when max(b.achfilebatchtype) = 'POST_FUNDED_PAYMENT'::text then 'Pay-Your-Way Payment'::text
     else 'Above Same Day ACH Limit' end AS description,
    min(pp.startdate) AS payperiodstartdate,
    max(pp.enddate) AS payperiodenddate,
    max(cpp.enddate) AS regularpayperiodenddate,
    max(p.approvedbyuserid) AS approvedbyuserid,
    max(p.approvedat) AS approvedat,
    sum(r.amount) AS amount
   FROM achfile a
     JOIN achfilebatch b ON a.id = b.achfileid
     JOIN achfilerecord r ON b.id = r.achfilebatchid
     JOIN payperiod cpp ON a.createdat::date >= cpp.startdate AND a.createdat::date <= cpp.enddate AND cpp.companyid = a.companyid AND cpp.employeeid IS NULL AND cpp.payperiodpreferenceid IS NOT NULL
     LEFT JOIN payment p ON p.id = r.paymentid
     LEFT JOIN payperiod pp ON pp.id = p.payperiodid
  WHERE
  (b.achfilebatchtype = 'POST_FUNDED_PAYMENT'::text
  OR (b.achfilebatchtype = 'OPTIMISTIC_FUNDED_PAYMENT'::text AND b.totalamount >= 1000000 ))
  AND (r.achfilerecordtype = ANY (ARRAY['PAYMENT'::text, 'TAX_DEBIT'::text, 'CONTRIBUTION_DEDUCTION_DEBIT'::text]))
  GROUP BY a.id, a.companyid, (a.createdat::date), 'Pay-Your-Way'::text
)
