-- THIS FILE DOESN'T FOLLOW THE CORRECT NAMING CONVENTION
-- B<PERSON><PERSON><PERSON><PERSON> IT NEEDS TO BE RAN AFTER SCRIPTS THAT ALSO DON'T HAVE THE CORRECT NAMING CONVENTION
-- THOSE ORIGINAL SCRIPTS CAN'T BE MOVED AS THEY ARE NOT EASILY RE-RUNNABLE UNFORTUNATELY

DROP VIEW IF EXISTS firesubmitted1099nec;

CREATE VIEW firesubmitted1099nec AS
SELECT cast(bfr.paymentyear AS DOUBLE PRECISION) AS year,
       afr.companyid                             AS companyid,
       ap.id                                     AS userid,
       max(bfr.employeeid)                       AS employeeid,
       bfr.payeetin                              AS recipientstin,
       bfr.firstpayeename                        AS recipientsname1,
       bfr.secondpayeename                       AS recipientsname2,
       bfr.payeemailingaddress                   AS recipientsaddress,
       bfr.payeecity                             AS recipientscity,
       bfr.payeestate                            AS recipientsstate,
       bfr.payeezip                              AS recipientszip,
       bfr.typeoftin                             AS recipientsTinType,
       afr.issuertaxpayeridentifier              AS payerstin,
       afr.firstissuername                       AS payersname,
       afr.issuershippingaddress                 AS payersaddress,
       afr.issuercity                            AS payerscity,
       afr.issuerstate                           AS payersstate,
       afr.issuerzip                             AS payerszip,
       afr.issuerphone                           AS payersphone,
       ffs.id                                    AS firefilesubmissionid,
       CAST(SUM(bfr.paymentamount1) AS TEXT)     AS nonemployeecompensation,
       CASE
           WHEN
                   (SELECT COUNT(*)
                    FROM bfirerecord b
                             JOIN firefilesubmission f ON f.id = b.fileid
                    WHERE b.correctedreturnindicator != 'BLANK'
                      AND ffs.id = b.fileid) > 0 THEN TRUE
           ELSE FALSE
           END                                   AS corrected
FROM employee e
         JOIN contractortaxfilingstatus c ON e.id = c.employeeid
         JOIN contractortaxfilingstatusfirefilesubmission ctfsffs
              ON ctfsffs.contractortaxfilingstatusid = c.id
         JOIN firefilesubmission ffs ON ctfsffs.firefilesubmissionid = ffs.id
    AND ffs.id = (
        SELECT iffs.id
        FROM firefilesubmission iffs
                 JOIN contractortaxfilingstatusfirefilesubmission ictfsffs ON ictfsffs.firefilesubmissionid = iffs.id
                 JOIN contractortaxfilingstatus ictfs ON ictfsffs.contractortaxfilingstatusid = ictfs.id
        WHERE ictfs.employeeid = c.employeeid
          AND iffs.federalfileid IS NULL
          AND (iffs.iscancelled = FALSE OR iffs.iscancelled IS NULL)
          AND (iffs.submissionstatus IS NULL OR iffs.submissionstatus <> 'BAD')
        ORDER BY iffs.createdat DESC
        LIMIT 1
    )
         JOIN bfirerecord bfr ON bfr.fileid = ffs.id
    AND bfr.employeeid = c.employeeid
         JOIN afirerecord afr ON bfr.arecordid = afr.id
         JOIN appuser ap ON e.userid = ap.id
WHERE e.employmenttype = 'CONTRACTOR'
GROUP BY bfr.paymentyear,
         afr.companyid,
         ap.id,
         --bfr.employeeid,
         bfr.payeetin,
         bfr.firstpayeename,
         bfr.secondpayeename,
         bfr.payeemailingaddress,
         bfr.payeecity,
         bfr.payeestate,
         bfr.payeezip,
         bfr.typeoftin,
         afr.issuertaxpayeridentifier,
         afr.firstissuername,
         afr.issuershippingaddress,
         afr.issuercity,
         afr.issuerstate,
         afr.issuerzip,
         afr.issuerphone,
         ffs.id;
