ALTER TABLE ONLY formresponsesignature DROP CONSTRAINT IF EXISTS formresponsesignature_signedbyuserid_fkey;
ALTER TABLE ONLY formresponsesignature DROP CONSTRAINT IF EXISTS formresponsesignature_signinguser_fk;

ALTER TABLE ONLY formresponsesignature
    ADD CONSTRAINT formresponsesignature_signedbyuserid_fkey FOREIGN KEY (signedbyuserid) REFERENCES appuser(id)
    ON DELETE SET NULL;

CREATE INDEX IF NOT EXISTS "formresponsesignature_signedbyuserid_idx" ON formresponsesignature USING BTREE ("signedbyuserid");
