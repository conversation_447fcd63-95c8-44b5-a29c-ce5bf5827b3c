create or replace view ptobalance as
select to_hex(e.id::text, coalesce(t.timeofftype, 'PAID_TIME_OFF')) as id,
       e.companyid as companyid,
       e.id employeeid,
       coalesce(t.timeofftype, 'PAID_TIME_OFF') as timeofftype,
       coalesce(sum(case when p.type in ('FLEXIBLE') and t.finalized = true then t.hours end), 0)::numeric as currenthours,
       coalesce(sum(case when p.type in ('FLEXIBLE') and t.finalized = false then t.hours end), 0)::numeric as pendinghours,
       coalesce(sum(case when p.type in ('FLEXIBLE') then t.hours end), 0)::numeric as availablehours,
       coalesce(bool_or(p.type in ('UNLIMITED') and current_timestamp between ce.startsat and coalesce(ce.endsat, 'infinity')), false) as unlimited
from employee e
         left outer join ptopolicycoveredemployee ce on ce.employeeid = e.id
         left outer join ptopolicy p on p.id = ce.policyid
         left outer join ptotransaction t on t.employeeid = e.id and t.policyid = p.id
group by e.companyid, e.id, coalesce(t.timeofftype, 'PAID_TIME_OFF');
