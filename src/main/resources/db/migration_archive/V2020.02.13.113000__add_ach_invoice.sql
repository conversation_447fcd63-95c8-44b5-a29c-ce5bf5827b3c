drop view if exists achinvoice;

create or replace view achinvoice as
    select
        a.id as invoicenumber,
        a.id as achfileid,
        a.companyid as companyid,
        a.createdat::date as achcreationdate,
        'Pay-Your-Way' as terms,
        'Pay-Your-Way Payment' as description,
        min(pp.startdate) as payperiodstartdate,
        max(pp.enddate) as payperiodenddate,
        max(cpp.enddate) as regularpayperiodenddate,
        max(p.approvedbyuserid) as approvedbyuserid,
        max(p.approvedat) as approvedat,
        sum(r.amount) as amount
      from achfile a
      join achfilebatch b on a.id = b.achfileid
      join achfilerecord r on b.id = r.achfilebatchid
      join payperiod cpp on a.createdat between cpp.startdate and cpp.enddate and cpp.companyid = a.companyid and cpp.employeeid is null
      left outer join payment p on p.id = r.paymentid
      left outer join payperiod pp on pp.id = p.payperiodid
     where b.achfilebatchtype = 'PYW_PAYMENT'
       and r.achfilerecordtype in ('PAYMENT', 'SUI_DEBIT')
     group by 1, 2, 3, 4, 5;

ALTER TABLE achfile ADD COLUMN IF NOT EXISTS invoicelocation text;

ALTER TABLE company ADD COLUMN IF NOT EXISTS traditionalfundingtype text;
ALTER TABLE company ADD COLUMN IF NOT EXISTS adhocfundingtype text;
ALTER TABLE company ADD COLUMN IF NOT EXISTS pywfundingtype text;
ALTER TABLE company ADD COLUMN IF NOT EXISTS podfundingtype text;

update company set traditionalfundingtype = 'PRE_FUNDED';
update company set adhocfundingtype = 'PRE_FUNDED';
update company set pywfundingtype = 'POST_FUNDED';
update company set podfundingtype = 'POST_FUNDED';

ALTER TABLE company ALTER COLUMN traditionalfundingtype SET NOT NULL;
ALTER TABLE company ALTER COLUMN adhocfundingtype SET NOT NULL;
ALTER TABLE company ALTER COLUMN pywfundingtype SET NOT NULL;
ALTER TABLE company ALTER COLUMN podfundingtype SET NOT NULL;
