drop view if exists companytaxformfilingsummary;

create or replace view companytaxformfilingsummary as
with instance_plus_state as (
	select fi.companyid,
	c.displayname companydisplayname,
	c.legalentityname companylegalname,
	fi.filingfrequency,
	fi.filingperiodstartdate,
	coalesce (ctj.state, tj.state) state,
	fi.filedAt
from companytaxformfilinginstance fi
join company c on fi.companyid = c.id
left join companytaxjurisdiction ctj on fi.companytaxjurisdictionid = ctj.id
left join taxfilingformdefinition tfd on fi.taxfilingformdefinitionid = tfd.id
left join taxjurisdictionfilingformdefinition jfd on jfd.taxfilingformdefinitionid = tfd.id
left join taxjurisdiction tj on jfd.taxjurisdictionid = tj.id
),
state_or_federal_complete as (
select companyid,
	companydisplayname,
	companylegalname,
	filingfrequency,
	filingperiodstartdate,
	state,
	bool_and(filedat is not null) complete,
	sum(case when filedat is not null then 1 else 0 end)
		/ count(*)::float * 100 completepercent
from instance_plus_state
group by companyid, companydisplayname, companylegalname, filingfrequency, filingperiodstartdate, state)
select
	md5(concat(companyid::text, filingfrequency::text, filingperiodstartdate::text)) id,
	companyid,
	companydisplayname,
	companylegalname,
	filingfrequency,
	filingperiodstartdate,
	sum(case when state is null then completepercent else 0 end) federalcompletepercent,
	sum(case when state is not null and complete = true then 1 else 0 end)
		/ nullif(sum(case when state is not null then 1 else 0 end), 0)::float * 100 statecompletepercent,
	array_agg(state) filter (where state is not null and complete = true)::text[] AS statecompletelist,
	array_agg(state) filter (where state is not null and complete = false)::text[] AS stateincompletelist
from state_or_federal_complete
group by companyid, companydisplayname, companylegalname, filingfrequency, filingperiodstartdate;
