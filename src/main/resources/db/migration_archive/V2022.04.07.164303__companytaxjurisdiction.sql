CREATE TABLE IF NOT EXISTS companytaxjurisdiction (
  id varchar PRIMARY KEY GENERATED ALWAYS AS (text(companyid) || '.' || taxjurisdictionid) STORED NOT NULL,
  companyid bigint REFERENCES company (id) ON DELETE CASCADE NOT NULL,
  taxjurisdictionid varchar REFERENCES taxjurisdiction (id) ON DELETE CASCADE NOT NULL,
  accountnumber varchar,
  filingfrequency varchar,
  username varchar,
  password varchar,
  notes varchar,
  active boolean DEFAULT TRUE,
  archived boolean DEFAULT FALSE,
  state varchar,
  taxtype varchar,
  createdat timestamp WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat timestamp WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp
);
