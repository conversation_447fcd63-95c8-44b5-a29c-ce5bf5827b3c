alter table approvalgroup
  rename to approvalgroup_tmp;

alter sequence approvalgroup_id_seq rename to approvalgroup_tmp_id_seq;

alter table team
  rename to approvalgroup;

alter table only approvalgroup
  rename constraint team_name_check to approvalgroup_name_check;

alter table only approvalgroup
  rename constraint team_companyid_fkey to approvalgroup_companyid_fkey;

alter index team_name_companyid_key
  rename to approvalgroup_name_companyid_key;

alter table only approvalgroup
  alter column createdat set default now(),
  alter column updatedat set default now(),
  alter column deletedat set default null,
  drop column deleted,
  add column deleted bool not null generated always as (deletedat is not null) stored;

alter sequence team_id_seq rename to approvalgroup_id_seq;

create index approvalgroup_deleted_idx
  on approvalgroup (deleted);

alter table only approvalgroupapprover
  drop constraint approvalgroupapprover_approvalgroupid_fkey,
  add foreign key (approvalgroupid) references approvalgroup (id) on update cascade on delete cascade;

create or replace function trigger_approvalgroup_default_name() returns trigger as
$$
begin
  if new.name is null then
    new.name = (select concat('New group ',
                              coalesce(max(regexp_replace(g.name, '\D', '', 'g')::numeric), 0) + 1)
                from approvalgroup g
                where g.companyid = new.companyId
                  and g.deleted is false
                  and g.name similar to 'New group [0-9]+');
  end if;
  return new;
end;
$$ language plpgsql;

create trigger approvalgroup_set_updatedat
  before update
  on approvalgroup
  for each row
execute procedure trigger_set_updatedat();

create trigger approvalgroup_default_name
  before insert or update
  on approvalgroup
  for each row
execute procedure trigger_approvalgroup_default_name();
