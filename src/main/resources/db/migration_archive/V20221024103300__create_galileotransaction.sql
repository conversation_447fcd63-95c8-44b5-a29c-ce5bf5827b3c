create table if not exists galileotransaction (
    id BIGSERIAL primary key,
    createdat timestamp without time zone not null default current_timestamp,
    updatedat timestamp without time zone not null default current_timestamp,
    occurredat timestamp without time zone not null,
    merchantname text not null,
    amount numeric not null,
    status text not null,
    galileoaccountid bigint not null references galileoaccount (id) on delete cascade
);
