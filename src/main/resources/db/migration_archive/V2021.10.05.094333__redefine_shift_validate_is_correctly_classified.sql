
CREATE OR REPLACE FUNCTION shift_validate_is_correctly_classified()
 RET<PERSON><PERSON> trigger
 LANGUAGE plpgsql
AS $function$
begin
    -- Active shift
    if COALESCE(NEW.overrideendatexclusive,
	    	NEW.endatexclusive,
	    	TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchoutat),
	    	TIMEZONE('Etc/UTC'::TEXT, NEW.punchoutat)) is null then
        return NEW;
    end if;

    -- Zero-duration shift
    if (COALESCE(NEW.overrideendatexclusive,
    			NEW.endatexclusive,
    			TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchoutat),
	    		TIMEZONE('Etc/UTC'::TEXT, NEW.punchoutat)) =
      	COALESCE(NEW.overridestartatinclusive,
      		NEW.startatinclusive,
      		TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchinat),
	    		TIMEZONE('Etc/UTC'::TEXT, NEW.punchinat))) then
        return NEW;
    end if;

    -- Correctly classified
    if (COALESCE(NEW.overrideendatexclusive,
    			NEW.endatexclusive,
    			TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchoutat),
	    		TIMEZONE('Etc/UTC'::TEXT, NEW.punchoutat)) -
        COALESCE(NEW.overridestartatinclusive,
        	NEW.startatinclusive,
      		TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchinat),
	    		TIMEZONE('Etc/UTC'::TEXT, NEW.punchinat))) =
       (select sum(endatexclusive - startatinclusive)
        from classifiedtimeworked
        where shiftid = NEW.id) then
        return NEW;
    end if;

    RAISE EXCEPTION 'shift (%) duration is %; classified duration is %',
      NEW.id,
      (COALESCE(NEW.overrideendatexclusive,
      	NEW.endatexclusive,
      	TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchoutat),
      	TIMEZONE('Etc/UTC'::TEXT, NEW.punchoutat)) -
        COALESCE(NEW.overridestartatinclusive,
        	NEW.startatinclusive,
      	TIMEZONE('Etc/UTC'::TEXT, NEW.overridepunchinat),
      	TIMEZONE('Etc/UTC'::TEXT, NEW.punchinat))),
      (select sum(endatexclusive - startatinclusive)
        	from classifiedtimeworked
        	where shiftid = NEW.id);
end;
$function$
