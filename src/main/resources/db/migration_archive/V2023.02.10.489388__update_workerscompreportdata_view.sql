create or replace view workerscompreportdata as
(
SELECT p.id,
       p.companyid,
       date_part('year'::text, p.fordate)::integer              AS year,
       date_part('month'::text, p.fordate)::integer             AS month,
       e.id                                                     AS employeeid,
       u.firstname,
       u.lastname,
       p.payperiodtype,
       p.fordate,
       p.paydate,
       COALESCE(wc.state, p.worklocationstate)                  AS state,
       wc.name                                                  AS jobname,
       wc.code                                                  AS classcode,
       wc.rate,
       (SELECT coalesce(sum(pe.amount), 0.00)
        FROM paymentearning pe
        WHERE pe.paymentid = p.id
          AND pe.type <> 'REIMBURSEMENT'::text)                 AS grossearnings,
       (SELECT coalesce(sum(pe.amount), 0.00)
        FROM paymentearning pe
        WHERE pe.paymentid = p.id
          AND pe.type = 'REIMBURSEMENT'::text)                  AS reimbursements,
       (SELECT coalesce(round(sum(pe.unitcount), 2), 0.00)
        FROM paymentearning pe
        WHERE pe.paymentid = p.id
          AND pe.type = 'OVERTIME_HOURLY'::text)                AS overtimehours,
       (SELECT COALESCE(sum(pe.amount), 0.00)
        FROM paymentearning pe
        WHERE pe.paymentid = p.id
          AND pe.type = 'OVERTIME_HOURLY'::text)                AS overtimeearnings,
       (SELECT coalesce(sum(pe.amount), 0.00)
        FROM paymentearning pe
        WHERE pe.paymentid = p.id
          AND pe.type = 'TIPS'::text)                           AS tipearnings,
       COALESCE((SELECT sum(pcd.amounter)
                 FROM paymentcontributiondeduction pcd
                 WHERE pcd.paymentid = p.id
                   AND pcd.type = 'WORKERS_COMPENSATION'::text),
                (SELECT round(COALESCE(sum(pe.amount), 0.00) / 100.0 * wc.rate, 2) AS round
                 FROM paymentearning pe
                 WHERE pe.paymentid = p.id
                   AND pe.type <> 'REIMBURSEMENT'::text), 0.00) AS liabilityamount,
       (SELECT COALESCE(sum(pcd.amounter), 0.00) AS "coalesce"
        FROM paymentcontributiondeduction pcd
        WHERE pcd.paymentid = p.id
          AND pcd.type = 'WORKERS_COMPENSATION'::text
          AND pcd.fundandremit = true)                          AS fundedamount
FROM payment p
         JOIN employee e ON e.id = p.employeeid
         JOIN employeeposition ep ON ep.employeeid = e.id AND p.fordate BETWEEN ep.startdate AND
    COALESCE(ep.enddate, 'infinity'::date)
         JOIN appuser u ON u.id = e.userid
         LEFT JOIN workerscompclass wc ON wc.id = ep.workerscompclassid
WHERE p.status = ANY (ARRAY ['PAID'::text, 'IMPORTED'::text])
GROUP BY p.id, p.companyid, (date_part('year'::text, p.fordate)::integer),
         (date_part('month'::text, p.fordate)::integer), e.id, u.firstname, u.lastname, p.payperiodtype, p.fordate,
         p.paydate, wc.state, wc.name, wc.code, wc.rate
ORDER BY (date_part('year'::text, p.fordate)::integer), (date_part('month'::text, p.fordate)::integer), p.fordate,
         u.lastname, u.firstname
    );
