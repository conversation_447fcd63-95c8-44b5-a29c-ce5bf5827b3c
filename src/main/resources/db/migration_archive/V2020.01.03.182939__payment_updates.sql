ALTER TABLE paymentearning ADD COLUMN effectivehourlypayrate numeric;
ALTER TABLE paymentearning ADD COLUMN time bigint DEFAULT 0;
ALTER TABLE paymentearning ADD COLUMN doubletimeworked bigint DEFAULT 0;
ALTER TABLE paymentearning ADD COLUMN timeoffconsumed bigint DEFAULT 0;
ALTER TABLE paymentearning ADD COLUMN timeoffaccrued bigint DEFAULT 0;
ALTER TABLE paymentearning ADD COLUMN timeoffremaining bigint DEFAULT 0;

ALTER TABLE paymentearning DROP CONSTRAINT paymentearning_payment_qualifiedtype_ukey;
ALTER TABLE paymentearning ADD CONSTRAINT paymentearning_paymentid_qualifiedtype_effectivehourlypayrate_ukey UNIQUE (paymentid, qualifiedtype, effectivehourlypayrate);

-- PAYMENT EARNING PAY RATE BACKFILL

UPDATE paymentearning
SET payrate = position.payrate
FROM
  payment,
  employeeposition position
WHERE
  payment.id = paymentearning.paymentid
  AND position.employeeid = payment.employeeid
  AND position.startdate <= payment.fordate
  AND (position.enddate >= payment.fordate OR position.enddate IS NULL);

-- HOURLY BACKFILL

UPDATE paymentearning
SET effectivehourlypayrate = payrate
WHERE type = 'REGULAR_HOURLY';

UPDATE paymentearning
SET effectivehourlypayrate = round(amount / (timeworked::numeric / 3600), 2)
WHERE
  type = 'REGULAR_HOURLY'
  AND timeworked > 0;

-- SALARY BACKFILL

UPDATE paymentearning
SET effectivehourlypayrate = round(payrate / 260 / 8, 2)
WHERE
  type = 'REGULAR_SALARY';

-- OTHER BACKFILL

UPDATE paymentearning SET time = timeworked;
DROP VIEW paystubearning;
ALTER TABLE paymentearning DROP COLUMN timeworked;
ALTER TABLE paymentearning DROP COLUMN payrate;

CREATE VIEW paystubearning AS
SELECT
  to_hex(VARIADIC ARRAY[e.userid::text, p.companyid::text, p.companypayperiodid::text, pe.type, pe.effectivehourlypayrate::text]) AS id,
  to_hex(VARIADIC ARRAY[e.userid::text, p.companyid::text, p.companypayperiodid::text]) AS paystubid,
  pe.type,
  pe.effectivehourlypayrate,
  sum(pe.amount) AS amount,
  max(pe.ytdamount) AS ytdamount,
  sum(pe.time)::bigint AS time,
  string_agg(pe.note, ','::text) AS note
FROM paymentearning pe
  JOIN payment p ON pe.paymentid = p.id
  JOIN employee e ON p.employeeid = e.id
GROUP BY e.userid, p.companyid, p.companypayperiodid, pe.type, pe.effectivehourlypayrate;
