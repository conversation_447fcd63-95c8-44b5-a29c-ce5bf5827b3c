
CREATE TABLE IF NOT EXISTS employeeaccounttransaction (
  id                                           BIGSERIAL PRIMARY KEY,
  companyid                                    BIGINT REFERENCES company (id) ON DELETE CASCADE NOT NULL,
  employeeid                                   BIGINT REFERENCES employee (id) ON DELETE CASCADE NOT NULL,
  employeecontributiondeductionguid            TEXT NOT NULL,
  paymentid                                    BIGINT REFERENCES payment (id) ON DELETE CASCADE,
  paymentcontributiondeductionid               BIGINT REFERENCES paymentcontributiondeduction (id) ON DELETE CASCADE,
  liabilityamount                              NUMERIC NOT NULL,
  assetamount                                  NUMERIC NOT NULL,
  fundingamount                                NUMERIC NOT NULL,
  description                                  TEXT,
  nonpaymenttransactiondate                    DATE,
  createdat                                    TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat                                    TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  CONSTRAINT transaction_payment_ecd_ukey UNIQUE (paymentid, employeecontributiondeductionguid),
  constraint transaction_date_check check ( (paymentid is not null and nonpaymenttransactiondate is null) or (paymentid is null and nonpaymenttransactiondate is not null))
);

CREATE INDEX IF NOT EXISTS employeeaccounttransaction_company_idx ON employeeaccounttransaction (companyid);
CREATE INDEX IF NOT EXISTS employeeaccounttransaction_employee_idx ON employeeaccounttransaction (employeeid);
CREATE INDEX IF NOT EXISTS employeeaccounttransaction_payment_idx ON employeeaccounttransaction (paymentid);


CREATE OR REPLACE VIEW employeeaccountbalance AS
    SELECT
        et.companyid,
        c.displayname as companyname,
        et.employeeid,
        u.lastname as lastname,
        u.firstname as firstname,
        u.middlename as middlename,
        et.employeecontributiondeductionguid,
        ecd.type as employeecontributiondeductiontype,
        ecd.name as employeecontributiondeductionname,
        SUM(CASE WHEN paymentfinalized then assetamount else 0.00 end) as assetamount,
        SUM(CASE WHEN paymentfinalized then liabilityamount else 0.00 end) as liabilityamount,
        SUM(CASE WHEN paymentfinalized then fundingamount else 0.00 end) as fundingamount,
        SUM(CASE WHEN paymentfinalized then liabilityamount else 0.00 end) - SUM(CASE WHEN paymentfinalized then assetamount else 0.00 end) as balance,
        SUM(CASE WHEN paymentfinalized then 0.00 else assetamount end) as pendingassetamount,
        SUM(CASE WHEN paymentfinalized then 0.00 else liabilityamount end) as pendingliabilityamount,
        SUM(CASE WHEN paymentfinalized then 0.00 else liabilityamount end) - SUM(CASE WHEN paymentfinalized then 0.00 else assetamount end) as pendingbalance,
        SUM(CASE WHEN paymentfinalized then 0.00 else fundingamount end) as pendingfundingamount
    FROM (
        SELECT
            et.*,
            p.status is null OR  p.status IN
                ('APPROVED', 'SUBMITTED', 'PAID', 'IMPORTED','PREPARED_FOR_FUNDING_REQUEST', 'FUNDING_REQUESTED','FUNDED')
            --then true else false end
            as paymentfinalized
        FROM
            employeeaccounttransaction et
        LEFT JOIN payment p on p.id=et.paymentid
        WHERE et.paymentid is null or p.status <> 'DELETED'
    ) et
    JOIN company c on c.id=et.companyid
    JOIN employee e on e.id=et.employeeid
    JOIN appuser u on u.id=e.userid
    LEFT JOIN (
        select id::text as guid,
            type,
            name
        from employeecontributiondeduction
        group by id, type, name
    ) ecd ON ecd.guid=et.employeecontributiondeductionguid
    GROUP BY
        et.companyid,
        c.displayname,
        u.firstname,
        u.lastname,
        u.middlename,
        et.employeeid,
        et.employeecontributiondeductionguid,
        ecd.type,
        ecd.name
