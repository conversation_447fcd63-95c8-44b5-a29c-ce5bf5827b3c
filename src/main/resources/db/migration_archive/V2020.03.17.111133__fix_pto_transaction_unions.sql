DROP VIEW ptobalance;
DROP VIEW ptotransaction;

CREATE VIEW ptotransaction AS
 SELECT p.fordate AS date,
    a.policyid,
    'WITHDRAWAL'::text AS type,
    0::numeric - a.withdrawnhours AS hours,
    p.status = ANY (ARRAY['SUBMITTED'::character varying::text, 'PAID'::character varying::text]) AS finalized,
    a.type AS timeofftype,
    p.companyid,
    p.employeeid
   FROM paymentptotransaction a
     JOIN payment p ON p.id = a.paymentid
UNION ALL
 SELECT a.date,
    a.policyid,
    'ADJUSTMENT'::text AS type,
    a.hours,
    true AS finalized,
    a.type AS timeofftype,
    e.companyid,
    a.employeeid
   FROM ptobalanceadjustment a
     JOIN employee e ON e.id = a.employeeid
UNION ALL
 SELECT p.fordate AS date,
    a.policyid,
    'ACCRUAL'::text AS type,
    a.accruedhours AS hours,
    p.status = ANY (ARRAY['SUBMITTED'::character varying::text, 'PAID'::character varying::text]) AS finalized,
    a.type AS timeofftype,
    p.companyid,
    p.employeeid
   FROM paymentptotransaction a
     JOIN payment p ON p.id = a.paymentid;

CREATE VIEW ptobalance AS  SELECT to_hex(VARIADIC ARRAY[e.id::text, COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text)]) AS id,
    e.companyid,
    e.id AS employeeid,
    COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text) AS timeofftype,
    COALESCE(sum(
        CASE
            WHEN p.type = 'FLEXIBLE'::text AND t.finalized = true THEN t.hours
            ELSE NULL::numeric
        END), 0::numeric) AS currenthours,
    COALESCE(sum(
        CASE
            WHEN p.type = 'FLEXIBLE'::text AND t.finalized = false THEN t.hours
            ELSE NULL::numeric
        END), 0::numeric) AS pendinghours,
    COALESCE(sum(
        CASE
            WHEN p.type = 'FLEXIBLE'::text THEN t.hours
            ELSE NULL::numeric
        END), 0::numeric) AS availablehours,
    (EXISTS ( SELECT 1
           FROM ptopolicycoveredemployee ce
          WHERE ce.employeeid = e.id AND ce.policytype = 'UNLIMITED'::text AND CURRENT_TIMESTAMP >= ce.startsat AND CURRENT_TIMESTAMP <= COALESCE(ce.endsat, 'infinity'::timestamp without time zone))) AS unlimited
   FROM employee e
     LEFT JOIN ptotransaction t ON t.employeeid = e.id
     LEFT JOIN ptopolicy p ON p.id = t.policyid
  GROUP BY e.companyid, e.id, (COALESCE(t.timeofftype, 'PAID_TIME_OFF'::text));
