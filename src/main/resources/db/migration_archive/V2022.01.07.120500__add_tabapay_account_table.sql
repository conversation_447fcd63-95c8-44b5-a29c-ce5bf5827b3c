CREATE TABLE IF NOT EXISTS tabapayaccount (
    id                      BIGSERIAL PRIMARY KEY,
    createdat               TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
    updatedat               TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
    userbankaccountid       BIGINT NOT NULL REFERENCES userbankaccount (id) ON DELETE CASCADE,
    accounttype             TEXT NOT NULL,
    accountid               TEXT,
    cardlast4               TEXT,
    cardexpirationdate      TEXT,
    cardpushenabled         BOOLEAN,
    cardnetwork             TEXT,
    cardtype                TEXT,
    cardpushavailability    TEXT,
    cardregulated           BOOLEAN,
    cardcurrency            TEXT,
    cardcountry             TEXT
);

CREATE UNIQUE INDEX tabapayaccount_type_uk ON tabapayaccount (userBankAccountId, accounttype);
