create or replace view annualf940 as (
 SELECT annual940innertable4.year,
    annual940innertable4.companyid,
    annual940innertable4.countofemployees,
    annual940innertable4.maxfutaamount,
    annual940innertable4.totalpaymentstoallemployees,
    annual940innertable4.totalpaymentstoeachemployeeinexcessof7000,
    annual940innertable4.question6subtotal,
    annual940innertable4.question7totaltaxablefutawages,
    round((annual940innertable4.question7totaltaxablefutawages * 0.006), 2) AS question8futataxbeforeadjustments,
    annual940innertable4.hasFringeBenefits,
    annual940innertable4.hasLifeInsurance,
    annual940innertable4.hasRetirement,
    annual940innertable4.hasDependentCare,
    annual940innertable4.hasOther,
    annual940innertable4.totalgrossearningstoallemployees
 FROM ( 
    SELECT 
        annual940innertable3.year,
        annual940innertable3.companyid,
        annual940innertable3.countofemployees,
        annual940innertable3.hasFringeBenefits,
        annual940innertable3.hasLifeInsurance,
        annual940innertable3.hasRetirement,
        annual940innertable3.hasDependentCare,
        annual940innertable3.hasOther,
        annual940innertable3.totalgrossearningstoallemployees,
        annual940innertable3.maxfutaamount,
        annual940innertable3.totalpaymentstoallemployees,
        annual940innertable3.totalpaymentstoeachemployeeinexcessof7000,
        annual940innertable3.question6subtotal,
        (annual940innertable3.totalgrossearningstoallemployees - annual940innertable3.question6subtotal) AS question7totaltaxablefutawages
    FROM ( 
        SELECT 
            annual940innertable2.year,
            annual940innertable2.companyid,
            annual940innertable2.countofemployees,
            annual940innertable2.hasFringeBenefits,
            annual940innertable2.hasLifeInsurance,
            annual940innertable2.hasRetirement,
            annual940innertable2.hasDependentCare,
            annual940innertable2.hasOther,
            annual940innertable2.totalgrossearningstoallemployees,
            annual940innertable2.maxfutaamount,
            annual940innertable2.totalpaymentstoallemployees,
            annual940innertable2.totalpaymentstoeachemployeeinexcessof7000,
            annual940innertable2.totalgrossearningstoallemployees - annual940innertable2.totalpaymentstoallemployees + annual940innertable2.totalpaymentstoeachemployeeinexcessof7000 AS question6subtotal
        FROM ( 
            SELECT 
                annual940innertable1.year,
                annual940innertable1.companyid,
                annual940innertable1.countofemployees,
                annual940innertable1.hasFringeBenefits,
                annual940innertable1.hasLifeInsurance,
                annual940innertable1.hasRetirement,
                annual940innertable1.hasDependentCare,
                annual940innertable1.hasOther,
                (annual940innertable1.countofemployees * 42) AS maxfutaamount,
                ( SELECT sum(totalpaymentstoemployee.totalpayment) AS sum
                  FROM totalpaymentstoemployee totalpaymentstoemployee
                  WHERE ((totalpaymentstoemployee.companyid = annual940innertable1.companyid) 
                    AND (totalpaymentstoemployee.year = annual940innertable1.year))
                ) AS totalgrossearningstoallemployees,
                ( SELECT sum(totalpaymentstoemployee.totalpaymentlesspretax) AS sum
                  FROM totalpaymentstoemployee totalpaymentstoemployee
                  WHERE ((totalpaymentstoemployee.companyid = annual940innertable1.companyid) 
                    AND (totalpaymentstoemployee.year = annual940innertable1.year))
                ) AS totalpaymentstoallemployees,
                ( 
                    SELECT sum(
                        CASE
                         WHEN (totalpaymentstoemployee.totalpaymentlesspretax > (7000)::numeric) THEN (totalpaymentstoemployee.totalpaymentlesspretax - (7000)::numeric)
                         ELSE (0)::numeric
                        END) AS sum
                    FROM totalpaymentstoemployee totalpaymentstoemployee
                    WHERE (
                        (totalpaymentstoemployee.companyid = annual940innertable1.companyid) 
                        AND (totalpaymentstoemployee.year = annual940innertable1.year))
                ) AS totalpaymentstoeachemployeeinexcessof7000
            FROM ( 
                SELECT 
                    date_part('year'::text, payment.paydate) AS year,
                    payment.companyid,
                    count(DISTINCT payment.employeeid) AS countofemployees,
                    (case when count(fringeBenefits.id) > 0 then true else false end) as hasFringeBenefits,
                    (case when count(lifeInsurance.id) > 0 then true else false end) as hasLifeInsurance,
                    false as hasRetirement,
                    (case when count(dependentCare.id) > 0 then true else false end) as hasDependentCare,
                    (case when count(reimbursements.id) > 0 then true else false end) as hasOther
                FROM (
                    payment payment
                    JOIN employee e ON (((e.id = payment.employeeid) AND (e.employmenttype = 'EMPLOYEE'::text)))
                    JOIN company company ON ((payment.companyid = company.id))
                    LEFT JOIN paymentearning reimbursements on reimbursements.paymentid=payment.id and reimbursements.amount>0 and reimbursements.type='REIMBURSEMENT'
                    LEFT JOIN paymentcontributiondeduction lifeInsurance on lifeInsurance.paymentid=payment.id and lifeInsurance.amountee>0 and lifeInsurance.type in ('GROUP_LIFE_INSURANCE', 'VOLUNTARY_LIFE_INSURANCE')
                    LEFT JOIN paymentcontributiondeduction fringeBenefits on fringeBenefits.paymentid=payment.id and fringeBenefits.amountee>0 and fringeBenefits.type in ('ACCIDENT_INSURANCE', 'CANCER_INSURANCE', 'COMMUTER_PLAN', 'CRITICAL_ILLNESS_INSURANCE', 'DENTAL_INSURANCE', 'DISABILITY_PLAN', 'FSA', 'HOSPITAL_INDEMNITY_INSURANCE', 'HRA', 'HSA', 'MEDICAL_INSURANCE', 'MEDICAL_INSURANCE_SECONDARY', 'MEDICARE_SUPPLEMENT_PLAN', 'PRESCRIPTION_PLAN', 'VISION_INSURANCE')
                    LEFT JOIN paymentcontributiondeduction dependentCare on dependentCare.paymentid=payment.id and dependentCare.amountee>0 and dependentCare.type in ('DRA')
                    )
                WHERE (payment.status = ANY (ARRAY['PAID'::text, 'IMPORTED'::text]))
                GROUP BY (date_part('year'::text, payment.paydate)), payment.companyid
            ) annual940innertable1
        ) annual940innertable2
    ) annual940innertable3
 ) annual940innertable4
)
