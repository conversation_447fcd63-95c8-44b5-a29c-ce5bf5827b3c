CREATE OR REPLACE VIEW fundingtransaction AS
SELECT
    payments.recordid as id,
	payments.companyid,
	payments.targetsettlementdate,
	payments.fundingtype as type,
	MAX(payments.amount) as amount,
	cast(SUM(payments.paymentcount) as bigint) as paymentcount,
	SUM(payments.totalgrossearnings) as totalgrossearnings,
	SUM(payments.totalnetearnings) as totalnetearnings,
	SUM(taxes.totaltaxesee) as totaltaxesee,
	SUM(taxes.totaltaxeser) as totaltaxeser,
	SUM(payments.totalcontributionser) as totalcontributionser,
	SUM(payments.totalgrossearnings) + SUM(taxes.totaltaxeser) + SUM(payments.totalcontributionser) as totalexpense,
	SUM(taxes.totaltaxes) as totaltaxfunding,
	SUM(payments.totalnetearnings) + SUM(taxes.totaltaxes) as totalfunding,
	SUM(taxes.totalcredit) as appliedtaxcredit
FROM (
        SELECT
                a.achfileid,
                SUM(case when taxtype like 'ER_%' then a.net else 0.00 end) as totaltaxeser,
                SUM(case when taxtype not like 'ER_%' then a.net else 0.00 end) as totaltaxesee,
                SUM(a.gross) as gross,
                SUM(a.net) as totaltaxes,
                SUM(a.grosscredit) as grosscredit,
                SUM(a.appliedcredit) as totalcredit,
                SUM(a.unappliedcredit) as unappliedcredit,
                SUM(a.deferred) as deferred,
                CASE when a.paymenttype = 'REGULAR' then 'PRE_FUNDED' else 'POST_FUNDED' end as fundingtype
        from taxtypeaccumulation a
        group by
                a.achfileid,
                a.paymenttype
        ) taxes
JOIN (
        SELECT
                record.id recordid,
                file.companyid,
                payment.achfileid as achfileid,
                batch.targetsettlementdate,
                payment.fundingtype,
                record.amount as amount,
                count(payment.*) as paymentcount,
                sum(payment.grossearnings) as totalgrossearnings,
                sum(payment.netearnings) as totalnetearnings,
                sum(payment.totaltaxesee) as totaltaxesee,
                sum(payment.totaltaxeser) as totaltaxeser,
                sum(payment.totalcontributionser) as totalcontributionser,
                sum(payment.totalexpense) as totalexpense,
                sum(payment.totaltaxfunding) as totaltaxfunding,
                sum(payment.totalfunding) as totalfunding
        FROM achfilerecord record
        INNER JOIN payment payment ON payment.fundingachfilerecordid = record.id
        INNER JOIN achfilebatch batch ON record.achfilebatchid = batch.id
        INNER JOIN achfile file ON record.achfileid = file.id
        WHERE
                record.achfilerecordtype = 'FUNDING_DEBIT'
                AND file.status = 'PAID'
        GROUP BY
                record.id,
                file.companyid,
                payment.achfileid,
                batch.targetsettlementdate,
                payment.fundingtype
	) payments
ON taxes.achfileid=payments.achfileid AND taxes.fundingtype = payments.fundingtype
GROUP BY
    payments.recordid,
    payments.companyid,
    payments.targetsettlementdate,
    payments.fundingtype
;
