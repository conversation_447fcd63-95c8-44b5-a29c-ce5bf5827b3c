drop view if exists w2data2020;

create or replace view w2data2020 as
 SELECT to_hex(VARIADIC ARRAY[p.employeeid, (date_part('year'::text, p.paydate))::bigint]) AS id,
    p.employeeid,
    e.userid,
    p.companyid,
    date_part('year'::text, p.paydate) AS year,
    ''::text AS controlnumber,
    sum(p.grossearnings) AS grossearnings,
    sum(ptfit.subjectwages) AS fitwages,
    sum(ptfit.amount) AS fit,
    (COALESCE(sum(ptfica.subjectwages), (0)::numeric) - COALESCE(sum(petips.amount), (0)::numeric)) AS ficawages,
    sum(ptfica.amount) AS fica,
    sum(ptmedi.subjectwages) AS mediwages,
    (COALESCE(sum(ptmedi.amount), (0)::numeric) + COALESCE(sum(ptmedi2.amount), (0)::numeric)) AS medi,
    COALESCE(sum(petips.amount), (0)::numeric) AS sstips,
    COALESCE(sum(peffcrasick.amount), (0)::numeric) AS ffcrasick,
    (0)::numeric AS ffcracaregiver,
    COALESCE(sum(peffcrafmla.amount), (0)::numeric) AS ffcrafmla,
    (0)::numeric AS alloctips,
    COALESCE((sum(pcddra.amountee) + sum(pcddra.amounter)), (0)::numeric) AS dra,
    (0)::numeric AS nqplans,
    sum(pcd401k.amountee) AS erisa401k,
    sum(pcd403b.amountee) AS erisa403b,
    COALESCE((sum(pcdhsa.amountee) + sum(pcdhsa.amounter)), (0)::numeric) AS hsa,
    sum(pcdroth.amountee) AS roth401k,
    (((((COALESCE((sum(pcdmedical.amountee) + sum(pcdmedical.amounter)), (0)::numeric) + COALESCE((sum(pcddental.amountee) + sum(pcddental.amounter)), (0)::numeric)) + COALESCE((sum(pcdvision.amountee) + sum(pcdvision.amounter)), (0)::numeric)) + COALESCE((sum(pcdprescription.amountee) + sum(pcdprescription.amounter)), (0)::numeric)) + COALESCE((sum(pcdhra.amountee) + sum(pcdhra.amounter)), (0)::numeric)) + COALESCE((sum(pcdhopital.amountee) + sum(pcdhopital.amounter)), (0)::numeric)) AS healthcoverage,
    'Off'::text AS statutoryemployee,
        CASE
            WHEN (((COALESCE((sum(pcd401k.amountee) + sum(pcd401k.amounter)), (0)::numeric) + COALESCE((sum(pcd403b.amountee) + sum(pcd403b.amounter)), (0)::numeric)) + COALESCE((sum(pcdroth.amountee) + sum(pcdroth.amounter)), (0)::numeric)) > (0)::numeric) THEN 'On'::text
            ELSE 'Off'::text
        END AS retirementplan,
    'Off'::text AS thirdpartysickpay
   FROM (((((((((((((((((((payment p
     JOIN employee e ON ((p.employeeid = e.id)))
     LEFT JOIN paymenttax ptfit ON (((ptfit.paymentid = p.id) AND (ptfit.type = 'FIT'::text))))
     LEFT JOIN paymenttax ptfica ON (((ptfica.paymentid = p.id) AND (ptfica.type = 'FICA'::text))))
     LEFT JOIN paymenttax ptmedi ON (((ptmedi.paymentid = p.id) AND (ptmedi.type = 'MEDI'::text))))
     LEFT JOIN paymenttax ptmedi2 ON (((ptmedi2.paymentid = p.id) AND (ptmedi2.type = 'MEDI2'::text))))
     LEFT JOIN paymentcontributiondeduction pcddra ON (((pcddra.paymentid = p.id) AND (pcddra.type = 'DRA'::text))))
     LEFT JOIN paymentearning petips ON (((petips.paymentid = p.id) AND (petips.type = 'TIPS'::text))))
     LEFT JOIN paymentearning peffcrasick ON (((peffcrasick.paymentid = p.id) AND (peffcrasick.type = 'EMERGENCY_FFCRA_SICK'::text))))
     LEFT JOIN paymentearning peffcrafmla ON (((peffcrafmla.paymentid = p.id) AND (peffcrafmla.type = 'EMERGENCY_FFCRA_FMLA'::text))))
     LEFT JOIN paymentcontributiondeduction pcd401k ON (((pcd401k.paymentid = p.id) AND (pcd401k.type = 'ERISA_401K'::text))))
     LEFT JOIN paymentcontributiondeduction pcd403b ON (((pcd403b.paymentid = p.id) AND (pcd403b.type = 'ERISA_403B'::text))))
     LEFT JOIN paymentcontributiondeduction pcdhsa ON (((pcdhsa.paymentid = p.id) AND (pcdhsa.type = 'HSA'::text))))
     LEFT JOIN paymentcontributiondeduction pcdroth ON (((pcdroth.paymentid = p.id) AND (pcdroth.type = 'ROTH_401K'::text))))
     LEFT JOIN paymentcontributiondeduction pcdmedical ON (((pcdmedical.paymentid = p.id) AND (pcdmedical.type = 'MEDICAL_INSURANCE'::text))))
     LEFT JOIN paymentcontributiondeduction pcddental ON (((pcddental.paymentid = p.id) AND (pcddental.type = 'DENTAL_INSURANCE'::text))))
     LEFT JOIN paymentcontributiondeduction pcdvision ON (((pcdvision.paymentid = p.id) AND (pcdvision.type = 'VISION_INSURANCE'::text))))
     LEFT JOIN paymentcontributiondeduction pcdprescription ON (((pcdprescription.paymentid = p.id) AND (pcdprescription.type = 'PRESCRIPTION_PLAN'::text))))
     LEFT JOIN paymentcontributiondeduction pcdhra ON (((pcdhra.paymentid = p.id) AND (pcdhra.type = 'HRA'::text))))
     LEFT JOIN paymentcontributiondeduction pcdhopital ON (((pcdhopital.paymentid = p.id) AND (pcdhopital.type = 'HOSPITAL_INDEMNITY_INSURANCE'::text))))
  WHERE (p.status = ANY (ARRAY['PAID'::text, 'IMPORTED'::text]))
  GROUP BY p.employeeid, e.userid, p.companyid, (date_part('year'::text, p.paydate));

/*
select * from w2data2020 where employeeid = 1584 and year = 2020;

select p.companyid, p.employeeid, pe.type, pe.amount, p.*, pe.* from payment p
join paymentearning pe on pe.paymentid = p.id
where pe.type in ('EMERGENCY_FFCRA_SICK', 'EMERGENCY_FFCRA_FMLA')
and p.paydate >= '2020-01-01'
--and p.companyid = 170
--and p.employeeid = 171
and pe.amount > 0
order by companyid, employeeid, fordate;
*/

drop view if exists w2nonfedtaxdata2020;

create or replace view w2nonfedtaxdata2020 as
 SELECT to_hex(VARIADIC ARRAY[p.employeeid, (date_part('year'::text, p.paydate))::bigint, max(pt.id)]) AS id,
    p.employeeid,
    e.userid,
    p.companyid,
    date_part('year'::text, p.paydate) AS year,
    ''::text AS controlnumber,
    pt.type,
        CASE
            WHEN (pt.type = 'SIT'::text) THEN 'STATE'::text
            WHEN (pt.type = ANY (ARRAY['CITY'::text, 'SCHL'::text])) THEN 'LOCAL'::text
            ELSE 'OTHER'::text
        END AS "group",
    pt.jurisdictiontaxid,
    max(pt.description) AS description,
    sum(pt.subjectwages) AS subjectwages,
    sum(pt.amount) AS amount
   FROM ((payment p
     JOIN employee e ON ((p.employeeid = e.id)))
     LEFT JOIN paymenttax pt ON (((pt.paymentid = p.id) AND (pt.payertype = 'EMPLOYEE'::text) AND (pt.type <> ALL (ARRAY['FIT'::text, 'FICA'::text, 'MEDI'::text, 'MEDI2'::text])))))
  WHERE (p.status = ANY (ARRAY['PAID'::text, 'IMPORTED'::text]))
  GROUP BY p.employeeid, e.userid, p.companyid, (date_part('year'::text, p.paydate)), pt.type, pt.jurisdictiontaxid;
