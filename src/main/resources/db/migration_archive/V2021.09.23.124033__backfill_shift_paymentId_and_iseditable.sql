DROP TRIGGER IF EXISTS shift_validate_is_correctly_classified ON Shift;
DROP TRIGGER IF EXISTS shift_update_sync_timestamps ON Shift;

UPDATE Shift
  SET
    paymentId =
    (SELECT p.id
      FROM Payment p
      WHERE p.payPeriodId = Shift.payPeriodId
        AND p.employeeId = Shift.employeeId
        AND p.status<>'DELETED'
    ),
    editable =
    (SELECT COUNT(*) < 1
      FROM Payment p
      WHERE p.payPeriodId = Shift.payPeriodId
        AND p.employeeId = Shift.employeeId
        AND p.status IN (
          'APPROVED'::text,
          'SUBMITTED'::text,
          'PAID'::text,
          'IMPORTED'::text,
          'PREPARED_FOR_FUNDING_REQUEST'::text,
          'FUNDING_REQUESTED'::text,
          'FUNDED'::text
        )
    )
  WHERE paymentId IS NULL;

  -- ------------------------------
-- assign update sync constraint
CREATE CONSTRAINT TRIGGER shift_update_sync_timestamps
    AFTER UPDATE
    ON Shift
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
EXECUTE FUNCTION shift_update_sync_timestamps();

CREATE CONSTRAINT TRIGGER shift_validate_is_correctly_classified
    AFTER INSERT OR UPDATE
    ON shift
    DEFERRABLE INITIALLY DEFERRED
    FOR EACH ROW
EXECUTE FUNCTION shift_validate_is_correctly_classified();
