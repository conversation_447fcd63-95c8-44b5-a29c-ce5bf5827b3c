CREATE TABLE IF NOT EXISTS systemholiday (
  id                     BIGSERIAL PRIMARY KEY,
  createdat              TIMES<PERSON>MP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  updatedat              TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT current_timestamp,
  name                   TEXT  NOT NULL,
  description            TEXT,
  date                   DATE NOT NULL,
  archivedat             TIMESTAMP WITHOUT TIME ZONE,
  archivedbyuserid       BIGINT,
  createdbyuserid        BIGINT NOT NULL,
  CONSTRAINT systemholiday_user_fk FOREIGN KEY (archivedbyuserid) REFERENCES appuser (id) ON DELETE SET NULL,
  CONSTRAINT systemholiday_createduser_fk FOREIGN KEY (createdbyuserid) REFERENCES appuser (id) ON DELETE SET NULL,
  CONSTRAINT systemholiday_date_uk UNIQUE (date)
);


with holidays as (
          SELECT 'New Year''s Day' as name, 'New Year''s Day is the first day of the Gregorian calendar, which is widely used in many countries such as the USA.' as description, '2019-01-01' as date
UNION ALL SELECT '<PERSON>. <PERSON>', '<PERSON> marks the anniversary of the date of birth of the influential American civil right leader of the same name.', '2019-01-21'
UNION ALL SELECT 'Presidents'' Day', 'Washington''s Birthday, or Presidents'' Day, honors the life and work of the first president of the United States, George Washington.', '2019-02-18'
UNION ALL SELECT 'Memorial Day', 'Memorial Day commemorates all Americans who have died in military service for the United States.', '2019-05-27'
UNION ALL SELECT 'Independence Day', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2019-07-04'
UNION ALL SELECT 'Labor Day', 'Labor Day is a federal holiday in the United States. It gives workers a day of rest and it celebrates their contribution to the American economy.', '2019-09-02'
UNION ALL SELECT 'Columbus Day', 'Columbus Day celebrates 15th century explorer Christopher Columbus''s arrival in America in 1492.', '2019-10-14'
UNION ALL SELECT 'Veterans Day', 'Veterans Day in the USA is a holiday to honor all who have served in the United States Military Services.', '2019-11-11'
UNION ALL SELECT 'Thanksgiving Day', 'Thanksgiving Day in the United States is traditionally a holiday to give thanks for the food collected at the end of the harvest season.', '2019-11-28'
UNION ALL SELECT 'Christmas Day', 'Christmas Day celebrates Jesus Christ''s birth.', '2019-12-25'
UNION ALL SELECT 'New Year''s Day', 'New Year''s Day is the first day of the Gregorian calendar, which is widely used in many countries such as the USA.', '2020-01-01'
UNION ALL SELECT 'Martin Luther King Jr. Day', 'Martin Luther King Day marks the anniversary of the date of birth of the influential American civil right leader of the same name.', '2020-01-20'
UNION ALL SELECT 'Presidents'' Day', 'Washington''s Birthday, or Presidents'' Day, honors the life and work of the first president of the United States, George Washington.', '2020-02-17'
UNION ALL SELECT 'Memorial Day', 'Memorial Day commemorates all Americans who have died in military service for the United States.', '2020-05-25'
UNION ALL SELECT 'Independence Day observed', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2020-07-03'
UNION ALL SELECT 'Independence Day', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2020-07-04'
UNION ALL SELECT 'Labor Day', 'Labor Day is a federal holiday in the United States. It gives workers a day of rest and it celebrates their contribution to the American economy.', '2020-09-07'
UNION ALL SELECT 'Columbus Day', 'Columbus Day celebrates 15th century explorer Christopher Columbus''s arrival in America in 1492.', '2020-10-12'
UNION ALL SELECT 'Veterans Day', 'Veterans Day in the USA is a holiday to honor all who have served in the United States Military Services.', '2020-11-11'
UNION ALL SELECT 'Thanksgiving Day', 'Thanksgiving Day in the United States is traditionally a holiday to give thanks for the food collected at the end of the harvest season.', '2020-11-26'
UNION ALL SELECT 'Christmas Day', 'Christmas Day celebrates Jesus Christ''s birth.', '2020-12-25'
UNION ALL SELECT 'New Year''s Day', 'New Year''s Day is the first day of the Gregorian calendar, which is widely used in many countries such as the USA.', '2021-01-01'
UNION ALL SELECT 'Martin Luther King Jr. Day', 'Martin Luther King Day marks the anniversary of the date of birth of the influential American civil right leader of the same name.', '2021-01-18'
UNION ALL SELECT 'Presidents'' Day', 'Washington''s Birthday, or Presidents'' Day, honors the life and work of the first president of the United States, George Washington.', '2021-02-15'
UNION ALL SELECT 'Memorial Day', 'Memorial Day commemorates all Americans who have died in military service for the United States.', '2021-05-31'
UNION ALL SELECT 'Independence Day', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2021-07-04'
UNION ALL SELECT 'Independence Day observed', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2021-07-05'
UNION ALL SELECT 'Labor Day', 'Labor Day is a federal holiday in the United States. It gives workers a day of rest and it celebrates their contribution to the American economy.', '2021-09-06'
UNION ALL SELECT 'Columbus Day', 'Columbus Day celebrates 15th century explorer Christopher Columbus''s arrival in America in 1492.', '2021-10-11'
UNION ALL SELECT 'Veterans Day', 'Veterans Day in the USA is a holiday to honor all who have served in the United States Military Services.', '2021-11-11'
UNION ALL SELECT 'Thanksgiving Day', 'Thanksgiving Day in the United States is traditionally a holiday to give thanks for the food collected at the end of the harvest season.', '2021-11-25'
UNION ALL SELECT 'Christmas Day', 'Christmas Day celebrates Jesus Christ''s birth.', '2021-12-25'
UNION ALL SELECT 'New Year''s Day', 'New Year''s Day is the first day of the Gregorian calendar, which is widely used in many countries such as the USA.', '2022-01-01'
UNION ALL SELECT 'Martin Luther King Jr. Day', 'Martin Luther King Day marks the anniversary of the date of birth of the influential American civil right leader of the same name.', '2022-01-17'
UNION ALL SELECT 'Presidents'' Day', 'Washington''s Birthday, or Presidents'' Day, honors the life and work of the first president of the United States, George Washington.', '2022-02-21'
UNION ALL SELECT 'Memorial Day', 'Memorial Day commemorates all Americans who have died in military service for the United States.', '2022-05-30'
UNION ALL SELECT 'Juneteenth National Independence Day', 'Commemorates the emancipation of slaves in the United States on the anniversary of the date on which emancipation was announced in Texas.', '2022-06-20'
UNION ALL SELECT 'Independence Day', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2022-07-04'
UNION ALL SELECT 'Labor Day', 'Labor Day is a federal holiday in the United States. It gives workers a day of rest and it celebrates their contribution to the American economy.', '2022-09-05'
UNION ALL SELECT 'Columbus Day', 'Columbus Day celebrates 15th century explorer Christopher Columbus''s arrival in America in 1492.', '2022-10-10'
UNION ALL SELECT 'Veterans Day', 'Veterans Day in the USA is a holiday to honor all who have served in the United States Military Services.', '2022-11-11'
UNION ALL SELECT 'Thanksgiving Day', 'Thanksgiving Day in the United States is traditionally a holiday to give thanks for the food collected at the end of the harvest season.', '2022-11-24'
UNION ALL SELECT 'Christmas Day', 'Christmas Day celebrates Jesus Christ''s birth.', '2022-12-25'
UNION ALL SELECT 'Christmas Day observed', 'Christmas Day celebrates Jesus Christ''s birth.', '2022-12-26'
UNION ALL SELECT 'New Year''s Day', 'New Year''s Day is the first day of the Gregorian calendar, which is widely used in many countries such as the USA.', '2023-01-01'
UNION ALL SELECT 'New Year''s Day observed', 'New Year''s Day is the first day of the Gregorian calendar, which is widely used in many countries such as the USA.', '2023-01-02'
UNION ALL SELECT 'Martin Luther King Jr. Day', 'Martin Luther King Day marks the anniversary of the date of birth of the influential American civil right leader of the same name.', '2023-01-16'
UNION ALL SELECT 'Presidents'' Day', 'Washington''s Birthday, or Presidents'' Day, honors the life and work of the first president of the United States, George Washington.', '2023-02-20'
UNION ALL SELECT 'Memorial Day', 'Memorial Day commemorates all Americans who have died in military service for the United States.', '2023-05-29'
UNION ALL SELECT 'Juneteenth National Independence Day', 'Commemorates the emancipation of slaves in the United States on the anniversary of the date on which emancipation was announced in Texas.', '2023-06-19'
UNION ALL SELECT 'Independence Day', 'On Independence Day, Americans celebrate the anniversary of publication of the Declaration of Independence from Great Britain in 1776.', '2023-07-04'
UNION ALL SELECT 'Labor Day', 'Labor Day is a federal holiday in the United States. It gives workers a day of rest and it celebrates their contribution to the American economy.', '2023-09-04'
UNION ALL SELECT 'Indigenous Peoples’ Day', 'Columbus Day celebrates 15th century explorer Christopher Columbus''s arrival in America in 1492.', '2023-10-09'
UNION ALL SELECT 'Veterans Day', 'Veterans Day in the USA is a holiday to honor all who have served in the United States Military Services.', '2023-11-10'
UNION ALL SELECT 'Thanksgiving Day', 'Thanksgiving Day in the United States is traditionally a holiday to give thanks for the food collected at the end of the harvest season.', '2023-11-23'
UNION ALL SELECT 'Christmas Day', 'Christmas Day celebrates Jesus Christ''s birth.', '2023-12-25'
)
insert into systemholiday (name, description, date, createdbyuserid)
  select h.name, h.description, h.date::date, u.id from holidays h join appuser u on 1=1 where u.username in ('everee.admin', 'admin.system');
