update payment set residentstate=null;
---------------------------------------
-- Update payment records with states
---------------------------------------
with paymentstates as (
    select
        paymentid,
        coalesce(taxstate, userstate) as state
    from (
        select
            paymentid,
            case
                when jurTaxState = '01' then 'AL'
                when jurTaxState = '02' then 'AK'
                when jurTaxState = '04' then 'AZ'
                when jurTaxState = '05' then 'AR'
                when jurTaxState = '06' then 'CA'
                when jurTaxState = '08' then 'CO'
                when jurTaxState = '09' then 'CT'
                when jurTaxState = '10' then 'DE'
                when jurTaxState = '11' then 'DC'
                when jurTaxState = '12' then 'FL'
                when jurTaxState = '13' then 'GA'
                when jurTaxState = '15' then 'HI'
                when jurTaxState = '16' then 'ID'
                when jurTaxState = '17' then 'IL'
                when jurTaxState = '18' then 'IN'
                when jurTaxState = '19' then 'IA'
                when jurTaxState = '20' then 'KS'
                when jurTaxState = '21' then 'KY'
                when jurTaxState = '22' then 'LA'
                when jurTaxState = '23' then 'ME'
                when jurTaxState = '24' then 'MD'
                when jurTaxState = '25' then 'MA'
                when jurTaxState = '26' then 'MI'
                when jurTaxState = '27' then 'MN'
                when jurTaxState = '28' then 'MS'
                when jurTaxState = '29' then 'MO'
                when jurTaxState = '30' then 'MT'
                when jurTaxState = '31' then 'NE'
                when jurTaxState = '32' then 'NV'
                when jurTaxState = '33' then 'NH'
                when jurTaxState = '34' then 'NJ'
                when jurTaxState = '35' then 'NM'
                when jurTaxState = '36' then 'NY'
                when jurTaxState = '37' then 'NC'
                when jurTaxState = '38' then 'ND'
                when jurTaxState = '39' then 'OH'
                when jurTaxState = '40' then 'OK'
                when jurTaxState = '41' then 'OR'
                when jurTaxState = '42' then 'PA'
                when jurTaxState = '72' then 'PR'
                when jurTaxState = '44' then 'RI'
                when jurTaxState = '45' then 'SC'
                when jurTaxState = '46' then 'SD'
                when jurTaxState = '47' then 'TN'
                when jurTaxState = '48' then 'TX'
                when jurTaxState = '49' then 'UT'
                when jurTaxState = '50' then 'VT'
                when jurTaxState = '51' then 'VA'
                when jurTaxState = '53' then 'WA'
                when jurTaxState = '54' then 'WV'
                when jurTaxState = '55' then 'WI'
                when jurTaxState = '56' then 'WY'
            end as taxstate,
            userstate
        from
        (
            -- Group by paymentId so each one has a single entry
            select
                p.id as paymentid,
                left(max(pt.jurisdictiontaxid),2) as jurTaxState,
                max(ua.state) as userState
            from payment p
            left join paymenttax pt
                on p.id=pt.paymentid
                and (pt.type='SIT' or pt.type='ER_SUTA')
                and pt.amount > 0
            left join employee e
                on e.id=p.employeeid
            left join useraddress ua
                on ua.userid=e.userid
                and ua.startdate <= p.fordate
                and (ua.enddate >= p.fordate or ua.enddate is null)
            where
                p.residentstate is null
            group by
                p.id
         ) paymentEntries
    ) paymentWithState
)
update payment
set residentstate=paymentstates.state
from paymentstates
where id = paymentstates.paymentid;
