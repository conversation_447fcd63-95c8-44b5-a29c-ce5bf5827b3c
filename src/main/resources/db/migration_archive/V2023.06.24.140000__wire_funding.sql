--------------------------------------------------------
-- Company Funding
--------------------------------------------------------
alter table companyfunding
    add column if not exists transferType text,
    add column if not exists payperiodid bigint references payperiod (id),
    alter column companybankaccountid drop not null,
    alter column accountnumber drop not null,
    alter column routingnumber drop not null,
    alter column accountname drop not null,
    alter column bankname drop not null;

-- less than 10,000 records in prod
with toupdate as (
select
    cf.id companyfundingid,
    a.payperiodid
from
    companyfunding cf
    join (select achfileid, max(payperiodid) payperiodid from achfilebatch where payperiodid is not null group by 1) a
        on a.achfileid=cf.achfileid
)
update companyfunding set payperiodid=tu.payperiodid from toupdate tu where companyfunding.id=tu.companyfundingid;

CREATE INDEX IF NOT EXISTS companyfunding_payperiodid_idx ON companyfunding (payperiodid);

UPDATE companyfunding set transfertype='ACH' WHERE transfertype is null;
ALTER TABLE companyfunding
    ALTER COLUMN transfertype SET NOT NULL,
    ALTER COLUMN transfertype SET DEFAULT 'ACH',
    ADD CONSTRAINT companyfunding_ach_ck check ( transfertype<>'ACH' or (companybankaccountid is not null AND accountnumber is not null AND routingnumber is not null AND accountname is not null AND bankname IS NOT NULL));

--------------------------------------------------------
-- JOURNAL ENTRY
--------------------------------------------------------
ALTER TABLE journalentry
    ADD COLUMN if not exists companyfundingid bigint;

ALTER TABLE journalentry
    DROP CONSTRAINT journalentry_cd_ach_check,
    ADD CONSTRAINT journalentry_cd_cf_ach_check check ( companydistributionid is not null or achfileid is not null or companyfundingid is not null);


--------------------------------------------------------
-- Company ACH Configuration
--------------------------------------------------------
ALTER TABLE company
    ADD COLUMN if not exists fundingTransferType text;

UPDATE company set fundingtransfertype='ACH' WHERE fundingtransfertype is null;
ALTER TABLE company ALTER COLUMN fundingtransfertype SET NOT NULL;
ALTER TABLE company ALTER COLUMN fundingtransfertype SET DEFAULT 'ACH';


--------------------------------------------------------
-- AccountTransaction
--------------------------------------------------------

ALTER TABLE accounttransaction
  ADD COLUMN IF NOT EXISTS companyfundingid bigint REFERENCES companyfunding (id),
  DROP CONSTRAINT accounttransaction_batch_cd_check,
  ADD CONSTRAINT accounttransaction_batch_cd_cf_check CHECK (companydistributionid IS NOT NULL OR achfilebatchid IS NOT NULL OR companyfundingid IS NOT NULL);

CREATE INDEX IF NOT EXISTS accounttransaction_companyfunding_idx ON accounttransaction (companyfundingid);
