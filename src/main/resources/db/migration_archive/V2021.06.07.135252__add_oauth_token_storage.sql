create table if not exists oauth_client_token
(
    authentication_id text primary key,
    token_id          text,
    token             bytea,
    user_name         text,
    client_id         text
);
create unique index if not exists oauth_client_token_token_id_ukey on oauth_client_token (token_id);
create index if not exists oauth_client_token_user_name_idx on oauth_client_token (user_name);

create table if not exists oauth_access_token
(
    authentication_id text primary key,
    token_id          text,
    token             bytea,
    user_name         text,
    client_id         text,
    authentication    bytea,
    refresh_token     text
);
create unique index if not exists oauth_access_token_token_id_ukey on oauth_access_token (token_id);
create index if not exists oauth_access_token_user_name_idx on oauth_access_token (user_name);

create table if not exists oauth_refresh_token
(
    token_id       text,
    token          bytea,
    authentication bytea
);
create unique index if not exists oauth_refresh_token_token_id_ukey on oauth_refresh_token (token_id);
