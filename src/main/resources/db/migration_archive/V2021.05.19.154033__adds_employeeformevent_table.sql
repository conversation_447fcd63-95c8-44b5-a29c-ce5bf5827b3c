CREATE TABLE IF NOT EXISTS employeeformevent (
  id                            BIGSERIAL PRIMARY KEY,
  createdat                     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  updatedat                     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  companyid                     BIGINT NOT NULL,
  employeeid                    BIGINT NOT NULL,
  formid                        BIGINT NOT NULL,
  formresponseid                BIGINT,
  formresponsefileid            BIGINT,
  eventoccurredat               TIMESTAMP WITHOUT TIME ZONE,
  userid                        BIGINT NOT NULL,
  eventtype                     text NOT NULL,
  CONSTRAINT employeeformevent_company_fk FOREIGN KEY (companyid) REFERENCES company (id),
  CONSTRAINT employeeformevent_employee_fk FOREIGN <PERSON>EY (employeeid) REFERENCES employee (id),
  CONSTRAINT employeeformevent_form_fk FOREIGN KEY (formid) REFERENCES form (id),
  CONSTRAINT employeeformevent_user_fk FOREI<PERSON><PERSON> (userid) REFERENCES appuser (id),
  CONSTRAINT employeeformevent_formresponse_fk FOREI<PERSON><PERSON> (formresponseid) REFERENCES formresponse (id),
  CONSTRAINT employeeformevent_formresponsefile_fk FOREIGN KEY (formresponsefileid) REFERENCES formresponsefile (id) ON DELETE CASCADE
);
