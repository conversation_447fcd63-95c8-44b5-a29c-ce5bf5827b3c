create table if not exists approvalgroup
(
    id        bigserial not null primary key,
    companyid bigint    not null references company (id) on update cascade on delete cascade,
    name      text      not null,
    createdat timestamp without time zone default now(),
    updatedat timestamp without time zone default now(),
    unique (companyid, name)
);

create table if not exists approvalgroupapprover
(
    id              text   not null primary key generated always as ( userid::text || '.' || companyid::text || '.' || approvalgroupid::text ) stored,
    userid          bigint not null references appuser (id) on update cascade on delete cascade,
    companyid       bigint not null references company (id) on update cascade on delete cascade,
    approvalgroupid bigint not null references approvalgroup (id) on update cascade on delete cascade,
    approvalscopes  text   not null,
    createdat       timestamp without time zone default now(),
    updatedat       timestamp without time zone default now(),
    unique (userid, companyid, approvalgroupid)
);

alter table only employee
    add column approvalgroupid bigint default null references approvalgroup (id) on update cascade on delete set null;

create or replace function trigger_set_updatedat() returns trigger as
$$
begin
    new.updatedat = now();
    return new;
end;
$$ language plpgsql;

create trigger approvalgroup_set_timestamp
    before update
    on approvalgroup
    for each row
execute procedure trigger_set_updatedat();

create trigger approvalgroupapprover_set_timestamp
    before update
    on approvalgroupapprover
    for each row
execute procedure trigger_set_updatedat();
