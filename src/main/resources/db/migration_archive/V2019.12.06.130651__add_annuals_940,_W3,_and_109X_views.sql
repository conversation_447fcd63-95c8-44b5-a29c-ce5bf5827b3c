DROP VIEW IF EXISTS
  annualPaymentByType,
  annualPaymentByStateByType,
  annualPaymentByEmployeeByStateByType,
  annualF940,
  paymentIdsByYearStateCompany,
  annualFW3,
  annualStateF940Equivalent
;

/* https://www.irs.gov/pub/irs-pdf/f940.pdf*/
create or replace view annualPaymentByType as
  SELECT
    extract(year from payment.paydate) as year,
    company.id as companyId,
    tax.type as taxType,
    sum(payment.grossearnings) as totalPaymentsToAllEmployees,
    sum(tax.amount) as taxAmount,
    sum(tax.subjectWages) as taxSubjectWages
  from Payment payment
  inner join Company company on payment.companyId = company.id
  inner join PaymentTax tax on tax.paymentId = payment.id
  WHERE payment.status IN ('PAID', 'IMPORT')
  GROUP BY 1, 2, 3
;

/* https://www.irs.gov/pub/irs-pdf/f940sa.pdf */
create or replace view annualPaymentByStateByType as
  SELECT
    extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    address.state as state,
    tax.type as taxType,
    sum(payment.grossearnings) as totalPaymentsToAllEmployees,
    sum(tax.amount) as taxAmount,
    sum(tax.subjectWages) as taxSubjectWages
  from Payment payment
  inner join Employee employee on payment.employeeId = employee.id
  inner join Appuser appuser on employee.userId = appuser.id
  inner join UserAddress address on address.userId = appuser.id
  inner join PaymentTax tax on tax.paymentId = payment.id
  WHERE
    address.startDate <= payment.payDate
    AND (address.endDate IS NULL OR address.endDate >= payment.payDate)
    AND payment.status IN ('PAID', 'IMPORT')
  GROUP BY 1, 2, 3, 4
;

/* https://www.irs.gov/pub/irs-pdf/f1099msc.pdf */
create or replace view annualPaymentByEmployeeByStateByType as
  SELECT
    extract(year from payment.paydate) as year,
    payment.companyId as companyId,
    payment.employeeId AS employeeId,
    address.state as state,
    tax.type as taxType,
    sum(payment.grossearnings) as totalPaymentsToAllEmployees,
    sum(tax.amount) as taxAmount,
    sum(tax.subjectWages) as taxSubjectWages
  from Payment payment
  inner join Employee employee on payment.employeeId = employee.id
  inner join Appuser appuser on employee.userId = appuser.id
  inner join UserAddress address on address.userId = appuser.id
  inner join PaymentTax tax on tax.paymentId = payment.id
  WHERE
    address.startDate <= payment.payDate
    AND (address.endDate IS NULL OR address.endDate >= payment.payDate)
    AND payment.status IN ('PAID', 'IMPORT')
  GROUP BY 1, 2, 3, 4, 5
;

/* https://www.irs.gov/pub/irs-pdf/f940.pdf*/
create or replace view annualF940 as
    select
      *,
      ROUND(question7TotalTaxableFutaWages * 0.006, 2) as question8FutaTaxBeforeAdjustments,
      ROUND(question7TotalTaxableFutaWages * 0.054, 2) as question9
      /*  (Amount of taxable FUTA wages that were paid that were excluded from state unemployemnt tax) as amountOfTaxableFutaWagesExcludedFromSuta */
      /*
      (SELECT ? from Worksheet) as question 10
      (SELECT ? from Schedule A) as question 11
      (question8FutaTaxBeforeAdjustments + question9 + question10 + question11) as totalFutaTaxAfterAdjustments,

      create view for questions 16*/
      FROM (
      select
        *,
        (totalPaymentsToAllEmployees - question6Subtotal) as question7TotalTaxableFutaWages
        FROM (
        select
          *,
          (amountNotSubjectToFuta + totalPaymentsToEachEmployeeInExcessOf7000) as question6Subtotal
          FROM (
          select
            extract(year from payment.paydate) as year,
            payment.companyId as companyId,
            company.federalEin as federalEin,
            company.displayName as displayName,
            company.legalEntityName as legalEntityName,
            company.state as state,
            company.zip as zip,
            (SELECT (annualPaymentByType.totalpaymentstoallemployees - annualPaymentByType.taxSubjectWages) AS amountNotSubjectToFuta
              FROM AnnualPaymentByType annualPaymentByType
              WHERE annualPaymentByType.taxType = 'ER_FUTA' and annualPaymentByType.companyId = company.id),
            (SELECT (annualPaymentByType.totalpaymentstoallemployees - annualPaymentByType.taxSubjectWages) AS amountNotSubjectToSuta
              FROM AnnualPaymentByType annualPaymentByType
              WHERE annualPaymentByType.taxType = 'ER_SUTA' and annualPaymentByType.companyId = company.id),
            sum(payment.grossearnings) as totalPaymentsToAllEmployees,
            sum(CASE WHEN payment.grossearnings > 7000 THEN payment.grossearnings ELSE 0 END) as totalPaymentsToEachEmployeeInExcessOf7000
        from Payment payment
        inner join Company company on payment.companyId = company.id
        WHERE payment.status IN ('PAID', 'IMPORT')
        group by 1, 2, 3, 4, 5, 6, 7, 8, 9
      ) as annual940InnerTable1
    ) as annual940InnerTable2
  ) as annual940InnerTable3
;

/* https://www.irs.gov/pub/irs-pdf/fw3.pdf */
create or replace view paymentIdsByYearStateCompany AS
SELECT
    extract(year from payment.paydate) as year,
    company.id as companyId,
    address.state as recipientState,
    array_agg(DISTINCT(payment.id) ORDER BY payment.id) as paymentIds
  from Payment payment
  inner join Company company on payment.companyId = company.id
  inner join Employee employee1 on payment.employeeId = employee1.id
  inner join Appuser appuser on employee1.userId = appuser.id
  inner join UserAddress address on address.userId = appuser.id
  WHERE
    address.startDate <= payment.payDate
    AND (address.endDate IS NULL OR address.endDate >= payment.payDate)
    AND payment.status IN ('PAID', 'IMPORT')
  GROUP BY 1, 2, 3
;

/* https://www.irs.gov/pub/irs-pdf/fw3.pdf */
create or replace view annualFW3 AS
  SELECT paymentAgg.year, paymentAgg.companyId, paymentAgg.recipientState,
    (SELECT sum(payment1.grossearnings)
      FROM Payment payment1
      WHERE payment1.id IN (select(unnest(paymentAgg.paymentIds)))) as wagesTipsAndOtherCompensation,
    (SELECT sum(tax2.amount)
      FROM PaymentTax tax2
      WHERE tax2.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax2.type = 'FIT') as federalIncomeTaxWithheld,
    (SELECT sum(tax3.subjectWages) FROM Payment payment3
      INNER JOIN PaymentTax tax3 ON tax3.paymentId = payment3.id
      INNER JOIN PaymentEarning earning3 ON earning3.paymentId = payment3.id
      WHERE tax3.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax3.type IN ('FICA', 'ER_FICA')
      AND earning3.type != 'TIPS') as socialSecurityWages,
    (SELECT sum(tax4.amount)
      FROM PaymentTax tax4
      WHERE tax4.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax4.type IN ('FICA', 'ER_FICA')) as socialSecurityTaxWithheld,
    (SELECT sum(tax5.subjectWages)
      FROM PaymentTax tax5
      WHERE tax5.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax5.type IN ('MEDI', 'ER_MEDI', 'MEDI2')) as medicareWagesAndTips,
    (SELECT sum(tax6.amount)
      FROM PaymentTax tax6
      WHERE tax6.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax6.type IN ('MEDI', 'ER_MEDI', 'MEDI2')) as medicareTaxWithheld,
    (SELECT sum(tax7.subjectWages) FROM Payment payment3
      INNER JOIN PaymentTax tax7 ON tax7.paymentId = payment3.id
      INNER JOIN PaymentEarning earning3 ON earning3.paymentId = payment3.id
      WHERE tax7.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax7.type IN ('FICA', 'ER_FICA')
      AND earning3.type = 'TIPS') as socialSecurityTips,
    (SELECT sum(tax8.subjectWages)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateWagesTipsEtc,
    (SELECT sum(tax8.amount)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateIncomeTaxWithheld,
    (SELECT count(distinct(payment12.employeeId))
      FROM Payment payment12
      WHERE payment12.id IN (select(unnest(paymentAgg.paymentIds)))) as countOfEmployees
    FROM paymentIdsByYearStateCompany paymentAgg;

create or replace view annualStateF940Equivalent AS
  SELECT paymentAgg.year,
  paymentAgg.companyId,
  paymentAgg.recipientState,
    (SELECT sum(payment1.grossearnings)
      FROM Payment payment1
      WHERE payment1.id IN (select(unnest(paymentAgg.paymentIds)))) as wagesTipsAndOtherCompensation,
    (SELECT sum(tax2.amount)
      FROM PaymentTax tax2
      WHERE tax2.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax2.type = 'FIT') as federalIncomeTaxWithheld,
    (SELECT sum(tax3.subjectWages) FROM Payment payment3
      INNER JOIN PaymentTax tax3 ON tax3.paymentId = payment3.id
      INNER JOIN PaymentEarning earning3 ON earning3.paymentId = payment3.id
      WHERE tax3.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax3.type IN ('FICA', 'ER_FICA')
      AND earning3.type != 'TIPS') as socialSecurityWages,
    (SELECT sum(tax4.amount)
      FROM PaymentTax tax4
      WHERE tax4.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax4.type IN ('FICA', 'ER_FICA')) as socialSecurityTaxWithheld,
    (SELECT sum(tax5.subjectWages)
      FROM PaymentTax tax5
      WHERE tax5.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax5.type IN ('MEDI', 'ER_MEDI', 'MEDI2')) as medicareWagesAndTips,
    (SELECT sum(tax6.amount)
      FROM PaymentTax tax6
      WHERE tax6.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax6.type IN ('MEDI', 'ER_MEDI', 'MEDI2')) as medicareTaxWithheld,
    (SELECT sum(tax7.subjectWages) FROM Payment payment3
      INNER JOIN PaymentTax tax7 ON tax7.paymentId = payment3.id
      INNER JOIN PaymentEarning earning3 ON earning3.paymentId = payment3.id
      WHERE tax7.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax7.type IN ('FICA', 'ER_FICA')
      AND earning3.type = 'TIPS') as socialSecurityTips,
    (SELECT sum(tax8.subjectWages)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateWagesTipsEtc,
    (SELECT sum(tax8.amount)
      FROM PaymentTax tax8
      WHERE tax8.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax8.type = 'SIT') as stateIncomeTaxWithheld,
    (SELECT count(distinct(payment9.employeeId))
      FROM Payment payment9
      WHERE payment9.id IN (select(unnest(paymentAgg.paymentIds)))) as countOfEmployees,
    (SELECT sum(tax10.amount)
      FROM PaymentTax tax10
      WHERE tax10.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax10.type IN ('ER_SUTA')) as stateUnemploymentWithheld,
    (SELECT sum(tax11.amount)
      FROM PaymentTax tax11
      WHERE tax11.paymentId IN (select(unnest(paymentAgg.paymentIds)))
      AND tax11.type IN ('ER_FUTA')) as federalUnemploymentWithheld,
    (SELECT sum(CASE WHEN payment12.grossearnings > 7000 THEN payment12.grossearnings ELSE 0 END)
      FROM Payment payment12
      WHERE payment12.id IN (select(unnest(paymentAgg.paymentIds)))) as totalPaymentsToEachEmployeeInExcessOf7000,
    (SELECT sum(payment13.grossearnings)
      FROM Payment payment13
      WHERE payment13.id IN (select(unnest(paymentAgg.paymentIds)))) as totalPaymentsToAllEmployees
    FROM paymentIdsByYearStateCompany paymentAgg;
