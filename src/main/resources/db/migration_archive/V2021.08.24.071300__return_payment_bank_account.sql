insert into achbankaccount (accountname, bankname, accountnumber, routingnumber, createdat, updatedat)
 SELECT 'Return Payments (TAB)', 'TAB Trans Alliance Bank', '*********', '*********', current_timestamp, current_timestamp
    WHERE NOT EXISTS (select id from achbankaccount where accountnumber='*********');

ALTER TABLE company ADD COLUMN IF NOT EXISTS returnpaymentlockboxaccountid bigint;

UPDATE company SET returnpaymentlockboxaccountid = 12 where returnpaymentlockboxaccountid is null;

ALTER TABLE company ALTER COLUMN returnpaymentlockboxaccountid SET NOT NULL;

ALTER TABLE company ADD CONSTRAINT company_returnpaymentlockboxaccountid_fk FOREIGN KEY
    (returnpaymentlockboxaccountid) REFERENCES achbankaccount;
