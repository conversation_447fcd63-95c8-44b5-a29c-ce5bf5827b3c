DROP VIEW IF EXISTS ptopolicycoveredemployee;
-- ptopolicycoveredemployee waiting period logic fix

CREATE OR REPLACE VIEW ptopolicycoveredemployee
AS

  SELECT MD5( VARIADIC ARRAY [ep.id, pp.id] :: Text )                                     AS id,
         e.companyid,
         e.id                                                                             AS employeeid,
         ep.id                                                                            AS positionid,
         pp.id                                                                            AS policyid,
         pp.type                                                                          AS policytype,
         -- Add all applicable tenure bonuses to either the workers overridden values, or the base policy values
         -- or assign effectiveptoaccrualrate to 0:0 if still in policy waiting period
         COALESCE(
                 CASE
                   WHEN ( DATE_PART( 'day', NOW( ) - e.startDate ) ):: INTEGER < pp.waitingperiod
                     THEN '0.0:0.0'
                 END,
                 CASE
                   WHEN SUM( pt.accrualrateincrease ) IS NULL THEN e.overrideptoaccrualrate
                   ELSE SPLIT_PART( e.overrideptoaccrualrate, ':', 1 ) || ':' ||
                        CAST( SPLIT_PART( e.overrideptoaccrualrate, ':', 2 ) AS NUMERIC ) +
                        SUM( pt.accrualrateincrease )
                 END,
                 CASE
                   WHEN SUM( pt.accrualrateincrease ) IS NULL THEN pp.defaultaccrualrate
                   ELSE
                         SPLIT_PART( pp.defaultaccrualrate, ':', 1 ) || ':' ||
                         CAST( SPLIT_PART( pp.defaultaccrualrate, ':', 2 ) AS NUMERIC ) + SUM( pt.accrualrateincrease )
                 END
           )                                                                              AS effectiveptoaccrualrate,
         DATE( GREATEST( pp.startdate, ( e.startdate +
                                         MAKE_INTERVAL( 0, 0, 0, pp.waitingperiod ) ) ) ) AS effectiveaccrualdate,
         GREATEST( pp.startdate, ep.startdate )                                           AS startdate,
         LEAST( pp.enddate, ep.enddate )                                                  AS enddate,
         -- Add all tenure bonuses to either the workers overridden values, or the base policy values
         COALESCE(
                 CASE
                   WHEN SUM( pt.maxhoursincrease ) IS NULL THEN e.overrideptomaxhours
                   ELSE e.overrideptomaxhours + SUM( pt.maxhoursincrease )
                 END,
                 CASE
                   WHEN SUM( pt.maxhoursincrease ) IS NULL THEN pp.defaultptomaxhours
                   ELSE pp.defaultptomaxhours + SUM( pt.maxhoursincrease )
                 END
           )                                                                              AS effectiveptomaxhours,
         pp.accrualpolicy,
         ( DATE_PART( 'YEAR', AGE( NOW( ), e.startdate ) ) ):: INTEGER                    AS yearsemployed,
         pp.defaultaccrualrate                                                            AS baseaccrualrate,
         pp.defaultptomaxhours                                                            AS basemaxhours,
         e.overrideptoaccrualrate                                                         AS individualaccrualrate,
         e.overrideptomaxhours                                                            AS individualmaxhours
  FROM employee e
         JOIN      company c ON e.companyid = c.id
         JOIN      ptopolicy pp ON e.companyid = pp.companyid
         JOIN      employeeposition ep ON e.id = ep.employeeid
                     -- Join on all the PTO Tenure Bonuses that they have reached
         LEFT JOIN ptotenurerule pt ON pt.id IN ( SELECT id
                                                  FROM ptotenurerule inner_pt
                                                  WHERE inner_pt.ptopolicyid = pp.id
                                                    AND ( DATE_PART( 'YEAR', AGE( NOW( ), e.startdate ) ) ) >= inner_pt.years
                                                  ORDER BY inner_pt.years DESC )
  WHERE "overlaps"( timezone( c.timezone, pp.startdate::timestamp WITH TIME ZONE ),
                    timezone( c.timezone, COALESCE( pp.enddate, 'infinity'::date )::timestamp WITH TIME ZONE ),
                    timezone( c.timezone, ep.startdate::timestamp WITH TIME ZONE ),
                    timezone( c.timezone, COALESCE( ep.enddate, 'infinity'::date )::timestamp WITH TIME ZONE ) )
    AND e.employmenttype = 'EMPLOYEE'::text
    AND ( pp.salaryeligible = TRUE AND ep.wagetype = 'SALARY'::text OR
          pp.fulltimehourlyeligible = TRUE AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND
          ep.wagetype = 'HOURLY'::text OR pp.parttimehourlyeligible = TRUE AND ep.wagetype = 'HOURLY'::text )
  GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate, ep.id, ep.startdate, ep.enddate;



DROP VIEW IF EXISTS ptobalance;
-- ptobalance contains an embedded ptopolicycoveredemployee view

CREATE OR REPLACE VIEW ptobalance AS
  (

  WITH ptotransaction_cte AS ( SELECT p.fordate                                       AS date,
                                      a.policyid,
                                      'WITHDRAWAL'::text                              AS type,
                                      0::numeric - a.withdrawnhours                   AS hours,
                                      p.status = ANY
                                      ( ARRAY ['SUBMITTED'::character varying::text,
                                        'PAID'::character varying::text,
                                        'PREPARED_FOR_FUNDING_REQUEST'::character varying::text,
                                        'FUNDING_REQUESTED'::character varying::text,
                                        'FUNDED'::character varying::text] )          AS finalized,
                                      a.type                                          AS timeofftype,
                                      p.companyid,
                                      p.employeeid,
                                      MD5( CONCAT( 'WITHDRAWAL'::text, p.id::text ) ) AS id
                               FROM paymentptotransaction a
                                      JOIN payment p ON p.id = a.paymentid AND p.status <> 'DELETED'::text
                               UNION ALL
                               SELECT a.date,
                                      a.policyid,
                                      'ADJUSTMENT'::text                              AS type,
                                      a.hours,
                                      TRUE                                            AS finalized,
                                      a.type                                          AS timeofftype,
                                      e.companyid,
                                      a.employeeid,
                                      MD5( CONCAT( 'ADJUSTMENT'::text, a.id::text ) ) AS id
                               FROM ptobalanceadjustment a
                                      JOIN employee e ON e.id = a.employeeid
                               UNION ALL
                               SELECT p.fordate                                    AS date,
                                      a.policyid,
                                      'ACCRUAL'::text                              AS type,
                                      a.accruedhours                               AS hours,
                                      p.status = ANY
                                      ( ARRAY ['SUBMITTED'::character varying::text,
                                        'PAID'::character varying::text,
                                        'PREPARED_FOR_FUNDING_REQUEST'::character varying::text,
                                        'FUNDING_REQUESTED'::character varying::text,
                                        'FUNDED'::character varying::text] )       AS finalized,
                                      a.type                                       AS timeofftype,
                                      p.companyid,
                                      p.employeeid,
                                      MD5( CONCAT( 'ACCRUAL'::text, p.id::text ) ) AS id
                               FROM paymentptotransaction a
                                      JOIN payment p ON p.id = a.paymentid AND p.status <> 'DELETED'::text ),

       ptopolicycoveredemployee_cte
         AS
         ( SELECT MD5( VARIADIC ARRAY [ep.id, pp.id] :: Text )                                     AS id,
                  e.companyid,
                  e.id                                                                             AS employeeid,
                  ep.id                                                                            AS positionid,
                  pp.id                                                                            AS policyid,
                  pp.type                                                                          AS policytype,
                  -- Add all applicable tenure bonuses to either the workers overridden values, or the base policy values
                  -- or assign effectiveptoaccrualrate to 0 if still in policy waiting period
                  COALESCE(
                          CASE
                            WHEN ( DATE_PART( 'day', NOW( ) - e.startDate ) ):: INTEGER < pp.waitingperiod
                              THEN '0.0:0.0'
                          END,
                          CASE
                            WHEN SUM( pt.accrualrateincrease ) IS NULL THEN e.overrideptoaccrualrate
                            ELSE SPLIT_PART( e.overrideptoaccrualrate, ':', 1 ) || ':' ||
                                 CAST( SPLIT_PART( e.overrideptoaccrualrate, ':', 2 ) AS NUMERIC ) +
                                 SUM( pt.accrualrateincrease )
                          END,
                          CASE
                            WHEN SUM( pt.accrualrateincrease ) IS NULL THEN pp.defaultaccrualrate
                            ELSE
                                  SPLIT_PART( pp.defaultaccrualrate, ':', 1 ) || ':' ||
                                  CAST( SPLIT_PART( pp.defaultaccrualrate, ':', 2 ) AS NUMERIC ) +
                                  SUM( pt.accrualrateincrease )
                          END
                    )                                                                              AS effectiveptoaccrualrate,
                  DATE( GREATEST( pp.startdate, ( e.startdate +
                                                  MAKE_INTERVAL( 0, 0, 0, pp.waitingperiod ) ) ) ) AS effectiveaccrualdate,
                  GREATEST( pp.startdate, ep.startdate )                                           AS startdate,
                  LEAST( pp.enddate, ep.enddate )                                                  AS enddate,
                  -- Add all tenure bonuses to either the workers overridden values, or the base policy values
                  COALESCE(
                          CASE
                            WHEN SUM( pt.maxhoursincrease ) IS NULL THEN e.overrideptomaxhours
                            ELSE e.overrideptomaxhours + SUM( pt.maxhoursincrease )
                          END,
                          CASE
                            WHEN SUM( pt.maxhoursincrease ) IS NULL THEN pp.defaultptomaxhours
                            ELSE pp.defaultptomaxhours + SUM( pt.maxhoursincrease )
                          END
                    )                                                                              AS effectiveptomaxhours,
                  pp.accrualpolicy,
                  ( DATE_PART( 'YEAR', AGE( NOW( ), e.startdate ) ) ):: INTEGER                    AS yearsemployed,
                  pp.defaultaccrualrate                                                            AS baseaccrualrate,
                  pp.defaultptomaxhours                                                            AS basemaxhours,
                  e.overrideptoaccrualrate                                                         AS individualaccrualrate,
                  e.overrideptomaxhours                                                            AS individualmaxhours
           FROM employee e
                  JOIN      company c ON e.companyid = c.id
                  JOIN      ptopolicy pp ON e.companyid = pp.companyid
                  JOIN      employeeposition ep ON e.id = ep.employeeid
                              -- Join on all the PTO Tenure Bonuses that they have reached
                  LEFT JOIN ptotenurerule pt ON pt.id IN ( SELECT id
                                                           FROM ptotenurerule inner_pt
                                                           WHERE inner_pt.ptopolicyid = pp.id
                                                             AND ( DATE_PART( 'YEAR', AGE( NOW( ), e.startdate ) ) ) >= inner_pt.years
                                                           ORDER BY inner_pt.years DESC )
           WHERE "overlaps"( timezone( c.timezone, pp.startdate::timestamp WITH TIME ZONE ),
                             timezone( c.timezone, COALESCE( pp.enddate, 'infinity'::date )::timestamp WITH TIME ZONE ),
                             timezone( c.timezone, ep.startdate::timestamp WITH TIME ZONE ),
                             timezone( c.timezone,
                                       COALESCE( ep.enddate, 'infinity'::date )::timestamp WITH TIME ZONE ) )
             AND e.employmenttype = 'EMPLOYEE'::text
             AND ( pp.salaryeligible = TRUE AND ep.wagetype = 'SALARY'::text OR
                   pp.fulltimehourlyeligible = TRUE AND ep.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold AND
                   ep.wagetype = 'HOURLY'::text OR pp.parttimehourlyeligible = TRUE AND ep.wagetype = 'HOURLY'::text )
           GROUP BY e.companyid, e.id, pp.id, pp.startdate, pp.enddate, ep.id, ep.startdate, ep.enddate )

  SELECT MD5( CONCAT( e.id::text, COALESCE( t.timeofftype, 'PAID_TIME_OFF'::text ) ) )         AS id,
         e.companyid,
         e.id                                                                                  AS employeeid,


         COALESCE( t.timeofftype, 'PAID_TIME_OFF'::text )                                      AS timeofftype,
         COALESCE( SUM(
                           CASE
                             WHEN p.type = 'FLEXIBLE'::text AND t.finalized = TRUE THEN t.hours
                             ELSE NULL::numeric
                           END ),
                   0::numeric )                                                                AS currenthours,
         COALESCE( SUM(
                           CASE
                             WHEN p.type = 'FLEXIBLE'::text AND t.finalized = FALSE THEN t.hours
                             ELSE NULL::numeric
                           END ),
                   0::numeric )                                                                AS pendinghours,
         COALESCE( SUM(
                           CASE
                             WHEN p.type = 'FLEXIBLE'::text THEN t.hours
                             ELSE NULL::numeric
                           END ),
                   0::numeric )                                                                AS availablehours,


         ( EXISTS( SELECT 1
                   FROM ptopolicycoveredemployee_cte ce
                   WHERE ce.employeeid = e.id
                     AND ce.policytype = 'UNLIMITED'::text
                     AND ce.startdate <= timezone( c.timezone, CURRENT_TIMESTAMP )::date
                     AND ( ce.enddate IS NULL OR
                           ce.enddate >= timezone( c.timezone, CURRENT_TIMESTAMP )::date ) ) ) AS unlimited,


         ( SELECT ce.effectiveptoaccrualrate
           FROM ptopolicycoveredemployee_cte ce
           WHERE ce.employeeid = e.id
             AND ce.startdate <= timezone( c.timezone, CURRENT_TIMESTAMP )::date
             AND ( ce.enddate IS NULL OR ce.enddate >= timezone( c.timezone, CURRENT_TIMESTAMP )::date ) )
                                                                                               AS effectiveptoaccrualrate,
         ( SELECT ce.effectiveaccrualdate
           FROM ptopolicycoveredemployee_cte ce
           WHERE ce.employeeid = e.id
             AND ce.startdate <= timezone( c.timezone, CURRENT_TIMESTAMP )::date
             AND ( ce.enddate IS NULL OR ce.enddate >= timezone( c.timezone, CURRENT_TIMESTAMP )::date ) )
                                                                                               AS effectiveaccrualdate,
         ( SELECT ce.effectiveptomaxhours
           FROM ptopolicycoveredemployee_cte ce
           WHERE ce.employeeid = e.id
             AND ce.startdate <= timezone( c.timezone, CURRENT_TIMESTAMP )::date
             AND ( ce.enddate IS NULL OR ce.enddate >= timezone( c.timezone, CURRENT_TIMESTAMP )::date ) )
                                                                                               AS effectiveptomaxhours,
         ( SELECT ce.policyid
           FROM ptopolicycoveredemployee_cte ce
           WHERE ce.employeeid = e.id
             AND ce.startdate <= timezone( c.timezone, CURRENT_TIMESTAMP )::date
             AND ( ce.enddate IS NULL OR ce.enddate >= timezone( c.timezone, CURRENT_TIMESTAMP )::date ) )
                                                                                               AS policyid
  FROM employee e
         JOIN      company c ON c.id = e.companyid
         LEFT JOIN ptotransaction_cte t ON t.employeeid = e.id
         LEFT JOIN ptopolicy p ON p.id = t.policyid
  GROUP BY c.id, e.id, ( COALESCE( t.timeofftype, 'PAID_TIME_OFF'::text ) )

    );
