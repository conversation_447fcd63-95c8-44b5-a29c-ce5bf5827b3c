DROP VIEW employmentstatuschange;

CREATE VIEW employmentstatuschange AS SELECT to_hex(VARIADIC ARRAY[concat((e.id)::text, ':', e.startdate,':HIRE')]) AS id,
    e.id AS employeeid,
    concat_ws(' '::text, u.firstname, u.middlename, u.lastname) AS employeefullname,
    u.taxpayeridentifier,
    u.dateofbirth,
    u.email,
    a.line1,
    a.line2,
    a.city,
    a.state,
    a.postalcode,
    c.id AS companyid,
    c.displayname AS companyname,
    e.employmenttype,
    e.startdate AS statuschangedate,
    'HIRE'::text AS statuschangetype,
    recordedHireAt AS statusChangeRecordedAt
   FROM (((employee e
     JOIN appuser u ON ((u.id = e.userid)))
     LEFT JOIN useraddress a ON (((u.id = a.userid) AND (e.startdate >= a.startdate) AND (e.startdate <= COALESCE(a.enddate, 'infinity'::date)))))
     JOIN company c ON ((c.id = e.companyid)))
  WHERE (e.startdate >= c.startdate)
UNION
 SELECT to_hex(VARIADIC ARRAY[concat((e.id)::text, ':', e.enddate,':SEPARATION')]) AS id,
    e.id AS employeeid,
    concat_ws(' '::text, u.firstname, u.middlename, u.lastname) AS employeefullname,
    u.taxpayeridentifier,
    u.dateofbirth,
    u.email,
    a.line1,
    a.line2,
    a.city,
    a.state,
    a.postalcode,
    c.id AS companyid,
    c.displayname AS companyname,
    e.employmenttype,
    e.enddate AS statuschangedate,
    'SEPARATION'::text AS statuschangetype,
    recordedSeparationAt AS statusChangeRecordedAt
   FROM (((employee e
     JOIN appuser u ON ((u.id = e.userid)))
     LEFT JOIN useraddress a ON (((u.id = a.userid) AND (e.enddate >= a.startdate) AND (e.enddate <= COALESCE(a.enddate, 'infinity'::date)))))
     JOIN company c ON ((c.id = e.companyid)))
  WHERE (e.enddate >= c.startdate);
