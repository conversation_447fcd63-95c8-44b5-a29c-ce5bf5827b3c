spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
logging.level.root=INFO
logging.level.com.everee=INFO
logging.level.com.intuit=DEBUG
log4j.format.msg.no.lookups=true
# We handle SQL exceptions via application-level exception monitoring. This is too noisy.
logging.level.org.hibernate.engine.jdbc.spi.SqlExceptionHelper=off
app.browser-support.cors.max-age=60
app.everee-accounting-sync-method=AUTOMATIC
app.mef.etin=71164
app.payments.import-requests-queue-name=payments-import-requests-canary.fifo
app.payments.calc-requests-queue-name=payments-calc-requests-canary.fifo
app.payments.cron-queue-name=payment-engine-cron-canary
app.payments.calc-schedule-queue-name=payments-calc-schedule-canary.fifo
app.payments.funding-distribution-queue-name=payments-funding-distribution-canary.fifo
app.notification.results-queue-name=notification-results-canary
app.notification.requests-queue-name=notification-requests-canary.fifo
app.notification.sms-dry-run-enabled=false
app.notification.push-dry-run-enabled=false
app.notification.email-dry-run-enabled=false
app.notification.allowed-email-types=SEND_BEGIN_ONBOARDING,WORKER_CLAIMED_BUT_ONBOARDINGIN_COMPLETE,SET_USER_PASSWORD,EMAILED_REPORT_NOTIFICATION,COMPANY_DOCUMENT_SIGNATURES,COMPANY_DOCUMENT_SIGNATURE_FROM_WORKER
app.notification.tax.email=<EMAIL>
app.notification.tabapay-balance-low.email-addresses=<EMAIL>,<EMAIL>,<EMAIL>
app.storage.s3BucketCustomerFacing=r365-everee-uploads-canary
app.storage.s3BucketEvereeInternal=r365-everee-protected-files-canary
app.storage.s3BucketPublic=r365-everee-uploads-public-canary
app.taxes.calc-filing-instances-queue-name=taxes-calc-filing-instances-canary.fifo
app.taxes.filling-transmitter-company-id=140
app.taxes.filling-transmitter-contact-app-user-id=1056
app.taxes.fire-filing-transmitter-control-code=37U32
# Symmetry Tax Engine
app.symmetry.base-url=https://ste.symmetry.com
#Employee Navigator
app.employeenavigator.dry-run-enabled=false
app.quickbooks.client-id=ABFPfzGEk8FIAlEpWHbKTEbJiaCYrnGxtRmu2W7dDUtlhG1Sc9
app.quickbooks.redirect-uri=https://payrollapp.restaurant365.com/integrations/QUICKBOOKS
app.quickbooks.redirect-uri-callback=https://k2-api-canary.restaurant365.com/integration/quickbooks/v1/store-connection
app.quickbooks.accounting-api-host=https://quickbooks.api.intuit.com
app.quickbooks.environment=PRODUCTION
app.everee-company-id=140
app.everee.company-id=140
# FinOps
app.finops.finops-sns-topic-arn=arn:aws:sns:us-east-2:************:finops-prod
# Google APIs
app.google.ach-report-sheet-id=1c48pWrWbBaEmhA7hiGzsCHrzkaVY5CGj3DkOtNP0URg
app.google.credit-requests-sheet-id=1lVsxR3iDKjT5mRM8hXi-g-3a0sppEctEkXmUMBcgUaQ
app.google.funding-agreements-drive-id=1qN2CGyAQ26Rw57QLxQk8vsRb-LKqH0nv
app.google.service-account-key-base64=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
## Global Settings ##
app.settings.configurable-pay-periods-enabled=true

app.notification.legacy-email-template-id=d-1b139b73c2564090a879d3261e80f0d7
app.notification.pay-yay-notification-email-template-id=d-57e8692df16c415fa3e5813845cb4ca2
app.notification.transactional-install-apps-email-template-id=d-a76631d2db28421eb8be04e64524cca4
app.notification.transactional-no-action-email-template-id=d-4934dfb07f6f4f33b18e475764e26d2e
app.notification.transactional-single-action-email-template-id=d-57e8692df16c415fa3e5813845cb4ca2
app.notification.worker-begin-onboarding-email-notification-template-id=d-57e8692df16c415fa3e5813845cb4ca2

app.webhook.delivery.enabled=true
spring.datasource.url=*******************************************************************************************
app.link.api-base-url=https://k2-api-canary.restaurant365.com/
app.link.app-base-url=https://k2-canary.restaurant365.com/
# Finicity
app.finicity.production=true
app.finicity.customer-prefix=
app.finicity.partner-id=2445584202070
# Bancorp SFTP
app.paycard.cdd.sftp-host=ftpsf.ratbbk.biz
app.paycard.cdd.sftp-port=22
app.paycard.cdd.sftp-username=C01478EVE
app.paycard.cdd.pgp-key=-----BEGIN PGP PUBLIC KEY BLOCK-----\nVersion: IPWorks! OpenPGP v9.0\n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\npp/gGTgA30JL7anYZLFkNDal9seOakvH4+o19LdAxwOcPb0=\n=NadD\n-----END PGP PUBLIC KEY BLOCK-----

# R365 Integration
app.r365.payrollGatewayUri=https://payrollgateway.restaurant365.com
app.launchdarkly.sdk-key=****************************************

# Removed from Elastic Beanstalk
app.link.api.base.url=https://k2-api-canary.restaurant365.com
app.link.app.base.url=https://k2-canary.restaurant365.com
sentry.environment=prod
server.port=80
spring.profiles.active=canary
app.browser-support.cors.allowed-hosts=\
  https://payrollapp-canary.restaurant365.com,\
  https://k2.restaurant365.com,\
  https://k2-api-canary.restaurant365.com,\
  https://k2-canary.restaurant365.com,\
  https://bridge-canary.restaurant365.com
