spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
logging.level.=DEBUG
app.browser-support.cors.allowed-hosts=\
  http://localhost:3000,\
  http://localhost:3001,\
  https://app-test.everee.com,\
  https://test.everee.com,\
  https://bridge-test.everee.com,\
  https://admin-test.everee.com,\
  https://timeclock-test.everee.com
app.payments.import-requests-queue-name=payments-import-requests-test.fifo
app.payments.calc-requests-queue-name=payments-calc-requests-test.fifo
app.payments.cron-queue-name=payment-engine-cron-test
app.payments.funding-distribution-queue-name=payments-funding-distribution-test.fifo
app.payments.calc-schedule-queue-name=payments-calc-schedule-test.fifo
app.notification.results-queue-name=notification-results-test
app.notification.requests-queue-name=notification-requests-test.fifo
app.notification.sms-dry-run-enabled=true
app.notification.push-dry-run-enabled=false
app.notification.email-dry-run-enabled=true
app.storage.s3BucketCustomerFacing=everee-uploads-test
app.storage.s3BucketEvereeInternal=everee-protected-files-test
app.storage.s3BucketPublic=r365-everee-uploads-public-test
app.taxes.calc-filing-instances-queue-name=taxes-calc-filing-instances-test.fifo
app.link.api-base-url=https://api-test.everee.com/
app.link.app-base-url=https://app-test.everee.com/
app.link.pay-card-embedded-base-url=https://paycard.everee.com
# Symmetry Tax Engine
app.symmetry.base-url=https://ste-staging.symmetry.com
app.symmetry.auth-token=296qqxh5Rn8N9YRO9a8CAYiPTONcKlQqbdF0sCugPKQ4nf
app.employeenavigator.dry-run-enabled=false
app.quickbooks.redirect-uri=https://app-test.everee.com/settings/company-settings/integrations/QUICKBOOKS
app.quickbooks.redirect-uri-callback=https://api-test.everee.com/integration/quickbooks/v1/store-connection
app.finicity.customer-prefix=test
app.taxes.filling-transmitter-company-id=1
app.taxes.filling-transmitter-contact-app-user-id=1
# Galileo
app.galileo.api-url=https://api-everee.cv.gpsrv.com/intserv/4.0
app.galileo.api-login=3y2mNh-0320
app.galileo.provider-id=26514
app.galileo.program-id=5316
app.galileo.product-id=1968
app.galileo.pin-change-base-url=https://agserv-everee.cv.gpsrv.com/agserv/direct/pin/en_US/26514-0320
app.galileo.events.jwt-secret-key=d5g5g8f888fdtjykioighghhg5hf6gfg
app.galileo.events.ip-whitelist=************,*************,*************,**************,*************,*************
app.galileo.master-funding-account-id=placeholder
# Launchdarkly
app.launchdarkly.sdk-key=****************************************
# Google APIs
app.google.funding-agreements-drive-id=1o3Lz5HHDYhTsZimOVKEPMD2RiIMgSEKB

# Demo Company Deletion API Permission
app.delete-demo-company-api-enabled=true
