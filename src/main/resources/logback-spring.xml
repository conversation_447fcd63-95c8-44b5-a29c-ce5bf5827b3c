<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <property resource="application.properties"/>
  <contextName>${spring.application.name}</contextName>

  <!--  this is the non-json appender so dev logging can remain the same-->
  <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="json-logger" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
  </appender>

<!--  <appender name="Sentry" class="io.sentry.logback.SentryAppender">-->
<!--    <minimumEventLevel>WARN</minimumEventLevel>-->
<!--    <minimumBreadcrumbLevel>WARN</minimumBreadcrumbLevel>-->
<!--  </appender>-->


  <!-- Enable the appenders -->
  <root level="INFO">
    <springProfile name="local">
      <appender-ref ref="Console"/>
    </springProfile>

<!--    <springProfile name="dev, staging, prod">-->
<!--      <appender-ref ref="Sentry"/>-->
<!--    </springProfile>-->

    <springProfile name="dev, staging, prod, canary, staging-canary, prod-canary">
      <appender-ref ref="json-logger"/>
    </springProfile>
  </root>
</configuration>
