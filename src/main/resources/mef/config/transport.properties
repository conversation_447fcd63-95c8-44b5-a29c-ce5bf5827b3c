#Number of seconds to wait to establish a connection to the remote server
#Set to 0 (zero) for infinite timeout duration.
connect.timeout.seconds=600

#Number of seconds to wait to receive response data once a connection has been established
#Set to 0 (zero) for infinite timeout duration.
read.timeout.seconds=600

#Enable http 1.1 chunking transfer coding
http.chunking.enabled=true

#Desired chunk size in bytes
http.chunk.size=8192