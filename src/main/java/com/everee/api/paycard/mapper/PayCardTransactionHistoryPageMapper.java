package com.everee.api.paycard.mapper;

import com.everee.api.integration.galileo.model.GalileoPostedTransaction;
import com.everee.api.paycard.dto.PayCardTransaction;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PayCardTransactionHistoryPageMapper {
  PayCardTransactionHistoryPageMapper MAPPER =
      Mappers.getMapper(PayCardTransactionHistoryPageMapper.class);

  @Mapping(source = "transactionDate", target = "authTimestamp")
  @Mapping(source = "postDate", target = "postTimestamp")
  @Mapping(source = "transactionAmount", target = "amount")
  @Mapping(source = "bestDisplayDescription", target = "details")
  @Mapping(source = "transactionCodeType", target = "transactionCode")
  PayCardTransaction convertTransaction(GalileoPostedTransaction transaction);

  List<PayCardTransaction> convertDbTransactions(List<GalileoPostedTransaction> transactions);
}
