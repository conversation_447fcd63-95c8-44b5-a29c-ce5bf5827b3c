package com.everee.api.paycard.report;

import static com.everee.api.pdf.brandcomponents.BrandFontFactory.brandBoldFont;
import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.brandCellStyle;
import static com.itextpdf.layout.property.UnitValue.createPercentArray;
import static java.lang.String.join;

import com.everee.api.model.PhysicalAddress;
import com.everee.api.paycard.model.PayCardStatementData;
import com.everee.api.pdf.brandcomponents.FormatParagraph;
import com.everee.api.tax.state.State;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.property.TextAlignment;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import lombok.NonNull;

public class OverviewSection extends Div {

  public static final DateTimeFormatter DISPLAY_DATE_FORMAT =
      DateTimeFormatter.ofPattern("MMM d, yyy");

  OverviewSection(@NonNull PayCardStatementData statementData) {
    setKeepTogether(true);
    add(new DocumentHeader("Everee Visa® Pay Card Account Statement").setMarginBottom(5));
    add(new StatementDetails(statementData));
    add(new StatementSummary(statementData));
    add(new LineSeparator(new SolidLine()).setMarginTop(10));
  }

  private static class DocumentHeader extends Paragraph {
    DocumentHeader(@NonNull String text) {
      super(text);
      setFont(brandBoldFont());
      setFontSize(16);
    }
  }

  private static class StatementDetails extends Table {
    StatementDetails(@NonNull PayCardStatementData statementData) {
      super(createPercentArray(new float[] {3, 3, 4}), true);
      setFixedLayout();
      addPersonalInfoCell(statementData);
      addPeriodInfoCell(statementData);
      addAccountInfoCell(statementData);
    }

    private void addPersonalInfoCell(@NonNull PayCardStatementData statementData) {
      var userDisplayName = statementData.getUserDisplayName();
      var address = Optional.ofNullable(statementData.getUserAddress());
      var line1 = address.map(PhysicalAddress::getLine1).orElse("");
      var line2 = address.map(PhysicalAddress::getLine2).orElse("");
      var city = address.map(PhysicalAddress::getCity).orElse("");
      var state = address.map(PhysicalAddress::getState).map(State::name).orElse("");
      var zip = address.map(PhysicalAddress::getPostalCode).orElse("");

      addCell(
          new Cell()
              .setTextAlignment(TextAlignment.LEFT)
              .addStyle(brandCellStyle())
              .add(new FormatParagraph(userDisplayName).setFont(brandBoldFont()))
              .add(new FormatParagraph(join(" ", line1, line2)))
              .add(
                  new FormatParagraph(
                      join(" ", join(address.isPresent() ? ", " : "", city, state), zip))));
    }

    private void addPeriodInfoCell(@NonNull PayCardStatementData statementData) {
      var reportStartDate = DISPLAY_DATE_FORMAT.format(statementData.getReportStartDate());
      var reportEndDate = DISPLAY_DATE_FORMAT.format(statementData.getReportEndDate());
      addCell(
          new Cell()
              .setTextAlignment(TextAlignment.LEFT)
              .addStyle(brandCellStyle())
              .add(new FormatParagraph("Statement period").setFont(brandBoldFont()))
              .add(new FormatParagraph("Period Start:", reportStartDate))
              .add(new FormatParagraph("Period End:", reportEndDate)));
    }

    private void addAccountInfoCell(@NonNull PayCardStatementData statementData) {
      addCell(
          new Cell()
              .setTextAlignment(TextAlignment.LEFT)
              .addStyle(brandCellStyle())
              .add(new FormatParagraph("Account number").setFont(brandBoldFont()))
              .add(new FormatParagraph(statementData.getStatementAccountNo())));
    }
  }

  private static class StatementSummary extends Table {
    StatementSummary(@NonNull PayCardStatementData statementData) {
      super(createPercentArray(new float[] {3, 7}), true);
      setFixedLayout();
      addFeesInfoCell(statementData);
      addSummaryInfoCell(statementData);
    }

    private void addFeesInfoCell(@NonNull PayCardStatementData statementData) {
      var feesYTD = statementData.getFeesYTD().toDisplayString();
      var feesInPeriod = statementData.getFees().toDisplayString();
      addCell(
          new Cell()
              .setTextAlignment(TextAlignment.LEFT)
              .addStyle(brandCellStyle())
              .add(new FormatParagraph("Summary of fees").setFont(brandBoldFont()))
              .add(new LabeledValue("Fees YTD:", feesYTD))
              .add(new LabeledValue("Prior month fees:", feesInPeriod))
              .setPaddingRight(20));
    }

    private void addSummaryInfoCell(@NonNull PayCardStatementData statementData) {
      var reportStartDate = DISPLAY_DATE_FORMAT.format(statementData.getReportStartDate());
      var reportEndDate = DISPLAY_DATE_FORMAT.format(statementData.getReportEndDate());
      var beginningBalance = statementData.getStartBalance().toDisplayString();
      var endingBalance = statementData.getEndBalance().toDisplayString();
      addCell(
          new Cell()
              .setTextAlignment(TextAlignment.LEFT)
              .addStyle(brandCellStyle())
              .add(new FormatParagraph("Account summary").setFont(brandBoldFont()))
              .add(new LabeledValue("Beginning balance on " + reportStartDate, beginningBalance))
              .add(new LabeledValue("Ending balance on " + reportEndDate, endingBalance))
              .setPaddingRight(180));
    }

    private static class LabeledValue extends Table {
      LabeledValue(@NonNull String label, String value) {
        super(createPercentArray(new float[] {7, 3}), true);
        setFixedLayout();
        addCell(
            new Cell()
                .setTextAlignment(TextAlignment.LEFT)
                .addStyle(brandCellStyle())
                .add(new FormatParagraph(label)));
        addCell(
            new Cell()
                .setTextAlignment(TextAlignment.RIGHT)
                .addStyle(brandCellStyle())
                .add(new FormatParagraph(value)));
      }
    }
  }
}
