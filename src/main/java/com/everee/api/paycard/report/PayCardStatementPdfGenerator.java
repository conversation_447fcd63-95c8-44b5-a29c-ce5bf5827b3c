package com.everee.api.paycard.report;

import com.everee.api.paycard.model.PayCardStatementData;
import com.everee.api.pdf.FilePdf;
import com.everee.api.properties.AppSupportProperties;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.CompressionConstants;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PayCardStatementPdfGenerator {
  private final AppSupportProperties supportProperties;

  public FilePdf generate(@NonNull PayCardStatementData statementData) throws IOException {
    try (var baos = new ByteArrayOutputStream()) {
      var pdfWriter = new PdfWriter(baos);
      pdfWriter.setCompressionLevel(CompressionConstants.BEST_COMPRESSION);
      var pdfDocument = new PdfDocument(pdfWriter);
      pdfDocument.setDefaultPageSize(PageSize.LETTER);

      addContent(pdfDocument, statementData);

      var result = new FilePdf();
      result.setData(baos.toByteArray());
      result.setFilename(getFilename(statementData));
      return result;
    }
  }

  private void addContent(
      @NonNull PdfDocument pdfDocument, @NonNull PayCardStatementData statementData) {
    try (var doc = new Document(pdfDocument)) {
      doc.setMargins(36, 36, 90, 36);
      doc.add(new OverviewSection(statementData));
      doc.add(new TransactionListSection(statementData));
      doc.add(new ResolutionNoticeSection(supportProperties));
    }
  }

  private String getFilename(@NonNull PayCardStatementData statementData) {
    return StringUtils.replace(statementData.getTitle(), " ", "_") + ".pdf";
  }
}
