package com.everee.api.paycard.dto;

import com.everee.api.company.TypeOfIncorporation;
import com.everee.api.money.Money;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
public class PayCardCddEntry {
  @Id private String id;
  private String subProgramId;
  private String accountNumber;
  private String contractedPartyName;
  private String contractedPartyDba;
  private String physicalAddress1;
  private String physicalAddress2;
  private String physicalAddressCity;
  private String physicalAddressState;
  private String physicalAddressZip;
  private String physicalAddressCountry = "USA";
  private Double numberOfYearsAtPhysicalLocation;
  private String ein;
  private String companyWebsite;
  private String stockTicker;
  private String stockExchange;
  private String fdicNumber;
  private String primaryContactFullName;
  private String primaryContactTitle;
  private String primaryContactDepartment;
  private String primaryContactDirectPhone;
  private String primaryContactPhoneExt;
  private String primaryContactFax;
  private String primaryContactCell;
  private String primaryContactEmailAddress;

  @Enumerated(EnumType.STRING)
  private TypeOfIncorporation typeOfIncorporation;

  private String ceoFullName;
  private String cfoFullName;
  private String cooFullName;
  private String principalAddress1;
  private String principalAddress2;
  private String principalAddressCity;
  private String principalAddressState;
  private String principalAddressZip;
  private String principalAddressCountry = "USA";
  private String stateOfIncorporationRegistrationName;
  private String stateOfIncorporationAddress1;
  private String stateOfIncorporationAddress2;
  private String stateOfIncorporationAddressCity;
  private String stateOfIncorporationAddressState;
  private String stateOfIncorporationAddressZip;
  private Money annualRevenue;
  private String typesOfProductsOffered;
  private Integer numberOfWorkers;
  private Double percentOfRevenueFromOutsideUS;
  private Money estimatedLoadAverages;
  private String cardUsage = "2";
  private String cardOutsideUs = "N";
  private Double percentageOfCardsToBeSentOutsideTheUs = 0d;
  private String whereCardsWillBeSentOutsideUs;
  private String naicsCode;
  private String cashWithdrawlAvailable = "Y";
  private Integer intendedUseForCard = 2;
  private String coBrander = "N";
  private Money expectedAchActivityVolume = Money.ZERO;
  private Money expectedWireTransferVolume = Money.ZERO;
  private Money expectedCheckActivityVolume = Money.ZERO;
  private Money expectedCashActivityVolume = Money.ZERO;
  private Money expectedRemoteDepositActivityVolume = Money.ZERO;
  private Long employeeId;
  private Long companyId;
}
