package com.everee.api.paycard.dto;

import com.everee.api.tax.state.State;
import com.everee.api.ubo.UBOIdType;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PayCardPhysicalDocEntry {
  private Long id;
  private String docReferenceId;
  private String passFail;
  private String relatedCipId;
  private LocalDate dateOfApplication;
  private String firstName;
  private String lastName;
  private String clientIdTrackingNumber;
  private LocalDate dateOfBirth;
  private String addressLine1;
  private String addressLine2;
  private String addressCity;
  private State addressState;
  private String addressPostalCode;
  private String phone;
  private String programName;
  private String countryOfIssuance;
  private State stateOfIssuance;
  private String idDocNumber;
  private UBOIdType idType;
  private LocalDate idIssueDate;
  private LocalDate idExpirationDate;
  private String accountNumber;
}
