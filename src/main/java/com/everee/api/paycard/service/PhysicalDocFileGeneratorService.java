package com.everee.api.paycard.service;

import static com.everee.api.paycard.service.CustomerDueDiligenceFileService.dateFormatter;

import com.everee.api.paycard.dto.PayCardPhysicalDocEntry;
import com.everee.api.tax.state.State;
import com.everee.api.ubo.UBOIdType;
import java.time.Clock;
import java.time.LocalDate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PhysicalDocFileGeneratorService {
  private final PayCardPhysicalDocEntryDAO payCardPhysicalDocEntryDAO;
  private final Clock clock;

  public byte[] generate() {
    var records = payCardPhysicalDocEntryDAO.listAll();
    if (records.size() == 0) {
      log.warn("No ubo entries exist");
      return null;
    }

    var writer = getPipeWriter();
    var today = LocalDate.now(clock);
    var yesterday = LocalDate.now(clock).minusDays(1);

    // Header row
    writer
        .append("H")
        .append("HEADER")
        .append("EVEREE INC.")
        .append("PhysicalDoc")
        .append(today)
        .appendLast(yesterday);

    // Record rows
    records.forEach(record -> writeDetailRecord(writer, record));

    // Trailer row
    writer
        .append("T")
        .append("TRAILER")
        .appendLast(StringUtils.leftPad(records.size() + "", 9, "0"));

    return writer.toString().getBytes();
  }

  private void writeDetailRecord(FlatFileWriter writer, PayCardPhysicalDocEntry record) {
    writer
        .append("D")
        .append(record.getDocReferenceId(), 100)
        .append(record.getPassFail(), 10)
        .append(record.getRelatedCipId(), 50)
        .append(record.getDateOfApplication(), 8)
        .append(record.getFirstName(), 50)
        .append(record.getLastName(), 50)
        .append(record.getClientIdTrackingNumber(), 50)
        .append(record.getDateOfBirth(), 8)
        .append(record.getAddressLine1(), 100)
        .append(record.getAddressLine2(), 100)
        .append(record.getAddressCity(), 50)
        .append(record.getAddressState(), 2)
        .append(record.getAddressPostalCode(), 5)
        .append(record.getPhone(), 10)
        .append(record.getProgramName(), 50)
        .append(getCountryStateOfIssuance(record), 3)
        .append(record.getIdDocNumber(), 50)
        .append(record.getIdType(), 50)
        .append(record.getIdIssueDate(), 8)
        .append(record.getIdExpirationDate(), 8)
        .appendLast(record.getAccountNumber(), 30);
  }

  private String getCountryStateOfIssuance(PayCardPhysicalDocEntry record) {
    if (StringUtils.isNotBlank(record.getCountryOfIssuance())
        && record.getCountryOfIssuance().equalsIgnoreCase("US")) {
      return Optional.ofNullable(record.getStateOfIssuance()).map(Enum::name).orElse("");
    }
    return record.getCountryOfIssuance();
  }

  private FlatFileWriter getPipeWriter() {
    return new FlatFileWriter("|", "\r\n")
        .withFormatter(LocalDate.class, field -> field.format(dateFormatter))
        .withFormatter(State.class, Enum::name)
        .withFormatter(UBOIdType.class, UBOIdType::getFlatFileValue);
  }
}
