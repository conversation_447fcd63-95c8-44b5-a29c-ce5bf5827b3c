package com.everee.api.paycard.service;

import com.everee.api.paycard.dto.PayCardUBOEntry;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PayCardUBOEntryDAO {
  private final NamedParameterJdbcTemplate jdbcTemplate;

  public List<PayCardUBOEntry> listAll() {
    var query =
        "select "
            + "    ubo.id id, "
            + "    c.legalEntityName, "
            + "    c.federalEin as legalEntityEIN, "
            + "    c.federalEin as accountNumber, "
            + "    ubo.entityType, "
            + "    ubo.firstName as primaryFirstName, "
            + "    ubo.lastName as primaryLastName, "
            + "    ubo.createdAt as dateOfApplication, "
            + "    ubo.firstName, "
            + "    ubo.lastName, "
            + "    ubo.percentageOwnership, "
            + "    ubo.dateOfBirth, "
            + "    ubo.addressLine1, "
            + "    ubo.addressLine2, "
            + "    ubo.addressCity, "
            + "    ubo.addressState, "
            + "    ubo.addressPostalCode, "
            + "    ubo.addressCountry, "
            + "    ubo.taxpayerIdentifier, "
            + "    ubo.idNumber, "
            + "    ubo.idCountry, "
            + "    ubo.idType, "
            + "    ubo.idIssueDate, "
            + "    ubo.idExpirationDate "
            + "from UltimateBusinessOwner ubo "
            + "join Company c on c.id = ubo.companyId "
            // filter out UBOs that are part of companies with no cardholders
            + "join PayCardCddEntry cdd on cdd.companyId = c.id "
            // filters out UBOs that are not 25% or more owners
            + "where ubo.percentageOwnership >= 25 ";

    var result =
        jdbcTemplate.query(query, BeanPropertyRowMapper.newInstance(PayCardUBOEntry.class));
    return result;
  }
}
