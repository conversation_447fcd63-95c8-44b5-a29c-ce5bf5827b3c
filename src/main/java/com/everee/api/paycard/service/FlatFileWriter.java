package com.everee.api.paycard.service;

import static org.apache.commons.lang3.StringUtils.truncate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.formula.functions.T;

@RequiredArgsConstructor
public class FlatFileWriter {
  private final String delimiter;
  private final String lineDelimiter;

  private StringBuffer file = new StringBuffer();
  private boolean newLine = true;
  private Map<Class, FlatFileFieldFormatter> formatters = new HashMap();
  private FlatFileFieldFormatter defaultFormatter = new StringFlatFileFieldFormatter();

  public FlatFileWriter append(Object field) {
    return append(field, -1);
  }

  public FlatFileWriter append(Object field, int maxLength) {
    file.append(newLine ? "" : delimiter)
        .append(
            Optional.ofNullable(field)
                .map(f -> formatters.getOrDefault(f.getClass(), defaultFormatter).format(f))
                .map(s -> maxLength > 0 ? truncate(s, maxLength) : s)
                .orElse(""));
    newLine = false;
    return this;
  }

  public FlatFileWriter appendLast(Object field) {
    return appendLast(field, -1);
  }

  public FlatFileWriter appendLast(Object field, int maxLength) {
    append(field, maxLength);
    nextLine();

    return this;
  }

  public FlatFileWriter nextLine() {
    file.append(lineDelimiter);
    newLine = true;

    return this;
  }

  public <T> FlatFileWriter withFormatter(Class<T> clazz, FlatFileFieldFormatter<T> formatter) {
    formatters.put(clazz, formatter);

    return this;
  }

  public String toString() {
    return file.toString();
  }

  public static interface FlatFileFieldFormatter<T> {
    String format(T field);
  }

  public static class StringFlatFileFieldFormatter implements FlatFileFieldFormatter<Object> {

    @Override
    public String format(Object field) {
      return Optional.ofNullable(field).map(Objects::toString).orElse("");
    }
  }
}
