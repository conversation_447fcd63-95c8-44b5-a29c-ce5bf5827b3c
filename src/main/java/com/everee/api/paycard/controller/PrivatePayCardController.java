package com.everee.api.paycard.controller;

import com.everee.api.auth.annotation.EvereeAdminAccess;
import com.everee.api.integration.galileo.repository.GalileoAccountRepository;
import com.everee.api.paycard.dto.PayCardDistributionSummary;
import com.everee.api.paycard.service.*;
import com.everee.api.payment.distribution.PaymentDistributionRepository;
import com.everee.api.payment.distribution.report.PaymentDistributionReportSummary;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.util.DateUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

@Slf4j
@EvereeAdminAccess
@RestController("PrivatePayCardController")
@RequestMapping("/api-private/v1/pay-cards")
@RequiredArgsConstructor
public class PrivatePayCardController {
  private static final int STATEMENT_GENERATION_BATCH_SIZE = 500;
  private final PayCardSignupService payCardSignupService;
  private final PayCardStatementService payCardStatementService;
  private final GalileoAccountRepository galileoAccountRepository;
  private final PayCardNotificationService payCardNotificationService;
  private final PayCardStatementRepository payCardStatementRepository;
  private final CompanyPayCardSummaryService companyPayCardSummaryService;
  private final PaymentDistributionRepository paymentDistributionRepository;
  private final CustomerDueDiligenceFileService customerDueDiligenceFileService;

  @PostMapping("/reset-signup")
  public void resetSignup(@RequestParam(name = "user-id") long userId) {
    payCardSignupService.resetSignup(userId);
  }

  @PostMapping("/send-cdd-edd-ubo-files")
  public void sendCddEddUboFiles() {
    customerDueDiligenceFileService.sendFiles();
  }

  @GetMapping("/company-pay-card-summaries")
  public Page<PaymentDistributionReportSummary> getCompanyPayCardSummaries(
      @RequestParam(name = "start-date") LocalDate startDate,
      @RequestParam(name = "end-date") LocalDate endDate,
      @RequestParam(name = "name-search", required = false) String nameSearch,
      @RequestParam(name = "demo-company", required = false) Boolean demoCompany,
      Pageable pageable) {
    return companyPayCardSummaryService.getCompanyPayCardSummaries(
        startDate, endDate, nameSearch, demoCompany, pageable);
  }

  @GetMapping("/company-pay-card-summaries/export")
  public StoredFileLink getExportPayCarSummariesReport(
      @RequestParam(name = "start-date") LocalDate startDate,
      @RequestParam(name = "end-date") LocalDate endDate,
      @RequestParam(name = "name-search", required = false) String nameSearch,
      @RequestParam(name = "demo-company", required = false) Boolean demoCompany) {
    return companyPayCardSummaryService.generateExport(startDate, endDate, nameSearch, demoCompany);
  }

  @GetMapping("/distribution-summaries")
  public Page<PayCardDistributionSummary> getPayCardDistributionSummaries(
      @RequestParam(name = "galileo-account-id") Set<String> galileoAccountIds,
      @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          @RequestParam(name = "min-timestamp", required = false)
          LocalDateTime minTimestamp,
      @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          @RequestParam(name = "max-timestamp", required = false)
          LocalDateTime maxTimestamp,
      Pageable pageable) {
    var today = LocalDate.now();
    return paymentDistributionRepository.getPayCardDistributionSummaries(
        galileoAccountIds,
        Optional.ofNullable(minTimestamp).orElse(today.atStartOfDay()),
        Optional.ofNullable(maxTimestamp).orElse(DateUtil.atEndOfDay(today)),
        pageable);
  }

  @PostMapping("/prepare-periodic-account-statements")
  public PayCardStatementBulkActionResults preparePayCardStatements(
      @RequestParam(name = "report-date", required = false) LocalDate reportDate,
      @RequestParam(name = "pmt-ref-no", required = false) Long pmtRefNo) {
    var lastMonthStartDate = LocalDate.now().minusMonths(1).withDayOfMonth(1);
    var reportStartDate =
        Optional.ofNullable(reportDate).map(d -> d.withDayOfMonth(1)).orElse(lastMonthStartDate);
    var usersCount = pmtRefNo != null ? 1 : galileoAccountRepository.countByIdIsNotNull();
    log.info(
        "EPC periodic statements - Preparing for {} user(s) for statement date: {}",
        usersCount,
        reportStartDate.format(DateTimeFormatter.ISO_DATE));

    var results = new PayCardStatementBulkActionResults();
    if (pmtRefNo != null) {
      payCardStatementService.preparePayCardStatementRecord(pmtRefNo, reportStartDate, results);
    } else {
      payCardStatementService.preparePayCardStatementRecords(reportStartDate, results);
    }

    log.info(
        "EPC periodic statements - Preparation results (pmtRefNo::statementDate): {}",
        results.toLogString());
    return results;
  }

  @PostMapping("/run-periodic-account-statements-batch")
  public PayCardStatementBulkActionResults runPayCardStatementsBatch(
      @RequestParam(name = "batch-size", required = false) Integer batchSize) {
    var generationBatchSize =
        Optional.ofNullable(batchSize).orElse(STATEMENT_GENERATION_BATCH_SIZE);
    var totalCount = payCardStatementRepository.countByStorageKeyIsNull();
    var processCount = totalCount <= generationBatchSize ? totalCount : generationBatchSize;
    log.info(
        "EPC periodic statements - Generating {} statements out of {} found ready.",
        processCount,
        totalCount);

    var results = new PayCardStatementBulkActionResults();
    payCardStatementService.processPayCardStatementBatch(generationBatchSize, results);

    log.info(
        "EPC periodic statements - Generation results (pmtRefNo::statementDate): {}",
        results.toLogString());
    return results;
  }

  @GetMapping("/statements/notifications")
  public void sendPayCardNotifications() {
    payCardNotificationService.processPayCardStatementNotifications();
  }

  @GetMapping("/enabled/notifications/{companyId}")
  public Integer sendPayCardEnabledNotifications(@PathVariable("companyId") Long companyId) {
    return payCardNotificationService.processPayCardEnabledNotifications(companyId);
  }
}
