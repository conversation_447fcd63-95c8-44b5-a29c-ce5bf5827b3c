package com.everee.api.annotations;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.BeanWrapperImpl;

public class AtLeastOneFieldValidator implements ConstraintValidator<AtLeastOneField, Object> {

    private String[] fields;

    @Override
    public void initialize(AtLeastOneField constraintAnnotation) {
        this.fields = constraintAnnotation.fields();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        for (String fieldName : fields) {
            Object fieldValue = new BeanWrapperImpl(value).getPropertyValue(fieldName);
            if (fieldValue != null) {
                return true;
            }
        }
        return false;
    }
}
