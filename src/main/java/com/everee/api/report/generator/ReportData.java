package com.everee.api.report.generator;

import com.everee.api.totals.Totals;
import java.util.Iterator;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import lombok.*;

@Getter
@RequiredArgsConstructor
public class ReportData<T> implements Iterator<T> {
  /**
   * Returns the data stream. But it is preferred to use this as an iterator so entities can be
   * managed as they are iterated.
   */
  @NonNull private final Stream<T> dataStream;

  /** Returns the totals for the data. */
  @NonNull private final Totals<T> totals;

  @Setter(AccessLevel.PUBLIC)
  @Getter(AccessLevel.NONE)
  private EntityManager entityManager;

  @Getter(AccessLevel.NONE)
  private Iterator<T> iterator;

  @Getter(AccessLevel.NONE)
  private T current;

  @Override
  public boolean hasNext() {
    if (iterator == null) {
      iterator = dataStream.iterator();
    }
    return iterator.hasNext();
  }

  @Override
  public T next() {
    if (entityManager != null && current != null) {
      // This will release iterated entity to allow for GC
      entityManager.detach(current);
    }
    current = iterator.next();
    return current;
  }
}
