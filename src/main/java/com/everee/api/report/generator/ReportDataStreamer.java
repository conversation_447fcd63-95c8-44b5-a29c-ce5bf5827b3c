package com.everee.api.report.generator;

import com.everee.api.report.exception.ReportGenerationException;
import com.everee.api.totals.Totals;
import java.util.stream.Stream;
import lombok.NonNull;

public interface ReportDataStreamer<T> {
  Totals<T> getTotals(@NonNull ReportContext<T> context) throws ReportGenerationException;

  Stream<T> getDataStream(@NonNull ReportContext<T> context) throws ReportGenerationException;
}
