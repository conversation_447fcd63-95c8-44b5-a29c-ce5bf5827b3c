package com.everee.api.report.generator;

import com.everee.api.partner.PartnerRepository;
import com.everee.api.report.exception.ReportGenerationException;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ReportService {
  private final ReportGenerationService reportGenerationService;
  private final PartnerRepository partnerRepository;

  @Transactional
  public ReportGenerationResult generateReport(@NonNull ReportContext context)
      throws ReportGenerationException {
    var partner = partnerRepository.findByCompanyId(context.getCompany().getId()).orElseThrow();
    context.setPartner(partner);
    switch (context.getDeliveryMethod()) {
      case HTTP:
        return reportGenerationService.generateReport(context);
      case EMAIL:
        reportGenerationService.generateAndEmailReport(context);
        return new ReportGenerationResult(ReportGenerationStatus.GENERATING_ASYNC);
    }
    return null;
  }
}
