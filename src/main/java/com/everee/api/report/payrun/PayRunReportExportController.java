package com.everee.api.report.payrun;

import com.everee.api.auth.annotation.FinancialManagerAccess;
import com.everee.api.auth.oauth2.OAuthScope;
import com.everee.api.auth.oauth2.Scope;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payrun.lookup.PayRunLookup;
import com.everee.api.report.delivery.ReportDeliveryMethod;
import com.everee.api.report.payrun.mapper.ReportLookupMapper;
import com.everee.api.report.v2.service.ReportService;
import com.everee.api.report.generator.ReportType;
import com.everee.api.report.payment.columntypes.DimensionColumn;
import com.everee.api.storage.StoredFileLink;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v2/pay-run-report")
@RequiredArgsConstructor
public class PayRunReportExportController {
  private final ReportService reportService;
  private final ReportLookupMapper reportLookupMapper;

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping
  @FinancialManagerAccess
  public StoredFileLink getLookupReport(
    @RequestParam(name = "summary-page", required = false) List<DimensionColumn> summaryPages,
    @RequestParam(name = "delivery-method", required = false) ReportDeliveryMethod deliveryMethod,
    PayRunReportLookup payRunReportLookup) {
    if (summaryPages == null) {
      summaryPages = new ArrayList<>();
    }
    PaymentLookup paymentLookup = reportLookupMapper.mapToPaymentLookup(payRunReportLookup);
    deliveryMethod = Optional.ofNullable(deliveryMethod).orElse(ReportDeliveryMethod.HTTP);
    return reportService.generateReport(ReportType.PAYMENT_DETAIL , summaryPages, paymentLookup, deliveryMethod);
  }
}
