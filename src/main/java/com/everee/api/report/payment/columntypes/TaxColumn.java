package com.everee.api.report.payment.columntypes;

import static java.lang.String.join;

import com.everee.api.excel.CellContent;
import com.everee.api.money.Money;
import com.everee.api.payment.total.PaymentTaxTotal;
import com.everee.api.report.payment.reportdata.RowData;
import java.util.Optional;
import java.util.function.Function;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum TaxColumn {
  JURISDICTION_TAX_ID(
      getHeaderMapper("Jurisdiction Tax ID"), TaxColumn::getJurisdictionTaxIdMapper),
  AMOUNT(getHeaderMapper("Amount"), TaxColumn::getAmountMapper),
  SUBJECT_WAGES(getHeaderMapper("Taxable Wages"), TaxColumn::getSubjectWagesMapper);

  private final Function<PaymentTaxTotal, String> columnHeader;
  private final Function<PaymentTaxTotal, Function<RowData, CellContent>> getContentFromRowData;

  private static Function<PaymentTaxTotal, String> getHeaderMapper(String typeDetail) {
    return taxTotal -> {
      var typeString = taxTotal.getJurisdictionTaxId();

      return join("|", typeString, typeDetail);
    };
  }

  private static Function<RowData, CellContent> getJurisdictionTaxIdMapper(
      PaymentTaxTotal taxTotal) {
    return payment ->
        findTaxByJurisdictionId(payment, taxTotal.getJurisdictionTaxId())
            .map(PaymentTaxTotal::getJurisdictionTaxId)
            .map(CellContent::new)
            .orElse(new CellContent(""));
  }

  private static Function<RowData, CellContent> getAmountMapper(PaymentTaxTotal taxTotal) {
    return payment ->
        findTaxByJurisdictionId(payment, taxTotal.getJurisdictionTaxId())
            .map(PaymentTaxTotal::getAmount)
            .map(CellContent::new)
            .orElse(new CellContent(Money.ZERO));
  }

  private static Function<RowData, CellContent> getSubjectWagesMapper(PaymentTaxTotal taxTotal) {
    return payment ->
      findTaxByJurisdictionId(payment, taxTotal.getJurisdictionTaxId())
        .map(PaymentTaxTotal::getSubjectWages)
        .map(CellContent::new)
        .orElse(new CellContent(Money.ZERO));
  }

  private static Optional<PaymentTaxTotal> findTaxByJurisdictionId(
      RowData rowData, String jurisdictionId) {
    for (var tax : rowData.getPaymentTotals().getTaxTotals()) {
      if (tax.getJurisdictionTaxId().equals(jurisdictionId)) {
        return Optional.of(tax);
      }
    }
    return Optional.empty();
  }
}
