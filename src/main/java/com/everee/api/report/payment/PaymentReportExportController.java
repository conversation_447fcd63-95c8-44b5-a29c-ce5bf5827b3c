package com.everee.api.report.payment;

import static com.everee.api.companyemployeeimport.CompanyEmployeeImportController.APPLICATION_XLSX_VALUE;
import static com.everee.api.report.delivery.StreamingReportUtils.getReportName;
import static com.everee.api.report.delivery.StreamingReportUtils.headers;

import com.everee.api.auth.annotation.FinancialManagerAccess;
import com.everee.api.auth.oauth2.OAuthScope;
import com.everee.api.auth.oauth2.Scope;
import com.everee.api.company.CompanyService;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.ReturnedDepositsReportService;
import com.everee.api.payment.UnpayableWorkersReportService;
import com.everee.api.payment.funding.FundingTransactionLookup;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.receivable.ReceivablePayment;
import com.everee.api.payment.receivable.ReceivablePaymentLookup;
import com.everee.api.payment.receivable.ReceivablePaymentLookupService;
import com.everee.api.report.accrual.AccrualReportExportService;
import com.everee.api.report.delivery.ReportDeliveryMethod;
import com.everee.api.report.funding.FundingReportExportService;
import com.everee.api.report.generator.ReportType;
import com.everee.api.report.payment.columntypes.DimensionColumn;
import com.everee.api.report.v2.service.ReportService;
import com.everee.api.storage.StoredFileLink;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

@RestController("PublicV2PaymentReportController")
@RequestMapping("/api/v2/payment-report")
@RequiredArgsConstructor
public class PaymentReportExportController {

  private final AccrualReportExportService accrualExportService;
  private final LookupReportExportService lookupReportExportService;
  private final ReceivablePaymentLookupService receivablePaymentLookupService;
  private final UnpayableWorkersReportService unpayableWorkersReportService;
  private final ReturnedDepositsReportService returnedDepositsReportService;
  private final ReportService reportService;

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping("/funding")
  @FinancialManagerAccess
  public StoredFileLink getFundingReport(
          @RequestParam(name = "funding-transaction-id") Set<Long> fundingTransactionIds) {

    var paymentLookup = new PaymentLookup().withCompanyFundingIds(fundingTransactionIds)
            .withPaymentStatuses(PaymentStatus.ALLOWED_FOR_REPORTS);

    var summaryPages = List.of(DimensionColumn.TEAM, DimensionColumn.TITLE);

    return reportService.generateReport(ReportType.FUNDING, summaryPages, paymentLookup , ReportDeliveryMethod.HTTP);
  }

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping("/accrual")
  @FinancialManagerAccess
  public StoredFileLink getAccrualReport(
    @RequestParam @Schema(description = "The year (e.g., 2024)", type = "integer", format = "int32") Year year,
    @RequestParam(name = "month-name") @Schema(type = "string", description = "The month") Month month) {
    var firstDayOfMonth = LocalDate.of(year.getValue(), month, 1);
    var lastDayOfMonth = firstDayOfMonth.plusMonths(1L).minusDays(1L);
    var paymentLookup =
        new PaymentLookup()
            .withMaxPayPeriodStartDateInclusive(lastDayOfMonth)
            .withMinPayDateInclusive(lastDayOfMonth.plusDays(1))
            .withPaymentStatuses(Set.of(PaymentStatus.PAID));

    return accrualExportService.generateReport(
        CompanyService.getAuthenticatedCompany(), firstDayOfMonth, lastDayOfMonth, paymentLookup);
  }

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping("/receivable")
  @FinancialManagerAccess
  public StoredFileLink getReceivableReport() {
    var company = CompanyService.getAuthenticatedCompany();
    var summaryPages = List.of(DimensionColumn.TEAM, DimensionColumn.TITLE);
    var paymentIds =
        receivablePaymentLookupService
            .streamAll(
                new ReceivablePaymentLookup().withCompanyId(company.getId()), Pageable.unpaged())
            .map(ReceivablePayment::getId)
            .collect(Collectors.toSet());
    var paymentLookup = new PaymentLookup().withIds(paymentIds);

    return lookupReportExportService.generateReport(
        company, summaryPages, paymentLookup, ReportDeliveryMethod.HTTP);
  }

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping
  @FinancialManagerAccess
  public StoredFileLink getLookupReport(
      @RequestParam(name = "summary-page", required = false) List<DimensionColumn> summaryPages,
      @RequestParam(name = "delivery-method", required = false) ReportDeliveryMethod deliveryMethod,
      PaymentLookup paymentLookup) {
    if (summaryPages == null) {
      summaryPages = new ArrayList<>();
    }
    deliveryMethod = Optional.ofNullable(deliveryMethod).orElse(ReportDeliveryMethod.HTTP);

    return reportService.generateReport(ReportType.PAYMENT_DETAIL , summaryPages, paymentLookup , deliveryMethod);

  }

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping("/unpayable-workers")
  @FinancialManagerAccess
  public ResponseEntity<StreamingResponseBody> getUnpayableWorkersReport(
      PaymentLookup lookup, Sort sort) {
    var company = CompanyService.getAuthenticatedCompany();

    return new ResponseEntity<>(
        stream -> unpayableWorkersReportService.generateReport(lookup, sort, stream),
        headers(getReportName(company, "unpayable_workers"), APPLICATION_XLSX_VALUE),
        HttpStatus.OK);
  }

  @Scope(OAuthScope.REPORTS_READ)
  @GetMapping("/returned-deposits")
  @FinancialManagerAccess
  public ResponseEntity<StreamingResponseBody> getReturnedDepositsReport(Sort sort) {
    var company = CompanyService.getAuthenticatedCompany();

    return new ResponseEntity<>(
        stream -> returnedDepositsReportService.generateReport(sort, stream),
        headers(getReportName(company, "returned_deposits"), APPLICATION_XLSX_VALUE),
        HttpStatus.OK);
  }
}
