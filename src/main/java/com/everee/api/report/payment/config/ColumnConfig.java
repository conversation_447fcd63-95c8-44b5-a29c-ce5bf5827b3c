package com.everee.api.report.payment.config;

import com.everee.api.earnings.EarningType;
import com.everee.api.excel.CellContent;
import com.everee.api.payment.total.PaymentTaxTotal;
import com.everee.api.payment.total.v1.BenefitTypeWithName;
import com.everee.api.report.payment.columntypes.*;
import com.everee.api.report.payment.reportdata.RowData;
import java.util.function.Function;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class ColumnConfig {
  private final String columnHeader;
  private final Function<RowData, CellContent> cellContentMapper;

  public ColumnConfig(DimensionColumn fieldType) {
    this.columnHeader = fieldType.getColumnHeader();
    this.cellContentMapper = fieldType.getContentFromRowData();
  }

  public ColumnConfig(ValueColumn fieldType) {
    this.columnHeader = fieldType.getColumnHeader();
    this.cellContentMapper = fieldType.getContentFromRowData();
  }

  public ColumnConfig(EarningColumn fieldType, EarningType type) {
    this.columnHeader = fieldType.getColumnHeader().apply(type);
    this.cellContentMapper = fieldType.getGetContentFromRowData().apply(type);
  }

  public ColumnConfig(BenefitColumn fieldType, BenefitTypeWithName benefitTypeWithName) {
    this.columnHeader = fieldType.getColumnHeader().apply(benefitTypeWithName);
    this.cellContentMapper = fieldType.getGetContentFromRowData().apply(benefitTypeWithName);
  }

  public ColumnConfig(TaxColumn fieldType, PaymentTaxTotal taxTotal) {
    this.columnHeader = fieldType.getColumnHeader().apply(taxTotal);
    this.cellContentMapper = fieldType.getGetContentFromRowData().apply(taxTotal);
  }

  public ColumnConfig(DimensionColumn fieldType, String header) {
    this.columnHeader = header;
    this.cellContentMapper = fieldType.getContentFromRowData();
  }

}
