package com.everee.api.report.payment.reportdata;

import com.everee.api.earnings.EarningType;
import com.everee.api.money.Money;
import com.everee.api.payment.Payment;
import com.everee.api.payment.request.entity.PaymentRequestRecipient;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class OffCyclePaymentRowData extends RowData {
  // --- One-time payment fields ---
  @NonNull EarningType earningType;
  @NonNull Money amount;
  @NonNull String note;

  public OffCyclePaymentRowData(Payment payment, PaymentRequestRecipient recipient) {
    super(payment);

    earningType = recipient.getEarningType();
    amount = recipient.getAmount();
    note = recipient.getNote();
  }

  public static List<OffCyclePaymentRowData> of(Payment payment) {
    return payment.getPaymentRecipients().stream()
        .map(r -> new OffCyclePaymentRowData(payment, r))
        .collect(Collectors.toList());
  }
}
