package com.everee.api.report.wotc.service;


import com.everee.api.report.wotc.dto.WotcReportDataRequest;
import com.everee.api.report.wotc.model.WotcReportData;
import com.everee.api.report.wotc.repository.WotcReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class WotcReportService {
    private final WotcReportRepository wotcReportRepository;

    public List<WotcReportData> getWotcReportData(WotcReportDataRequest request) {
        return wotcReportRepository.getWotcReportData(request.getWorkerIds(), request.getStartDate(), request.getEndDate());
    }

}
