package com.everee.api.report.aculoantransactionsreport;

import com.everee.api.ach.AchFileBatchRepository;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AcuLoanTransactionsReportService {

  private final AchFileBatchRepository achFileBatchRepository;

  public Page<AcuLoanTransactionsReportTransaction> getAcuLoanTransactionsReportTransactions(
      LocalDate startDate, LocalDate endDate, Pageable pageable) {
    return achFileBatchRepository.getAcuLoanTransactions(startDate, endDate, pageable);
  }
}
