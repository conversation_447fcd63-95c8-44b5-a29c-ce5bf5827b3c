package com.everee.api.report.paymentimport;

import com.everee.api.company.CompanyService;
import com.everee.api.employee.lookup.WorkerLifecycleStatus;
import com.everee.api.storage.StoredFileLink;
import java.time.LocalDate;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("PublicV2AdHocPaymentUploadTemplateController")
@RequestMapping("/api/v2/ad-hoc-payment-upload-template")
@RequiredArgsConstructor
public class PaymentImportTemplateController {
  private final PaymentImportTemplateService paymentImportTemplateService;

  @GetMapping
  public StoredFileLink getPaymentImportTemplate(
      @RequestParam(name = "lifecycle-status", required = false)
          Set<WorkerLifecycleStatus> lifecycleStatuses,
      @RequestParam(name = "date", required = false) LocalDate date) {
    if (CollectionUtils.isEmpty(lifecycleStatuses)) {
      lifecycleStatuses = Set.of(WorkerLifecycleStatus.ACTIVE, WorkerLifecycleStatus.ONBOARDING);
    }

    return paymentImportTemplateService.generateTemplate(
        CompanyService.getAuthenticatedCompany(), lifecycleStatuses, date);
  }
}
