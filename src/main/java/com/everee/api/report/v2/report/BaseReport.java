package com.everee.api.report.v2.report;

import com.everee.api.company.DetailedCompany;
import com.everee.api.lookup.Lookup;
import com.everee.api.partner.Partner;
import com.everee.api.payment.Payment;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.report.generator.ReportType;
import com.everee.api.report.payment.columntypes.DimensionColumn;
import com.everee.api.report.payment.reportdata.RowData;
import com.everee.api.report.v2.page.IPage;
import com.everee.api.report.v2.page.PageWriter;
import lombok.Getter;
import lombok.Setter;


import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.everee.api.report.v2.ReportUtil.*;

@Setter
@Getter
public abstract class BaseReport implements IReport {
    protected List<IPage> pages = new ArrayList<>();
    protected PageWriter pageWriter = new PageWriter();
    private Lookup lookup;
    private DetailedCompany company;
    private List<Payment> payments;
    private RowData grandTotal;


    @Override
    public ByteArrayOutputStream generateReport(Partner partner, Lookup lookup, DetailedCompany company, List<DimensionColumn> summaryPages) {
        reset();
        this.lookup = lookup;
        this.company = company;

        generateContentPages(summaryPages);

        return pageWriter.writeReport(partner);
    }

    private void reset() {
        pageWriter = new PageWriter();
        pages = new ArrayList<>();
    }

    public RowData calculateTotals(List<Payment> payments) {
        var paymentRows = convertToRowData(payments);
        var employeeRows = groupByEmployee(paymentRows);
        return accumulateGrandTotal(employeeRows);
    }

    public List<Payment> getPullbackPayments() {
        return payments.stream().filter(Payment::isPulledBack).collect(Collectors.toList());
    }

    public List<Payment> getPayments() {
        return payments.stream().filter(payment -> payment.getStatus() != PaymentStatus.DELETED).collect(Collectors.toList());
    }

    public abstract ReportType getReportType();
}
