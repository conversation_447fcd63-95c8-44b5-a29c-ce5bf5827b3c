package com.everee.api.report.v2.report;

import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.featureflag.FeatureFlag;
import com.everee.api.featureflag.FeatureFlagProvider;
import com.everee.api.lookup.LookupIdEncoder;
import com.everee.api.model.DateRange;
import com.everee.api.money.Money;
import com.everee.api.payment.Payment;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payrun.lookup.PayRunLookup;
import com.everee.api.payrun.lookup.PayRunLookupService;
import com.everee.api.payrun.models.DecoratedPayRun;
import com.everee.api.payrun.models.PayRunStatus;
import com.everee.api.payrun.models.PayRunType;
import com.everee.api.report.generator.ReportType;
import com.everee.api.report.payment.columntypes.DimensionColumn;
import com.everee.api.report.payment.columntypes.ValueColumn;
import com.everee.api.report.payment.config.ColumnConfig;
import com.everee.api.report.v2.page.DetailPage;
import com.everee.api.report.v2.page.OffCyclePaymentsPage;
import com.everee.api.report.v2.page.SummaryPage;
import com.everee.api.report.v2.page.WorkerSummaryPage;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;

import static com.everee.api.report.v2.ReportUtil.getPullbackTotal;
import static com.everee.api.report.v2.ReportUtil.sortPayments;

@Component
@RequiredArgsConstructor
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PayrunReport extends BaseReport implements IReport {

  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final LookupIdEncoder lookupIdEncoder;
  private final UserService userService;
  private final PaymentLookupService paymentLookupService;
  private final CompanyLocalTimeService timeService;
  private final PayRunLookupService payRunLookupService;
  private final FeatureFlagProvider featureFlagProvider;

  private DecoratedPayRun payRun;

  @Override
  public ReportType getReportType() {
    return ReportType.PAYRUN_DETAIL;
  }

  @Override
  public void generateContentPages(List<DimensionColumn> summaryPages) {
    this.setPayments(fetchSortedPayments((PaymentLookup) getLookup()));
    this.setGrandTotal(calculateTotals(getPayments()));

    addCoverPage();

    summaryPages.forEach(dimensionColumn -> pages.add(new SummaryPage(dimensionColumn)));
    pages.add(new WorkerSummaryPage());

    var isPayrollOffCycleEnabled = isPayrollOffCycleEnabled(getCompany().getId());

    var detailPage = new DetailPage("Payment Detail");
    detailPage.setPtpcEnabled(getCompany().isPtpcEnabled());
    detailPage.setPayrollOffCycleEnabled(isPayrollOffCycleEnabled);

    var offCyclePaymentsPage = new OffCyclePaymentsPage();
    offCyclePaymentsPage.setPayrollOffCycleEnabled(isPayrollOffCycleEnabled);

    pages.add(detailPage);
    pages.add(offCyclePaymentsPage);

    pages.forEach(page -> page.createPage(getPayments(), getGrandTotal(), pageWriter));

    addPullbackPage();
  }

  public void addPullbackPage() {
    var pullbackPage = new DetailPage("Pullback").setPtpcEnabled(getCompany().isPtpcEnabled());
    pullbackPage.addColumn(new ColumnConfig(DimensionColumn.PULLED_BACK_ON));

    var pullbackPaymentTotals = calculateTotals(getPullbackPayments());
    pullbackPage.createPage(getPullbackPayments(), pullbackPaymentTotals, pageWriter);
  }

  @Override
  public void addCoverPage() {
    var paymentLookup = (PaymentLookup) getLookup();
    var totals = getGrandTotal();
    var reportDate =
      Optional.ofNullable(getCompany())
        .map(c -> timeService.companyLocalNowDate(c.getId()))
        .orElse(LocalDate.now());

    var reportDateRange = DateRange.of(reportDate, reportDate);

    pageWriter.createSheet("Cover Page");

    pageWriter.writeHeaderCells(List.of(getReportType().getReportTitle(), ""));
    pageWriter.advanceRow();

    pageWriter.writeCell("Company");
    pageWriter.writeCell(
      Optional.ofNullable(getCompany()).map(DetailedCompany::getDisplayName).orElse(""));
    pageWriter.advanceRow();

    pageWriter.writeCell("Report Date");
    pageWriter.writeCell(reportDateRange.getLocalizedDescription(Locale.ENGLISH));

    pageWriter.advanceRow(3);
    pageWriter.writeHeaderCells(List.of("Property", "Value"));

    if (isPayRunsLoaded(paymentLookup) && payRun != null) {
      pageWriter.writeIfPresent("Pay Run Type", Set.of(payRun.getType()), PayRunType::getDisplayName);
      pageWriter.writeIfPresent("Pay Run Pay Date", payRun.getPayDate());
      pageWriter.writeIfPresent("Pay Run Status", Set.of(payRun.getStatus()), PayRunStatus::getDisplayName);
      if (payRun.getPayPeriod() != null) {
        pageWriter.writeIfPresent("Pay Period Type", Set.of(payRun.getPayPeriod().getPayPeriodType()), PayPeriodType::getDisplayName);
      }
    }

    pageWriter.advanceRow(3);
    pageWriter.writeHeaderCells(List.of("Description", "Amount"));

    pageWriter.writeCell("Gross Pay");
    pageWriter.writeCell(ValueColumn.GROSS_EARNING.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell(ValueColumn.PRETAX_DEDUCTION.getColumnHeader());
    pageWriter.writeCell(ValueColumn.PRETAX_DEDUCTION.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell(ValueColumn.POSTTAX_DEDUCTION.getColumnHeader());
    pageWriter.writeCell(ValueColumn.POSTTAX_DEDUCTION.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell(ValueColumn.DEFERRED_COMPENSATION.getColumnHeader());
    pageWriter.writeCell(ValueColumn.DEFERRED_COMPENSATION.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell("Total Employee Tax");
    pageWriter.writeCell(ValueColumn.TOTAL_TAX_EE.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell("Net Pay");
    pageWriter.writeCell(ValueColumn.NET_PAY.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell("Pullback");
    pageWriter.writeCell(Money.zeroOr(getPullbackTotal(getPullbackPayments(), false)));

    pageWriter.advanceRow();

    pageWriter.writeCell(ValueColumn.TOTAL_CONTRIBUTION.getColumnHeader());
    pageWriter.writeCell(ValueColumn.TOTAL_CONTRIBUTION.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell("Total Employer Tax");
    pageWriter.writeCell(ValueColumn.TOTAL_TAX_ER.getContentFromRowData().apply(totals));
    pageWriter.advanceRow();

    pageWriter.writeCell("Total Expense");
    pageWriter.writeCell(ValueColumn.TOTAL_EXPENSE.getContentFromRowData().apply(totals));
    pageWriter.advanceRow(3);

    pageWriter.writeCell("");
    pageWriter.writeCell("Report Code: ");
    pageWriter.writeCell(lookupIdEncoder.toId(paymentLookup));
  }

  public List<Payment> fetchSortedPayments(PaymentLookup lookup) {
    if (isPayRunsLoaded(lookup)) {
      payRun = payRunLookupService.findOneOrThrow(new PayRunLookup().setIds(lookup.getPayRunIds()));
    }
    return sortPayments(this.paymentLookupService.listAll(lookup, Pageable.unpaged()).toList());
  }

  private static boolean isPayRunsLoaded(PaymentLookup paymentLookup) {
    return paymentLookup.getPayRunIds() != null && !paymentLookup.getPayRunIds().isEmpty();
  }

  private boolean isPayrollOffCycleEnabled(Long companyId) {
      var payrollPayRunsSettingValue = featureFlagProvider.intValue(
      FeatureFlag.PAYROLL_OFF_CYCLE_ENABLED, companyId, 1
      );
      return payrollPayRunsSettingValue > 1;
  }
}
