package com.everee.api.report.v2;

import com.everee.api.money.Money;
import com.everee.api.payment.*;
import com.everee.api.payment.distribution.PaymentDistribution;
import com.everee.api.payment.distribution.PaymentDistributionStatus;
import com.everee.api.report.payment.columntypes.DimensionColumn;
import com.everee.api.report.payment.reportdata.RowData;


import java.util.Comparator;
import java.util.List;

import java.util.Optional;
import java.util.stream.Collectors;

public class ReportUtil {


    public static Money getPullbackTotal(List<Payment> pullbackPayments, boolean includeERAmounts) {

        return pullbackPayments.stream().map(
                payment -> {
                    var distributionTotal =
                            payment.getDistributions().stream()
                                    .filter(
                                            pd ->
                                                    pd.getStatus() == PaymentDistributionStatus.DISTRIBUTED
                                                            || pd.getStatus() == PaymentDistributionStatus.SUBMITTED)
                                    .map(PaymentDistribution::getAmounts)
                                    .map(PaymentAmounts::getAmount)
                                    .reduce(Money.ZERO, Money::plus);

                    var erTaxTotal = payment.getTotalTaxesER();
                    var eeTaxTotal = payment.getTotalTaxesEE();

                    var fundedEcds =
                            payment.getContributionDeductions().stream()
                                    .filter(PaymentContributionDeduction::isFundAndRemit)
                                    .collect(Collectors.toList());
                    var fundedEcdAmountsEE =
                            fundedEcds.stream()
                                    .map(PaymentContributionDeduction::getAmountsEE)
                                    .map(PaymentAmountsLtd::getAmount)
                                    .reduce(Money.ZERO, Money::plus);

                    return distributionTotal
                            .plus(eeTaxTotal)
                            .plus(fundedEcdAmountsEE)
                            .plus(includeERAmounts ? fundedEcdAmountsEE.plus(erTaxTotal) : Money.ZERO);
                }).reduce(Money.ZERO, Money::plus);
    }

    public static List<RowData> convertToRowData(List<Payment> payments) {
        return payments.stream().map(RowData::new).collect(Collectors.toList());
    }

    public static List<RowData> groupByEmployee(List<RowData> rowDataPerPayment) {
        return rowDataPerPayment.stream()
                .collect(
                        Collectors.groupingBy(
                                DimensionColumn.EMPLOYEE_ID.getContentFromRowData(),
                                Collectors.reducing((left, right) -> RowData.accumulate(left, right)))).values()
                .stream()
                .flatMap(Optional::stream)
                .sorted(Comparator.comparing(DimensionColumn.FULL_NAME.getContentFromRowData()))
                .collect(Collectors.toList());
    }

    public static RowData accumulateGrandTotal(List<RowData> employeeRows) {
        var rowData = new RowData();
        rowData.setFullName("TOTAL");
        employeeRows.forEach(rowData::accumulate);

        return rowData;
    }

    public static List<Payment> sortPayments(List<Payment> payments) {
        return payments.stream()
                .sorted(Comparator.comparing(Payment::getPayeeDisplayFullName)
                        .thenComparing(Payment::getPayDate, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

    }

}