package com.everee.api.report.v2;

import com.everee.api.company.DetailedCompany;
import com.everee.api.storage.StorageAccess;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.util.FileNameUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ReportExporter {
    private final StorageService storageService;


    public StoredFileLink exportReport(
            ByteArrayOutputStream byteArrayOutputStream,
            DetailedCompany company,
            String title) {

        var optionalCompany = Optional.ofNullable(company);
        var path = List.of(title, optionalCompany.map(c -> Long.toString(c.getId())).orElse("all"));
        var safeTitle = FileNameUtils.fileNameSafeString(title);
        var companyName =
                FileNameUtils.fileNameSafeString(
                        optionalCompany.map(DetailedCompany::getDisplayName).orElse("companies"));
        var companyLocalNowDate =
                optionalCompany
                        .map(CompanyLocalTimeService::companyLocalNowTimestamp)
                        .orElse(LocalDateTime.now());
        var fileName =
                String.join(
                        "_",
                        safeTitle,
                        companyName,
                        companyLocalNowDate.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        return storageService.storeForImmediateDownload(
                byteArrayOutputStream.toByteArray(),
                fileName + ".xlsx",
                StorageAccess.CUSTOMER_FACING,
                path);
    }
}
