package com.everee.api.employeeTermination;

import com.everee.api.model.BaseModel;
import com.everee.api.money.Money;
import java.time.LocalDate;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class EmployeeTerminationRequest extends BaseModel {
  @Deprecated(forRemoval = true)
  private Long employeeId;

  @NotNull private boolean shouldPaySeverance;

  private Money severanceAmount;

  @NotNull private boolean payFinalPaymentNow;

  private Long newManagerUserId;

  @NotNull private LocalDate dateOfSeparation;

  private LocalDate dateOfNotice;

  private LocalDate dateOfCoverageEnding;

  private EmployeeSeparationType employeeSeparationType;

  private ContractorSeparationType contractorSeparationType;

  private String notes;

  private boolean eligibleForRehire;

  private Set<Long> paymentRequestRecipientIdsToDelete;

  @Deprecated(forRemoval = true)
  public Long getNewManagerId() {
    return newManagerUserId;
  }

  @Deprecated(forRemoval = true)
  public void setNewManagerId(Long newManagerId) {
    this.newManagerUserId = newManagerId;
  }
}
