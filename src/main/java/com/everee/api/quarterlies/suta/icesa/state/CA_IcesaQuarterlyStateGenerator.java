package com.everee.api.quarterlies.suta.icesa.state;

import static com.everee.api.annuals.w2.efw2.EFW2Generator.EL;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.blank;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.money;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.name;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.taxpayerId;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.toFixedLengthString;
import static com.everee.api.annuals.w2.efw2.state.StateEfw2Generator.alphaNumeric;
import static com.everee.api.quarterlies.suta.icesa.IcesaUtils.buildAddress;
import static org.apache.commons.lang3.StringUtils.leftPad;

import com.everee.api.annuals.TaxFilingGenerationException;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.UnemploymentRates.CompanyUnemploymentRates;
import com.everee.api.quarterlies.Quarter;
import com.everee.api.quarterlies.suta.mmref.EmployeeTaxSummary;
import com.everee.api.tax.filing.bulk.exporter.TaxFilingExporterCompanyTotals;
import com.everee.api.tax.state.State;
import java.time.Month;
import java.time.Year;
import java.util.List;
import lombok.Getter;
import org.springframework.stereotype.Service;

@Service
public class CA_IcesaQuarterlyStateGenerator extends IcesaQuarterlyStateGenerator {
  @Getter private State state = State.CA;

  @Override
  public StringBuffer generateStateEmployerRecord(
      DetailedCompany company,
      Year year,
      Quarter quarter,
      List<EmployeeTaxSummary> employeeTaxSummaries) {
    var companyAddress =
        company.getActiveAddress(year.atMonth(Month.DECEMBER).atEndOfMonth()).orElseThrow();

    return new StringBuffer()
        .append("E") // 1
        .append(blank(22)) // 2-23
        .append(name(company.getLegalEntityName(), 50)) // 24-73
        .append(buildAddress(companyAddress, false, true)) // 74-158
        .append(blank(117)) // 159 - 275
        .append(EL);
  }

  @Override
  public StringBuffer generateStateEmployeeRecord(
      EmployeeTaxSummary data, TaxFilingExporterCompanyTotals totals)
      throws TaxFilingGenerationException {
    var user = data.getUser();
    return new StringBuffer()
        .append("S")
        .append(taxpayerId(user.getTaxpayerIdentifier())) // 2-10
        .append(name(user.getLastName(), 20)) // 11-30
        .append(name(user.getFirstName(), 12)) // 31-42
        .append(name(user.getMiddleName(), 1)) // 43
        .append(getState().getCode()) // 44-45
        .append(blank(18)) // 46-63
        .append(money(data.getTotalSubjectWages(), 14)) // 64-77
        .append(blank(69)) // 78-146
        .append(alphaNumeric(data.getCompanySutaAccountNumber(), 8)) // 147-154
        .append("000") // 155-157
        .append(blank(19)) // 158-176
        .append(money(data.getTotalSitSubjectWages(), 14)) // 177-190
        .append(money(data.getSitTaxAmount(), 14)) // 191-204
        .append(blank(6)) // 205-210
        .append("S") // 211
        .append(blank(3)) // 212-214
        .append(
            toFixedLengthString(data.getQuarter().getMonth3())
                + data.getYear().getValue()) // 215-220
        .append(blank(55)) // 221-275
        .append(EL);
  }

  @Override
  public StringBuffer generateStateEmployerTotalRecord(
      DetailedCompany company,
      TaxFilingExporterCompanyTotals totals,
      State state,
      Year year,
      Quarter quarter,
      CompanyUnemploymentRates rate) {
    return new StringBuffer()
        .append("T")
        .append(leftPad(totals.getRecordCount() + "", 7, "0")) // 2-8
        .append(blank(18)) // 9-26
        .append(money(totals.getGrossSubjectWages(), 14)) // 27-40
        .append(blank(158)) // 41-198
        .append(money(totals.getGrossSitSubjectWages(), 14)) // 199-212
        .append(money(totals.getSitTaxAmount(), 14)) // 213-226
        .append(leftPad(totals.getEmployeeCount(year, quarter.getMonth1()) + "", 7, "0")) // 227-233
        .append(leftPad(totals.getEmployeeCount(year, quarter.getMonth2()) + "", 7, "0")) // 234-240
        .append(leftPad(totals.getEmployeeCount(year, quarter.getMonth3()) + "", 7, "0")) // 241-247
        .append(blank(28)) // 248-275
        .append(EL);
  }
}
