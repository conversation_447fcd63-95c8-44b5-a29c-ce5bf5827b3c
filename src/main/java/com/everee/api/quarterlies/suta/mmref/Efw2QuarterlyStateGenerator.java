package com.everee.api.quarterlies.suta.mmref;

import static com.everee.api.annuals.w2.efw2.EFW2Generator.EL;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.blank;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.money;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.name;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.numericOnly;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.right;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.taxpayerId;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.toFixedLengthString;
import static com.everee.api.annuals.w2.efw2.state.StateEfw2Generator.alphaNumeric;
import static org.apache.commons.lang3.StringUtils.leftPad;

import com.everee.api.annuals.TaxFilingGenerationException;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.UnemploymentRates.CompanyUnemploymentRates;
import com.everee.api.quarterlies.Quarter;
import com.everee.api.tax.filing.bulk.exporter.TaxFilingExporterCompanyTotals;
import com.everee.api.tax.state.State;
import java.time.Year;

public abstract class Efw2QuarterlyStateGenerator {
  public abstract State getState();

  public StringBuffer generateStateEmployeeRecord(
      EmployeeTaxSummary data, TaxFilingExporterCompanyTotals totals)
      throws TaxFilingGenerationException {
    var user = data.getUser();
    return new StringBuffer()
        .append("RS")
        .append(getState().getCode())
        .append(right(getTaxingIdentityCode(), 5)) // 5-9
        .append(taxpayerId(user.getTaxpayerIdentifier())) // 10-18
        .append(name(user.getFirstName(), 15)) // 19-33
        .append(name(user.getMiddleName(), 15)) // 34-48
        .append(name(user.getLastName(), 20)) // 49-68
        .append(blank(128)) // 69-196
        .append(
            toFixedLengthString(data.getQuarter().getMonth3())
                + data.getYear().getValue()) // 197-202
        .append(getAdditionalUnemploymentReporting(data, totals)) // 203 - 242
        .append(blank(5)) // 243-247
        .append(alphaNumeric(data.getCompanySutaAccountNumber(), 20)) // 248-267
        .append(getUnemploymentReporting(data, totals)) // 268-512
        .append(EL);
  }

  public StringBuffer generateStateEmployerRecord(
      DetailedCompany company,
      TaxFilingExporterCompanyTotals totals,
      State state,
      Year year,
      Quarter quarter,
      CompanyUnemploymentRates rate) {
    return new StringBuffer()
        .append("RT")
        .append(leftPad(totals.getRecordCount() + "", 7, "0"))
        .append(money(totals.getGrossEarnings(), 15)) // Wages, Tips and Other
        .append(blank(375)) // 25-399
        .append(money(totals.getExcessWages(), 15)) // 400-414
        .append(money(totals.getSubjectWages(), 15)) // 415-429
        .append(leftPad(numericOnly(rate.getSuiRate() + ""), 5, "0")) // 430-434
        .append(money(totals.getTaxAmount(), 9)) // 435-443
        .append(leftPad(totals.getEmployeeCount(year, quarter.getMonth1()) + "", 5)) // 444-448
        .append(leftPad(totals.getEmployeeCount(year, quarter.getMonth2()) + "", 5)) // 449-453
        .append(leftPad(totals.getEmployeeCount(year, quarter.getMonth3()) + "", 5)) // 454-458
        .append(blank(54))
        .append(EL);
  }

  // Can be overriden by state generator if necessary
  protected String getTaxingIdentityCode() {
    return blank(5);
  }

  // Can be overriden by state generator if necessary
  // 268-512
  protected StringBuffer getUnemploymentReporting(
      EmployeeTaxSummary data, TaxFilingExporterCompanyTotals totals) {
    return new StringBuffer().append(blank(245)) // 268 - 512
    ;
  };

  // Can be overriden by state generator if necessary
  protected StringBuffer getAdditionalUnemploymentReporting(
      EmployeeTaxSummary data, TaxFilingExporterCompanyTotals totals) {
    return new StringBuffer()
        .append(money(data.getTotalWages())) // 203-213
        .append(money(data.getTotalSubjectWages())) // 214-224
        .append(blank(2)) // 225-226
        .append(blank(8)) // 227-234
        .append(blank(8)) // 235-242
    ;
  };
}
