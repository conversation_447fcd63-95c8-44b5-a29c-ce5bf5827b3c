package com.everee.api.quarterlies;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.time.temporal.IsoFields;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
public enum Quarter {
  Q1(Month.JANUARY, Month.FEBRUARY, Month.MARCH),
  Q2(Month.APRIL, Month.MAY, Month.JUNE),
  Q3(Month.JULY, Month.AUGUST, Month.SEPTEMBER),
  Q4(Month.OCTOBER, Month.NOVEMBER, Month.DECEMBER);

  private final List<Month> months;

  Quarter(Month... months) {
    this.months = Arrays.stream(months).sorted().collect(Collectors.toUnmodifiableList());
  }

  public Month getMonth1() {
    return months.get(0);
  }

  public Month getMonth2() {
    return months.get(1);
  }

  public Month getMonth3() {
    return months.get(2);
  }

  public static Quarter getNextQuarter(Quarter currentQuarter) {
    return Q1.equals(currentQuarter)
        ? Q2
        : Q2.equals(currentQuarter) ? Q3 : Q3.equals(currentQuarter) ? Q4 : Q1;
  }

  public LocalDate firstDate(Year year) {
    return LocalDate.of(year.getValue(), getMonth1(), 1);
  }

  public LocalDate lastDate(Year year) {
    return LocalDate.of(year.getValue(), getMonth3(), getMonth3().length(year.isLeap()));
  }

  public LocalDate getDueDate(Year year) {
    var dueDate = lastDate(year).plusMonths(1);
    if (dueDate.getDayOfWeek().equals(DayOfWeek.SATURDAY)) {
      return dueDate.minusDays(1);
    }
    if (dueDate.getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
      return dueDate.plusDays(1);
    }
    return dueDate;
  }

  public int getQuarterNumber() {
    return ordinal() + 1;
  }

  public static Quarter getQuarter(LocalDate localDate) {
    return Quarter.values()[localDate.get(IsoFields.QUARTER_OF_YEAR) - 1];
  }
}
