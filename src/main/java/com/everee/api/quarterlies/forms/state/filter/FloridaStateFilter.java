package com.everee.api.quarterlies.forms.state.filter;

import com.everee.api.quarterlies.forms.state.StateSuperSetOfFieldsQuarterlyFor2019;
import com.everee.api.tax.state.State;
import java.util.stream.Stream;

public class FloridaStateFilter implements StateFilter {

  @Override
  public State getState() {
    return State.FL;
  }

  @Override
  public void applyFilter(StateSuperSetOfFieldsQuarterlyFor2019 stateData) {
    stateData.setFederalIncomeTaxWithheldFromWagesTipsAndOther(null);
    stateData.setStateIncomeTaxWithheldFromWagesTipsAndOther(null);

    Stream.of(stateData.getMonth1(), stateData.getMonth2(), stateData.getMonth3())
        .forEach(
            stateMonthData -> {
              stateMonthData.setFederalIncomeTaxWithheldFromWagesTipsAndOther(null);
              stateMonthData.setStateIncomeTaxWithheldFromWagesTipsAndOther(null);
              stateMonthData.setDays(null);
            });

    stateData.setSubjectWagesMinusOutOfStateWages(null);
    stateData.setYearToEndOfQuarterOutOfStateWages(null);
    stateData.setSpecialStateTaxes(null);
  }
}
