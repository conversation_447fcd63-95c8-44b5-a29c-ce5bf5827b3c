package com.everee.api.quarterlies.forms.selfcheck;

import com.everee.api.quarterlies.AbstractQuarterlyFormGenerator;
import com.everee.api.quarterlies.QuarterliesService;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SelfCheckGenerator extends AbstractQuarterlyFormGenerator<SelfCheckData> {

  private QuarterliesService quarterliesService;

  private List getCompaniesWithNoFein() {
    return getEntityManager()
        .createQuery(
            "SELECT c FROM Company c "
                + "WHERE (c.federalEin IS NULL OR TRIM(c.federalEin) = '')"
                + "AND c.id IN ("
                + "SELECT DISTINCT(p.companyId) FROM Payment p "
                + "WHERE p.payDate >= :startDate "
                + "AND p.payDate <= :endDate"
                + " AND p.status IN ('PAID', 'IMPORTED')"
                + ")")
        .setParameter("startDate", getQuarter().firstDate(getYear()))
        .setParameter("endDate", getQuarter().lastDate(getYear()))
        .getResultList();
  }

  private List getEmployeesWithNoSsn() {
    return getEntityManager()
        .createQuery(
            "SELECT DISTINCT(e) FROM Payment p "
                + " INNER JOIN p.employee e"
                + " WHERE p.payDate >= :startDate"
                + " AND p.payDate <= :endDate"
                + " AND p.status IN ('PAID', 'IMPORTED')"
                + " AND (e.user.taxpayerIdentifier IS NULL OR TRIM(e.user.taxpayerIdentifier) = '')")
        .setParameter("startDate", getQuarter().firstDate(getYear()))
        .setParameter("endDate", getQuarter().lastDate(getYear()))
        .getResultList();
  }

  private List getPaymentsWithoutAnAddress() {
    return getEntityManager()
        .createQuery(
            "SELECT paymentOuter FROM Payment paymentOuter "
                + " WHERE paymentOuter.payDate >= :startDate"
                + " AND paymentOuter.payDate <= :endDate"
                + " AND paymentOuter.status IN ('PAID', 'IMPORTED')"
                + " AND paymentOuter.id NOT IN ("
                + "SELECT p.id FROM Payment p "
                + " INNER JOIN p.employee e"
                + " INNER JOIN p.employee.user.homeAddresses a"
                + " WHERE  p.payDate >= :startDate"
                + " AND p.payDate <= :endDate"
                + " AND p.status IN ('PAID', 'IMPORTED')"
                + " AND a.startDate <= p.payDate"
                + " AND (a.endDate IS NULL OR a.endDate >= p.payDate)"
                + ")")
        .setParameter("startDate", getQuarter().firstDate(getYear()))
        .setParameter("endDate", getQuarter().lastDate(getYear()))
        .getResultList();
  }

  private List getPaymentsWithImportedStatus() {
    return getEntityManager()
        .createQuery(
            "SELECT payment FROM Payment payment "
                + " WHERE payment.payDate >= :startDate"
                + " AND payment.payDate <= :endDate"
                + " AND payment.status IN ('IMPORTED')")
        .setParameter("startDate", getQuarter().firstDate(getYear()))
        .setParameter("endDate", getQuarter().lastDate(getYear()))
        .getResultList();
  }

  private List getPaymentsWithoutSitOrFit() {
    return getEntityManager()
        .createQuery(
            "SELECT paymentOuter FROM Payment paymentOuter "
                + " WHERE paymentOuter.payDate >= :startDate"
                + " AND paymentOuter.payDate <= :endDate"
                + " AND paymentOuter.status IN ('PAID', 'IMPORTED')"
                + " AND paymentOuter.id NOT IN ("
                + "SELECT p.id FROM Payment p "
                + " INNER JOIN p.taxes t"
                + " WHERE  p.payDate >= :startDate"
                + " AND p.payDate <= :endDate"
                + " AND p.status IN ('PAID', 'IMPORTED')"
                + " AND t.type IN ('SIT', 'FIT')"
                + ")")
        .setParameter("startDate", getQuarter().firstDate(getYear()))
        .setParameter("endDate", getQuarter().lastDate(getYear()))
        .getResultList();
  }

  @Override
  public SelfCheckData generate() {
    return new SelfCheckData()
        .putIfNotEmpty(
            "Companies with data in " + yearQuarter() + " but no FEIN",
            this::getCompaniesWithNoFein)
        .putIfNotEmpty(
            "Employees with data in " + yearQuarter() + " but no SSN/TIN",
            this::getEmployeesWithNoSsn)
        .putIfNotEmpty(
            "Payments in " + yearQuarter() + " but employee has no address",
            this::getPaymentsWithoutAnAddress)
        .putIfNotEmpty(
            "Payments in " + yearQuarter() + " with imported status",
            this::getPaymentsWithImportedStatus)
        .putIfNotEmpty(
            "Payments in " + yearQuarter() + " without SIT or FIT taxes",
            this::getPaymentsWithoutSitOrFit);
  }

  private String yearQuarter() {
    return getYear() + "-" + getQuarter();
  }
}
