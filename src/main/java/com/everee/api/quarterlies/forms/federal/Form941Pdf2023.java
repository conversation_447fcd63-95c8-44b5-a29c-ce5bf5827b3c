package com.everee.api.quarterlies.forms.federal;

import static com.everee.api.util.ObjectUtils.optionally;

import com.everee.api.company.CompanyAddress;
import com.everee.api.money.Money;
import com.everee.api.pdf.PdfFillableForm;
import com.everee.api.pdf.PdfMoney;
import com.everee.api.pdf.PdfSplitString;
import com.everee.api.pdf.PdfString;
import com.everee.api.quarterlies.Quarter;
import com.everee.api.tax.state.State;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
@PdfFillableForm("taxforms/f941_2023.pdf")
public class Form941Pdf2023 extends Form941Pdf {
  public Form941Pdf2023(FederalForm941Data data) {
    super(data);
  }
  /** Part 1 * */

  /* Quarter */
  @PdfString("topmostSubform[0].Page1[0].Header[0].ReportForQuarter[0].c1_1[0]")
  public Boolean getQuarter1() {
    return getQuarter() == Quarter.Q1 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].Header[0].ReportForQuarter[0].c1_1[1]")
  public Boolean getQuarter2() {
    return getQuarter() == Quarter.Q2 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].Header[0].ReportForQuarter[0].c1_1[2]")
  public Boolean getQuarter3() {
    return getQuarter() == Quarter.Q3 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].Header[0].ReportForQuarter[0].c1_1[3]")
  public Boolean getQuarter4() {
    return getQuarter() == Quarter.Q4 ? true : null;
  }

  /* EIN */
  @PdfSplitString({
    "Text11", "Text1", "Text2", "Text3", "Text4", "Text5", "Text6", "Text7", "Text8"
  })
  public String getEinSplit() {
    return ein;
  }

  @PdfString({"Text9", "Text10"})
  public String getEinPart1() {
    return ein.substring(0, 2);
  }

  @PdfString({"Text12", "Text13"})
  public String getEinPart2() {
    return ein.substring(2, 9);
  }

  @PdfString("topmostSubform[0].Page2[0].f2_2[0]")
  private String ein;

  /* Employer Info */
  @PdfString({
    "topmostSubform[0].Page1[0].Header[0].EntityArea[0].f1_3[0]",
    "topmostSubform[0].Page2[0].Name_ReadOrder[0].f1_3[0]",
    "topmostSubform[0].Page3[0].Name_ReadOrder[0].f1_3[0]"
  })
  private String name;

  @PdfString("topmostSubform[0].Page1[0].Header[0].EntityArea[0].f1_4[0]")
  private String tradeName;

  private CompanyAddress companyAddress;

  @PdfString("topmostSubform[0].Page1[0].Header[0].EntityArea[0].f1_5[0]")
  public String getAddressLine1AndLine2() {
    return optionally(
        getCompanyAddress(),
        address ->
            Stream.of(address.getLine1(), address.getLine2())
                .filter(StringUtils::hasText)
                .collect(Collectors.joining(" ")));
  }

  @PdfString("topmostSubform[0].Page1[0].Header[0].EntityArea[0].f1_6[0]")
  public String getAddressCity() {
    return optionally(getCompanyAddress(), CompanyAddress::getCity);
  }

  @PdfString("topmostSubform[0].Page1[0].Header[0].EntityArea[0].f1_7[0]")
  public State getAddressState() {
    return optionally(getCompanyAddress(), CompanyAddress::getState);
  }

  @PdfString("topmostSubform[0].Page1[0].Header[0].EntityArea[0].f1_8[0]")
  public String getAddressZip() {
    return optionally(getCompanyAddress(), CompanyAddress::getPostalCode);
  }

  /* Question 1 */
  @PdfString("topmostSubform[0].Page1[0].f1_12[0]")
  private Long numberOfEmployeesPaid;

  /* Question 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_13[0]",
      cents = "topmostSubform[0].Page1[0].f1_14[0]")
  private Money wagesTipsAndOther;

  /* Question 3 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_15[0]",
      cents = "topmostSubform[0].Page1[0].f1_16[0]")
  private Money federalIncomeTaxWithheldFromWagesTipsAndOther;

  /* Question 4 */
  private boolean question4;

  @PdfString("topmostSubform[0].Page1[0].c1_2[0]")
  public Boolean getQuestion4() {
    return question4 ? true : null;
  }

  /* Question 5a, Column 1 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_17[0]",
      cents = "topmostSubform[0].Page1[0].f1_18[0]")
  private Money taxableSocialSecurityWages;

  /* Question 5a, Column 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_19[0]",
      cents = "topmostSubform[0].Page1[0].f1_20[0]")
  private Money taxableSocialSecurityWagesColumn2;

  /* Question 5a(i), Column 1 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_21[0]",
      cents = "topmostSubform[0].Page1[0].f1_22[0]")
  private Money qualifiedSickLeaveWages;

  /* Question 5a(i), Column 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_23[0]",
      cents = "topmostSubform[0].Page1[0].f1_24[0]")
  public Money qualifiedSickLeaveWagesColumn2;

  /* Question 5a(ii), Column 1 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_25[0]",
      cents = "topmostSubform[0].Page1[0].f1_26[0]")
  private Money qualifiedFamilyLeaveWages;

  /* Question 5a(ii), Column 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_27[0]",
      cents = "topmostSubform[0].Page1[0].f1_28[0]")
  public Money qualifiedFamilyLeaveWagesColumn2;

  /* Question 5b, Column 1 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_29[0]",
      cents = "topmostSubform[0].Page1[0].f1_30[0]")
  private Money taxableSocialSecurityTips;

  /* Question 5b, Column 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_31[0]",
      cents = "topmostSubform[0].Page1[0].f1_32[0]")
  private Money taxableSocialSecurityTipsColumn2;

  /* Question 5c, Column 1 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_33[0]",
      cents = "topmostSubform[0].Page1[0].f1_34[0]")
  private Money taxableMedicareWagesAndTips;

  /* Question 5c, Column 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_35[0]",
      cents = "topmostSubform[0].Page1[0].f1_36[0]")
  private Money taxableMedicareWagesAndTipsColumn2;

  /* Question 5d, Column 1 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_37[0]",
      cents = "topmostSubform[0].Page1[0].f1_38[0]")
  private Money taxableWagesAndTipsSubjectToAdditional;

  /* Question 5d, Column 2 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_39[0]",
      cents = "topmostSubform[0].Page1[0].f1_40[0]")
  private Money taxableWagesAndTipsSubjectToAdditionalColumn2;

  /* Question 5e */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_41[0]",
      cents = "topmostSubform[0].Page1[0].f1_42[0]")
  private Money question5E;

  /* Question 5f */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_43[0]",
      cents = "topmostSubform[0].Page1[0].f1_44[0]")
  private Money taxDueOnUnreportedTips;

  /* Question 6 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_45[0]",
      cents = "topmostSubform[0].Page1[0].f1_46[0]")
  private Money taxesBeforeAdjustments;

  /* Question 7 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_47[0]",
      cents = "topmostSubform[0].Page1[0].f1_48[0]")
  private Money currentQuartersAdjustmentForFractionsOfCents;

  /* Question 8 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_49[0]",
      cents = "topmostSubform[0].Page1[0].f1_50[0]")
  private Money currentQuartersAdjustmentForSickPay;

  /* Question 9 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_51[0]",
      cents = "topmostSubform[0].Page1[0].f1_52[0]")
  private Money currentQuartersAdjustmentForTipsAndGroupLifeInsurance;

  /* Question 10 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_53[0]",
      cents = "topmostSubform[0].Page1[0].f1_54[0]")
  private Money totalTaxesAfterAdjustments;

  /* Question 11a */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_55[0]",
      cents = "topmostSubform[0].Page1[0].f1_56[0]")
  private Money qualifiedSmallBusinessPayrollTaxCredit;

  /* Question 11b */
  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_57[0]",
      cents = "topmostSubform[0].Page1[0].f1_58[0]")
  private Money nonrefundableSickFamilyCredit;

  /* Question 11c */

  /* Question 11d */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_3[0]",
      cents = "topmostSubform[0].Page2[0].f2_4[0]")
  public Money nonrefundableSickFamilyCreditPart2;

  /* Question 11e */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_5[0]",
      cents = "topmostSubform[0].Page2[0].f2_6[0]")
  public Money totalNonrefundableCobra;

  /* Question 11f */
  @PdfString("topmostSubform[0].Page2[0].f2_7[0]")
  public Integer totalIndividualsProvidedCobra;

  /* Question 11g */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_8[0]",
      cents = "topmostSubform[0].Page2[0].f2_9[0]")
  public Money totalNonrefundableCredits;

  /* Question 12 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_10[0]",
      cents = "topmostSubform[0].Page2[0].f2_11[0]")
  private Money totalTaxesAfterAdjustmentsAndCredits;

  /* Question 13a */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_12[0]",
      cents = "topmostSubform[0].Page2[0].f2_13[0]")
  private Money totalDepositsForThisQuarter;

  /* Question 13b */

  /* Question 13c */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_16[0]",
      cents = "topmostSubform[0].Page2[0].f2_17[0]")
  private Money refundableSickFamilyCredit;

  /* Question 13d */

  /* Question 13e */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_20[0]",
      cents = "topmostSubform[0].Page2[0].f2_21[0]")
  public Money totalDepositsDeferralsAndRefundableCredits2021;

  /* Question 13f */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_22[0]",
      cents = "topmostSubform[0].Page2[0].f2_23[0]")
  private Money totalAdvances;

  /* Question 13g */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_24[0]",
      cents = "topmostSubform[0].Page2[0].f2_25[0]")
  public Money totalDepositsDeferralsAndRefundableCreditsLessAdvances;

  /* Question 14 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_30[0]",
      cents = "topmostSubform[0].Page2[0].f2_31[0]")
  private Money balanceDue;

  /* Question 15 */
  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_32[0]",
      cents = "topmostSubform[0].Page2[0].f2_33[0]")
  private Money overpayment;

  /* Refund */
  @PdfString("topmostSubform[0].Page2[0].c2_1[1]")
  public Boolean getSendRefund() {
    return true;
  }
}
