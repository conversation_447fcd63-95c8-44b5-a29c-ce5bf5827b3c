package com.everee.api.quarterlies.forms.state.filter;

import com.everee.api.quarterlies.forms.state.StateSuperSetOfFieldsQuarterlyFor2019;
import com.everee.api.tax.state.State;
import java.util.stream.Stream;

public class TexasStateFilter implements StateFilter {

  @Override
  public State getState() {
    return State.TX;
  }

  @Override
  public void applyFilter(StateSuperSetOfFieldsQuarterlyFor2019 stateData) {
    stateData.setFederalIncomeTaxWithheldFromWagesTipsAndOther(null);
    stateData.setStateIncomeTaxWithheldFromWagesTipsAndOther(null);

    Stream.of(stateData.getMonth1(), stateData.getMonth2(), stateData.getMonth3())
        .forEach(
            stateMonthData -> {
              stateMonthData.setFederalIncomeTaxWithheldFromWagesTipsAndOther(null);
              stateMonthData.setStateIncomeTaxWithheldFromWagesTipsAndOther(null);
            });

    stateData.setExcessSUTAWagesPaidToAllEmployees(null);
    stateData.setSubjectWagesMinusOutOfStateWages(null);
    stateData.setYearToEndOfQuarterOutOfStateWages(null);
    stateData.setSpecialStateTaxes(null);
  }
}
