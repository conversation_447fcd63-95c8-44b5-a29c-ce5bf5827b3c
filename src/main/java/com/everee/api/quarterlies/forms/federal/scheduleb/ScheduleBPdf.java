package com.everee.api.quarterlies.forms.federal.scheduleb;

import static com.everee.api.util.ObjectUtils.optionally;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toCollection;

import com.everee.api.money.Money;
import com.everee.api.pdf.IPdfFillable;
import com.everee.api.pdf.PdfBase;
import com.everee.api.pdf.PdfFillableForm;
import com.everee.api.pdf.PdfFiller;
import com.everee.api.pdf.PdfMoney;
import com.everee.api.pdf.PdfSplitString;
import com.everee.api.pdf.PdfString;
import com.everee.api.pdf.filler.AbstractPdfValueFiller;
import com.everee.api.quarterlies.Quarter;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.Year;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

@Data
@PdfFillableForm("taxforms/f941sb_2019.pdf")
public class ScheduleBPdf implements IPdfFillable {

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_207_0_[0]",
      cents = "topmostSubform[0].Page1[0].f1_208_0_[0]")
  public Money total;

  @PdfSplitString({
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_001_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_002_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_003_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_004_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_005_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_006_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_007_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_008_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_009_0_[0]"
  })
  private String ein;

  @PdfString("topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_010_0_[0]")
  private String name;

  @PdfSplitString({
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_011_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_012_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_013_0_[0]",
    "topmostSubform[0].Page1[0].HeaderAndEntity[0].Entity[0].f1_014_0_[0]"
  })
  private Year year;

  private Quarter quarter;

  @PdfScheduleBMonth(month = 1)
  private ScheduleBMonth month1;

  @PdfScheduleBMonth(month = 2)
  private ScheduleBMonth month2;

  @PdfScheduleBMonth(month = 3)
  private ScheduleBMonth month3;

  public ScheduleBPdf(ScheduleB scheduleB) {
    BeanUtils.copyProperties(scheduleB, this);
  }

  @PdfString("topmostSubform[0].Page1[0].ReportQuarter[0].c1_1_0_[0]")
  public Boolean getQuarter1() {
    return getQuarter() == Quarter.Q1 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].ReportQuarter[0].c1_1_0_[1]")
  public Boolean getQuarter2() {
    return getQuarter() == Quarter.Q2 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].ReportQuarter[0].c1_1_0_[2]")
  public Boolean getQuarter3() {
    return getQuarter() == Quarter.Q3 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].ReportQuarter[0].c1_1_0_[3]")
  public Boolean getQuarter4() {
    return getQuarter() == Quarter.Q4 ? true : null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].Month1[0].f1_077_0_[0]",
      cents = "topmostSubform[0].Page1[0].Month1[0].f1_078_0_[0]")
  public Money getMonth1Total() {
    return optionally(getMonth1(), ScheduleBMonth::getTotal);
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].Month2[0].f1_141_0_[0]",
      cents = "topmostSubform[0].Page1[0].Month2[0].f1_142_0_[0]")
  public Money getMonth2Total() {
    return optionally(getMonth2(), ScheduleBMonth::getTotal);
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].Month3[0].f1_205_0_[0]",
      cents = "topmostSubform[0].Page1[0].Month3[0].f1_206_0_[0]")
  public Money getMonth3Total() {
    return optionally(getMonth3(), ScheduleBMonth::getTotal);
  }

  @Target({ElementType.FIELD, ElementType.METHOD})
  @Retention(RetentionPolicy.RUNTIME)
  @PdfBase(ScheduleBPdfMonthFiller.class)
  public @interface PdfScheduleBMonth {
    int month();
  }

  @Slf4j
  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class ScheduleBPdfMonthFiller extends AbstractPdfValueFiller<ScheduleBMonth> {
    private int month;

    @Override
    public void fill(PdfFiller pdfFiller, ScheduleBMonth scheduleBMonth) {
      Pattern fieldNamePattern =
          Pattern.compile(
              "topmostSubform\\[\\d+]\\.Page\\d+\\[\\d+]\\.Month"
                  + month
                  + "\\[\\d+]\\.Day(?<range>\\d+-\\d+)\\[\\d+]\\.f\\d+_\\d+_\\d+_\\[\\d+]");

      Map<DayRange, LinkedList<String>> rangesMap =
          pdfFiller
              .fields()
              .map(input -> fieldNamePattern.matcher(input))
              .filter(Matcher::find)
              .collect(
                  groupingBy(
                      match -> DayRange.fromRange(match.group("range")),
                      mapping(Matcher::group, toCollection(LinkedList::new))));

      Map<Integer, MoneyFields> dayToMoneyFieldsMap = new ConcurrentHashMap<>(31);

      rangesMap.forEach(
          (dayRange, fieldNames) -> {
            for (int day = dayRange.getStart(); day <= dayRange.getEnd(); day++) {
              dayToMoneyFieldsMap.put(day, new MoneyFields(fieldNames.pop(), fieldNames.pop()));
            }
          });

      scheduleBMonth
          .getDays()
          .forEach(
              (day, scheduleBDay) -> {
                var total = scheduleBDay.getTotal();
                if (!total.eq(Money.ZERO)) {
                  var split = total.getAmount().toPlainString().split("\\.");
                  var dollars = split[0];
                  var cents = split.length == 2 ? split[1] : "";
                  var moneyFields = dayToMoneyFieldsMap.get(day);

                  pdfFiller.set(moneyFields.getDollars(), dollars);
                  pdfFiller.set(moneyFields.getCents(), cents);
                }
              });
    }

    @Getter
    @RequiredArgsConstructor
    private enum DayRange {
      Column1(1, 8),
      Column2(9, 16),
      Column3(17, 24),
      Column4(25, 31);
      private final int start, end;

      public static DayRange fromRange(String range) {
        return Arrays.stream(DayRange.values())
            .filter(dayRange -> dayRange.getRange().equals(range))
            .findFirst()
            .orElse(null);
      }

      public String getRange() {
        return start + "-" + end;
      }
    }

    @Value
    private static class MoneyFields {
      private final String dollars, cents;
    }
  }
}
