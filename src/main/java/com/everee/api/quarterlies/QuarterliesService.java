package com.everee.api.quarterlies;

import com.everee.api.company.DetailedCompany;
import com.everee.api.pdf.IPdfFillable;
import com.everee.api.quarterlies.forms.federal.*;
import com.everee.api.quarterlies.forms.federal.scheduleb.FederalForm941ScheduleBGenerator;
import com.everee.api.quarterlies.forms.federal.scheduleb.ScheduleB;
import com.everee.api.quarterlies.forms.federal.scheduleb.ScheduleBPdf;
import com.everee.api.quarterlies.forms.selfcheck.SelfCheckGenerator;
import com.everee.api.quarterlies.forms.state.StateSuperSetOfFieldsQuarterlyFor2019Generator;
import com.google.common.collect.Range;
import java.time.Year;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuarterliesService {
  private final EntityManager entityManager;
  private final StateSuperSetOfFieldsQuarterlyFor2019Generator
      stateSuperSetOfFieldsQuarterlyFor2019Generator;

  private static boolean yearIsBetween(Year year, int startInclusive, int endExclusive) {
    return Range.closedOpen(startInclusive, endExclusive).contains(year.getValue());
  }

  public String getQuarterlyFormCsv(
      QuarterlyForm form, Quarter quarter, Year year, DetailedCompany company) {
    return getGenerator(form, quarter, year, company).generateCsv();
  }

  public Object getQuarterlyForm(
      QuarterlyForm form, Quarter quarter, Year year, DetailedCompany company) {
    return getGenerator(form, quarter, year, company).generate();
  }

  public IPdfFillable getQuarterlyFormPdf(
      QuarterlyForm form, Quarter quarter, Year year, DetailedCompany company) {
    Object quarterlyFormData = getQuarterlyForm(form, quarter, year, company);
    if (quarterlyFormData == null) {
      return null;
    }

    IPdfFillable quarterlyPdf = null;
    switch (form) {
      case FEDERAL_FORM_941:
        // Need to do year selection more elegant in the future, but for now...
        switch (year.getValue()) {
          case 2019:
            quarterlyPdf = new Form941Pdf2019((FederalForm941Data) quarterlyFormData);
            break;
          case 2020:
            switch (quarter) {
              case Q1:
                quarterlyPdf = new Form941Pdf2020((FederalForm941Data) quarterlyFormData);
                break;
              default:
                quarterlyPdf = new Form941Pdf2020Q2((FederalForm941Data) quarterlyFormData);
                break;
            }
            break;
          case 2021:
            quarterlyPdf = new Form941Pdf2021((FederalForm941Data) quarterlyFormData);
            break;
          case 2022:
            quarterlyPdf = new Form941Pdf2022((FederalForm941Data) quarterlyFormData);
            break;
          case 2023:
            quarterlyPdf = new Form941Pdf2023((FederalForm941Data) quarterlyFormData);
            break;
          case 2024:
            quarterlyPdf = new Form941Pdf2024((FederalForm941Data) quarterlyFormData);
            break;
          case 2025:
            quarterlyPdf = new Form941Pdf2025((FederalForm941Data) quarterlyFormData);
            break;
          default:
            throw new UnsupportedOperationException("No 941 pdf exists for year " + year);
        }
        break;
      case FEDERAL_FORM_941_SCHEDULE_B:
        quarterlyPdf = new ScheduleBPdf((ScheduleB) quarterlyFormData);
        break;
    }

    return quarterlyPdf;
  }

  public AbstractQuarterlyFormGenerator<?> getGenerator(
      QuarterlyForm form, Quarter quarter, Year year, DetailedCompany company) {
    AbstractQuarterlyFormGenerator<?> generator;

    generator = findGenerator(form, quarter, year);
    generator.setCompany(company);
    generator.setQuarter(quarter);
    generator.setYear(year);
    generator.setEntityManager(entityManager);

    return generator;
  }

  private AbstractQuarterlyFormGenerator<?> findGenerator(
      QuarterlyForm form, Quarter quarter, Year year) {
    switch (form) {
      case FEDERAL_FORM_941:
        if (yearIsBetween(year, 2019, 3000)) {
          return new FederalForm941Generator();
        }
        break;
      case FEDERAL_FORM_941_SCHEDULE_B:
        if (yearIsBetween(year, 2019, 3000)) {
          return new FederalForm941ScheduleBGenerator();
        }
        break;
      case STATE_QUARTERLY_FORMS:
        return stateSuperSetOfFieldsQuarterlyFor2019Generator;
      case SELF_CHECK:
        return new SelfCheckGenerator();
    }
    throw new UnsupportedOperationException(
        "Unable to find FormGenerator for form=`" + form + "` and year=`" + year + "`");
  }
}
