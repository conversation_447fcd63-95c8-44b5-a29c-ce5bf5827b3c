package com.everee.api.irs.mef;

import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "app.mef")
public class MefProperties {
  @NotNull private String etin;
  @NotNull private String efin;
  @NotNull private String tin;
  @NotNull private String reportingAgentPin;
  @NotNull private String returnVersion;
  @NotNull private String returnVersion940;
  @NotNull private String daytimePhone;
  @NotNull private String softwareId941;
  @NotNull private String softwareId940;
}
