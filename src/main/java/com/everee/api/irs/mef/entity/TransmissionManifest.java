package com.everee.api.irs.mef.entity;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import lombok.Data;

@Data
@XmlRootElement(name = "TransmissionManifest")
@XmlAccessorType(XmlAccessType.FIELD)
public class TransmissionManifest {
  @XmlElement(name = "SubmissionDataList")
  private SubmissionDataList submissionDataList;
}
