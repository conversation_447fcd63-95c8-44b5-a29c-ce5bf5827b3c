package com.everee.api.password;

import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.storage.StorageService;
import com.everee.api.user.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SetPasswordSuccessNotificationService {
  private final PartnerRepository partnerRepository;
  private final NotificationService notificationService;
  private final MessageSource messageSource;
  private final StorageService storageService;

  void sendSetPasswordSuccessMessage(User user) {
    var partner = partnerRepository.findDefaultPartner().orElseThrow();
    var notification = new SetPasswordSuccessNotification(partner, messageSource, storageService);
    notificationService.enqueueUserNotification(notification, user.getId());
  }
}
