package com.everee.api.password;

import org.passay.AllowedCharacterRule;
import org.passay.CharacterRule;
import org.passay.EnglishCharacterData;
import org.passay.LengthRule;
import org.passay.PasswordValidator;
import org.passay.UsernameRule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PasswordComplexityValidatorProvider {

  @Bean("PasswordValidator")
  public PasswordValidator passwordValidator() {
    return new PasswordValidator(
        new LengthRule(8, Integer.MAX_VALUE),
        new CharacterRule(EnglishCharacterData.Alphabetical, 1),
        new CharacterRule(EnglishCharacterData.Digit, 1),
        new UsernameRule());
  }

  @Bean("PinValidator")
  public PasswordValidator pinValidator() {
    return new PasswordValidator(
        new LengthRule(4, Integer.MAX_VALUE),
        new AllowedCharacterRule(EnglishCharacterData.Digit.getCharacters().toCharArray()));
  }
}
