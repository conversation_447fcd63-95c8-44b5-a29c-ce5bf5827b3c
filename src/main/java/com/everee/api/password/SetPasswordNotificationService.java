package com.everee.api.password;

import com.everee.api.notification.email.EmailRecipient;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.storage.StorageService;
import com.everee.api.user.DetailedUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SetPasswordNotificationService {

  private final MessageSource messageSource;
  private final DetailedUserRepository detailedUserRepository;
  private final NotificationService notificationService;
  private final PartnerRepository partnerRepository;
  private final StorageService storageService;

  void sendSetPasswordMessage(SetPasswordToken setPasswordToken) {
    var user = detailedUserRepository.getOne(setPasswordToken.getUserId());
    var partner = partnerRepository.findDefaultPartner().orElseThrow();
    var recipient = new EmailRecipient(user.getDisplayFullName(), user.getEmail());
    var notification =
        new SetPasswordNotification(
            partner,
            setPasswordToken.getUserId(),
            setPasswordToken.getToken(),
            messageSource,
            storageService);

    log.debug("Sending password reset with link: {}", notification.getButtonLinkUrl());

    notificationService.enqueueEmailNotification(notification, recipient);
  }
}
