package com.everee.api.messaging;

import com.everee.api.hazelcast.HazelcastChangeListener;
import com.everee.api.hazelcast.HazelcastService;
import com.everee.api.properties.AppDistributedCacheProperties;
import com.hazelcast.core.EntryEvent;
import java.text.MessageFormat;
import java.util.concurrent.ConcurrentMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedStatusService extends HazelcastChangeListener {
  private final HazelcastService hazelcastService;
  private final SimpMessagingTemplate messagingTemplate;
  private final AppDistributedCacheProperties distributedCacheProperties;

  public ConcurrentMap<String, StatusMessage> getStatusMap(String name) {
    return hazelcastService.getMap(name);
  }

  public void start(StatusMessage status) {
    if (distributedCacheProperties.isEnabled()) {
      sendStatusToAllClients(status);
      status.setListenerUUID(
          hazelcastService.addListener(getStatusMap(status.getMapName()), this, status.getKey()));
    }
  }

  public void end(StatusMessage status) {
    if (distributedCacheProperties.isEnabled()) {
      hazelcastService.removeListener(getStatusMap(status.getMapName()), status.getListenerUUID());
      getStatusMap(status.getMapName()).remove(status.getKey());
    }
  }

  /**
   * As a listener this is called automatically by Hazelcast when an entry is updated
   *
   * @param entryEvent the event invoked when an entry is updated
   */
  @Override
  public void entryUpdated(EntryEvent entryEvent) {
    var status = (StatusMessage) entryEvent.getValue();
    sendStatusToConnectedClients(status);
  }

  public void sendStatusToAllClients(StatusMessage status) {
    if (distributedCacheProperties.isEnabled() && status != null) {
      getStatusMap(status.getMapName()).put(status.getKey(), status);
    }
  }

  public void sendStatusToConnectedClients(StatusMessage status) {
    if (distributedCacheProperties.isEnabled() && status != null) {
      log.info(
          "Sending status to connected clients: ["
              + status.getKey()
              + "] = "
              + status.getFormattedProcessedPercent());
      messagingTemplate.convertAndSend(
          MessageFormat.format(status.getMessageDestination(), status.getKey()), status);
    }
  }
}
