package com.everee.api.unemploymentsettings;

import com.everee.api.auditlog.AuditLogEntityListener;
import com.everee.api.auditlog.EntityDeletedAuditor;
import com.everee.api.model.BaseModel;
import com.everee.api.money.Money;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Entity
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@EntityListeners({AuditLogEntityListener.class, EntityDeletedAuditor.class})
public class FederalUnemploymentOverride extends BaseModel {
  private static final String FEDERAL_WAGE_BASE =
      "(SELECT d.futawagebase FROM federalunemploymentdefault d " + "WHERE d.taxyear = taxYear)";

  @NotNull private Long companyId;

  @NotNull private Integer taxYear;

  @NotNull
  @DecimalMax("0.1")
  private Double futaRate;

  @Setter(AccessLevel.NONE)
  @Formula(FEDERAL_WAGE_BASE)
  private Money futaWageBase;

  @Data
  public static class CreateParams {
    @NotNull private Long companyId;

    @NotNull private Integer taxYear;

    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("0.1")
    private Double futaRate;
  }

  @Data
  public static class UpdateParams {
    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("0.1")
    private Double futaRate;
  }
}
