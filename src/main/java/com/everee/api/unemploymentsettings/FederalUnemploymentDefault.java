package com.everee.api.unemploymentsettings;

import com.everee.api.model.BaseModel;
import com.everee.api.money.Money;
import javax.persistence.Entity;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FederalUnemploymentDefault extends BaseModel {
  @NotNull private Integer taxYear;

  @NotNull
  @DecimalMax("0.1")
  private Double futaRate;

  @NotNull private Money futaWageBase;

  @Data
  public static class CreateParams {
    @NotNull private Integer taxYear;

    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("0.1")
    private Double futaRate;

    @NotNull private Money futaWageBase;
  }

  @Data
  public static class UpdateParams {
    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("0.1")
    private Double futaRate;

    @NotNull private Money futaWageBase;
  }
}
