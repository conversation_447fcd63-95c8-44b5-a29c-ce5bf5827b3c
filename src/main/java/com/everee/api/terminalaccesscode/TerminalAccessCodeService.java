package com.everee.api.terminalaccesscode;

import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.query.Query;
import com.everee.api.terminalsession.TerminalSession_;
import com.everee.api.time.CompanyLocalTimeService;
import java.security.SecureRandom;
import java.util.Set;
import javax.persistence.EntityManager;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TerminalAccessCodeService {
  private final TerminalAccessCodeRepository terminalAccessCodeRepository;
  private final EntityManager entityManager;
  private final CompanyLocalTimeService companyLocalTimeService;

  private Long getRandomCode() {
    // use base36 - confusions
    SecureRandom rnd = new SecureRandom();
    var number = rnd.nextInt(999999 - 111111) + 111111;
    return Long.valueOf(number);
  }

  public Page<TerminalAccessCode> listTerminalAccessCodes(
      @NotNull Pageable pageable, Long companyId, Long locationId) {
    final Query<TerminalAccessCode> terminalAccessCodeQuery =
        new Query<>(entityManager, TerminalAccessCode.class);
    terminalAccessCodeQuery.eqIfPresent(TerminalSession_.LOCATION_ID, locationId);

    return terminalAccessCodeQuery.findAll(pageable);
  }

  public TerminalAccessCode createTerminalAccessCode(
      Long companyId,
      Long userId,
      CreateTerminalAccessCodeRequest createTerminalAccessCodeRequest) {
    var terminalAccessCode = new TerminalAccessCode();
    terminalAccessCode.setNickname(createTerminalAccessCodeRequest.getNickname());
    terminalAccessCode.setCompanyId(companyId);
    terminalAccessCode.setLocationId(createTerminalAccessCodeRequest.getLocationId());
    terminalAccessCode.setCreatedByUserId(userId);
    terminalAccessCode.setCreatedAt(companyLocalTimeService.companyLocalNowTimestamp(companyId));
    terminalAccessCode.setCode(getRandomCode().toString());

    return terminalAccessCodeRepository.save(terminalAccessCode);
  }

  public void deleteTerminalAccessCodes(Set<TerminalAccessCode> terminalAccessCodes) {
    terminalAccessCodeRepository.deleteAll(terminalAccessCodes);
  }

  public void deleteTerminalAccessCode(Long companyId, String accessCode) {
    var terminalAccessCode =
        terminalAccessCodeRepository
            .findById(accessCode)
            .orElseThrow(ResourceNotFoundException::new);
    if (!terminalAccessCode.getCompanyId().equals(companyId)) {
      throw new ResourceNotFoundException();
    }
    terminalAccessCodeRepository.delete(terminalAccessCode);
  }
}
