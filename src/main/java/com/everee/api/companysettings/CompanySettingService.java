package com.everee.api.companysettings;

import com.everee.api.exception.ResourceNotFoundException;

import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

import com.everee.api.model.DateRange;
import com.everee.api.tax.state.State;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanySettingService {
  private final CompanySettingRepository companySettingsRepository;
  private final CompanyEmployeeCountRepository employeeCountRepository;

  public PageImpl<CompanySetting> getAllPaginated(Long companyId, List<String> settingIds, Integer year, Pageable pageable) {

    if (year == null || year == 0) {
      year = Year.now().getValue();
    }

    // Fetch predefined settings from the other service
    var predefinedSettings = getReadOnlySettingsList(companyId, year);

    // Fetch paginated settings from the database
    Page<CompanySetting> dbSettingsPage;
    if (settingIds == null) {
      dbSettingsPage = getAllPaginated(companyId, pageable);
    } else {
      dbSettingsPage = getBySettingIdsPaginated(companyId, settingIds, pageable);
    }

    // Convert the database page content to a list
    List<CompanySetting> dbSettings = dbSettingsPage.getContent();

    // Merge predefined settings with paginated database settings
    List<CompanySetting> allSettings = new ArrayList<>(predefinedSettings);
    allSettings.addAll(dbSettings);

    // Calculate the new total size
    int totalSize = predefinedSettings.size() + (int) dbSettingsPage.getTotalElements();

    // Apply pagination
    int start = (int) pageable.getOffset();
    int end = Math.min((start + pageable.getPageSize()), totalSize);
    List<CompanySetting> paginatedSettings = allSettings.subList(start, Math.min(end, allSettings.size()));

    // Create a new Page object
    return new PageImpl<>(paginatedSettings, pageable, totalSize);
  }
  protected PageImpl<CompanySetting> getAllPaginated(Long companyId, Pageable pageable) {
    List<CompanySetting> companySettingsList;

    try (var stream =
        companySettingsRepository.streamCompanySettingsWithDefaults(companyId, pageable)) {
      companySettingsList = stream.collect(Collectors.toList());
    }

    var totalCount = companySettingsRepository.countCompanySettings();

    return new PageImpl<>(companySettingsList, pageable, totalCount);
  }

  protected PageImpl<CompanySetting> getBySettingIdsPaginated(
      Long companyId, List<String> settingIds, Pageable pageable) {
    List<CompanySetting> companySettingsList;

    try (var stream =
        companySettingsRepository.streamBySettingIdsWithDefaults(companyId, settingIds, pageable)) {
      companySettingsList = stream.collect(Collectors.toList());
    }

    var totalCount = companySettingsRepository.countCompanySettingsBySettingIds(settingIds);

    return new PageImpl<>(companySettingsList, pageable, totalCount);
  }

  public CompanySetting updateOrCreate(Long companyId, String settingId, CompanySetting request) {
    var companySettingWithDefault =
        companySettingsRepository.findBySettingIdsWithDefaults(
            companyId, Collections.singletonList(settingId));

    if (companySettingWithDefault.isEmpty()) {
      throw new ResourceNotFoundException("Company setting not found with id: " + settingId);
    }

    var companySetting =
        companySettingsRepository.findByCompanyIdAndSettingId(companyId, settingId);

    // creating company setting for the first time
    if (companySetting == null)
      companySetting = new CompanySetting(companyId, settingId, request.getValue());
    else companySetting.setValue(request.getValue());

    return companySettingsRepository.save(companySetting);
  }

  public int getSutaPaidEmployeeCount(Long companyId, Integer year, Set<State> states) {
    var startDate = LocalDate.parse(year + "-01-01");
    var endDate = LocalDate.parse(year + "-12-31");

    return employeeCountRepository.getSutaPaidEmployeeCount(companyId, DateRange.of(startDate, endDate), states);
  }

  public CompanySetting findOneBySettingsId(Long companyId, String settingId) {
    return companySettingsRepository.findByCompanyIdAndSettingId(companyId,settingId);
  }

  public CompanySetting getReadOnlySettingsBySettingsId(Long companyId, ReadOnlyCompanySetting readOnlySettingsId, Integer year) {
    switch (readOnlySettingsId) {
      case IL_STATE_TAX_EMPLOYER_UNEMPLOYMENT: // KIWI-110
        var employeeCount = getSutaPaidEmployeeCount(companyId, /* (prior year) */ year - 1, Set.of(State.IL));
        var companySetting = new CompanySetting();
        companySetting.setCompanyId(companyId);
        companySetting.setSettingId("IL_STATE_TAX_EMPLOYER_UNEMPLOYMENT");
        companySetting.setLabel("Number of employees is 25 or more");
        companySetting.setValue(employeeCount >= 25 ? "Yes" : "No");
        companySetting.setDescription("Yes if 25 or more employees are employed annually in Illinois, which requires the monthly wage report to be filed.");
        companySetting.setGroupLabel("Taxes | Illinois Unemployment - Employer");

        return companySetting;
      default:
        throw new SettingNotSupportedException();
    }
  }

  public List<CompanySetting> getReadOnlySettingsList(Long companyId, Integer year) {
    return Arrays
      .stream(ReadOnlyCompanySetting.values())
      .map(setting -> getReadOnlySettingsBySettingsId(companyId, setting, year))
      .collect(Collectors.toList());
  }
}
