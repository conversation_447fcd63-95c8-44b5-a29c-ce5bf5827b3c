package com.everee.api.companysettings;

public enum ReadOnlyCompanySetting {
  IL_STATE_TAX_EMPLOYER_UNEMPLOYMENT("IL_STATE_TAX_EMPLOYER_UNEMPLOYMENT");

  private final String settingId;
  ReadOnlyCompanySetting(String settingId) {
    this.settingId = settingId;
  }

  public String getSettingId() {
    return settingId;
  }

  public static ReadOnlyCompanySetting fromSettingId(String settingId) {
    for (ReadOnlyCompanySetting setting : values()) {
      if (setting.getSettingId().equals(settingId)) {
        return setting;
      }
    }

    return null;
  }
}
