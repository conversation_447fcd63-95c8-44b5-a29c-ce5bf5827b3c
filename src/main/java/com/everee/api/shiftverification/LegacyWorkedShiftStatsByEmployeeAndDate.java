package com.everee.api.shiftverification;

import com.everee.api.approvalgroup.ApprovalGroup;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.CoreEmployee;
import com.everee.api.timeclock.LegacyWorkedShift;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import javax.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.Immutable;

@Data
@Entity
@Immutable
@Deprecated
@Table(name = "ShiftStatsByEmployeeAndDate")
public class LegacyWorkedShiftStatsByEmployeeAndDate implements Serializable {

  @Id private String id;

  private Long companyId;

  @OneToOne
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  private Long employeeId;

  private Long approvalGroupId;

  @OneToOne
  @JoinColumn(name = "approvalGroupId", insertable = false, updatable = false)
  private ApprovalGroup approvalGroup;

  @ManyToOne
  @JoinColumn(name = "employeeId", insertable = false, updatable = false)
  private CoreEmployee employee;

  private LocalDate workLocationEffectivePunchInDate;

  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumns({
    @JoinColumn(
        name = "workLocationEffectivePunchInDate",
        insertable = false,
        updatable = false,
        referencedColumnName = "workLocationEffectivePunchInDate"),
    @JoinColumn(
        name = "employeeId",
        insertable = false,
        updatable = false,
        referencedColumnName = "employeeId")
  })
  @Where(clause = "verified = false")
  private List<LegacyWorkedShift> shifts;

  private Duration totalDuration;

  private Long totalShifts;
  private Long totalVerified;
  private Long totalUnverified;
  private Long totalEditable;

  @SuppressWarnings("unused")
  public String getEmployeeFirstName() {
    return Optional.ofNullable(employee).map(CoreEmployee::getFirstName).orElse(null);
  }

  @SuppressWarnings("unused")
  public String getEmployeeLastName() {
    return Optional.ofNullable(employee).map(CoreEmployee::getLastName).orElse(null);
  }

  @SuppressWarnings("unused")
  public String getEmployeeDisplayFullName() {
    return Optional.ofNullable(employee).map(CoreEmployee::getDisplayFullName).orElse(null);
  }
}
