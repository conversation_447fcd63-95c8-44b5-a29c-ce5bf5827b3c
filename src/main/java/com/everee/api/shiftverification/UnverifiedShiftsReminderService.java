package com.everee.api.shiftverification;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;

import com.everee.api.approvalgroup.ApprovalGroupApprover;
import com.everee.api.approvalgroup.ApprovalGroupApproverRepository;
import com.everee.api.approvalgroup.ApprovalScope;
import com.everee.api.company.Company;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.configurations.CompanyTimeTrackingConfiguration;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.link.DynamicLinkBuilder;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.storage.StorageService;
import com.everee.api.user.DetailedUserRepository;
import com.everee.api.user.User;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnverifiedShiftsReminderService {

  private final MessageSource messageSource;
  private final NotificationService notificationService;
  private final LegacyWorkedShiftVerificationService shiftVerificationService;
  private final DetailedUserRepository detailedUserRepository;
  private final ApprovalGroupApproverRepository approvalGroupApproverRepository;
  private final PartnerRepository partnerRepository;
  private final StorageService storageService;

  @Transactional(readOnly = true)
  public void sendAll(Duration granularity) {
    log.debug("Sending reminders for unverified shifts");

    var activeWindow = Window.before(LocalTime.now(), granularity);

    // FIXME: Needs optimization (i.e. select distinct companyid...) --kjensen 6/15/22
    shiftVerificationService.streamAllUnverifiedShifts(Pageable.unpaged())
        .collect(Collectors.groupingBy(LegacyWorkedShiftStatsByEmployeeAndDate::getCompany))
        .entrySet().stream()
        .filter(entry -> shouldFire(entry.getKey(), activeWindow))
        .forEach(entry -> notifyRecipients(entry.getKey(), entry.getValue()));
  }

  private void notifyRecipients(
      DetailedCompany company, List<LegacyWorkedShiftStatsByEmployeeAndDate> data) {
    if (shouldSendForCompany(company)) {
      log.info("Sending shift verification reminders for {}", company.getDisplayName());
      var reminder = buildNotification(company);
      streamAllRecipients(company, data).forEach(userId -> send(reminder, userId));
    } else {
      log.info("Skipping shift verification reminders for {}", company.getDisplayName());
    }
  }

  private UnverifiedShiftsReminder buildNotification(DetailedCompany company) {
    var partner = partnerRepository.findByCompanyId(company.getId()).orElseThrow();
    var companyId = company.getId();
    var companyName = company.getDisplayName();

    return new UnverifiedShiftsReminder(
        partner, companyId, companyName, messageSource, getLinkUrl(), storageService);
  }

  private Set<Long> streamAllRecipients(
      Company company, List<LegacyWorkedShiftStatsByEmployeeAndDate> data) {
    var recipientUserIds = new HashSet<Long>();

    // Check for presence of null approvalGroupId on any shift
    var shiftWithNoGroupExists =
        data.stream()
            .map(LegacyWorkedShiftStatsByEmployeeAndDate::getApprovalGroupId)
            .anyMatch(Objects::isNull);

    var shiftApprovalGroupsIds = new HashSet<Long>();
    var coveredApprovalGroupIds = new HashSet<Long>();

    // Get non-null approvalGroupIds, look up approvers, and get their user ids
    data.stream()
        .map(LegacyWorkedShiftStatsByEmployeeAndDate::getApprovalGroupId)
        .filter(Objects::nonNull)
        .peek(shiftApprovalGroupsIds::add)
        .map(
            approvalGroupId ->
                approvalGroupApproverRepository.findByCompanyIdAndApprovalGroupIdAndNotSuspended(
                    company.getId(), approvalGroupId, Pageable.unpaged()))
        .flatMap(Page::stream)
        .filter(
            approvalGroupApprover -> approvalGroupApprover.hasApprovalScope(ApprovalScope.LABOR))
        .peek(approver -> coveredApprovalGroupIds.add(approver.getApprovalGroupId()))
        .map(ApprovalGroupApprover::getUserId)
        .forEach(recipientUserIds::add);

    // Check for presence of approval groups without an approver
    var shiftWithNoApproverExist = shiftApprovalGroupsIds.size() > coveredApprovalGroupIds.size();

    // if there are any non-grouped shifts or groups without approvers: notify the admins
    if (shiftWithNoGroupExists || shiftWithNoApproverExist) {
      detailedUserRepository
          .streamAllByCompanyRole(company.getId(), CompanyRoleType.FINANCIAL_MANAGER)
          .map(User::getId)
          .forEach(recipientUserIds::add);
    }

    return recipientUserIds;
  }

  private void send(UnverifiedShiftsReminder reminder, Long userId) {
    notificationService.enqueueUserNotification(reminder, userId);
  }

  private boolean shouldFire(DetailedCompany company, Window activeWindow) {
    var desiredTriggerTime =
        Optional.ofNullable(company.getTimeTrackingConfiguration())
            .map(CompanyTimeTrackingConfiguration::getVerificationReminderTime)
            .orElse(null);
    var desiredTriggerDays =
        Optional.ofNullable(company.getTimeTrackingConfiguration())
            .map(CompanyTimeTrackingConfiguration::getVerificationReminderDays)
            .orElse(Collections.emptySet());

    if (desiredTriggerTime == null) {
      return false;
    }

    var companyNow =
        company
            .getNowDate()
            .atTime(desiredTriggerTime)
            .atZone(company.getWorkweekConfig().getTimezone());

    var isDesiredTriggerDay = desiredTriggerDays.contains(companyNow.getDayOfWeek());

    var companyTimeZoneTriggerTime = companyNow.withZoneSameInstant(UTC_ZONE_ID).toLocalTime();

    return isDesiredTriggerDay && activeWindow.contains(companyTimeZoneTriggerTime);
  }

  private String getLinkUrl() {
    return new DynamicLinkBuilder().pathSegment("timesheet", "verify").build().toString();
  }

  private static boolean shouldSendForCompany(DetailedCompany company) {
    return Optional.ofNullable(company)
        .map(DetailedCompany::getTimeTrackingConfiguration)
        .map(CompanyTimeTrackingConfiguration::isVerificationEnabled)
        .orElse(false);
  }

  @Data
  private static class Window {
    private final LocalTime startTime;
    private final LocalTime endTime;

    boolean contains(LocalTime time) {
      return time.isAfter(startTime) && time.isBefore(endTime) || time.equals(endTime);
    }

    static Window before(LocalTime referenceTime, Duration duration) {
      return new Window(referenceTime.minus(duration), referenceTime);
    }
  }
}
