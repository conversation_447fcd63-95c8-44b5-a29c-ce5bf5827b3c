package com.everee.api.shiftverification;

import static com.everee.api.query.where.Where.property;
import static com.everee.api.shiftverification.LegacyWorkedShiftStatsByEmployeeAndDate_.*;

import com.everee.api.approvalgroup.ApprovalScope;
import com.everee.api.auth.util.AuthorizationCheckService;
import com.everee.api.query.Query;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import javax.validation.constraints.NotNull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LegacyWorkedShiftVerificationService {

  private final EntityManager entityManager;
  private final AuthorizationCheckService authorizationCheckService;

  public Page<LegacyWorkedShiftStatsByEmployeeAndDate> listUnverifiedShifts(
      @NotNull Pageable pageable, @NotNull Long companyId) {
    return findUnverifiedQuery(companyId).findAll(pageable);
  }

  public Stream<LegacyWorkedShiftStatsByEmployeeAndDate> streamAllUnverifiedShifts(
      @NotNull Pageable pageable) {
    return findUnverifiedQuery().streamAll(pageable);
  }

  private Query<LegacyWorkedShiftStatsByEmployeeAndDate> findUnverifiedQuery(
      @NonNull Long companyId) {
    var query = findUnverifiedQuery().where(property(COMPANY_ID).equal(companyId));
    if (authorizationCheckService.isFinancialManager(companyId)) {
      // Financial managers should see unverified shifts for the entire company
      return query;
    } else {
      var approvalGroupIds =
          authorizationCheckService.getAuthorizedApprovalGroupIds(ApprovalScope.LABOR, companyId);
      return query.where(property(APPROVAL_GROUP_ID).in(approvalGroupIds));
    }
  }

  private Query<LegacyWorkedShiftStatsByEmployeeAndDate> findUnverifiedQuery() {
    return new Query<>(entityManager, LegacyWorkedShiftStatsByEmployeeAndDate.class)
        .where(property(TOTAL_EDITABLE).greaterThan(0))
        .where(property(TOTAL_UNVERIFIED).greaterThan(0));
  }
}
