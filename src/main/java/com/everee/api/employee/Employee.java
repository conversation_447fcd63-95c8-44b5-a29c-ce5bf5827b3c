package com.everee.api.employee;

import com.everee.api.model.EmploymentType;
import com.everee.api.money.Money;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.payment.PaymentMethod;
import com.everee.api.payment.PaymentType;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.phase.Phased;
import com.everee.api.util.DisplayFullName;
import java.io.Serializable;
import java.util.EnumSet;

public interface Employee extends Serializable, Phased, DisplayFullName {
  String FIRST_NAME_FORMULA = "coalesce((select u.firstname from appuser u where u.id = userid))";

  String MIDDLE_NAME_FORMULA = "coalesce((select u.middlename from appuser u where u.id = userid))";

  String LAST_NAME_FORMULA = "coalesce((select u.lastname from appuser u where u.id = userid))";

  String FULL_NAME_FORMULA =
      "(select "
          + "concat(u.firstname, ' ', case when u.middlename is null or u.middlename = '' then null else concat(substring(u.middlename from 1 for 1), ' ') end, u.lastname)"
          + "from appuser u where u.id = userid)";

  String FIRST_LAST_NAME_FORMULA =
      "(select concat(u.firstname, ' ', u.lastname) from appuser u where u.id = userid)";

  String DISPLAY_FULL_NAME_FORMULA =
      "(select "
          + "concat(u.lastname, ', ', u.firstname, case when u.middlename is null or u.middlename = '' then null else concat(' ', substring(u.middlename from 1 for 1)) end)"
          + "from appuser u where u.id = userid)";

  String BUSINESS_NAME_FORMULA =
      "(SELECT pt.businessname FROM payeetype pt WHERE pt.userid = userid)";

  String PAYEE_TYPE_NAME_FORMULA =
      "coalesce((SELECT pt.typeName FROM payeetype pt WHERE pt.userid = userid), 'INDIVIDUAL')";

  String ACCOUNT_LABEL_BY_USER_ID_FORMULA =
      "(SELECT "
          + "    CASE WHEN pt.typename = 'BUSINESS' AND trim(pt.businessname) <> '' THEN pt.businessname ELSE concat(u.firstname, ' ', case when u.middlename is null or u.middlename = '' then null else concat(substring(u.middlename from 1 for 1), ' ') end, u.lastname) END "
          + "        FROM appuser u "
          + "        JOIN payeetype pt on u.id = pt.userid"
          + " WHERE u.id = userid)";

  String FULL_ACCOUNT_LABEL_BY_USER_ID_FORMULA =
      "(SELECT "
          + "    CASE WHEN pt.typename = 'BUSINESS' AND trim(pt.businessname) <> '' THEN pt.businessname ELSE concat(u.lastname, ', ', u.firstname, case when u.middlename is null or u.middlename = '' then null else concat(' ', substring(u.middlename from 1 for 1)) end)  END "
          + "        FROM appuser u "
          + "        JOIN payeetype pt on u.id = pt.userid"
          + " WHERE u.id = userid)";

  String FULL_ACCOUNT_LABEL_BY_EMPLOYEE_ID_FORMULA =
      "(SELECT "
          + "    CASE WHEN pt.typename = 'BUSINESS' AND trim(pt.businessname) <> '' THEN pt.businessname ELSE concat(u.lastname, ', ', u.firstname, case when u.middlename is null or u.middlename = '' then null else concat(' ', substring(u.middlename from 1 for 1)) end)  END "
          + "        FROM appuser u "
          + "        JOIN payeetype pt on u.id = pt.userid "
          + "        JOIN employee e ON e.userid = u.id "
          + " WHERE e.id = employeeId)";

  String ACCOUNT_LABEL_BY_EMPLOYEE_ID_FORMULA =
      "(SELECT "
          + "    CASE WHEN pt.typename = 'BUSINESS' AND trim(pt.businessname) <> '' THEN pt.businessname ELSE concat(u.firstname, ' ', case when u.middlename is null or u.middlename = '' then null else concat(substring(u.middlename from 1 for 1), ' ') end, u.lastname) END "
          + "        FROM appuser u "
          + "        JOIN payeetype pt on u.id = pt.userid "
          + "        JOIN employee e ON e.userid = u.id "
          + " WHERE e.id = employeeId)";

  String DATE_OF_BIRTH_FORMULA =
      "coalesce((select u.dateofbirth from appuser u where u.id = userid))";

  String TAX_PAYER_IDENTIFIER_FORMULA =
      "coalesce((select coalesce(u.taxpayerIdentifier, u.unverifiedTaxpayerIdentifier) from appuser u where u.id = userid))";

  Long getId();

  Long getUserId();

  Long getCompanyId();

  Long getApprovalGroupId();

  String getWorkerId();

  EmploymentType getEmploymentType();

  String getAccountLabel();

  String getFullAccountLabel();

  boolean isDailyPywPermitted();

  boolean isWeeklyPywPermitted();

  Long getSchedulingWorkLocationId();

  Money getSeveranceAmount();

  String getExternalWorkerId();

  boolean isHasNonResidentCertificate();

  boolean isHasNexus();

  String getBusinessName();

  PayeeTypeName getPayeeTypeName();

  String getTaxpayerIdentifier();

  EnumSet<PaymentType> getSupportedPaymentTypes();

  PaymentMethod getPreferredPaymentMethod();

  PaymentDistributionType getPreferredPaymentDistributionType();
}
