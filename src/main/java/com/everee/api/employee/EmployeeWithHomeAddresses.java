package com.everee.api.employee;

import com.everee.api.tax.state.State;
import java.time.LocalDate;

public interface EmployeeWithHomeAddresses {
  Long getEmployeeId();

  String getFullName();

  String getLastName();

  String getFirstName();

  String getMiddleName();

  String getEmail();

  String getPhoneNumber();

  LocalDate getDateOfBirth();

  String getAddressLine1();

  String getAddressLine2();

  String getAddressCity();

  State getAddressState();

  String getAddressPostalCode();

  String getWorkerId();
}
