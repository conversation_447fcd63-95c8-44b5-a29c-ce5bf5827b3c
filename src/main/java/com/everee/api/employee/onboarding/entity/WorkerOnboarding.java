package com.everee.api.employee.onboarding.entity;

import com.everee.api.approvalgroup.ApprovalGroup;
import com.everee.api.config.datetime.UtcLocalDateTimeSerializer;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.Employee;
import com.everee.api.employee.onboarding.WorkerOnboardingStatus;
import com.everee.api.model.BaseModelV2;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Optional;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Entity
@Table(name = "workeronboarding")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WorkerOnboarding extends BaseModelV2 {
  private static final String INVITATION_SENT_BY_USER_DISPLAY_NAME_FORMULA =
      "(select "
          + "concat(u.lastname, ', ', u.firstname, case when u.middlename is null or u.middlename = '' then null else concat(' ', substring(u.middlename from 1 for 1)) end)"
          + "from appuser u where u.id = invitationSentByUserId)";

  private static final String WORKER_CREATED_BY_USER_DISPLAY_NAME_FORMULA =
      "(select "
          + "concat(u.lastname, ', ', u.firstname, case when u.middlename is null or u.middlename = '' then null else concat(' ', substring(u.middlename from 1 for 1)) end)"
          + "from appuser u where u.id = workerCreatedByUserId)";

  private static final String PAYABLE_FORMULA =
      "(SELECT (o.status = 'COMPLETE' "
          + "   AND o.documentsverifiedat IS NOT NULL "
          + "   AND (u.taxpayeridentifier IS NOT NULL "
          + "     OR u.unverifiedtaxpayeridentifier IS NOT NULL "
          + "     OR c.allowmissingssnsenabled) "
          + "   AND COUNT(a.id) > 0 "
          + "   AND COUNT(b.id) > 0)"
          + "   FROM workeronboarding o"
          + "   JOIN employee e ON e.id=o.employeeid"
          + "   JOIN appuser u ON u.id=e.userid"
          + "   JOIN company c ON c.id=e.companyid"
          + "   LEFT JOIN useraddress a ON a.userid=e.userid"
          + "   LEFT JOIN userbankaccount b ON b.userid=e.userid"
          + "   WHERE o.id=id"
          + "   GROUP BY o.status, o.documentsverifiedat, u.taxpayeridentifier, u.unverifiedtaxpayeridentifier, c.allowmissingssnsenabled)";

  private static final String CLAIMED_FORMULA = "(select claimedat is not null)";

  private static final String COMPLETE_FORMULA = "(select completedonboardingat is not null)";

  @JsonIgnore
  @OneToOne
  @JoinColumn(name = "employeeId")
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  private DetailedEmployee employee;

  @JsonIgnore
  @Column(insertable = false, updatable = false)
  private Long employeeId;

  @Formula("(select e.integrationid from employee e where e.id = employeeid)")
  @Setter(AccessLevel.NONE)
  private String workerId;

  @Formula("(select e.externalworkerid from employee e where e.id = employeeid)")
  @Setter(AccessLevel.NONE)
  private String externalWorkerId;

  @NotNull private Long companyId;

  @Enumerated(EnumType.STRING)
  private WorkerOnboardingStatus status = WorkerOnboardingStatus.NOT_STARTED;

  @JsonIgnore private String invitationToken;

  private Long workerCreatedByUserId;

  @Setter(AccessLevel.NONE)
  @Formula(WORKER_CREATED_BY_USER_DISPLAY_NAME_FORMULA)
  private String workerCreatedByUserDisplayName;

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  private LocalDateTime claimedAt;

  private LocalDateTime personalInformationVerifiedAt;

  private LocalDateTime contactInformationVerifiedAt;

  private LocalDateTime payFrequencyVerifiedAt;

  private LocalDateTime directDepositVerifiedAt;

  private LocalDateTime withholdingsVerifiedAt;

  private LocalDateTime documentsVerifiedAt;

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  private LocalDateTime completedOnboardingAt;

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  private LocalDateTime lockedAt;

  @Column(name = "invitationsentbyuserid")
  private Long lastOnboardingReminderSentByUserId;

  @Column(name = "lastinvitationremindersentat")
  private ZonedDateTime lastOnboardingReminderSentAt;

  @NotNull
  @Column(name = "invitationreminderssentcount")
  private Integer automatedOnboardingRemindersSentCount = 0;

  @Column(name = "nextinvitationreminderat")
  private ZonedDateTime nextAutomatedOnboardingReminderScheduledAt;

  @JsonIgnore private Integer accountLookupAttempts = 0;
  @JsonIgnore private Integer claimAttempts = 0;

  @Setter(AccessLevel.NONE)
  @Formula(PAYABLE_FORMULA)
  private boolean payable;

  @Setter(AccessLevel.NONE)
  @Formula(CLAIMED_FORMULA)
  private boolean claimed;

  @Setter(AccessLevel.NONE)
  @Formula(COMPLETE_FORMULA)
  private boolean complete;

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getPersonalInformationVerifiedAt() {
    return Optional.ofNullable(personalInformationVerifiedAt).orElse(completedOnboardingAt);
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getContactInformationVerifiedAt() {
    return Optional.ofNullable(contactInformationVerifiedAt).orElse(completedOnboardingAt);
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getPayFrequencyVerifiedAt() {
    return Optional.ofNullable(payFrequencyVerifiedAt).orElse(completedOnboardingAt);
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getDirectDepositVerifiedAt() {
    return Optional.ofNullable(directDepositVerifiedAt).orElse(completedOnboardingAt);
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getWithholdingsVerifiedAt() {
    return Optional.ofNullable(withholdingsVerifiedAt).orElse(completedOnboardingAt);
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getDocumentsVerifiedAt() {
    return Optional.ofNullable(documentsVerifiedAt).orElse(completedOnboardingAt);
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public boolean isOnboardingReminderSent() {
    return lastOnboardingReminderSentAt != null;
  }

  public boolean isLocked() {
    return lockedAt != null;
  }

  public boolean isPersonalInformationVerified() {
    return getPersonalInformationVerifiedAt() != null;
  }

  public boolean isContactInformationVerified() {
    return getContactInformationVerifiedAt() != null;
  }

  public boolean isPayFrequencyVerified() {
    return getPayFrequencyVerifiedAt() != null;
  }

  public boolean isDirectDepositVerified() {
    return getDirectDepositVerifiedAt() != null;
  }

  public boolean isWithholdingsVerified() {
    return getWithholdingsVerifiedAt() != null;
  }

  public boolean isDocumentsVerified() {
    return documentsVerifiedAt != null;
  }

  public boolean isComplete() {
    return status == WorkerOnboardingStatus.COMPLETE;
  }

  public String getDisplayFullName() {
    return Optional.ofNullable(getEmployee()).map(Employee::getDisplayFullName).orElse(null);
  }

  public String getFullName() {
    return Optional.ofNullable(getEmployee()).map(Employee::getFullName).orElse(null);
  }

  public String getApprovalGroupName() {
    return Optional.ofNullable(getEmployee())
        .map(DetailedEmployee::getApprovalGroup)
        .map(ApprovalGroup::getName)
        .orElse(null);
  }

  public String getTitle() {
    return Optional.ofNullable(getEmployee()).map(DetailedEmployee::getTitle).orElse(null);
  }

  @SuppressWarnings("unused")
  public LocalDate getHireDate() {
    return Optional.ofNullable(getEmployee()).map(DetailedEmployee::getStartDate).orElse(null);
  }

  public void incrementAccountLookupAttempts() {
    this.accountLookupAttempts++;
  }

  public void incrementClaimAttempts() {
    this.claimAttempts++;
  }
}
