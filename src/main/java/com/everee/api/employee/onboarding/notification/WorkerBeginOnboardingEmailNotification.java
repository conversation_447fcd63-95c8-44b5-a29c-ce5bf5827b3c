package com.everee.api.employee.onboarding.notification;

import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import java.net.URI;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class WorkerBeginOnboardingEmailNotification extends TransactionalSingleActionEmailTemplate {
  @NonNull Partner partner;
  @NonNull String companyName;
  @NonNull URI onboardingLink;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;

  @Override
  public boolean isDeliveryCritical() {
    return true;
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.preheader", getCompanyName(), getPartnerName());
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.banner-text", getCompanyName(), getPartnerName());
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.body-paragraph1-text",
        getCompanyName(),
        getPartnerName());
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage("worker.onboarding.invitation.email.body-paragraph2-text");
  }

  @Override
  public String getBodyParagraph3Text() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.body-paragraph3-text", getCompanyName());
  }

  @Override
  public String getBodyParagraph4Text() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.body-paragraph4-text",
        getPartner().getSupportEmailAddress());
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("worker.onboarding.invitation.email.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    log.debug("Preparing onboarding invitation email with link: {}", onboardingLink);
    return onboardingLink.toString();
  }

  @Override
  public String getEmailSubject() {
    return getLocalizedMessage("worker.onboarding.invitation.email.subject", getCompanyName());
  }

  @Override
  public EmailType getEmailType() {
      return EmailType.SEND_BEGIN_ONBOARDING;
  }
}
