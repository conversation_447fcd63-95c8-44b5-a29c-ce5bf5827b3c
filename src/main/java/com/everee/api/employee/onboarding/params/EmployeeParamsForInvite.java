package com.everee.api.employee.onboarding.params;

import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.worker.createparams.WorkerLegalWorkLocationParamsForCreate;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EmployeeParamsForInvite extends WorkerParamsForInvite {
  @NotNull private WorkerLegalWorkLocationParamsForCreate legalWorkAddress;

  @NotNull
  @JsonProperty("typicalWeeklyHours")
  private Integer expectedWeeklyHours;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PayType payType;

  @NotNull private Money payRate;

  private Boolean eligibleForOvertime;

  private String title;

  private Long workersCompClassId;

  private boolean statutoryEmployee;

  @Enumerated(EnumType.STRING)
  private PaymentDistributionType preferredPaymentDistributionType;

  private boolean corporateOfficer;
}
