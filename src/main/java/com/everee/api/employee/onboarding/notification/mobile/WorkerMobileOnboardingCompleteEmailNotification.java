package com.everee.api.employee.onboarding.notification.mobile;

import static com.everee.api.notification.user.UserNotificationChannel.EMPLOYEE_MOBILE_ONBOARDING_COMPLETE;

import com.everee.api.link.WebappLinkBuilder;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.notification.user.UserNotification;
import com.everee.api.notification.user.UserNotificationChannel;
import com.everee.api.partner.Partner;
import com.everee.api.properties.AppSupportProperties;
import com.everee.api.storage.StorageService;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import org.springframework.context.MessageSource;

@Value
@EqualsAndHashCode(callSuper = true)
public class WorkerMobileOnboardingCompleteEmailNotification
    extends TransactionalSingleActionEmailTemplate implements UserNotification {

  /**
   * Json example to use with sendgrid:
   *
   * <p>{ "company_name":"Boomerang", "banner_text": "Congra<PERSON>, <PERSON>!", "subject": "Congrats!
   * Onboarding is now complete", "preheader":"Onboarding is now complete", "body_paragraph1_text":
   * "You’re all set up to get paid with Everee! Thanks for taking the time to onboard with us.",
   * "body_paragraph2_text": "Use the mobile app to get quick access to your pay info. Of course,
   * you can always get that info in your account portal at app.everee.com", "body_paragraph3_text":
   * null, "body_paragraph4_text": null, "body_paragraph5_text": null, "button_label": "View payment
   * details", "button_url":"http://app.everee.com/", "partner_name":null, "partner_logo_url":null,
   * "partner_contact_info":"Everee, Inc. 26 Rio Grande Street, Ste 2072\nSalt Lake City, Utah
   * 84101", "partner_secondary_color":"#604F7B" }
   */
  @NonNull Partner partner;

  @NonNull String companyName;
  @NonNull String workerFirstName;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;
  @NonNull AppSupportProperties supportProperties;
  @NonNull Long companyId;

  @Override
  public boolean isDeliveryCritical() {
    return true;
  }

  @Override
  public String getTitle() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.push.title");
  }

  @Override
  public String getMessage() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.push.message");
  }

  @Override
  public UserNotificationChannel getChannel() {
    return EMPLOYEE_MOBILE_ONBOARDING_COMPLETE;
  }

  @Override
  public String getEmailSubject() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.email.subject");
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.email.preheader");
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage(
        "worker.mobile.onboarding-complete.email.banner_text", workerFirstName);
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.email.button_label");
  }

  @Override
  public String getButtonLinkUrl() {
    var builder = new WebappLinkBuilder().pathSegment("mobile-account-setup");
    return builder.build().toString();
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.email.body_paragraph1_text");
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage("worker.mobile.onboarding-complete.email.body_paragraph2_text");
  }
}
