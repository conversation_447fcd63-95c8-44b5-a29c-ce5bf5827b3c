package com.everee.api.employee.onboarding.notification;

import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import java.net.URI;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class EpcOnlyWorkerBeginOnboardingEmailNotification
    extends TransactionalSingleActionEmailTemplate {
  @NonNull Partner partner;
  @NonNull String companyName;
  @NonNull String workerFirstName;
  @NonNull URI onboardingLink;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;

  @Override
  public boolean isDeliveryCritical() {
    return true;
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage("worker.onboarding.invitation.email.epc-only.preheader");
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.epc-only.banner-text", workerFirstName);
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage(
        "worker.onboarding.invitation.email.epc-only.body-paragraph1-text", getCompanyName());
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage("worker.onboarding.invitation.email.epc-only.body-paragraph2-text");
  }

  @Override
  public String getBodyParagraph3Text() {
    return getLocalizedMessage("worker.onboarding.invitation.email.epc-only.body-paragraph3-text");
  }

  @Override
  public String getBodyParagraph5Text() {
    return getLocalizedMessage("worker.onboarding.invitation.email.epc-only.body-paragraph5-text");
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("worker.onboarding.invitation.email.epc-only.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    log.debug("Preparing onboarding invitation email with link: {}", onboardingLink);
    return onboardingLink.toString();
  }

  @Override
  public String getEmailSubject() {
    return getLocalizedMessage("worker.onboarding.invitation.email.epc-only.subject");
  }
}
