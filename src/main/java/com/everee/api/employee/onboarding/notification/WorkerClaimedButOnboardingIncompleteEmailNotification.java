package com.everee.api.employee.onboarding.notification;

import static com.everee.api.notification.user.UserNotificationChannel.EMPLOYEE_ONBOARDING_REMINDER;

import com.everee.api.link.WebappLinkBuilder;
import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.notification.user.UserNotification;
import com.everee.api.notification.user.UserNotificationChannel;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class WorkerClaimedButOnboardingIncompleteEmailNotification
    extends TransactionalSingleActionEmailTemplate implements UserNotification {
  @NonNull Partner partner;
  @NonNull String companyName;
  @NonNull String workerEmail;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;
  boolean routeToMobileLandingPage;
  @NonNull Long companyId;

  @Override
  public boolean isDeliveryCritical() {
    return true;
  }

  @Override
  public String getTitle() {
    return null;
  }

  @Override
  public String getMessage() {
    return getLocalizedMessage("worker.onboarding.reminder.sms.message");
  }

  @Override
  public UserNotificationChannel getChannel() {
    return EMPLOYEE_ONBOARDING_REMINDER;
  }

  @Override
  public String getEmailSubject() {
    return getLocalizedMessage("worker.onboarding.reminder.email.subject");
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage("worker.onboarding.reminder.email.preheader", getPartnerName());
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage("worker.onboarding.reminder.email.banner-text");
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage(
        "worker.onboarding.reminder.email.body-paragraph1-text", getPartnerName());
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage(
        "worker.onboarding.reminder.email.body-paragraph2-text", getCompanyName());
  }

  @Override
  public String getBodyParagraph3Text() {
    return getLocalizedMessage(
        "worker.onboarding.reminder.email.body-paragraph3-text", getPartnerName(), workerEmail);
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("worker.onboarding.reminder.email.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    return getLinkUrl();
  }

  private String getLinkUrl() {
    var builder = new WebappLinkBuilder();
    if (routeToMobileLandingPage) {
      builder.pathSegment("mobile-account-setup");
    }
    return builder.build().toString();
  }

  @Override
  public EmailType getEmailType() {
      return EmailType.WORKER_CLAIMED_BUT_ONBOARDINGIN_COMPLETE;
  }
}
