package com.everee.api.employee.onboarding.entity;

import static org.hibernate.jpa.QueryHints.HINT_FETCH_SIZE;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.stream.Stream;
import javax.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface WorkerOnboardingRepository extends JpaRepository<WorkerOnboarding, Long> {
  @Query(
      "select w from WorkerOnboarding w"
          + " join DetailedCompany c on c.id = w.companyId"
          + " where w.companyId = :companyId"
          + "   and w.lastOnboardingReminderSentAt is null"
          + "   and c.workerOnboardingSettings.onboardingNotificationsEnabled = TRUE")
  @QueryHints({@QueryHint(name = HINT_FETCH_SIZE, value = "250")})
  Stream<WorkerOnboarding> getAllNeverSentOnboardingReminder(@Param("companyId") Long companyId);

  @Query(
      "select w from WorkerOnboarding w"
          + " join DetailedCompany c on c.id = w.companyId"
          + " where w.companyId = :companyId"
          + "   and w.claimedAt is null"
          + "   and c.workerOnboardingSettings.onboardingNotificationsEnabled = TRUE")
  @QueryHints({@QueryHint(name = HINT_FETCH_SIZE, value = "250")})
  Stream<WorkerOnboarding> getAllNonClaimedOnboarding(@Param("companyId") Long companyId);

  @Query(
      "select"
          + " c.id as companyId,"
          + " c.displayName as companyDisplayName,"
          + " pt.loginRequestParamName as partnerLoginRequestParamName,"
          + " e.email,"
          + " w.invitationToken,"
          + " w.claimedAt,"
          + " w.lockedAt,"
          + " w.completedOnboardingAt,"
          + " w.automatedOnboardingRemindersSentCount,"
          + " e.id as employeeId,"
          + " e.firstName,"
          + " e.middleName,"
          + " e.lastName,"
          + " e.employmentType,"
          + " coalesce(p.typeName, 'INDIVIDUAL') as payeeTypeName,"
          + " c.payCardStatus as companyPayCardStatus"
          + " from WorkerOnboarding w"
          + " join DetailedCompany c"
          + "      on c.id = w.companyId"
          + "     and c.startDate <= :date and coalesce(c.endDate, 'infinity') >= :date"
          + " join Partner pt"
          + "      on pt.id = c.partnerId"
          + " join DetailedEmployee e"
          + "      on e.id = w.employeeId"
          + "     and e.startDate <= :date and coalesce(c.endDate, 'infinity') >= :date"
          + " left join PayeeType p"
          + "      on p.id = e.payeeTypeId"
          + " where w.nextAutomatedOnboardingReminderScheduledAt <= :timestamp"
          + "   and c.workerOnboardingSettings.onboardingNotificationsEnabled = TRUE")
  @QueryHints({@QueryHint(name = HINT_FETCH_SIZE, value = "250")})
  Stream<WorkerOnboardingReminderParams> getAutomatedOnboardingRemindersDue(
      @Param("date") LocalDate forDate, @Param("timestamp") ZonedDateTime timestamp);

  @Modifying
  @Query(
      value =
          "update WorkerOnboarding w set"
              + " w.lastOnboardingReminderSentAt = :sentAt,"
              + " w.nextAutomatedOnboardingReminderScheduledAt = :sendNextReminderAt,"
              + " w.automatedOnboardingRemindersSentCount = w.automatedOnboardingRemindersSentCount + 1"
              + " where w.employeeId = :employeeId")
  void recordAutomatedOnboardingReminderSentAtAndScheduleNextAttempt(
      @Param("employeeId") Long employeeId,
      @Param("sentAt") ZonedDateTime sentAt,
      @Param("sendNextReminderAt") ZonedDateTime sendNextReminderAt);

  @Modifying
  @Query(
      value =
          "update WorkerOnboarding w set"
              + " w.nextAutomatedOnboardingReminderScheduledAt = null"
              + " where w.employeeId = :employeeId")
  void cancelFutureAutomatedOnboardingReminderAttempts(@Param("employeeId") Long employeeId);
}
