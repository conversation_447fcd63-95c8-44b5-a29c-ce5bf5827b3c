package com.everee.api.employee.onboarding;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.onboarding.entity.WorkerOnboarding;
import com.everee.api.employee.onboarding.entity.WorkerOnboardingRepository;
import com.everee.api.employee.onboarding.lookup.WorkerOnboardingLookup;
import com.everee.api.employee.onboarding.lookup.WorkerOnboardingLookupService;
import com.everee.api.employee.onboarding.notification.WorkerNotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.util.BooleanApiParam;
import java.time.Duration;
import java.time.ZonedDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkerOnboardingReminderService {
  private final CompanyService companyService;
  private final WorkerOnboardingLinkService workerOnboardingLinkService;
  private final WorkerOnboardingLookupService workerOnboardingLookupService;
  private final WorkerOnboardingRepository workerOnboardingRepository;
  private final WorkerAutomatedOnboardingReminderService automatedOnboardingReminderService;
  private final WorkerNotificationService workerNotificationService;
  private final PartnerRepository partnerRepository;

  @Value("${app.onboarding-email-throttle}")
  private Boolean onboardingEmailThrottle;

  @Transactional
  public void sendOnboardingReminders(
      DetailedCompany company, WorkerOnboardingLookup lookup, Long sentByUserId) {
    lookup.setComplete(BooleanApiParam.EXCLUDE_ALL);

    workerOnboardingLookupService
        .listAll(lookup, Pageable.unpaged())
        .forEach(onboarding -> sendOnboardingReminderThrottled(company, onboarding, sentByUserId));
  }

  @Transactional
  public void sendOnboardingRemindersToAllNonNotifiedWorkers(DetailedCompany company) {
    workerOnboardingRepository
        .getAllNeverSentOnboardingReminder(company.getId())
        .forEach(onboarding -> sendOnboardingReminderThrottled(company, onboarding, null));
  }

  @Transactional
  public void sendOnboardingReminderToAllUnclaimedWorkers(DetailedCompany company) {
    workerOnboardingRepository
        .getAllNonClaimedOnboarding(company.getId())
        .forEach(onboarding -> sendOnboardingReminderThrottled(company, onboarding, null));
  }

  @Transactional
  public void sendOnboardingReminderThrottled(
      DetailedCompany company, WorkerOnboarding onboarding, Long sentByUserId) {
    if (!company.getWorkerOnboardingSettings().isOnboardingNotificationsEnabled()) return;
    if (onboarding.isClaimed() && onboarding.isComplete()) return;

    if (onboardingEmailThrottle == null) {
      onboardingEmailThrottle = true;
    }

    var throttlePeriod = Duration.ofMinutes(15);
    var threshold = ZonedDateTime.now().minus(throttlePeriod);
    var invitationSentAt = onboarding.getLastOnboardingReminderSentAt();
    var throttled =
        onboarding.isOnboardingReminderSent()
            && invitationSentAt.isAfter(threshold)
            && onboardingEmailThrottle;

    if (throttled) {
      log.info(
          "Onboarding reminder email not sent to {}: throttled because a reminder was sent recently ({} ago)",
          onboarding.getEmployee().getEmail(),
          Duration.between(invitationSentAt, ZonedDateTime.now()).toString());
    } else {
      sendOnboardingReminder(onboarding, sentByUserId);
    }
  }

  protected void sendOnboardingReminder(WorkerOnboarding onboarding, Long currentUserId) {
    var company = companyService.getCompany(onboarding.getCompanyId());
    var partner = partnerRepository.findByCompanyId(company.getId()).orElseThrow();
    if (onboarding.getEmployee().isEnded(company.getNowDate())) {
      return;
    }

    if (onboarding.isClaimed()) {
      workerNotificationService.sendClaimedButOnboardingIncompleteNotification(
          partner,
          company.getDisplayName(),
          onboarding.getDisplayFullName(),
          onboarding.getEmployee().getEmail(),
          company.getId(),
          onboarding.getEmployee().getUserId());
    } else {
      workerNotificationService.sendBeginOnboardingNotification(
          partner,
          company.getDisplayName(),
          company.getPayCardStatus(),
          onboarding.getEmployee().getEmail(),
          onboarding.getEmployee(),
          workerOnboardingLinkService.buildLink(onboarding),
          company.getId(),
          onboarding.getEmployee().getUserId(),
          onboarding.getInvitationToken());
    }

    onboarding.setLastOnboardingReminderSentAt(ZonedDateTime.now());
    onboarding.setLastOnboardingReminderSentByUserId(currentUserId);
    automatedOnboardingReminderService.scheduleNextAutomatedOnboardingReminder(onboarding);
  }
}
