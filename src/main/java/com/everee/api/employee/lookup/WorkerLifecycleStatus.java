package com.everee.api.employee.lookup;

import com.everee.api.employee.onboarding.WorkerOnboardingStatus;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.DisplayName;
import com.everee.api.phase.Phase;
import java.util.EnumSet;
import java.util.Locale;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
public enum WorkerLifecycleStatus implements DisplayName {
  ONBOARDING("workerlifecyclestatus.ONBOARDING.title"),
  ACTIVE("workerlifecyclestatus.ACTIVE.title"),
  SEPARATED("workerlifecyclestatus.SEPARATED.title");

  private final String localizationKey;

  @Setter private MessageSource messageSource;

  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of(localizationKey);
  }

  public String getDisplayName() {
    return getLocalizedTitle().getLocalizedValue(messageSource, Locale.ENGLISH);
  }

  public static WorkerLifecycleStatus from(Phase phase, WorkerOnboardingStatus onboardingStatus) {
    if (phase == Phase.ENDED) {
      return SEPARATED;
    }

    if (phase == Phase.ACTIVE && onboardingStatus == WorkerOnboardingStatus.COMPLETE) {
      return ACTIVE;
    }

    return ONBOARDING;
  }

  @Component
  public static class WorkerLifecycleStatusServiceInjector {
    @Autowired private MessageSource messageSource;

    @PostConstruct
    public void postConstruct() {
      for (WorkerLifecycleStatus s : EnumSet.allOf(WorkerLifecycleStatus.class))
        s.setMessageSource(messageSource);
    }
  }
}
