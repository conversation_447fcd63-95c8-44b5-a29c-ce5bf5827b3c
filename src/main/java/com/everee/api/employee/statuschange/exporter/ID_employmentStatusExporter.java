package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.ExporterUtils.blank;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.leftString;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.recordPostalCode;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChange;
import com.everee.api.model.DateRange;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionService;
import com.everee.api.tax.state.State;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ID_employmentStatusExporter extends BaseEmploymentStatusExporterFixedWidth {
  @Getter private final StorageService storageService;
  private final CompanyService companyService;
  private final CompanyTaxJurisdictionService companyTaxJurisdictionService;
  private final EmployeeService employeeService;
  @Getter private State state = State.ID;

  @Override
  public String getFileName(List<EmploymentStatusChange> content, DateRange range) {
    return "NEWHIRE.txt";
  }

  @Override
  protected String generateNewHireExportData(
      Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    return newHiresByCompany.entrySet().stream()
            .map(
                entry -> {
                  var companyId = entry.getKey();
                  var company = companyService.getCompany(companyId);

                  var employerRecord = generateEmployerRecord(company);

                  var newHires = entry.getValue();

                  var newHiresForCompany =
                      newHires.stream()
                          .map(newHire -> generateEmployeeRecord(newHire, company))
                          .collect(Collectors.joining("\r\n"));

                  return newHiresForCompany + "\r\n" + employerRecord;
                })
            .collect(Collectors.joining("\r\n"))
        + "\r\n";
  }

  private String generateEmployerRecord(DetailedCompany company) {
    var companyTaxJurisdiction =
        companyTaxJurisdictionService.getStateSutaRecord(company.getId(), getState());

    StringBuffer record = new StringBuffer();
    record.append("2S"); // Record identifer
    record.append(
        companyTaxJurisdiction
            .map(csi -> leftString(csi.getAccountNumber(), 10))
            .orElse(blank(10)));
    record.append(leftString(company.getFederalEin(), 9, ""));
    record.append(
        leftString(
            StringUtils.firstNonBlank(company.getLegalEntityName(), company.getDisplayName()), 20));

    var companyAddress = company.getActiveAddress(LocalDate.now()).orElse(new CompanyAddress());
    addAddress(record, companyAddress);

    record.append(blank(28));

    return record.toString().toUpperCase();
  }

  private String generateEmployeeRecord(EmploymentStatusChange newHire, DetailedCompany company) {
    var employee = employeeService.get(newHire.getEmployeeId(), newHire.getCompanyId());
    StringBuffer record = new StringBuffer();
    record.append("1S"); // Record identifer
    record.append(
        leftString(Optional.ofNullable(newHire.getTaxpayerIdentifier()).orElse(""), 9, ""));
    record.append(leftString(employee.getLastName(), 20, ""));
    record.append(leftString(employee.getFirstName(), 15, ""));
    record.append(leftString(Optional.ofNullable(employee.getMiddleName()).orElse(""), 1));

    record.append(leftString(newHire.getStatusChangeDate().format(mmddyyyy), 8));

    addAddress(record, newHire);

    record.append(leftString(company.getFederalEin(), 9, ""));

    record.append(blank(1));

    record.append(Optional.ofNullable(newHire.getWorkState()).orElse(getState()).getCode());

    record.append(blank(2));

    return record.toString().toUpperCase();
  }

  private void addAddress(StringBuffer record, PhysicalAddress address) {
    record.append(leftString(address.getLine1(), 29));
    record.append(leftString(address.getCity(), 18));

    record.append(
        leftString(Optional.ofNullable(address.getState()).map(State::toString).orElse(""), 2));

    recordPostalCode(record, address.getPostalCode(), true);
  }
}
