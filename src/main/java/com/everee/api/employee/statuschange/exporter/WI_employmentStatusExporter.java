package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.ExporterUtils.blank;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.leftString;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.recordPostalCode;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChange;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.state.State;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class WI_employmentStatusExporter extends BaseEmploymentStatusExporterFixedWidth {
  @Getter private final StorageService storageService;
  private final CompanyService companyService;
  private final EmployeeService employeeService;
  @Getter private State state = State.WI;

  @Override
  protected String generateNewHireExportData(
      Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    return newHiresByCompany.entrySet().stream()
        .map(
            entry -> {
              var companyId = entry.getKey();
              var company = companyService.getCompany(companyId);

              var newHires = entry.getValue();

              var newHiresForCompany =
                  newHires.stream()
                      .map(newHire -> generateEmployeeRecord(newHire, company))
                      .collect(Collectors.joining("\r\n"));

              return newHiresForCompany;
            })
        .collect(Collectors.joining("\r\n"));
  }

  private String generateEmployeeRecord(EmploymentStatusChange newHire, DetailedCompany company) {
    var employee = employeeService.get(newHire.getEmployeeId(), newHire.getCompanyId());
    StringBuffer record = new StringBuffer();

    // Employer Information
    record.append(leftString(company.getFederalEin(), 9, ""));
    record.append(
        leftString(
            Optional.ofNullable(company.getLegalEntityName()).orElse(company.getDisplayName()),
            35));
    addAddress(
        record,
        company.getActiveAddress(newHire.getStatusChangeDate()).orElse(new CompanyAddress()));

    record.append(
        leftString(Optional.ofNullable(newHire.getTaxpayerIdentifier()).orElse(""), 9, ""));
    record.append(leftString(employee.getLastName(), 20, ""));
    record.append(leftString(employee.getFirstName(), 15, ""));
    record.append(leftString(Optional.ofNullable(employee.getMiddleName()).orElse(""), 15));

    addAddress(record, newHire);

    record.append(
        leftString(
            Optional.ofNullable(employee.getDateOfBirth())
                .map(dob -> dob.format(yyyymmdd))
                .orElse(""),
            8));

    record.append(leftString(newHire.getStatusChangeDate().format(yyyymmdd), 8));
    record.append(
        leftString(Optional.ofNullable(newHire.getWorkState()).orElse(getState()).name(), 2));

    record.append(blank(43)); // Filler

    return record.toString();
  }

  private void addAddress(StringBuffer record, PhysicalAddress address) {
    record.append(leftString(address.getLine1(), 35));
    record.append(leftString(address.getLine2(), 35));

    record.append(leftString(address.getCity(), 22));

    record.append(
        leftString(Optional.ofNullable(address.getState()).map(State::toString).orElse(""), 2));

    recordPostalCode(record, address.getPostalCode(), false);

    record.append(blank(25)); // Country
    record.append(blank(15)); // International Zip
  }
}
