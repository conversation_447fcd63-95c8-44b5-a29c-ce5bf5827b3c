package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.ExporterUtils.blank;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.leftString;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.recordForeignInfo;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.recordPostalCode;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChange;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionService;
import com.everee.api.tax.state.State;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class GA_employmentStatusExporter extends BaseEmploymentStatusExporterFixedWidth {
  @Getter private final StorageService storageService;
  private final CompanyService companyService;
  private final CompanyTaxJurisdictionService companyTaxJurisdictionService;
  private final EmployeeService employeeService;
  @Getter private State state = State.GA;

  @Override
  protected String generateNewHireExportData(
      Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    return generateHeaderRecord()
        + "\r\n"
        + newHiresByCompany.entrySet().stream()
            .map(
                entry -> {
                  var companyId = entry.getKey();
                  var company = companyService.getCompany(companyId);
                  var companyTaxJurisdiction =
                      companyTaxJurisdictionService
                          .getStateSitRecord(companyId, getState())
                          .orElse(new CompanyTaxJurisdiction());

                  var newHires = entry.getValue();

                  var newHiresForCompany =
                      newHires.stream()
                          .map(
                              newHire ->
                                  generateEmployeeRecord(newHire, company, companyTaxJurisdiction))
                          .collect(Collectors.joining("\r\n"));

                  return newHiresForCompany;
                })
            .collect(Collectors.joining("\r\n"))
        + "\r\n"
        + generateTotalRecord(newHiresByCompany);
  }

  private String generateEmployeeRecord(
      EmploymentStatusChange newHire,
      DetailedCompany company,
      CompanyTaxJurisdiction companyTaxJurisdiction) {
    var employee = employeeService.get(newHire.getEmployeeId(), newHire.getCompanyId());
    StringBuffer record = new StringBuffer();
    record.append("W4"); // Record identifer
    record.append(
        leftString(Optional.ofNullable(newHire.getTaxpayerIdentifier()).orElse(""), 9, ""));
    record.append(leftString(employee.getFirstName(), 16, ""));
    record.append(leftString(Optional.ofNullable(employee.getMiddleName()).orElse(""), 16));
    record.append(leftString(employee.getLastName(), 30, ""));

    addAddress(record, newHire, true);
    recordForeignInfo(record);

    record.append(
        leftString(
            Optional.ofNullable(employee.getDateOfBirth())
                .map(dob -> dob.format(yyyymmdd))
                .orElse(""),
            8));

    record.append(leftString(newHire.getStatusChangeDate().format(yyyymmdd), 8));

    record.append(getState()); // State of hire

    record.append(leftString(company.getFederalEin(), 9, ""));

    record.append(leftString(companyTaxJurisdiction.getAccountNumber(), 12, ""));

    record.append(
        leftString(
            StringUtils.firstNonBlank(company.getLegalEntityName(), company.getDisplayName()), 45));

    var companyAddress = company.getActiveAddress(LocalDate.now()).orElse(new CompanyAddress());
    addAddress(record, companyAddress, true);
    recordForeignInfo(record);

    // Optional address (setting as always blank since we don't have this)
    addAddress(record, new CompanyAddress(), true);
    recordForeignInfo(record);

    record.append(blank(1)); // Medical Insurance offered

    record.append(blank(10)); // Phone number - optional
    record.append(blank(8)); // Filler
    record.append(blank(1)); // Multi state employer Y/N
    record.append(blank(14)); // Filler
    record.append(blank(16)); // Name of medical insurance company

    return record.toString();
  }

  private String generateHeaderRecord() {
    StringBuffer record = new StringBuffer();
    record.append("H4"); // Record identifer
    record.append("13");
    record.append(blank(9)); // Filler
    record.append("W4");
    record.append(blank(1)); // Filler
    record.append("01");
    record.append(getNow().format(yyyymmdd));
    record.append(getNow().format(DateTimeFormatter.ofPattern("HHmmss")));

    record.append(blank(769)); // Filler

    return record.toString();
  }

  private String generateTotalRecord(Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    var total =
        newHiresByCompany.values().stream().flatMap(Collection::stream).count()
            + 2; // Add 2 for trailer and header record

    StringBuffer record = new StringBuffer();
    record.append("T4"); // Record identifer
    record.append(StringUtils.leftPad(total + "", 11, "0"));

    record.append(blank(788)); // Filler

    return record.toString();
  }

  LocalDateTime getNow() {
    return LocalDateTime.now();
  }

  private void addAddress(StringBuffer record, PhysicalAddress address, boolean includeLines2And3) {
    record.append(leftString(address.getLine1(), 40));
    if (includeLines2And3) {
      record.append(leftString(address.getLine2(), 40));
      record.append(leftString("", 40)); // line 3 of address
    }
    record.append(leftString(address.getCity(), 25));

    record.append(
        leftString(Optional.ofNullable(address.getState()).map(State::toString).orElse(""), 2));

    recordPostalCode(record, address.getPostalCode());
  }
}
