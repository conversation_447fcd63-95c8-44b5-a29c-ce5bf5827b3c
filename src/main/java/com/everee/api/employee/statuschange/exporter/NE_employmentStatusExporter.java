package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.ExporterUtils.blank;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.leftString;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.recordPostalCode;
import static org.apache.commons.lang3.StringUtils.leftPad;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChange;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.state.State;
import java.time.Clock;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class NE_employmentStatusExporter extends BaseEmploymentStatusExporterFixedWidth {
  @Getter private final StorageService storageService;
  private final CompanyService companyService;
  private final EmployeeService employeeService;
  private final Clock clock;
  @Getter private State state = State.NE;
  @Getter private boolean fileNameWithCompanyStateNewhireAndDate = true;

  @Override
  protected String generateNewHireExportData(
      Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    return newHiresByCompany.entrySet().stream()
        .map(
            entry -> {
              var companyId = entry.getKey();
              var company = companyService.getCompany(companyId);

              var newHires = entry.getValue();

              var newHiresForCompany =
                  newHires.stream()
                      .map(newHire -> generateEmployeeRecord(newHire, company))
                      .collect(Collectors.joining("\r\n"));

              return generateEmployerRecord(company, newHires.size()) + "\r\n" + newHiresForCompany;
            })
        .collect(Collectors.joining("\r\n"));
  }

  private String generateEmployerRecord(DetailedCompany company, int numberRecords) {
    StringBuffer record = new StringBuffer();
    record.append("HR");
    record.append(leftPad(numberRecords + "", 5, "0"));
    record.append(leftString(LocalDate.now(clock).format(mmddyyyy), 8));
    record.append(leftString(company.getFederalEin(), 9, ""));

    record.append(blank(236)); // Filler

    record.append("\r\n");
    record.append("RR");

    record.append(
        leftString(
            Optional.ofNullable(company.getLegalEntityName()).orElse(company.getDisplayName()),
            45));

    addAddress(
        record, company.getActiveAddress(LocalDate.now(clock)).orElse(new CompanyAddress()), true);

    record.append(leftString(company.getFirstName() + " " + company.getLastName(), 40));
    record.append(leftString(company.getPhone(), 10, ""));

    record.append(blank(7)); // Filler

    return record.toString();
  }

  private String generateEmployeeRecord(EmploymentStatusChange newHire, DetailedCompany company) {
    var employee = employeeService.get(newHire.getEmployeeId(), newHire.getCompanyId());
    StringBuffer record = new StringBuffer();
    record.append("NH");
    record.append(
        leftString(Optional.ofNullable(newHire.getTaxpayerIdentifier()).orElse(""), 9, ""));

    record.append(leftString(employee.getFirstName(), 16, ""));
    record.append(leftString(Optional.ofNullable(employee.getMiddleName()).orElse(""), 16));
    record.append(leftString(employee.getLastName(), 30, ""));

    addAddress(record, newHire, true);

    record.append(
        leftString(Optional.ofNullable(newHire.getWorkState()).orElse(getState()).name(), 2));

    record.append(leftString(newHire.getStatusChangeDate().format(mmddyyyy), 8));
    record.append(
        leftString(
            Optional.ofNullable(employee.getDateOfBirth())
                .map(dob -> dob.format(mmddyyyy))
                .orElse(""),
            8));

    record.append(employee.getEmploymentType() == EmploymentType.CONTRACTOR ? "Y" : "N");
    record.append(blank(1)); // Health insurance
    record.append(blank(8)); // Health insurance date

    record.append(blank(3)); // Filler

    return record.toString();
  }

  private void addAddress(StringBuffer record, PhysicalAddress address, boolean line3) {
    record.append(leftString(address.getLine1(), 40));
    record.append(leftString(address.getLine2(), 40));
    if (line3) record.append(blank(40)); // line 3

    record.append(leftString(address.getCity(), 25));

    record.append(
        leftString(Optional.ofNullable(address.getState()).map(State::toString).orElse(""), 2));

    recordPostalCode(record, address.getPostalCode(), false);
  }
}
