package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.ExporterUtils.blank;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.leftString;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.recordLongPostalCode;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChange;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.state.State;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MN_employmentStatusExporter extends BaseEmploymentStatusExporterFixedWidth {
  @Getter private final StorageService storageService;
  private final CompanyService companyService;
  private final EmployeeService employeeService;
  @Getter private State state = State.MN;
  @Getter private boolean fileNameWithCompanyStateNewhireAndDate = true;

  @Override
  protected String generateNewHireExportData(
      Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    return newHiresByCompany.entrySet().stream()
        .map(
            entry -> {
              var companyId = entry.getKey();
              var company = companyService.getCompany(companyId);

              var newHires = entry.getValue();

              var newHiresForCompany =
                  newHires.stream()
                      .map(newHire -> generateEmployeeRecord(newHire, company))
                      .collect(Collectors.joining("\r\n"));

              return newHiresForCompany;
            })
        .collect(Collectors.joining("\r\n"));
  }

  private String generateEmployeeRecord(EmploymentStatusChange newHire, DetailedCompany company) {
    var employee = employeeService.get(newHire.getEmployeeId(), newHire.getCompanyId());
    StringBuffer record = new StringBuffer();
    record.append(leftString("MN Newhire Record", 17)); // Record identifer
    record.append("1.00");
    record.append(leftString(employee.getFirstName(), 16, ""));
    record.append(leftString(Optional.ofNullable(employee.getMiddleName()).orElse(""), 16));
    record.append(leftString(employee.getLastName(), 30, ""));

    record.append(
        leftString(Optional.ofNullable(newHire.getTaxpayerIdentifier()).orElse(""), 9, ""));

    addAddress(record, newHire);

    record.append(
        leftString(
            Optional.ofNullable(employee.getDateOfBirth())
                .map(dob -> dob.format(mmddyyyy))
                .orElse(""),
            8));

    record.append(leftString(newHire.getStatusChangeDate().format(mmddyyyy), 8));
    record.append(
        leftString(Optional.ofNullable(newHire.getWorkState()).orElse(getState()).name(), 2));

    record.append(employee.getEmploymentType() == EmploymentType.CONTRACTOR ? "Y" : "N");
    record.append(blank(1));

    // Employer Information
    record.append(leftString(company.getFederalEin(), 9, ""));
    record.append(blank(12));
    record.append(
        leftString(
            Optional.ofNullable(company.getLegalEntityName()).orElse(company.getDisplayName()),
            45));
    addAddress(
        record,
        company.getActiveAddress(newHire.getStatusChangeDate()).orElse(new CompanyAddress()));

    record.append(blank(10)); // phone
    record.append(blank(6)); // phone extension
    record.append(blank(20)); // contact

    addAddress(record, new CompanyAddress()); // optional address

    record.append(blank(16)); // phone
    record.append(blank(20)); // contact
    record.append(blank(32)); // Filler

    return record.toString();
  }

  private void addAddress(StringBuffer record, PhysicalAddress address) {
    record.append(leftString(address.getLine1(), 40));
    record.append(leftString(address.getLine2(), 40));
    record.append(leftString("", 40)); // line 3 of address

    record.append(leftString(address.getCity(), 25));

    record.append(
        leftString(Optional.ofNullable(address.getState()).map(State::toString).orElse(""), 2));

    recordLongPostalCode(record, address.getPostalCode());

    record.append(blank(2)); // Country code
  }
}
