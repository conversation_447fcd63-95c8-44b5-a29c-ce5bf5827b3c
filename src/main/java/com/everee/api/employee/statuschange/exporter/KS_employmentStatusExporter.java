package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.ExporterUtils.leftString;
import static com.everee.api.employee.statuschange.exporter.ExporterUtils.withString;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChange;
import com.everee.api.model.DateRange;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionService;
import com.everee.api.tax.state.State;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class KS_employmentStatusExporter extends BaseEmploymentStatusExporterFixedWidth {
  @Getter private final StorageService storageService;
  private final CompanyService companyService;
  private final CompanyTaxJurisdictionService companyTaxJurisdictionService;
  private final EmployeeService employeeService;
  @Getter private State state = State.KS;

  private static final DateTimeFormatter newHireDateFormat =
      DateTimeFormatter.ofPattern("MM/dd/yy");
  private static final String HEADER_ROW =
      "Employee SSN,Employee First Name,Employee Middle Name,Employee Last Name,Employee Address Line 1,Employee Address Line 2 (optional),Employee City,Employee State,Employee Zip Code,Employee Foreign Country Code (optional),Employee Date of Hire,State Hired In,Employee Date of Return (optional),Employee Return Code (optional),Comments (optional),Federal EIN,Kansas Employer Serial Number (enter 000000 if you do not have a serial number),Employer Name,Employer Address Line 1,Employer Address Line 2 (optional),Employer City,Employer State,Employer Zip Code,Employer Foreign Country Code (optional)";

  @Override
  public String getFileName(List<EmploymentStatusChange> content, DateRange range) {
    return getState() + "_NewHires_" + range.getDateRangeInfo(Locale.ENGLISH) + ".csv";
  }

  @Override
  protected String generateNewHireExportData(
      Map<Long, List<EmploymentStatusChange>> newHiresByCompany) {
    return HEADER_ROW
        + "\r\n"
        + newHiresByCompany.entrySet().stream()
            .map(
                entry -> {
                  var companyId = entry.getKey();
                  var company = companyService.getCompany(companyId);
                  var companyTaxJurisdiction =
                      companyTaxJurisdictionService.getStateSutaRecord(companyId, getState());

                  var newHires = entry.getValue();

                  var newHiresForCompany =
                      newHires.stream()
                          .map(
                              newHire ->
                                  generateEmployeeRecord(newHire, company, companyTaxJurisdiction))
                          .collect(Collectors.joining("\r\n"));

                  return newHiresForCompany;
                })
            .collect(Collectors.joining("\r\n"));
  }

  private String generateEmployeeRecord(
      EmploymentStatusChange newHire,
      DetailedCompany company,
      Optional<CompanyTaxJurisdiction> companyTaxJurisdiction) {
    var employee = employeeService.get(newHire.getEmployeeId(), newHire.getCompanyId());
    StringBuffer record = new StringBuffer();
    // Employee Information
    record
        .append(leftString(Optional.ofNullable(newHire.getTaxpayerIdentifier()).orElse(""), 9, ""))
        .append(",");
    record.append(withString(employee.getFirstName(), "")).append(",");
    record.append(withString(employee.getMiddleName(), "")).append(",");
    record.append(withString(employee.getLastName(), "")).append(",");
    addAddress(record, newHire);
    record.append(",");

    record.append(newHire.getStatusChangeDate().format(newHireDateFormat)).append(",");
    record
        .append(
            leftString(Optional.ofNullable(newHire.getWorkState()).orElse(getState()).name(), 2))
        .append(",");
    record.append(",,,");

    // Employer Information

    record.append(withString(company.getFederalEin(), "")).append(",");
    record
        .append(
            withString(
                companyTaxJurisdiction
                    .map(CompanyTaxJurisdiction::getAccountNumber)
                    .map(ExporterUtils::withNumericString)
                    .map(str -> StringUtils.truncate(str, 6))
                    .filter(str -> !str.isEmpty())
                    .orElse("000000"),
                ""))
        .append(",");

    record
        .append(
            withString(
                Optional.ofNullable(company.getLegalEntityName()).orElse(company.getDisplayName()),
                " "))
        .append(",");
    addAddress(
        record,
        company.getActiveAddress(newHire.getStatusChangeDate()).orElse(new CompanyAddress()));

    return record.toString();
  }

  private void addAddress(StringBuffer record, PhysicalAddress address) {
    record.append(withString(address.getLine1(), " ")).append(",");
    record.append(withString(address.getLine2(), " ")).append(",");
    record.append(withString(address.getCity(), " ")).append(",");
    record
        .append(
            withString(Optional.ofNullable(address.getState()).map(State::toString).orElse(""), ""))
        .append(",");

    record.append(withString(address.getPostalCode(), "")).append(",");
    record.append(withString(null, "")); // foreign country code
  }
}
