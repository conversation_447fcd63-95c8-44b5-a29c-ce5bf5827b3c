package com.everee.api.employee;

import com.everee.api.file.DocumentType;
import com.everee.api.lookup.Lookup;
import com.everee.api.util.BooleanApiParam;
import java.util.Set;
import lombok.Data;

@Data
public class EmployeeDocumentLookup implements Lookup {

  private BooleanApiParam deleted;
  private BooleanApiParam published;
  private Set<Long> employeeIds;
  private Set<Long> companyIds;
  private Set<Integer> taxYears;
  private BooleanApiParam dismissed = BooleanApiParam.UNSPECIFIED;
  private Set<DocumentType> documentTypes;
}
