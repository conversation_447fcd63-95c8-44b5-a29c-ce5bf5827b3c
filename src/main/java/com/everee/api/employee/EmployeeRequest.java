package com.everee.api.employee;

import com.everee.api.approvalgroup.ApprovalGroup;
import com.everee.api.company.DetailedCompany;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.timeoff.types.TimeOffType;
import com.everee.api.user.DetailedUser;
import com.everee.api.util.DisplayFullName;
import com.everee.api.worker.createparams.WorkerPositionParamsForCreate;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import javax.persistence.*;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
@Embeddable
public class EmployeeRequest implements DisplayFullName {
  @Transient private Long userId;

  @Transient private Long companyId;

  private Long approvalGroupId;

  @Transient private Long workLocationId;

  @ManyToOne
  @JoinColumn(name = "approvalgroupid", insertable = false, updatable = false)
  private ApprovalGroup approvalGroup;

  private String firstName;

  private String middleName;

  private String lastName;

  @Email private String email;

  private String phoneNumber;

  private String title;

  @Enumerated(EnumType.STRING)
  @Column(name = "wagetype")
  private PayType payType;

  @Enumerated(EnumType.STRING)
  private EmploymentType employmentType;

  private Integer expectedWeeklyHours = 0;

  private Money payRate;

  @JsonProperty("isEmploymentEligible")
  private boolean employmentEligible;

  private LocalDate dateOfBirth;

  private String taxpayerIdentifier;

  @Column(name = "companyemployeeid")
  private String externalWorkerId;

  private Boolean dailyPywPermitted = true;

  private Boolean weeklyPywPermitted = true;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  @NotNull
  private LocalDate startDate;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate endDate;

  @Transient @Deprecated private boolean active;

  private String ptoAccrualRate;

  private BigDecimal ptoMaxHours;

  private BigDecimal ptoCurrentBalance;

  @Enumerated(EnumType.STRING)
  private TimeOffType timeOffType;

  public boolean isEligibleForOvertime() {
    return EmploymentType.EMPLOYEE.equals(employmentType) && PayType.HOURLY.equals(payType);
  }

  @Deprecated
  public PayType getWageType() {
    return payType;
  }

  public boolean isContractor() {
    return this.employmentType == EmploymentType.CONTRACTOR;
  }

  public boolean isValid() {
    if (employmentType == null) return false;

    // Email isn't required if the worker was terminated in a prior tax year.
    var isEmailRequired = endDate == null || (endDate.getYear() == LocalDate.now().getYear());
    var isEmailValid = !isEmailRequired || email != null;

    var isPhoneValid = !StringUtils.hasText(phoneNumber) || phoneNumber.matches("\\d{10}");

    switch (employmentType) {
      case EMPLOYEE:
        return firstName != null
            && lastName != null
            && startDate != null
            && isEmailValid
            && isPhoneValid
            && payType != null
            && payRate != null
            && expectedWeeklyHours != null;

      case CONTRACTOR:
        return firstName != null && lastName != null && startDate != null && isEmailValid;

      default:
        throw new IllegalStateException(
            String.format("Unsupported worker type: %s", employmentType));
    }
  }

  public DetailedUser toUser() {
    var user = new DetailedUser();
    user.setFirstName(getFirstName());
    user.setMiddleName(getMiddleName());
    user.setLastName(getLastName());
    user.setDateOfBirth(getDateOfBirth());
    user.setUnverifiedEmail(getEmail());
    user.setUnverifiedPhoneNumber(getPhoneNumber());
    user.setUnverifiedTaxpayerIdentifier(getTaxpayerIdentifier());
    user.setNoPassword();

    return user;
  }

  public DetailedEmployee toEmployee(DetailedCompany company) {
    var employee = new DetailedEmployee();
    employee.setUserId(userId);
    employee.setCompanyId(companyId);
    employee.setI9citizen(employmentEligible);
    employee.setExternalWorkerId(externalWorkerId);
    employee.setStartDate(startDate);
    employee.setEndDate(endDate);
    employee.setApprovalGroupId(approvalGroupId);
    employee.setSchedulingWorkLocationId(workLocationId);
    employee.setEmploymentType(employmentType);
    if (employmentType == EmploymentType.EMPLOYEE)
      employee.setSupportedPaymentTypes(company.getDefaultEmployeeSupportedPaymentTypes());
    if (employmentType == EmploymentType.CONTRACTOR)
      employee.setSupportedPaymentTypes(company.getDefaultContractorSupportedPaymentTypes());
    return employee;
  }

  public WorkerPositionParamsForCreate toWorkerPositionParamsForCreate() {
    var isHourly = PayType.HOURLY.equals(this.getPayType());

    return new WorkerPositionParamsForCreate()
        .setTitle(this.getTitle())
        .setPayType(this.getPayType())
        .setPayRate(this.getPayRate())
        // TODO: we can remove this logic when we support overtime for salary workers
        .setEligibleForOvertime(
            Optional.of(this.isEligibleForOvertime()).map(e -> e && isHourly).orElse(isHourly))
        .setExpectedWeeklyHours(this.getExpectedWeeklyHours());
  }
}
