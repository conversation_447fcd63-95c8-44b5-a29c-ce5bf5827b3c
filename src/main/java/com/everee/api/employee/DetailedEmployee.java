package com.everee.api.employee;

import com.everee.api.approvalgroup.ApprovalGroup;
import com.everee.api.auditlog.AuditLogEntityListener;
import com.everee.api.config.PresignedCustomerFacingFileLocationSerializer;
import com.everee.api.employee.lookup.WorkerLifecycleStatus;
import com.everee.api.employee.onboarding.entity.WorkerOnboarding;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.employeeTermination.ContractorSeparationType;
import com.everee.api.employeeTermination.EmployeeSeparationType;
import com.everee.api.hibernate.PIIEncryptor;
import com.everee.api.model.BaseModelV2;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.payeetype.PayeeType;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.payment.PaymentMethod;
import com.everee.api.payment.PaymentType;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.phase.ContiguousPhasedSet;
import com.everee.api.symmetry.StateCalcOverride;
import com.everee.api.time.WorkerLocalTimeService;
import com.everee.api.timeoff.accrualrate.PtoAccrualRate;
import com.everee.api.user.DetailedUser;
import com.everee.api.user.TinVerificationState;
import com.everee.api.util.DisplayFullName;
import com.everee.api.util.EnumUtils;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocation;
import com.everee.api.worklocation.WorkLocation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;
import org.javers.core.metamodel.annotation.DiffIgnore;

@Data
@Accessors(chain = true)
@Entity
@Table(name = "employee")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("Employee")
@EntityListeners(AuditLogEntityListener.class)
public class DetailedEmployee extends BaseModelV2<DetailedEmployee> implements Employee {
  protected static final String PREFERRED_FIRST_NAME_FORMULA =
      "(SELECT COALESCE(i.preferredname, u.firstname) "
          + " FROM appuser u "
          + " LEFT JOIN useradditionalinfo i ON i.userid=u.id "
          + " WHERE u.id=userid)";

  protected static final String LIFECYCLE_STATUS_FORMULA =
      "(select (case"
          + " when now() between startdate and coalesce(enddate, 'infinity') and o.status = 'COMPLETE' then 'ACTIVE'"
          + " when coalesce(enddate, 'infinity') < now() then 'SEPARATED'"
          + " else 'ONBOARDING' end)"
          + " from workeronboarding o where o.employeeid = {alias}.id)";

  private static final String EFFECTIVE_ACCRUAL_RATE_FORMULA =
      "(select pce.effectiveptoaccrualrate from ptopolicycoveredemployee pce "
          + " join company c ON c.id=pce.companyid "
          + " where pce.employeeid = id and pce.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE and (pce.enddate IS NULL OR pce.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))";

  private static final String EMAIL_ADDRESS_FORMULA =
      "coalesce((select coalesce(u.email, u.unverifiedemail) from appuser u where u.id = userid))";

  private static final String PHONE_NUMBER_FORMULA =
      "coalesce((select coalesce(u.phonenumber, u.unverifiedphonenumber) from appuser u where u.id = userid))";

  private static final String PICTURE_THUMBNAIL_FORMULA =
      "coalesce((select up.thumbnaillocation from userpicture up where up.userid = userid))";

  private static final String PAY_TYPE_FORMULA =
      "(select p.wagetype from employeeposition p "
          + " join company c on c.id=p.companyid "
          + " where p.employeeid = id and p.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE and (p.enddate IS NULL OR p.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))";

  private static final String EMPLOYMENT_STATUS_FORMULA =
      "(select case when p.expectedweeklyhours >= c.fulltimeweeklyhoursthreshold then 'FULL_TIME' else 'PART_TIME' end from employeeposition p "
          + " join company c on c.id = companyid "
          + " where p.employeeid = id and p.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE and (p.enddate IS NULL OR p.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))";

  private static final String TITLE_FORMULA =
      "(select p.title from employeeposition p "
          + " join company c on c.id=p.companyid "
          + " where p.employeeid = id and p.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE and (p.enddate IS NULL OR p.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))";

  private static final String PAY_RATE_FORMULA =
      "(select p.payrate from employeeposition p "
          + " join company c on c.id=p.companyid "
          + " where p.employeeid = id and p.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE and (p.enddate IS NULL OR p.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))";

  private static final String TIME_ZONE_FORMULA =
      "COALESCE("
          + "(SELECT w.timezone FROM worklocation w "
          + " JOIN  workerlegalworklocation l ON w.id=l.worklocationid "
          + " JOIN company c ON c.id=l.companyid "
          + " WHERE l.employeeid = id "
          + "   AND l.startdate <= date (timezone(c.timezone, CURRENT_TIMESTAMP)) "
          + "   AND (l.enddate is NULL OR l.enddate >= date (timezone(c.timezone, CURRENT_TIMESTAMP))) "
          + " ),"
          + "(SELECT a.timezone FROM useraddress a "
          + " JOIN company c ON c.id=companyid "
          + " WHERE a.userid = userid "
          + "   AND a.startdate <= date (timezone(c.timezone, CURRENT_TIMESTAMP)) "
          + "   AND (a.enddate is NULL OR a.enddate >= date (timezone(c.timezone, CURRENT_TIMESTAMP))) "
          + " ),"
          + "(SELECT c.timezone FROM company c "
          + " WHERE c.id=companyid "
          + " )"
          + ")";

  private static final String PAY_PERIOD_PREFERENCE_FORMULA =
      "(select case"
          + " when supportedpaymenttypes like '%PAYROLL%' then"
          + "   coalesce("
          + "     (select ppp.payperiodtype from payperiodpreference ppp where ppp.employeeid = id and ppp.startdate <= current_date and (ppp.enddate >= current_date or ppp.enddate is null)),"
          + "     (select ppp.payperiodtype from payperiodpreference ppp where ppp.companyid = companyid and ppp.employeeid is null and ppp.startdate <= current_date and (ppp.enddate >= current_date or ppp.enddate is null))"
          + "   )"
          + " else null end)";

  private static final String NUMBER_OF_DIRECT_REPORTS_FORMULA =
      "(SELECT COUNT(*) FROM employee e WHERE e.reportstoworkerid = integrationid)";

  private static final String SCHEDULING_LOCATION_NAME =
      "(SELECT w.name FROM worklocation w WHERE w.id=worklocationid)";

  private static final String TIN_VERIFICATION_STATE =
      "(SELECT u.tin_verification_state FROM appuser u WHERE u.id=userid)";

  private static final String PTPC_ENABLED =
      "(SELECT c.ptpcEnabled AND tba.accountid is not null AND tba.depositsBlocked=false  FROM company c LEFT JOIN tabapayaccount tba ON tba.userid=userId AND tba.accountType='CARD' WHERE c.id=companyId)";

  @Setter(AccessLevel.NONE)
  @JsonIgnore
  private LocalDateTime recordedHireAt;

  @Setter(AccessLevel.NONE)
  @JsonIgnore
  private LocalDateTime recordedSeparationAt;

  private Long userId;

  @NotNull private Long companyId;

  private Long approvalGroupId;

  // Do NOT use for tax or overtime calculation purposes. Use LegalWorkLocation services instead
  @Column(name = "worklocationid")
  private Long schedulingWorkLocationId;

  private String reportsToWorkerId;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(NUMBER_OF_DIRECT_REPORTS_FORMULA)
  private int numberOfDirectReports;

  private boolean dailyPywPermitted = true;

  private boolean weeklyPywPermitted = true;

  @OneToOne(mappedBy = "employee", cascade = CascadeType.ALL)
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  private WorkerOnboarding onboarding;

  @ManyToOne
  @JoinColumn(name = "approvalgroupid", insertable = false, updatable = false)
  private ApprovalGroup approvalGroup;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(name = "worklocationid", insertable = false, updatable = false)
  // Do NOT use for tax or overtime calculation purposes. Use LegalWorkLocation services instead
  private WorkLocation schedulingWorkLocation;

  @NotNull private boolean i9citizen = true;

  @Column(name = "integrationid")
  @NotNull
  private String workerId = UUID.randomUUID().toString();

  private String externalWorkerId;

  @NotNull private boolean shouldPaySeverance;

  private Money severanceAmount;

  private Long severancePaymentId;

  @NotNull private boolean payFinalPaymentNow;

  @NotNull private boolean newManagerSelectionMade;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate dateOfNotice;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate dateBenefitsCoverageEnds;

  @Enumerated(EnumType.STRING)
  private EmployeeSeparationType employeeSeparationType;

  @Enumerated(EnumType.STRING)
  private ContractorSeparationType contractorSeparationType;

  private String notes;

  @NotNull private boolean eligibleForRehire;

  @NotNull
  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate startDate;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate endDate;

  @NotNull
  @Enumerated(EnumType.STRING)
  private EmploymentType employmentType;

  private boolean statutoryEmployee;

  private PtoAccrualRate overridePtoAccrualRate;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PREFERRED_FIRST_NAME_FORMULA)
  private String preferredFirstName;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(FIRST_NAME_FORMULA)
  private String firstName;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(MIDDLE_NAME_FORMULA)
  private String middleName;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(LAST_NAME_FORMULA)
  private String lastName;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(FULL_NAME_FORMULA)
  private String fullName;

  @DiffIgnore
  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Formula(FIRST_LAST_NAME_FORMULA)
  private String firstLastName;

  @Deprecated(forRemoval = true)
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(DISPLAY_FULL_NAME_FORMULA)
  private String displayFullName;

  @Setter(AccessLevel.NONE)
  @Formula(ACCOUNT_LABEL_BY_USER_ID_FORMULA)
  private String accountLabel;

  @Setter(AccessLevel.NONE)
  @Formula(FULL_ACCOUNT_LABEL_BY_USER_ID_FORMULA)
  private String fullAccountLabel;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(EMAIL_ADDRESS_FORMULA)
  private String email;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PHONE_NUMBER_FORMULA)
  private String phoneNumber;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(DATE_OF_BIRTH_FORMULA)
  private LocalDate dateOfBirth;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PICTURE_THUMBNAIL_FORMULA)
  @JsonSerialize(using = PresignedCustomerFacingFileLocationSerializer.class)
  private String pictureThumbnailUrl;

  /** @deprecated use the {@link WorkerLocalTimeService} for timezone related functions */
  @DiffIgnore
  @Deprecated
  @Setter(AccessLevel.NONE)
  @Formula(TIME_ZONE_FORMULA)
  private ZoneId workLocationTimeZone;

  @DiffIgnore
  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Formula(LIFECYCLE_STATUS_FORMULA)
  @Enumerated(EnumType.STRING)
  private WorkerLifecycleStatus lifecycleStatus;

  @JsonIgnore
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @JoinColumn(name = "userId", insertable = false, updatable = false)
  @OneToOne(fetch = FetchType.LAZY)
  private DetailedUser user;

  /**
   * @deprecated this comes with a big performance cost. Pull this value from the PtoBalance when
   *     needed
   */
  @Deprecated
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(EFFECTIVE_ACCRUAL_RATE_FORMULA)
  private PtoAccrualRate ptoPolicyEffectiveAccrualRate;

  @Convert(converter = PIIEncryptor.class)
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(TAX_PAYER_IDENTIFIER_FORMULA)
  private String taxpayerIdentifier;

  /**
   * NOTE: this is a contiguous phased record collection. The getter has been overridden to supply
   * the wrapper. See {@link #getPositions()}
   */
  @JsonIgnore
  @ToString.Exclude
  @EqualsAndHashCode.Exclude
  @OneToMany(
      fetch = FetchType.LAZY,
      mappedBy = "employeeId",
      orphanRemoval = true,
      cascade = CascadeType.ALL)
  private Set<EmployeePosition> positions = new HashSet<>();

  /**
   * NOTE: this is a contiguous phased record collection. The getter has been overridden to supply
   * the wrapper. See {@link #getLegalWorkLocations()} ()}
   */
  @JsonIgnore
  @ToString.Exclude
  @EqualsAndHashCode.Exclude
  @OneToMany(
      fetch = FetchType.EAGER,
      mappedBy = "employeeId",
      orphanRemoval = true,
      cascade = CascadeType.ALL)
  private Set<WorkerLegalWorkLocation> legalWorkLocations = new HashSet<>();

  @JsonFormat(shape = Shape.STRING)
  private BigDecimal overridePtoMaxHours;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PAY_PERIOD_PREFERENCE_FORMULA)
  @Enumerated(EnumType.STRING)
  private PayPeriodType payPeriodPreferenceType;

  /** Caution: this property is referencing an active phased record as of "now" */
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(TITLE_FORMULA)
  private String title;

  /** Caution: this property is referencing an active phased record as of "now" */
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PAY_TYPE_FORMULA)
  @Enumerated(EnumType.STRING)
  private PayType payType;

  /** Caution: this property is referencing an active phased record as of "now" */
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PAY_RATE_FORMULA)
  private Money payRate;

  /** Caution: this property is referencing an active phased record as of "now" */
  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(EMPLOYMENT_STATUS_FORMULA)
  @Enumerated(EnumType.STRING)
  private EmploymentStatus employmentStatus;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(SCHEDULING_LOCATION_NAME)
  private String schedulingLocationName;

  private boolean hasNonResidentCertificate = false;

  private boolean hasNexus = false;

  @Column(name = "supportedPaymentTypes")
  @JsonIgnore
  @Setter(AccessLevel.NONE)
  private String supportedPaymentTypeValues;

  @JsonIgnore @ToString.Exclude private Long companyEmployeeImportJobId;

  @JsonIgnore private Long payeeTypeId;

  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @JoinColumn(name = "payeetypeid", insertable = false, updatable = false)
  @ManyToOne(fetch = FetchType.LAZY)
  @JsonIgnoreProperties({"hibernateLazyInitializer"})
  private PayeeType payeeType;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(TIN_VERIFICATION_STATE)
  @Enumerated(EnumType.STRING)
  private TinVerificationState tinVerificationState;

  @DiffIgnore
  @Setter(AccessLevel.NONE)
  @Formula(PTPC_ENABLED)
  private boolean ptpcEnabled;

  private LocalDateTime ptpcNotSetupNotificationSentAt;
  private LocalDateTime ptpcAvailableNotificationSentAt;

  @Enumerated(EnumType.STRING)
  private PaymentMethod preferredPaymentMethod = PaymentMethod.DIRECT_DEPOSIT;

  @JsonIgnore
  @Enumerated(EnumType.STRING)
  private PaymentDistributionType preferredPaymentDistributionType = PaymentDistributionType.ACH;

  @Enumerated(EnumType.STRING)
  private StateCalcOverride stateCalcOverride = StateCalcOverride.ALL;

  private boolean oregonWCOverride = false;

  private boolean corporateOfficer = false;

  public PayeeTypeName getPayeeTypeName() {
    return Optional.ofNullable(payeeType)
        .map(PayeeType::getTypeName)
        .orElse(PayeeTypeName.INDIVIDUAL);
  }

  public EnumSet<PaymentType> getSupportedPaymentTypes() {
    return EnumUtils.getEnumSet(supportedPaymentTypeValues, PaymentType.class);
  }

  @Override
  public String getBusinessName() {
    if (payeeType == null) {
      return null;
    }
    return payeeType.getBusinessName();
  }

  public DetailedEmployee setSupportedPaymentTypes(EnumSet<PaymentType> supportedPaymentTypes) {
    this.supportedPaymentTypeValues = EnumUtils.getEnumString(supportedPaymentTypes);
    return this;
  }

  public String getFullName() {
    return Optional.ofNullable(fullName)
        .orElseGet(() -> DisplayFullName.getFullName(firstName, middleName, lastName));
  }

  @Deprecated(forRemoval = true)
  public String getDepartment() {
    return Optional.ofNullable(approvalGroup).map(ApprovalGroup::getName).orElse(null);
  }

  @Deprecated(forRemoval = true)
  public String getCompanyEmployeeId() {
    return externalWorkerId;
  }

  public ContiguousPhasedSet<EmployeePosition> getPositions() {
    return new ContiguousPhasedSet<>(positions);
  }

  public ContiguousPhasedSet<WorkerLegalWorkLocation> getLegalWorkLocations() {
    return new ContiguousPhasedSet<>(legalWorkLocations);
  }

  public boolean isOnboardingComplete() {
    return Optional.ofNullable(onboarding).map(WorkerOnboarding::isComplete).orElse(false);
  }

  @JsonIgnore
  public boolean isTerminated(LocalDate contextDate) {
    return endDate != null && endDate.isBefore(contextDate);
  }

  /** @deprecated This is for client backward compatibility. DO NOT USE!! */
  @Deprecated(forRemoval = true)
  public EmployeePositionDeprecated getActivePosition() {
    return new EmployeePositionDeprecated(
        getTitle(), getPayType(), getPayRate(), getEmploymentStatus());
  }

  /** @deprecated This is for client backward compatibility. DO NOT USE!! */
  @Value
  @Deprecated(forRemoval = true)
  private static class EmployeePositionDeprecated {
    String title;
    PayType payType;
    Money payRate;
    EmploymentStatus employmentStatus;
  }
}
