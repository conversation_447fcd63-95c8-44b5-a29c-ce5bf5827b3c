package com.everee.api.employee;

import com.everee.api.config.attributeconverter.YearIntAttributeConverter;
import com.everee.api.file.Document;
import com.everee.api.file.DocumentType;
import com.everee.api.file.SystemFolderType;
import com.everee.api.form.FormSignatureType;
import com.everee.api.form.response.FormSignatureState;
import com.everee.api.model.BaseModelV2;
import com.everee.api.util.FileNameUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Entity
@ToString
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("employee")
public class EmployeeDocument extends BaseModelV2 implements Document<EmployeeDocument> {

  @NotNull private Long employeeId;

  @NotNull private Long companyId;

  private String fileName;

  private Long fileSizeBytes;

  private String location;

  private Long folderId;

  @Enumerated(EnumType.STRING)
  @Setter(AccessLevel.NONE)
  @Formula("coalesce((select f.systemFolderType from folder f where f.id = folderId))")
  private SystemFolderType systemFolderType;

  private Long ownerId;

  private String mimeType;

  public String getMimeType() {
    return Optional.ofNullable(mimeType)
        .orElseGet(() -> FileNameUtils.getMimeTypeFromFilename(getFileName()));
  }

  private LocalDate archivedAt;

  private Long archivedBy;

  private LocalDate deletedAt;

  private Long deletedBy;

  @Enumerated(EnumType.STRING)
  private DocumentType documentType;

  private LocalDateTime dismissedAt;

  private String documentMetadata;

  private LocalDateTime publishedAt;

  private LocalDateTime publishedToCompanyAt;

  @Convert(converter = YearIntAttributeConverter.class)
  private Year taxYear;

  @Transient @Nullable private String url;
  @Transient @Nullable private String previewUrl;

  @Setter(AccessLevel.NONE)
  @Formula("coalesce((select u.firstname from appuser u where u.id = ownerid))")
  private String ownerName;

  private Long employeeFormId;

  private Long fireFileSubmissionId;

  @Setter(AccessLevel.NONE)
  @Formula(COMPANY_DOCUMENT_LOCATION_FORMULA)
  @JsonIgnore
  private String companyDocumentLocation;

  /** @deprecated use {@link #workerSignatureState and #companySignatureState} instead. */
  @Deprecated
  @Setter(AccessLevel.NONE)
  @Enumerated(EnumType.STRING)
  @Formula(EMPLOYEE_RESPONSE_SIGNATURE_FORMULA)
  private FormSignatureState signatureState;

  @Setter(AccessLevel.NONE)
  @Enumerated(EnumType.STRING)
  @Formula(COMPANY_SIGNATURE_FORMULA)
  private FormSignatureState companySignatureState;

  @Setter(AccessLevel.NONE)
  @Enumerated(EnumType.STRING)
  @Formula(WORKER_SIGNATURE_FORMULA)
  private FormSignatureState workerSignatureState;

  @Setter(AccessLevel.NONE)
  @Enumerated(EnumType.STRING)
  @Formula(FORM_SIGNATURE_TYPE)
  private FormSignatureType formSignatureType;

  @JsonIgnore
  @Override
  public List<String> getStoragePath(Long companyId) {
    return List.of("document-store", companyId.toString(), "employeedocs", employeeId.toString());
  }

  private static final String EMPLOYEE_RESPONSE_SIGNATURE_FORMULA =
      "(select "
          + " coalesce(case when f.signaturetype in ('EMPLOYEE_AND_COMPANY', 'COMPANY') then coalesce(cs.state, 'NONE') else s.state end, case when f.signaturetype in ('EMPLOYEE_AND_COMPANY', 'EMPLOYEE') then s.state else cs.state end, case when f.signaturetype <> 'NONE' then 'NONE' else null end)"
          + " from employeeform ef join form f on ef.formid=f.id left join formresponse r on r.id=ef.formresponseid left join formresponsesignature s on r.id=s.formresponseid AND s.signatureType = 'EMPLOYEE_FORM' left join formresponsesignature cs on r.id=cs.formresponseid AND cs.signatureType = 'COMPANY_FORM' where ef.id = employeeFormId)";

  private static final String COMPANY_SIGNATURE_FORMULA =
      "(SELECT "
          + "     CASE WHEN s.state IS NOT NULL THEN s.state ELSE 'NONE' END "
          + "FROM employeeform ef "
          + "    JOIN form f ON ef.formid = f.id "
          + "    LEFT JOIN formresponse r ON r.id=ef.formresponseid "
          + "    LEFT JOIN formresponsesignature s ON r.id=s.formresponseid AND s.signatureType = 'COMPANY_FORM' "
          + "WHERE ef.id = employeeFormId)";

  private static final String WORKER_SIGNATURE_FORMULA =
      "(SELECT "
          + "     CASE WHEN s.state IS NOT NULL THEN s.state ELSE 'NONE' END "
          + "FROM employeeform ef "
          + "    JOIN form f ON ef.formid = f.id "
          + "    LEFT JOIN formresponse r ON r.id=ef.formresponseid "
          + "    LEFT JOIN formresponsesignature s ON r.id=s.formresponseid AND s.signatureType = 'EMPLOYEE_FORM' "
          + "WHERE ef.id = employeeFormId)";

  private static final String FORM_SIGNATURE_TYPE =
      "(SELECT f.signaturetype FROM employeedocument ed "
          + "    JOIN employeeform ef ON ed.employeeformid = ef.id "
          + "    JOIN form f ON ef.formid = f.id "
          + " WHERE ef.id = employeeFormId) ";

  private static final String COMPANY_DOCUMENT_LOCATION_FORMULA =
      "(select "
          + " cd.location"
          + " from employeeform ef join companydocument cd on cd.signatureFormId=ef.formid where ef.id = employeeFormId)";
}
