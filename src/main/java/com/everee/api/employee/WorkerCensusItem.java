package com.everee.api.employee;

import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentMethod;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.tax.state.State;
import java.time.LocalDate;
import java.time.LocalDateTime;

public interface WorkerCensusItem {
  String getFullName();

  String getLastName();

  String getFirstName();

  String getMiddleName();

  String getEmail();

  String getPhoneNumber();

  LocalDate getDateOfBirth();

  String getTaxpayerIdentifier();

  String getAddressLine1();

  String getAddressLine2();

  String getAddressCity();

  State getAddressState();

  String getAddressPostalCode();

  LocalDate getHireDate();

  LocalDate getTerminationDate();

  String getTitle();

  String getApprovalGroupName();

  String getWorkLocationName();

  EmploymentType getWorkerType();

  PayType getPayType();

  Money getPayRate();

  Boolean getEligibleForOvertime();

  LocalDate getPayRateEffectiveDate();

  PayPeriodType getPayPeriodPreferenceType();

  Long getTypicalWeeklyHours();

  String getWorkerId();

  String getExternalWorkerId();

  Long getEmployeeId();

  String getPreferredName();

  String getShirtSize();

  String getPartnerShirtSize();

  String getDietaryRestrictions();

  String getEmergencyContact1FullName();

  String getEmergencyContact1Phone();

  String getEmergencyContact1Email();

  String getEmergencyContact1Relationship();

  String getEmergencyContact2FullName();

  String getEmergencyContact2Phone();

  String getEmergencyContact2Email();

  String getEmergencyContact2Relationship();

  String getPayeeType();

  String getBusinessName();

  String getDba();

  String getOnboardingStatus();

  LocalDateTime getCreatedAt();

  PaymentMethod getPreferredPaymentMethod();
}
