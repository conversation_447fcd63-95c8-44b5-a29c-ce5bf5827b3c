package com.everee.api.employee.contributiondeduction;

import com.everee.api.auth.annotation.PeopleApproverAccess;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController("PublicV2EmployeeContributionDeductionTypeController")
@RequestMapping("/api/v2/contribution-deductions/types")
@PeopleApproverAccess
@RequiredArgsConstructor
public class EmployeeContributionDeductionTypeController {

  private final ContributionDeductionService deductionService;

  @GetMapping("/multiples")
  @ResponseStatus(HttpStatus.OK)
  public Set<EmployeeContributionDeductionType> getContributionTypesAllowingMultiples() {
    return deductionService.getEmployeeContributionDeductionTypesAllowingMultiples();
  }
}
