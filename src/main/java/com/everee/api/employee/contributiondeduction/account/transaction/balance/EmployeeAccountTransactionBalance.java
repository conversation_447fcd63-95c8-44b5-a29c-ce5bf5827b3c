package com.everee.api.employee.contributiondeduction.account.transaction.balance;

import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.money.Money;
import com.everee.api.util.DisplayFullName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Type;

@Data
@Entity
@Immutable
public class EmployeeAccountTransactionBalance implements DisplayFullName {

  @Id private String id;

  @NotNull private Long companyId;
  @NotNull private String companyName;

  @NotNull private String workerId;
  @NotNull private Long employeeId;

  @NotNull private String firstName;
  @NotNull private String lastName;
  private String middleName;

  private Long paymentId;
  @NotNull private LocalDate transactionDate;
  private boolean finalized;

  @NotNull private Money liabilityAmount = Money.ZERO;
  @NotNull private Money assetAmount = Money.ZERO;
  @NotNull private Money balance = Money.ZERO;

  @NotNull private Money pendingLiabilityAmount = Money.ZERO;
  @NotNull private Money pendingAssetAmount = Money.ZERO;
  @NotNull private Money pendingBalance = Money.ZERO;

  @Type(type = "list-array")
  @Column(columnDefinition = "bigint[]")
  private List<Long> employeeAccountTransactionIds;

  @JsonIgnore private String types;

  public Set<EmployeeContributionDeductionType> getTypesWithBalance() {
    return Arrays.stream(types.split(","))
        .map(EmployeeContributionDeductionType::valueOf)
        .collect(Collectors.toSet());
  }
}
