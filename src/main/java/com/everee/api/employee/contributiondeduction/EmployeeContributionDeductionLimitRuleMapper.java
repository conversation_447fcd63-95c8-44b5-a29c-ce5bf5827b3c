package com.everee.api.employee.contributiondeduction;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EmployeeContributionDeductionLimitRuleMapper {
  EmployeeContributionDeductionLimitRuleMapper MAPPER =
      Mappers.getMapper(EmployeeContributionDeductionLimitRuleMapper.class);

  ContributionDeductionLimitRuleParams toParams(EmployeeContributionDeductionLimitRule limitRule);
  void updateFromParams(@MappingTarget EmployeeContributionDeductionLimitRule limitRule,
                        ContributionDeductionLimitRuleParams limitRuleParams);
}
