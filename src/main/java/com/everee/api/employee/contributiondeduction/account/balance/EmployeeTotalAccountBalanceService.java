package com.everee.api.employee.contributiondeduction.account.balance;

import static java.util.Optional.ofNullable;

import com.everee.api.companyemployeeimport.ExcelService;
import com.everee.api.money.Money;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.WillClose;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EmployeeTotalAccountBalanceService {
  private static final List<String> EMPLOYEE_TOTAL_ACCOUNT_BALANCE_HEADERS =
      List.of("Worker", "Balance");
  private static final String NEGATIVE_BALANCE = "Negative Balance";

  private final EmployeeTotalAccountBalanceLookupService employeeTotalAccountBalanceLookupService;
  private final ExcelService excelService;

  public void exportExcelData(
      EmployeeTotalAccountBalanceLookup lookup, Sort sort, @WillClose OutputStream outputStream)
      throws IOException {
    var mutator = excelService.create();
    mutator.sheet(NEGATIVE_BALANCE);
    var row = new AtomicInteger(0);
    var col = new AtomicInteger(0);
    EMPLOYEE_TOTAL_ACCOUNT_BALANCE_HEADERS.forEach(
        (colName) -> setExcelCell(mutator, row, col, colName));

    col.set(0);
    row.getAndIncrement();

    // default sort
    if (sort == null || sort.isEmpty()) {
      sort = Sort.by(Sort.Order.asc("lastName"), Sort.Order.asc("firstName"));
    }

    employeeTotalAccountBalanceLookupService
        .streamAll(lookup, Pageable.unpaged(), sort)
        .forEach(
            item -> {
              // Worker
              setExcelCell(mutator, row, col, item.getFullName());

              // Balance
              var balance =
                  ofNullable(item.getBalance())
                      .map(m -> m.times(-1))
                      .map(Money::toDisplayString)
                      .orElse(null);
              setExcelCell(mutator, row, col, balance);

              col.set(0);
              row.getAndIncrement();
            });

    mutator.writeTo(outputStream);
  }

  private void setExcelCell(
      ExcelService.ExcelMutator mutator, AtomicInteger row, AtomicInteger col, String value) {
    mutator.cell(row.get(), col.getAndIncrement()).set(value);
  }
}
