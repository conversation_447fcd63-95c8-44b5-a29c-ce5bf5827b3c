package com.everee.api.employee.contributiondeduction.report;

import static java.util.Optional.ofNullable;

import com.everee.api.company.DetailedCompany;
import com.everee.api.companyemployeeimport.ExcelService;
import com.everee.api.companyemployeeimport.ExcelService.ExcelMutator;
import com.everee.api.employee.contributiondeduction.*;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.DateRange;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentRepository;
import com.everee.api.payment.contributiondeduction.PaymentContributionDeductionRepository;
import com.everee.api.storage.StorageAccess;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.util.FileNameUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ContributionDeductionReportService {

  private final ExcelService excelService;
  private final EmployeeContributionDeductionRepository employeeContributionDeductionRepository;
  private final MessageSource messageSource;
  private final PaymentRepository paymentRepository;
  private final StorageService storageService;
  private final PaymentContributionDeductionRepository contributionDeductionRepository;

  @Value("${app.r365.integration.contribution.deduction.report}")
  protected boolean CONTRIBUTE_DEDUCTION_REPORT_REPLACE_WORK_TO_EMPLOYEE;

  @Transactional(readOnly = true)
  public StoredFileLink generateBenefitsDeductionsSettingsAndTotalsReport(
      DetailedCompany company, LocalDate startDate, LocalDate endDate) throws IOException {
    var companyLocalNowDate = CompanyLocalTimeService.companyLocalNowDate(company);
    var col = new AtomicInteger();
    var row = new AtomicInteger();
    var mutator = excelService.create();

    var totalContributionDeductionItemsStream =
        paymentRepository.streamTotalContributionDeductionExportItems(
            company.getId(), startDate, endDate);
    mutator.sheet("Total Benefits & Deductions");

    col.set(0);
    row.set(0);

    List<String> headerTBD = new ArrayList<>();
    headerTBD.addAll(
        List.of(
            "Full name",
            "Last name",
            "First name",
            "Type",
            "Detailed name",
            "Date range",
            "Total deductions",
            "Total contributions",
            getMessage("contribution.deduction.report.payrollEmployeeID")));

    if (!CONTRIBUTE_DEDUCTION_REPORT_REPLACE_WORK_TO_EMPLOYEE) {
      addHeader(headerTBD, "External worker ID");
      addHeader(headerTBD, "Everee ID");
    }
    headerTBD.forEach((colName) -> setExcelCell(mutator, row, col, colName));

    col.set(0);
    row.getAndIncrement();

    totalContributionDeductionItemsStream.forEach(
        item -> {
          var dateRange =
              DateRange.of(startDate, endDate)
                  .getLocalizedDescription(LocaleContextHolder.getLocale());

          setExcelCell(mutator, row, col, item.getWorkerDisplayFullName());
          setExcelCell(mutator, row, col, item.getWorkerLastName());
          setExcelCell(mutator, row, col, item.getWorkerFirstName());
          setExcelCell(mutator, row, col, localize(item.getType().getLocalizedTitle()));
          setExcelCell(mutator, row, col, item.getName());
          setExcelCell(mutator, row, col, dateRange);
          setExcelCell(mutator, row, col, item.getDeductionAmount().getAmount().toPlainString());
          setExcelCell(mutator, row, col, item.getContributionAmount().getAmount().toPlainString());
          setExcelCell(mutator, row, col, item.getWorkerId());
          if (!CONTRIBUTE_DEDUCTION_REPORT_REPLACE_WORK_TO_EMPLOYEE) {
            setExcelCell(mutator, row, col, item.getExternalWorkerId());
            setExcelCell(mutator, row, col, item.getEmployeeId().toString());
          }

          row.getAndIncrement();
          col.set(0);
        });

    totalContributionDeductionItemsStream.close();

    var contributionDeductionSettingsStream =
        employeeContributionDeductionRepository.streamEmployeeContributionDeductionExportItems(
            company.getId(), companyLocalNowDate, startDate, endDate);
    mutator.sheet("Benefits & Deductions Settings");

    col.set(0);
    row.set(0);

    List<String> headerBDS = new ArrayList<>();
    headerBDS.addAll(
        List.of(
            "Full name",
            "Last name",
            "First name",
            "Type",
            "Detailed name",
            "Effective date",
            "Expiry date",
            "Method",
            "Distribution method",
            "Fixed deduction (monthly)",
            "Fixed contribution (monthly)",
            "Percent deduction",
            "Percent contribution",
            getMessage("contribution.deduction.report.payrollEmployeeID")));

    if (!CONTRIBUTE_DEDUCTION_REPORT_REPLACE_WORK_TO_EMPLOYEE) {
      addHeader(headerBDS, "External worker ID");
      addHeader(headerBDS, "Everee ID");
    }
    headerBDS.forEach((colName) -> setExcelCell(mutator, row, col, colName));

    col.set(0);
    row.getAndIncrement();

    contributionDeductionSettingsStream.forEach(
        item -> {
          var amountType =
              ofNullable(item.getAmountType())
                  .map(EmployeeContributionDeductionAmountType::getLocalizedTitle)
                  .map(this::localize)
                  .orElse(null);

          var fixedMethod =
              ofNullable(item.getFixedMethod())
                  .map(ContributionDeductionFixedMethod::getLocalizedTitle)
                  .map(this::localize)
                  .orElse(null);

          var fixedDeductionAmount =
              EmployeeContributionDeductionAmountType.FIXED.equals(item.getAmountType())
                  ? item.getAmountEe().getAmount().toPlainString()
                  : null;

          var fixedContributionAmount =
              EmployeeContributionDeductionAmountType.FIXED.equals(item.getAmountType())
                  ? item.getAmountEr().getAmount().toPlainString()
                  : null;

          var percentDeduction =
              EmployeeContributionDeductionAmountType.PERCENT.equals(item.getAmountType())
                  ? item.getPercentEe().getAmount().toPlainString()
                  : null;

          var percentContribution =
              EmployeeContributionDeductionAmountType.PERCENT.equals(item.getAmountType())
                  ? item.getPercentEr().getAmount().toPlainString()
                  : null;

          var endDateLabel = ofNullable(item.getEndDate()).map(LocalDate::toString).orElse(null);

          setExcelCell(mutator, row, col, item.getWorkerDisplayFullName());
          setExcelCell(mutator, row, col, item.getWorkerLastName());
          setExcelCell(mutator, row, col, item.getWorkerFirstName());
          setExcelCell(mutator, row, col, localize(item.getType().getLocalizedTitle()));
          setExcelCell(mutator, row, col, item.getName());
          setExcelCell(mutator, row, col, item.getStartDate().toString());
          setExcelCell(mutator, row, col, endDateLabel);
          setExcelCell(mutator, row, col, amountType);
          setExcelCell(mutator, row, col, fixedMethod);
          setExcelCell(mutator, row, col, fixedDeductionAmount);
          setExcelCell(mutator, row, col, fixedContributionAmount);
          setExcelCell(mutator, row, col, percentDeduction);
          setExcelCell(mutator, row, col, percentContribution);
          setExcelCell(mutator, row, col, item.getWorkerId());
          if (!CONTRIBUTE_DEDUCTION_REPORT_REPLACE_WORK_TO_EMPLOYEE) {
            setExcelCell(mutator, row, col, item.getExternalWorkerId());
            setExcelCell(mutator, row, col, item.getEmployeeId().toString());
          }

          row.getAndIncrement();
          col.set(0);
        });

    contributionDeductionSettingsStream.close();

    var outputStream = new ByteArrayOutputStream();
    mutator.writeTo(outputStream);

    var path = List.of("benefits-deductions-report", company.getId().toString());
    var safeTitle = FileNameUtils.fileNameSafeString("Benefits_Deductions_Report");
    var companyName = FileNameUtils.fileNameSafeString(company.getDisplayName());
    var fileName = String.join("_", safeTitle, companyName, companyLocalNowDate.toString());

    return storageService.storeForImmediateDownload(
        outputStream.toByteArray(), fileName + ".xlsx", StorageAccess.CUSTOMER_FACING, path);
  }

  public StoredFileLink getWorkerPaymentContributionDeductionSummariesReport(
      DetailedCompany company,
      LocalDate startDate,
      LocalDate endDate,
      Set<EmployeeContributionDeductionType> types) {
    var mutator = excelService.create();
    mutator.sheet("Contributions - Deductions");

    var col = new AtomicInteger();
    var row = new AtomicInteger();
    row.set(0);
    col.set(0);

    var headers =
        new ArrayList<>(
            List.of("Worker", "Worker ID", "External worker ID", "Fund and remit", "Amount"));
    if (types.stream().anyMatch(GarnishmentEmployeeContributionDeductionType.DEFAULT_GARNISHMENT_TYPES::contains)) {
      headers.addAll(
          List.of(
              "Agency", "Agency Address", "Order ID", "Case ID", "Remittance ID", "Last 4 of SSN"));
    }
    headers.forEach((colName) -> setExcelCell(mutator, row, col, colName));

    contributionDeductionRepository
        .getWorkerPaymentContributionDeductionSummaries(
            company.getId(), startDate, endDate, types, null, null, Pageable.unpaged())
        .stream()
        .forEach(
            summary -> {
              col.set(0);
              row.getAndIncrement();
              setExcelCell(mutator, row, col, summary.getFullName());
              setExcelCell(mutator, row, col, summary.getWorkerId());
              setExcelCell(mutator, row, col, summary.getExternalWorkerId());
              setExcelCell(mutator, row, col, getFundAndRemitValue(summary.getFundingAmount()));
              setExcelCell(mutator, row, col, summary.getAmountEE().getAmount().toPlainString());
              if (types.stream().anyMatch(GarnishmentEmployeeContributionDeductionType.DEFAULT_GARNISHMENT_TYPES::contains)) {
                setExcelCell(mutator, row, col, summary.getAgency());
                setExcelCell(mutator, row, col, summary.getAgencyAddress());
                setExcelCell(mutator, row, col, summary.getOrderId());
                setExcelCell(mutator, row, col, summary.getCaseId());
                setExcelCell(mutator, row, col, summary.getRemittanceId());
                setExcelCell(mutator, row, col, summary.getTaxpayerIdLast4());
              }
            });

    var byteArrayOs = new ByteArrayOutputStream();
    try {
      mutator.writeTo(byteArrayOs);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }

    var filename =
        "ContributionDeductions_"
            + DateRange.of(startDate, endDate).getDateRangeInfo(LocaleContextHolder.getLocale())
            + "_"
            + company.getDisplayName()
            + ".xlsx";
    return storageService.storeForImmediateDownload(
        byteArrayOs.toByteArray(),
        filename,
        StorageAccess.CUSTOMER_FACING,
        List.of("reports", company.getId().toString(), "contribution_deduction"));
  }

  private String getFundAndRemitValue(Money fundingAmount) {
    return Money.zeroOr(fundingAmount).isPlus() ? "Enabled" : "Disabled";
  }

  private String localize(@NonNull LocalizedString localizedString) {
    return localizedString.getLocalizedValue(messageSource, LocaleContextHolder.getLocale());
  }

  private void setExcelCell(
      ExcelMutator mutator, AtomicInteger row, AtomicInteger col, String value) {
    mutator.cell(row.get(), col.getAndIncrement()).set(value);
  }

  private String getMessage(String code, Object... args) {
    return messageSource.getMessage(code, args, Locale.ENGLISH);
  }

  public void addHeader(List<String> headers, String name) {
    headers.add(name);
  }
}
