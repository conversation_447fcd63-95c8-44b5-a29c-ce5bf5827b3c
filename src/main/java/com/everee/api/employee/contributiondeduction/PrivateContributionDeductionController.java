package com.everee.api.employee.contributiondeduction;

import com.everee.api.payment.PaymentContextDateType;
import com.everee.api.payment.PaymentType;
import com.everee.api.payment.contributiondeduction.WorkerPaymentContributionDeductionSummaryImpl;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.util.DateUtil;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Optional;
import java.util.Set;
import javax.validation.Valid;

import com.everee.api.util.PageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api-private/v1/contribution-deductions")
public class PrivateContributionDeductionController {
  private final ContributionDeductionLookupService lookupService;
  private final ContributionDeductionService deductionService;

  @GetMapping
  public Page<EmployeeContributionDeduction> listContributionDeductions(
      @RequestParam(name = "employee-id", required = false) Set<Long> employeeIds,
      @RequestParam(name = "type", required = false) Set<EmployeeContributionDeductionType> types,
      @RequestParam(name = "hidden", required = false) Boolean hidden,
      PhaseLookup phaseLookup,
      Pageable pageable) {
    var lookup =
        new ContributionDeductionLookup()
            .withEmployeeIds(employeeIds)
            .withTypes(types)
            .withPhaseLookup(phaseLookup)
            .withHidden(hidden);

    return lookupService.listAll(lookup, pageable);
  }

  @PostMapping
  public EmployeeContributionDeduction createContributionDeduction(
      @RequestParam(name = "payment-type", required = false) PaymentType paymentType,
      @Valid @RequestBody ContributionDeductionParams params) {
    params.setSkipRecalcPayments(true);
    return deductionService.create(params, paymentType);
  }

  @PutMapping("/{id}")
  public EmployeeContributionDeduction updateContributionDeduction(
      @PathVariable Long id, @Valid @RequestBody ContributionDeductionParams params) {
    var lookup =
        new ContributionDeductionLookup()
            .withIds(Set.of(id))
            .withPhaseLookup(PhaseLookup.unphased());
    params.setSkipRecalcPayments(true);
    return deductionService.update(lookup, params);
  }

  @PostMapping("/calculate-previous-month-liability-shortages")
  public void calculatePreviousMonthLiabilityShortages(
      @RequestParam(name = "company-id", required = false) Set<Long> companyIds,
      @RequestParam(name = "context-date") Optional<LocalDate> contextDate) {
    deductionService.calculatePreviousMonthLiabilityShortages(companyIds, contextDate);
  }

  @GetMapping("/payment-worker-summary")
  public Page<WorkerPaymentContributionDeductionSummaryImpl>
      listWorkerPaymentContributionDeductionSummariesForCompany(
          @RequestParam(name = "start-date", required = false) LocalDate startDate,
          @RequestParam(name = "end-date", required = false) LocalDate endDate,
          @RequestParam(name = "type", required = false)
              Set<EmployeeContributionDeductionType> types,
          @RequestParam(name = "with-funding", required = false) Boolean withFunding,
          @RequestParam(name = "company-id", required = false) Long companyId,
          @RequestParam(
                  name = "contribution-deduction-context-date-type-override",
                  required = false)
              PaymentContextDateType contributionDeductionContextDateTypeOverride,
          @RequestParam(name = "year-month", required = false) YearMonth yearMonth,
          @RequestParam(name = "use-month", required = false) Boolean useMonths,
          @RequestParam(name = "all", required = false, defaultValue = "false") Boolean isReturnAll,
          @RequestParam(name = "min-funding-amount", required = false, defaultValue = "" + Double.MIN_VALUE) Double minFundingAmount,
          @RequestParam(name = "max-funding-amount", required = false, defaultValue = "" + Double.MAX_VALUE) Double maxFundingAmount,
          @RequestParam(name = "first-name", required = false, defaultValue = "") String workerName,
          @RequestParam(name = "agency", required = false, defaultValue = "") String agency,
          Pageable pageable
  ) {
      var dateRange = DateUtil.getDateRange(yearMonth, startDate, endDate, useMonths);
      if (isReturnAll) {
          pageable = PageUtils.getConditionalPageable(true, pageable, true);;
      }
      return deductionService.listWorkerPaymentContributionDeductionSummariesForCompany(dateRange, types,
              withFunding, companyId, contributionDeductionContextDateTypeOverride
              , minFundingAmount, maxFundingAmount, workerName, agency, pageable);
  }
}
