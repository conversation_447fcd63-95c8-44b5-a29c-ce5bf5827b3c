package com.everee.api.employee.contributiondeduction;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.EmployeeService;
import com.everee.api.logging.MarkerBuilder;
import com.everee.api.model.BaseModelV2;
import com.everee.api.model.TransactionHandler;
import com.everee.api.money.Money;
import com.everee.api.payment.*;
import com.everee.api.payment.paymentdeductionoverride.PaymentDeductionOverrideService;
import com.everee.api.payment.paymentdeductionoverride.PaymentDeductionOverrideUpsertParams;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payperiod.PayPeriodService;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payrun.PayRunService;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContributionDeductionImportService {
  private final ContributionDeductionLookupService contributionDeductionLookupService;
  private final PayRunService payRunService;
  private final PayPeriodService payPeriodService;
  private final EmployeeService employeeService;
  private final PaymentService paymentService;
  private final ContributionDeductionService contributionDeductionService;
  private final PaymentDeductionOverrideService paymentDeductionOverrideService;
  private final EmployeeContributionDeductionRepository employeeContributionDeductionRepository;
  private final PaymentCalculationService paymentCalculationService;
  private final TransactionHandler transactionHandler;

  @Transactional
  public void deleteContributionDeductionsByImportId(String importId) {
    // Deletes imported amounts for variable deduction types
    contributionDeductionService.deleteAmountDetailsByImportId(importId);

    // Deletes imported amounts for fixed deduction types
    paymentDeductionOverrideService.deleteContributionDeductionOverridesByImportId(importId);
  }


  @Transactional
  public void importDeductionsByPayPeriod(
    Long payPeriodId,
    List<ContributionDeductionImportParams> paramsList
  ) {
    var payPeriod = payPeriodService.getPayPeriod(payPeriodId);
    importDeductions(payPeriod, paramsList);
  }

  @Transactional
  public void importDeductionsByPayRun(
    Long payRunId,
    List<ContributionDeductionImportParams> paramsList
  ) {

    var payRun = payRunService.getPayRun(payRunId);
    var payPeriod = payRun.getPayPeriod();
    importDeductions(payPeriod, paramsList);
  }

  private void importDeductions(
    PayPeriod payPeriod,
    List<ContributionDeductionImportParams> paramsList
  ) {
    var sw = new StopWatch();
    sw.start(LoggingKeys.TIMING_IMPORT_PREPARE_MS);
    var companyId = payPeriod.getCompanyId();

    var groupedDeductions = groupImportParamsByWorkerAndType(paramsList);
    var workerEmployeeMap = getWorkerEmployeeMap(companyId, groupedDeductions.keySet());

    // Accumulators so that bulk operations are possible at the end of the process
    var affectedEcds = new ArrayList<EmployeeContributionDeduction>();
    var affectedOverrideUpsertParams = new ArrayList<PaymentDeductionOverrideUpsertParams>();
    var affectedPaymentIds = new HashSet<Long>();

    // Accumulator for logging purposes
    var resultsMap = getResultsMap(paramsList);

    sw.stop();

    // The actual processing needs to happen in an independent transaction so
    // that it gets committed before the payment recalculation message gets
    // created. The payment engine service will potentially not see any new
    // entities otherwise.
    transactionHandler.executeInNewTransaction(
      () -> {
        sw.start(LoggingKeys.TIMING_IMPORT_PROCESS_MS);

        // Loops over each workerId in the import
        for (var workerIdEntry: groupedDeductions.entrySet()) {
          var workerId = workerIdEntry.getKey();
          var importParamsByDeductionType = workerIdEntry.getValue();

          var employeeId = workerEmployeeMap.get(workerId);

          processEmployeeDeductionParams(
            payPeriod,
            employeeId,
            importParamsByDeductionType,
            affectedPaymentIds,
            affectedEcds,
            affectedOverrideUpsertParams,
            resultsMap
          );
        }
        sw.stop();

        // Saves the processed amounts
        sw.start(LoggingKeys.TIMING_IMPORT_BULK_SAVE_MS);
        bulkSaveImportedAmounts(affectedEcds, affectedOverrideUpsertParams);
        sw.stop();

      }
    );

    // Queues payment recalculations for the imported amounts to be reflected
    sw.start(LoggingKeys.TIMING_IMPORT_TRIGGER_PAYMENT_RECALC_MS);
    triggerPaymentRecalculations(affectedPaymentIds);
    sw.stop();

    logDeductionImportCompleted(payPeriod.getId(), sw, resultsMap);
  }

  private void processEmployeeDeductionParams(
    PayPeriod payPeriod,
    Long employeeId,
    Map<EmployeeContributionDeductionType, List<ContributionDeductionImportParams>> importParamsByDeductionType,
    Set<Long> affectedPaymentIds,
    List<EmployeeContributionDeduction> affectedEcds,
    List<PaymentDeductionOverrideUpsertParams> affectedOverrideUpsertParams,
    Map<String, ImportResultCounts> resultsMap
  ) {
    var companyId = payPeriod.getCompanyId();

    var payment =
      paymentService.getOrCreatePayment(companyId, employeeId, payPeriod);

    if (payment.getStatus().isFinalized()) {
      // Finalized payments are ignored during the import process and should be
      // registered as skipped in the result counter.
      updateSkippedParamsByImportIdCounter(resultsMap, importParamsByDeductionType);
    } else {
      affectedPaymentIds.add(payment.getId());

      // Loops over each ecdType that's imported for that workerId
      for (var deductionTypeEntry: importParamsByDeductionType.entrySet()) {
        var ecdType = deductionTypeEntry.getKey();
        var ecdTypeImportParams = deductionTypeEntry.getValue();

        processDeductionParams(
          payment,
          ecdType,
          ecdTypeImportParams,
          affectedEcds,
          affectedOverrideUpsertParams
        );
      }
    }
  }

  private void processDeductionParams(
    Payment payment,
    EmployeeContributionDeductionType ecdType,
    List<ContributionDeductionImportParams> ecdTypeImportParams,
    List<EmployeeContributionDeduction> affectedEcds,
    List<PaymentDeductionOverrideUpsertParams> affectedOverrideUpsertParams
  ) {
    var paymentId = payment.getId();

    var ecd =
      getOrCreateEcdForImport(
        payment.getCompanyId(),
        payment.getEmployeeId(),
        ecdType,
        payment.getPayPeriod()
      );

    var ecdId = ecd.getId();

    if (isEcdTypeVariable(ecdType)) {
      // Creates an amount detail entry for each imported variable deduction amount
      // and attaches them to the main ECD entity, as they're saved in cascade.
      var newEcdads =
        mapImportParamsToAmountDetails(paymentId, ecd, ecdTypeImportParams);

      ecd.getEcdAmountDetails().addAll(newEcdads);

      // Adds the ECD with the imported amounts to the accumulator
      affectedEcds.add(ecd);
    } else {

      var upsertParams =
        mapImportParamsToOverrideUpsertParams(paymentId, ecd, ecdTypeImportParams);

      // Adds the override to the accumulator
      affectedOverrideUpsertParams.add(upsertParams);
    }
  }

  /**
   * Utility method that creates a map with import IDs as keys and assigns an
   * ImportResults instance to act as an accumulator.
   */
  private Map<String, ImportResultCounts> getResultsMap(
    List<ContributionDeductionImportParams> paramsList
  ) {
    var paramsByImportId =
      paramsList
        .stream()
        .collect(Collectors.groupingBy(ContributionDeductionImportParams::getImportId));

    return paramsByImportId
      .entrySet()
      .stream()
      .collect(
        Collectors.toMap(
          Map.Entry::getKey,
          params ->
            new ImportResultCounts()
              .setTotal(params.getValue().size())
        )
      );
  }

  /**
   * Amounts for variable deductions get saved as a child collection of the ECD,
   * fixed deductions are saved as independent override records.
   */
  private void bulkSaveImportedAmounts(
    List<EmployeeContributionDeduction> ecds,
    List<PaymentDeductionOverrideUpsertParams> overrideUpsertParams
  ) {
    if (ecds != null && !ecds.isEmpty()) {
      employeeContributionDeductionRepository.saveAll(ecds);
    }

    if (overrideUpsertParams != null && !overrideUpsertParams.isEmpty()) {
      paymentDeductionOverrideService.upsertPaymentDeductionOverrides(overrideUpsertParams);
    }
  }

  private void triggerPaymentRecalculations(Set<Long> paymentIds) {
    if (paymentIds.isEmpty()) {
      return;
    }

    paymentCalculationService.triggerPaymentsRecalculation(List.copyOf(paymentIds));
  }

  private void logDeductionImportCompleted(
    Long payPeriodId,
    StopWatch sw,
    Map<String, ImportResultCounts> resultCounter
  ) {
    resultCounter.forEach(
      (importId, counter) -> {
        var loggingMarkerBuilder =
          new MarkerBuilder()
            .set(LoggingKeys.PAY_PERIOD_ID, payPeriodId)
            .set(LoggingKeys.IMPORT_ID, importId)
            .set(LoggingKeys.TOTAL_ITEM_COUNT, counter.getTotal())
            .set(LoggingKeys.SKIPPED_ITEM_COUNT, counter.getSkipped())
            .set(LoggingKeys.ELAPSED_MS, sw.getTotalTimeMillis());

        for (var swTask : sw.getTaskInfo()) {
          loggingMarkerBuilder.set(swTask.getTaskName(), swTask.getTimeMillis());
        }

        log.info(loggingMarkerBuilder.buildMarker(), "Completed processing import '{}'", importId);
      }
    );
  }

  /**
   * <p>
   *   Creates a single override for non-variable deduction types.
   * </p>
   * <p>
   *   If there are multiple imported amounts for the same employee and ECD type
   *   the last one will be used and the rest gets ignored.
   * </p>
   * <p>
   *   If there was already an import-generated override for this ECD, it gets
   *   overriden by the current import's resolved value.
   * </p>
   */
  private PaymentDeductionOverrideUpsertParams mapImportParamsToOverrideUpsertParams(
    Long paymentId,
    EmployeeContributionDeduction ecd,
    List<ContributionDeductionImportParams> paramsList
  ) {
    var params = paramsList.get(paramsList.size() - 1);

    return new PaymentDeductionOverrideUpsertParams(
      paymentId,
      ecd.getId(),
      params.getAmountEE(),
      params.getAmountEE(),
      ecd.getAmountER(),
      ecd.getAmountER(),
      params.getImportId()
    );

  }

  private List<EmployeeContributionDeductionAmountDetail> mapImportParamsToAmountDetails(
    Long paymentId,
    EmployeeContributionDeduction ecd,
    List<ContributionDeductionImportParams> paramsList
  ) {
    return paramsList
        .stream()
        .map(
          params ->
            new EmployeeContributionDeductionAmountDetail()
              .setPaymentId(paymentId)
              .setImportId(params.getImportId())
              .setEmployeeContributionDeductionId(ecd.getId())
              .setAmountEE(params.getAmountEE())
              .setAmountER(ecd.getAmountER())

        )
        .collect(Collectors.toList());
  }

  private EmployeeContributionDeduction getOrCreateEcdForImport(
    Long companyId,
    Long employeeId,
    EmployeeContributionDeductionType ecdType,
    PayPeriod payPeriod
  ) {
    return getEcdForImport(companyId, employeeId, ecdType, payPeriod)
        .orElseGet(
          () -> {
            var newEcdParams = new ContributionDeductionParams();
            newEcdParams.setCompanyId(companyId);
            newEcdParams.setEmployeeId(employeeId);
            newEcdParams.setType(ecdType);
            newEcdParams.setStartDate(payPeriod.getEndDate());
            newEcdParams.setAmountEE(Money.ZERO);
            newEcdParams.setAmountER(Money.ZERO);

            // Since this is a new point at which we can create ECDs, this
            // TO-DO comment is copy-pasted here as well:
            // ---
            // LIME-1968
            // TODO: Singles out the NET_ADJUSTMENT deduction as a temporary workaround, but the 'carry
            //  over' setting will be a new property on the EmployeeContributionDeductionType enum,
            //  added in a future task.
            newEcdParams.setCarryOverBalances(
              ecdType != EmployeeContributionDeductionType.NET_ADJ
            );

            // Multiple ECDs could potentially be created for a same payment, we
            // don't want to trigger a recalculation each time.
            newEcdParams.setSkipRecalcPayments(true);

            if (isEcdTypeVariable(ecdType)) {
              newEcdParams.setAmountType(EmployeeContributionDeductionAmountType.VARIABLE);
              newEcdParams.setTypeGuid(ecdType.createTypeGuid());
            } else {
              newEcdParams.setAmountType(EmployeeContributionDeductionAmountType.FIXED);
              newEcdParams.setFrequency(DeductionFrequency.PER_PAY);
              newEcdParams.setFixedMethod(ContributionDeductionFixedMethod.ALL_PAYMENTS);
              newEcdParams.setEndDate(payPeriod.getEndDate());

              // Import-created non-variable ECDs should be invisible to the
              // user and to always have unique typeGuid values, to prevent
              // potential collisions with other ECDs of the same type.
              //
              // This is necessary so that we can express imported amounts for
              // non-variable deductions as an override entry, which is
              // prevented by a DB constraint. Having multiple ECDs for the
              // same type allows us to work around this limitation.
              newEcdParams.setTypeGuid(UUID.randomUUID().toString());
              newEcdParams.setHidden(true);
            }

            return contributionDeductionService.create(newEcdParams);
          }
        );
  }

  private Optional<EmployeeContributionDeduction> getEcdForImport(
    Long companyId,
    Long employeeId,
    EmployeeContributionDeductionType ecdType,
    PayPeriod payPeriod
  ) {
    var lookup =
      new ContributionDeductionLookup()
        .withEmployeeIds(Set.of(employeeId))
        .withCompanyIds(Set.of(companyId))
        .withTypes(Set.of(ecdType))
        .withPhaseLookup(
          new PhaseLookup(
            Set.of(Phase.ACTIVE),
            payPeriod.getEndDate(),
            payPeriod.getEndDate()
          )
        );

    if (payPeriod.getPayPeriodType() == PayPeriodType.AD_HOC) {
      lookup.setFixedMethod(ContributionDeductionFixedMethod.ALL_PAYMENTS);
    }

    var foundEcds = contributionDeductionLookupService.findAll(lookup);

    // A fixed ECD can exist as hidden if it was auto-created during a previous
    // import process.
    // Another non-hidden could be found at the same time if it was added to the
    // employee profile after that import, we should prioritize the latter.
    if (!isEcdTypeVariable(ecdType)) {
      var existingNonHiddenEcd =
        foundEcds
          .stream()
          .filter(x -> !x.isHidden())
          .findFirst();

      if (existingNonHiddenEcd.isPresent()) {
        return existingNonHiddenEcd;
      }

      // We know the found ECDs don't support the variable amount type so we
      // can safely return the first one.
      return foundEcds.stream().findFirst();
    }

    // Takes an employee profile ECD that was configured to use variable amounts
    // since, at this point, we know that the ECD supports the variable amount type
    // but that doesn't mean it doesn't support FIXED or PERCENT as well.
    return foundEcds
      .stream()
      .filter(ecd -> ecd.getAmountType() == EmployeeContributionDeductionAmountType.VARIABLE)
      .findFirst();
  }

  private boolean isEcdTypeVariable(EmployeeContributionDeductionType ecdType) {
    return ecdType
      .getAllowedAmountTypes()
      .contains(EmployeeContributionDeductionAmountType.VARIABLE);
  }

  /**
   * Builds a map of params per deduction type, per workerId.
   * @return A map that uses workerIds as keys, and each value is another a map
   * that uses deduction types as keys.
   */
  private Map<String, Map<EmployeeContributionDeductionType, List<ContributionDeductionImportParams>>> groupImportParamsByWorkerAndType(
    List<ContributionDeductionImportParams> paramsList
  ) {
    return paramsList
      .stream()
      .collect(
        Collectors.groupingBy(
          ContributionDeductionImportParams::getWorkerId,
          Collectors.groupingBy(ContributionDeductionImportParams::getType)
        )
      );
  }

  /**
   * Given a list of worker IDs, returns a map to each corresponding employee ID.
   * @return A map that uses workerIds as keys and employeeIds as values.
   */
  private Map<String, Long> getWorkerEmployeeMap(Long companyId, Set<String> workerIds) {
    return employeeService
      .findByCompanyIdAndWorkerIdIn(companyId, workerIds)
      .stream()
      .collect(Collectors.toMap(DetailedEmployee::getWorkerId, BaseModelV2::getId));
  }

  private void updateSkippedParamsByImportIdCounter(
    Map<String, ImportResultCounts> resultCounter,
    Map<EmployeeContributionDeductionType, List<ContributionDeductionImportParams>> skippedParamsMap
  ) {
    // Takes a list of params grouped by deduction type, re-groups it into a
    // params list grouped by import ID and iterates each import ID entry to
    // update the provided counter adding or creating a counter for that ID.
    skippedParamsMap
      .values()
      .stream()
      .flatMap(List::stream)
      .collect(Collectors.groupingBy(ContributionDeductionImportParams::getImportId))
      .forEach(
        (importId, params) -> {
          resultCounter.compute(
            importId,
            (key, value) -> {
              var counts = Objects.requireNonNullElseGet(value, ImportResultCounts::new);
              return counts.setSkipped(counts.getSkipped() + params.size());
            }
          );
        }
      );
  }

  @Data
  @Accessors(chain = true)
  private class ImportResultCounts {
    private int skipped;
    private int total;
  }

  public static class LoggingKeys {
    static final String DEDUCTION_IMPORT_PREFIX = "deduction_import";

    static final String IMPORT_ID =
      DEDUCTION_IMPORT_PREFIX + "." + "import_id";

    static final String PAY_PERIOD_ID =
      DEDUCTION_IMPORT_PREFIX + "." + "pay_period_id";

    static final String TOTAL_ITEM_COUNT =
      DEDUCTION_IMPORT_PREFIX + "." + "total_item_count";

    static final String SKIPPED_ITEM_COUNT =
      DEDUCTION_IMPORT_PREFIX + "." + "skipped_item_count";

    static final String ELAPSED_MS =
      DEDUCTION_IMPORT_PREFIX + "." + "elapsed_ms";

    static final String TIMING_IMPORT_PREPARE_MS =
      DEDUCTION_IMPORT_PREFIX + "." + "import_prepare_ms";

    static final String TIMING_IMPORT_PROCESS_MS =
      DEDUCTION_IMPORT_PREFIX + "." + "import_process_ms";

    static final String TIMING_IMPORT_BULK_SAVE_MS =
      DEDUCTION_IMPORT_PREFIX + "." + "import_bulk_save_ms";

    static final String TIMING_IMPORT_TRIGGER_PAYMENT_RECALC_MS =
      DEDUCTION_IMPORT_PREFIX + "." + "import_trigger_payment_recalc_ms";
  }
}
