package com.everee.api.employee.contributiondeduction.account;

import static com.everee.api.companyemployeeimport.CompanyEmployeeImportController.APPLICATION_XLSX_VALUE;
import static com.everee.api.report.delivery.StreamingReportUtils.getReportName;
import static com.everee.api.report.delivery.StreamingReportUtils.headers;

import com.everee.api.company.CompanyService;
import com.everee.api.employee.contributiondeduction.account.balance.EmployeeTotalAccountBalance;
import com.everee.api.employee.contributiondeduction.account.balance.EmployeeTotalAccountBalanceLookup;
import com.everee.api.employee.contributiondeduction.account.balance.EmployeeTotalAccountBalanceLookupService;
import com.everee.api.employee.contributiondeduction.account.balance.EmployeeTotalAccountBalanceService;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionLookup;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionLookupService;
import com.everee.api.employee.contributiondeduction.account.transaction.balance.EmployeeAccountTransactionBalance;
import com.everee.api.employee.contributiondeduction.account.transaction.balance.EmployeeAccountTransactionBalanceLookup;
import com.everee.api.employee.contributiondeduction.account.transaction.balance.EmployeeAccountTransactionBalanceLookupService;
import java.util.Set;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

@RestController
@RequestMapping("/api/v2/employee-accounts")
@RequiredArgsConstructor
public class EmployeeAccountController {
  private final EmployeeAccountTransactionBalanceLookupService
      employeeAccountTransactionBalanceLookupService;
  private final EmployeeTotalAccountBalanceLookupService employeeTotalAccountBalanceLookupService;
  private final EmployeeTotalAccountBalanceService employeeTotalAccountBalanceService;
  private final EmployeeAccountTransactionLookupService employeeAccountTransactionLookupService;

  @GetMapping("/transactions")
  public Page<EmployeeAccountTransaction> listEmployeeAccountTransactions(
      @Valid EmployeeAccountTransactionLookup lookup, Pageable pageable) {
    return employeeAccountTransactionLookupService.listAll(lookup, pageable);
  }

  @GetMapping("/transaction-balances")
  public Page<EmployeeAccountTransactionBalance> listEmployeeAccountTransactionBalances(
      @Valid EmployeeAccountTransactionBalanceLookup lookup, Pageable pageable) {
    return employeeAccountTransactionBalanceLookupService.listAll(lookup, pageable);
  }

  @GetMapping("/total-balances")
  public Page<EmployeeTotalAccountBalance> listEmployeeAccountBalances(
      @Valid EmployeeTotalAccountBalanceLookup lookup, Pageable pageable) {
    return employeeTotalAccountBalanceLookupService.listAll(lookup, pageable);
  }

  @GetMapping("/report/total-balances")
  public ResponseEntity<StreamingResponseBody> getEmployeeAccountBalancesReport(
      @Valid EmployeeTotalAccountBalanceLookup lookup, Sort sort) {
    var company = CompanyService.getAuthenticatedCompany();

    return new ResponseEntity<>(
        stream -> employeeTotalAccountBalanceService.exportExcelData(lookup, sort, stream),
        headers(getReportName(company, "negative_balance"), APPLICATION_XLSX_VALUE),
        HttpStatus.OK);
  }

  @GetMapping("/total-balances/{workerId}")
  public EmployeeTotalAccountBalance getEmployeeAccountBalance(
      @PathVariable("workerId") String workerId) {
    return employeeTotalAccountBalanceLookupService.findOneOrThrow(
        new EmployeeTotalAccountBalanceLookup().setWorkerIds(Set.of(workerId)));
  }
}
