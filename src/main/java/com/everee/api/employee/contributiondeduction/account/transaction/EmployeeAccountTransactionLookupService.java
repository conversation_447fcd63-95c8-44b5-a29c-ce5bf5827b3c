package com.everee.api.employee.contributiondeduction.account.transaction;

import static com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction_.ACH_FILE_ID;
import static com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction_.EMPLOYEE_CONTRIBUTION_DEDUCTION_GUID;
import static com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction_.EMPLOYEE_ID;
import static com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction_.FUNDING_ACH_FILE_ID;
import static com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction_.PAYMENT_ID;
import static com.everee.api.model.BaseModelV2_.ID;
import static com.everee.api.query.where.Where.by;
import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.query.Query;
import com.everee.api.query.update.UpdateProperty;
import com.everee.api.util.BooleanApiParam;
import java.util.List;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class EmployeeAccountTransactionLookupService
    implements LookupService<
        EmployeeAccountTransaction,
        EmployeeAccountTransactionLookup,
        Query<EmployeeAccountTransaction>> {

  private final EntityManager entityManager;

  @Override
  public void configureQuery(
      EmployeeAccountTransactionLookup lookup, Query<EmployeeAccountTransaction> query) {
    query
        .where(property(ID).in(lookup.getIds()))
        .where(property(EMPLOYEE_ID).in(lookup.getEmployeeIds()))
        .where(property(PAYMENT_ID).in(lookup.getPaymentIds()))
        .where(property(ACH_FILE_ID).in(lookup.getAchFileIds()))
        .where(
            property(EMPLOYEE_CONTRIBUTION_DEDUCTION_GUID)
                .in(lookup.getEmployeeContributionDeductionGuids()))
        .where(by(presenceAsBooleanApiParam(BooleanApiParam.from(lookup.getFunded()), ACH_FILE_ID)))
        .where(
            by(
                presenceAsBooleanApiParam(
                    BooleanApiParam.from(lookup.getCompanyFunded()), FUNDING_ACH_FILE_ID)))
        .where(
            by(
                presenceAsBooleanApiParam(
                    BooleanApiParam.from(lookup.getPaymentTransaction()), PAYMENT_ID)));
  }

  @Override
  public Query<EmployeeAccountTransaction> createQuery() {
    return new Query<>(entityManager, EmployeeAccountTransaction.class);
  }

  @Transactional
  public void updateAchFileId(EmployeeAccountTransactionLookup lookup, Long achFileId) {
    update(lookup, List.of(new UpdateProperty<>(ACH_FILE_ID, achFileId)));
  }

  @Transactional
  public void updateFundingAchFileId(EmployeeAccountTransactionLookup lookup, Long achFileId) {
    update(lookup, List.of(new UpdateProperty<>(FUNDING_ACH_FILE_ID, achFileId)));
  }
}
