package com.everee.api.employee.contributiondeduction.account.balance;

import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.query.Query;
import java.math.BigDecimal;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EmployeeTotalAccountBalanceLookupService
    implements LookupService<
        EmployeeTotalAccountBalance,
        EmployeeTotalAccountBalanceLookup,
        Query<EmployeeTotalAccountBalance>> {

  private final EntityManager entityManager;

  @Override
  public void configureQuery(
      EmployeeTotalAccountBalanceLookup lookup, Query<EmployeeTotalAccountBalance> query) {
    query.where(property(EmployeeTotalAccountBalance_.COMPANY_ID).in(lookup.getCompanyIds()));
    query.where(property(EmployeeTotalAccountBalance_.WORKER_ID).in(lookup.getWorkerIds()));
    if (Boolean.TRUE.equals(lookup.getHasBalance()))
      query.where(
          property(EmployeeTotalAccountBalance_.BALANCE).greaterThan(new BigDecimal("0.0")));
    if (Boolean.FALSE.equals(lookup.getHasBalance()))
      query.where(property(EmployeeTotalAccountBalance_.BALANCE).equal(new BigDecimal("0.0")));
  }

  @Override
  public Query<EmployeeTotalAccountBalance> createQuery() {
    return new Query<>(entityManager, EmployeeTotalAccountBalance.class);
  }
}
