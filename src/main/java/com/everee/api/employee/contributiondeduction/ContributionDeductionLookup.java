package com.everee.api.employee.contributiondeduction;

import com.everee.api.company.ExternalBenefitsProvider;
import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import com.everee.api.phase.PhaseLookup;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@RequestParams
public class ContributionDeductionLookup implements Lookup {
  @ApiParam(name = "id", allowMultiple = true)
  private Set<Long> ids;

  @ApiParam(name = "company-id", allowMultiple = true)
  private Set<Long> companyIds;

  @ApiParam(name = "employee-id", allowMultiple = true)
  private Set<Long> employeeIds;

  @ApiParam(name = "type", allowMultiple = true)
  private Set<EmployeeContributionDeductionType> types;

  @ApiParam(name = "type-guid", allowMultiple = true)
  private Set<String> typeGuids;

  @ApiParam(name = "hidden")
  private Boolean hidden;

  @ApiParam(hidden = true)
  private Set<LocalDate> startDates;

  @ApiParam(hidden = true)
  private Set<LocalDate> endDates;

  @ApiParam(hidden = true)
  private Set<LocalDate> benefitPlanYearStartDates;

  @ApiParam(hidden = true)
  private Set<LocalDate> benefitPlanYearEndDates;

  @ApiParam(hidden = true)
  private Set<ExternalBenefitsProvider> externalBenefitsProviders;

  @ApiParam(name = "fixed-Method")
  private ContributionDeductionFixedMethod fixedMethod;

  private PhaseLookup phaseLookup;
}
