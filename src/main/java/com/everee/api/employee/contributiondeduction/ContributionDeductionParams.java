package com.everee.api.employee.contributiondeduction;

import com.everee.api.company.ExternalBenefitsProvider;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContributionDeductionParams {
  private Long id;
  private Long companyId;
  private Long employeeId;
  private EmployeeContributionDeductionType type;
  private String typeGuid;
  private String description;
  private EmployeeContributionDeductionAmountType amountType;
  private Money amountEE;
  private Money amountER;
  private Money thresholdAmountEE;
  private BigDecimal percentEE;
  private BigDecimal percentER;
  private Money percentLimitEE;
  private Money percentLimitER;
  private Money amountLimitEE;
  private Money maxTotalAmountEE;
  private LocalDate startDate;
  private LocalDate endDate;
  private ContributionDeductionFixedMethod fixedMethod;
  private boolean carryOverBalances;
  private ExternalBenefitsProvider managedByProvider;
  private ContributionDeductionLimitType limitType;
  private ContributionDeductionGarnishmentDetailsParams garnishmentDetails;
  private DeductionFrequency frequency;
  private String name;
  private LocalDate benefitPlanYearStartDate;
  private LocalDate benefitPlanYearEndDate;
  private boolean hidden;
  private ContributionDeductionLimitRuleParams limitRule;

  @JsonIgnore private boolean skipRecalcPayments;
}
