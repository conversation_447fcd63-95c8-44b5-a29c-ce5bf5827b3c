package com.everee.api.employee.contributiondeduction;

import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContributionDeductionGarnishmentDetailsParams {
  private String agencyName;
  private String agencyAddress;
  private String agencyAddress2;
  private String agencyCity;
  private String agencyState;
  private String agencyZip;

  @Deprecated(forRemoval = true)
  private String agencyIds;

  private String orderId;
  private String caseId;
  private String remittanceId;

  private boolean receivedNotice = false;
  private LocalDate receivedDate;
  private LocalDate updatedReceivedDate;
  private Long garnishmentOrderCompanyDocumentId;
  private Boolean deleteGarnishmentOrderFile;
  private String garnishmentOrderUploadFileBytes;
  private String garnishmentOrderUploadFileName;

  private Boolean eeNotified;
  private LocalDate eeNotifiedDate;
  private Long eeNotifiedEmployeeDocumentId;
  private Boolean deleteEeNotifiedFile;
  private String eeNotifiedUploadFileBytes;
  private String eeNotifiedUploadFileName;

  private Boolean hasArrears;
  private Boolean hasDependentObligation;
  private Boolean hasMedicalObligation;
  private String issuingCounty;
  private String issuingCourt;
  private String issuingState;
  private String causeNumber;
}
