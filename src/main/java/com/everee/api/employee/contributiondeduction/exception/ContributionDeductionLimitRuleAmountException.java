package com.everee.api.employee.contributiondeduction.exception;


import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ContributionDeductionLimitRuleAmountException extends LocalizedRuntimeException {
  public ContributionDeductionLimitRuleAmountException() {
    super("contributiondeduction.ContributionDeductionLimitRuleAmountException.message");
  }
}
