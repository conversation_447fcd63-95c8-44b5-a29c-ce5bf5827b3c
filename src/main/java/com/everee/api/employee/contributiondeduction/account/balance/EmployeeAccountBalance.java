package com.everee.api.employee.contributiondeduction.account.balance;

import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.money.Money;
import com.everee.api.util.DisplayFullName;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Immutable
public class EmployeeAccountBalance implements DisplayFullName {

  @NotNull private Long companyId;
  @NotNull private String companyName;

  @NotNull private Long employeeId;
  @NotNull private String firstName;
  @NotNull private String lastName;
  private String middleName;

  @Id @NotNull private String employeeContributionDeductionGuid;

  @NotNull
  @Enumerated(EnumType.STRING)
  private EmployeeContributionDeductionType employeeContributionDeductionType;

  private String employeeContributionDeductionName;

  @NotNull private Money liabilityAmount = Money.ZERO;
  @NotNull private Money assetAmount = Money.ZERO;
  @NotNull private Money fundingAmount = Money.ZERO;
  @NotNull private Money balance = Money.ZERO;

  @NotNull private Money pretaxLiabilityAmount = Money.ZERO;
  @NotNull private Money pretaxAssetAmount = Money.ZERO;
  @NotNull private Money pretaxBalance = Money.ZERO;

  @NotNull private Money pendingLiabilityAmount = Money.ZERO;
  @NotNull private Money pendingAssetAmount = Money.ZERO;
  @NotNull private Money pendingFundingAmount = Money.ZERO;
  @NotNull private Money pendingBalance = Money.ZERO;
}
