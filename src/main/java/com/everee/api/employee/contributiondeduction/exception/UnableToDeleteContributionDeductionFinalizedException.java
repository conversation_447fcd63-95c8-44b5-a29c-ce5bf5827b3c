package com.everee.api.employee.contributiondeduction.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.payment.Payment;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.time.format.DateTimeFormatter;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class UnableToDeleteContributionDeductionFinalizedException extends LocalizedRuntimeException {

  public UnableToDeleteContributionDeductionFinalizedException(Payment payment) {
    super("contributiondeduction.UnableToDeleteContributionDeductionFinalizedException.message",
      DateTimeFormatter.ofPattern(LocalizedString.of("contributiondeduction.UnableToDeleteContributionDeductionFinalizedException.dateFormat")
        .getLocalizedValue()).format(payment.getPayDate()));
  }
}
