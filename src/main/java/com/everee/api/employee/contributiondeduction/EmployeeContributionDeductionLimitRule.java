package com.everee.api.employee.contributiondeduction;

import com.everee.api.money.Money;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.math.BigDecimal;

@Data
@Embeddable
@Accessors(chain = true)
public class EmployeeContributionDeductionLimitRule {
  @Enumerated(EnumType.STRING)
  private EmployeeContributionDeductionLimitRuleType limitRuleType;
  private Money limitRuleAmount;
  private BigDecimal limitRulePercent;

  public ContributionDeductionLimitRuleParams toParams() {
    return EmployeeContributionDeductionLimitRuleMapper.MAPPER.toParams(this);
  }
}
