package com.everee.api.employee.contributiondeduction.account.balance;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import io.swagger.annotations.ApiParam;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@RequestParams
public class EmployeeTotalAccountBalanceLookup implements Lookup {
  @ApiParam(name = "company-id", allowMultiple = true)
  private Set<Long> companyIds;

  @ApiParam(name = "worker-id", allowMultiple = true)
  private Set<String> workerIds;

  @ApiParam(name = "has-balance")
  private Boolean hasBalance;
}
