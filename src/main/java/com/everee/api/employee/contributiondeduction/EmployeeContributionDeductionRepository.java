package com.everee.api.employee.contributiondeduction;

import static org.hibernate.jpa.QueryHints.HINT_FETCH_SIZE;

import com.everee.api.company.DetailedCompany;
import com.everee.api.config.jpa.RefreshingSave;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import javax.annotation.Nullable;
import javax.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EmployeeContributionDeductionRepository
    extends JpaRepository<EmployeeContributionDeduction, Long>,
        RefreshingSave<EmployeeContributionDeduction> {

  List<EmployeeContributionDeduction> findByEmployeeId(Long employeeId);

  List<EmployeeContributionDeduction> findAllByEmployeeIdAndTypeGuid(
      Long employeeId, String typeGuid);

  List<EmployeeContributionDeduction> findAllByEmployeeIdAndType(
    Long employeeId, EmployeeContributionDeductionType type);

  @Query(
      "select"
          + "   e.displayFullName as workerDisplayFullName,"
          + "   e.lastName as workerLastName,"
          + "   e.firstName as workerFirstName,"
          + "   ecd.type as type,"
          + "   ecd.name as name,"
          + "   ecd.amountType as amountType,"
          + "   ecd.fixedMethod as fixedMethod,"
          + "   ecd.amountEE as amountEe,"
          + "   ecd.amountER as amountEr,"
          + "   ecd.percentEE as percentEe,"
          + "   ecd.percentER as percentEr,"
          + "   ecd.startDate as startDate,"
          + "   ecd.endDate as endDate,"
          + "   e.workerId as workerId,"
          + "   e.externalWorkerId as externalWorkerId,"
          + "   e.id as employeeId"
          + " from EmployeeContributionDeduction ecd"
          + " join CoreEmployee e on e.id = ecd.employeeId"
          + " where e.companyId = :companyId"
          + "   and e.startDate <= :workerActiveDate and coalesce(e.endDate, 'infinity') >= :workerActiveDate"
          + "   and ecd.startDate <= :settingEndDate and coalesce(ecd.endDate, 'infinity') >= :settingStartDate"
          + " order by e.displayFullName asc, ecd.type asc, ecd.startDate desc")
  @QueryHints({@QueryHint(name = HINT_FETCH_SIZE, value = "250")})
  Stream<EmployeeContributionDeductionExportItem> streamEmployeeContributionDeductionExportItems(
      @Param("companyId") Long companyId,
      @Param("workerActiveDate") LocalDate workerActiveDate,
      @Param("settingStartDate") LocalDate settingStartDate,
      @Param("settingEndDate") LocalDate settingEndDate);

  @Query(
      "SELECT"
          + "   c"
          + " FROM EmployeeContributionDeduction ecd"
          + " JOIN CoreEmployee e on e.id = ecd.employeeId"
          + " JOIN DetailedCompany c on c.id = e.companyId"
          + " JOIN Payment p on p.employeeId = e.id"
          + " WHERE ecd.startDate <= :endDate AND coalesce(ecd.endDate, 'infinity') >= :startDate"
          + "   AND p.payDate between :startDate AND :endDate"
          + "   AND p.status IN ('APPROVED', 'SUBMITTED', 'PAID', 'IMPORTED','PREPARED_FOR_FUNDING_REQUEST', 'FUNDING_REQUESTED','FUNDED')"
          + "   AND ecd.carryOverBalances = true"
          + "   AND ecd.fixedMethod = 'DISTRIBUTED'"
          + " GROUP BY c")
  Set<DetailedCompany> getCompaniesWithCarryOverEmployeeContributionDeductions(
      @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

  @Query(
      nativeQuery = true,
      value =
          "  SELECT"
              + "        liabilitytotals.ecdguid as ecdGuid,"
              + "        ecd.type as ecdType,"
              + "        liabilitytotals.employeeid as employeeId,"
              + "        liabilitytotals.companyid as companyId,"
              + "        coalesce(epf.payPeriodType, cpf.payPeriodType) AS payPeriodType, "
              + "        coalesce(ep.enddate, cp.enddate) as periodEndDate, "
              + "        liabilitytotals.totalliability as actualLiability,"
              + "        liabilitytotals.totalpretaxliability as actualPretaxLiability,"
              + "        ecd.amountee AS expectedLiability "
              + "  FROM"
              + "        employeecontributiondeduction ecd"
              + "       JOIN"
              + "        ("
              + "          SELECT "
              + "             employeecontributiondeductionguid as ecdguid, "
              + "             eat.employeeid, "
              + "             eat.companyid, "
              + "             sum(liabilityamount) AS totalliability,"
              + "             sum(pretaxliabilityamount) AS totalpretaxliability"
              + "          FROM employeeaccounttransaction eat"
              + "          LEFT JOIN payment p ON p.id=eat.paymentid AND p.status IN ('APPROVED', 'SUBMITTED', 'PAID', 'IMPORTED','PREPARED_FOR_FUNDING_REQUEST', 'FUNDING_REQUESTED','FUNDED')"
              + "          JOIN company c ON c.id=eat.companyid"
              + "          WHERE "
              + "             coalesce(CASE WHEN c.contributionDeductionContextDateType='FOR_DATE' "
              + "                          THEN p.fordate "
              + "                          ELSE p.paydate END, "
              + "                     eat.nonpaymenttransactiondate) BETWEEN :startDate AND :endDate"
              + "             AND eat.companyid= :companyId"
              + "             AND CASE WHEN CAST(CAST(:employeeId AS TEXT) AS BIGINT) is null THEN eat.employeeId "
              + "                      ELSE CAST(CAST(:employeeId AS TEXT) AS BIGINT) END = eat.employeeId"
              + "          GROUP BY employeecontributiondeductionguid, eat.employeeid, eat.companyid"
              + "        ) liabilitytotals "
              + "                       ON liabilitytotals.ecdguid=ecd.typeguid "
              + "                       AND ecd.employeeid=liabilitytotals.employeeid"
              + "        LEFT JOIN payperiodpreference epf "
              + "                       ON epf.employeeid=ecd.employeeid "
              + "                       AND epf.startdate <= :endDate "
              + "                       AND coalesce(epf.enddate, 'infinity') >= :endDate"
              + "        LEFT JOIN payperiod ep "
              + "                       ON epf.employeeId=ep.employeeId "
              + "                       AND epf.companyId <= ep.companyId "
              + "                       AND epf.id = ep.payPeriodPreferenceId "
              + "                       AND ep.startDate <= :endDate "
              + "                       AND coalesce(ep.endDate, 'infinity') >= :endDate"
              + "        JOIN payperiodpreference cpf "
              + "                       ON cpf.companyid=liabilitytotals.companyid "
              + "                       AND cpf.startdate <= :endDate "
              + "                       AND coalesce(cpf.enddate, 'infinity') >= :endDate "
              + "                       AND cpf.employeeid is null"
              + "       JOIN payperiod cp "
              + "                       ON cpf.companyId <= cp.companyId "
              + "                       AND cpf.id = cp.payPeriodPreferenceId "
              + "                       AND cp.startDate <= :endDate "
              + "                       AND coalesce(cp.endDate, 'infinity') >= :endDate"
              + " WHERE ecd.fixedMethod='DISTRIBUTED'"
              + "       AND ecd.carryOverBalances = true"
              + "       AND ecd.startDate <= :endDate AND coalesce(ecd.enddate, 'infinity') >= :endDate")
  List<EmployeeContributionDeductionMonthTotal> getDistributedContributionDeductionsTotalsForRange(
      @Param("startDate") LocalDate startDate,
      @Param("endDate") LocalDate endDate,
      @Param("companyId") Long companyId,
      @Nullable @Param("employeeId") Long employeeId);

  @Query(
      "select ecd from EmployeeContributionDeduction ecd "
          + "where ecd.companyId is null and ecd.employeeId is null and ecd.type = 'PTPC_FEE' "
          + "and ecd.startDate <= :forDate and coalesce(ecd.endDate, 'infinity') >= :forDate")
  EmployeeContributionDeduction findSystemPtpcFeeECD(@Param("forDate") LocalDate forDate);

  @Query(
    "SELECT DISTINCT ecd " +
      "FROM EmployeeContributionDeduction ecd " +
      "LEFT JOIN EmployeeContributionDeductionAmountDetail ecdad ON ecdad.employeeContributionDeductionId = ecd.id " +
      "WHERE ecdad.importId = :importId"
  )
  List<EmployeeContributionDeduction> findAllWithAmountDetailsForImportId(@Param("importId") String importId);
}
