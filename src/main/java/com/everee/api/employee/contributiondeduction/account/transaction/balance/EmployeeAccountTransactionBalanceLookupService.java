package com.everee.api.employee.contributiondeduction.account.transaction.balance;

import static com.everee.api.query.where.Where.by;
import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.query.Query;
import com.everee.api.util.BooleanApiParam;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EmployeeAccountTransactionBalanceLookupService
    implements LookupService<
        EmployeeAccountTransactionBalance,
        EmployeeAccountTransactionBalanceLookup,
        Query<EmployeeAccountTransactionBalance>> {

  private final EntityManager entityManager;

  @Override
  public void configureQuery(
      EmployeeAccountTransactionBalanceLookup lookup,
      Query<EmployeeAccountTransactionBalance> query) {
    query.where(property(EmployeeAccountTransactionBalance_.ID).in(lookup.getIds()));
    query.where(
        property(EmployeeAccountTransactionBalance_.EMPLOYEE_ID).in(lookup.getEmployeeIds()));
    query.where(property(EmployeeAccountTransactionBalance_.WORKER_ID).in(lookup.getWorkerIds()));
    query.where(
        property(EmployeeAccountTransactionBalance_.EMPLOYEE_ACCOUNT_TRANSACTION_IDS)
            .containsAll(lookup.getTransactionIds()));
    if (lookup.getMinTransactionDate() != null) {
      query.where(
          property(EmployeeAccountTransactionBalance_.TRANSACTION_DATE)
              .greaterThanOrEqual(lookup.getMinTransactionDate()));
    }
    if (lookup.getMaxTransactionDate() != null) {
      query.where(
          property(EmployeeAccountTransactionBalance_.TRANSACTION_DATE)
              .lessThanOrEqual(lookup.getMaxTransactionDate()));
    }
    query.where(
        by(
            presenceAsBooleanApiParam(
                BooleanApiParam.from(lookup.getFinalized()),
                EmployeeAccountTransactionBalance_.FINALIZED)));
  }

  @Override
  public Query<EmployeeAccountTransactionBalance> createQuery() {
    return new Query<>(entityManager, EmployeeAccountTransactionBalance.class);
  }
}
