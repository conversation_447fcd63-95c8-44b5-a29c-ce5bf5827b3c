package com.everee.api.employee.contributiondeduction.garnishment;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GarnishmentConfigurationMapper {
  GarnishmentConfigurationMapper MAPPER = Mappers.getMapper(GarnishmentConfigurationMapper.class);

  GarnishmentConfiguration toUnifiedDto(GarnishmentDefaultConfiguration defaultConfiguration);
  GarnishmentConfiguration toUnifiedDto(GarnishmentStateConfiguration stateConfiguration);
}
