package com.everee.api.employee.contributiondeduction.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
public class UnexpectedContributionDeductionLimitTypeException extends LocalizedRuntimeException {

  public UnexpectedContributionDeductionLimitTypeException() {
    super("contributiondeduction.UnexpectedContributionDeductionLimitTypeException.message");
  }
}
