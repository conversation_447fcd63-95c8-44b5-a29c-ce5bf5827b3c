package com.everee.api.employee.event;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.Employee;
import java.time.LocalDate;
import lombok.Getter;

@Getter
public abstract class WorkerUpdatedEvent extends WorkerEvent {
  private final Employee previousEmployeeState;
  private final LocalDate positionChangeEffectiveDate;

  public WorkerUpdatedEvent(DetailedEmployee employee) {
    this(employee, employee);
  }

  public WorkerUpdatedEvent(DetailedEmployee employee, Employee previousEmployeeState) {
    super(employee);
    this.previousEmployeeState = previousEmployeeState;
    this.positionChangeEffectiveDate = null;
  }

  public WorkerUpdatedEvent(DetailedEmployee employee, LocalDate positionChangeEffectiveDate) {
    super(employee);
    this.previousEmployeeState = null;
    this.positionChangeEffectiveDate = positionChangeEffectiveDate;
  }
}
