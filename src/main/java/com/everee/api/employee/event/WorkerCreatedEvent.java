package com.everee.api.employee.event;

import com.everee.api.employee.DetailedEmployee;
import lombok.Getter;

public class WorkerCreatedEvent extends WorkerEvent {

  @Getter boolean sendOnboardingInvitationImmediately;

  public WorkerCreatedEvent(
      DetailedEmployee employee, boolean sendOnboardingInvitationImmediately) {
    super(employee);
    this.sendOnboardingInvitationImmediately = sendOnboardingInvitationImmediately;
  }

  @Override
  public String getActionName() {
    return "created";
  }
}
