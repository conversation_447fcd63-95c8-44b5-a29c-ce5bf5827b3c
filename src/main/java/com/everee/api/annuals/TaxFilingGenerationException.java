package com.everee.api.annuals;

import com.everee.api.company.DetailedCompany;
import com.everee.api.user.User;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxFilingGenerationException extends Exception {
  User user;
  DetailedCompany company;

  public TaxFilingGenerationException(Exception cause) {
    super(cause);
  }

  public TaxFilingGenerationException(User user, DetailedCompany company, Exception cause) {
    super(cause);
    this.user = user;
    this.company = company;
  }

  public TaxFilingGenerationException(User user, DetailedCompany company, String message) {
    super(message);
    this.user = user;
    this.company = company;
  }
}
