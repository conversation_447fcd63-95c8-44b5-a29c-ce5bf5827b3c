package com.everee.api.annuals.federal;

import static com.everee.api.util.ObjectUtils.optionally;

import com.everee.api.money.Money;
import com.everee.api.tax.state.State;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Year;
import java.util.Optional;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Transient;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Immutable
public class AnnualF940ScheduleAState {
  @JsonUnwrapped @EmbeddedId private AnnualF940ScheduleAStateId id;
  private Money totalPaymentsToAllEmployees;
  private Money taxAmount;
  private Money taxSubjectWages;
  private BigDecimal creditReductionRate;

  public Money getCreditReductionAmount() {
    return Optional.ofNullable(getCreditReductionRate())
        .map(rate -> taxSubjectWages.times(rate))
        .orElse(null);
  }

  @Embeddable
  @Data
  public static class AnnualF940ScheduleAStateId implements Serializable {
    private Year year;
    private Long companyId;
    private String recipientStateCode;

    @Transient
    public State getState() {
      return optionally(getRecipientStateCode(), State::fromCode);
    }
  }
}
