package com.everee.api.annuals.federal;

import static com.everee.api.money.Money.zeroOr;
import static com.everee.api.util.ObjectUtils.optionally;

import com.everee.api.annuals.federal.AnnualF940ScheduleAState.AnnualF940ScheduleAStateId;
import com.everee.api.company.CompanyAddress;
import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.pdf.*;
import com.everee.api.quarterlies.Quarter;
import com.everee.api.tax.state.State;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.time.LocalDate;
import java.time.Month;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.*;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Immutable;
import org.springframework.util.StringUtils;

@Data
@Entity
@Table(name = "annualf940")
@Immutable
@PdfFillableForm(
    /* NOTE: The original PDF has field inputs that are "failing" the flatten operation.
    The offending fields topmostSubform[0].Page2[0].f1_1[0] and
    topmostSubform[0].Page3[0].EIN_ReadOrder[0].f1_1[0] have been replaced in the
    f940_2023_modified.pdf version of the file with Text1 and Text2 using pdfescape.com.  */
    value = "taxforms/f940_2023_modified.pdf")
@Accessors(chain = true)
@Slf4j
public class AnnualF940_2023 implements AnnualF940 {
  @JsonUnwrapped @EmbeddedId private Annual940Id id;

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_14[0]",
      cents = "topmostSubform[0].Page1[0].f1_15[0]")
  private Money totalGrossEarningsToAllEmployees;

  // This is all FUTA subject payments to all employees
  @Column(name = "totalPaymentsToAllEmployees")
  private Money totalFutaSubjectPaymentsToAllEmployees;

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_16[0]",
      cents = "topmostSubform[0].Page1[0].f1_17[0]")
  public Money getPaymentsExemptFromFUTATax() {
    return totalGrossEarningsToAllEmployees.minus(totalFutaSubjectPaymentsToAllEmployees);
  }

  private Boolean hasFringeBenefits;
  private Boolean hasLifeInsurance;
  private Boolean hasRetirement;
  private Boolean hasDependentCare;
  private Boolean hasOther;

  @PdfString("topmostSubform[0].Page1[0].Checkboxes4a-b[0].c1_7[0]")
  public Boolean getCheckFringeBenefits() {
    return Boolean.TRUE.equals(hasFringeBenefits) ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].Checkboxes4a-b[0].c1_8[0]")
  public Boolean getCheckLifeInsurance() {
    return Boolean.TRUE.equals(hasLifeInsurance) ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].Checkboxes4c-d[0].c1_9[0]")
  public Boolean getCheckRetirement() {
    return Boolean.TRUE.equals(hasRetirement) ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].Checkboxes4c-d[0].c1_10[0]")
  public Boolean getCheckDependentCare() {
    return Boolean.TRUE.equals(hasDependentCare) ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].c1_11[0]")
  public Boolean getCheckOther() {
    return Boolean.TRUE.equals(hasOther)
            || (getPaymentsExemptFromFUTATax().isPlus()
                && !Boolean.TRUE.equals(getCheckFringeBenefits())
                && !Boolean.TRUE.equals(getCheckLifeInsurance())
                && !Boolean.TRUE.equals(getCheckRetirement())
                && !Boolean.TRUE.equals(getCheckDependentCare()))
        ? true
        : null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_18[0]",
      cents = "topmostSubform[0].Page1[0].f1_19[0]")
  private Money totalPaymentsToEachEmployeeInExcessOf7000;

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_20[0]",
      cents = "topmostSubform[0].Page1[0].f1_21[0]")
  private Money question6Subtotal;

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_22[0]",
      cents = "topmostSubform[0].Page1[0].f1_23[0]")
  private Money question7TotalTaxableFutaWages;

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_24[0]",
      cents = "topmostSubform[0].Page1[0].f1_25[0]")
  private Money question8FutaTaxBeforeAdjustments;

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_32[0]",
      cents = "topmostSubform[0].Page1[0].f1_33[0]")
  public Money getQuestion12() {
    return getQuestion8FutaTaxBeforeAdjustments()
        .plus(Money.zeroOr(getCreditReductionTotalFromScheduleA()));
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_34[0]",
      cents = "topmostSubform[0].Page1[0].f1_35[0]")
  public Money getFutaDeposits() {
    return getQuestion16Quarters().values().stream()
        .map(AnnualF940Quarter::getFutaDeposit)
        .filter(Objects::nonNull)
        .reduce(Money::sum)
        .orElse(Money.ZERO);
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_36[0]",
      cents = "topmostSubform[0].Page1[0].f1_37[0]")
  public Money getBalanceDue() {
    Money question8 = zeroOr(getQuestion8FutaTaxBeforeAdjustments());
    Money deposits = zeroOr(getFutaDeposits());
    if (question8.gt(deposits)) {
      return question8.minus(deposits);
    }
    return Money.ZERO;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_38[0]",
      cents = "topmostSubform[0].Page1[0].f1_39[0]")
  public Money getOverpayment() {
    Money question8 = zeroOr(getQuestion8FutaTaxBeforeAdjustments());
    Money deposits = zeroOr(getFutaDeposits());
    if (question8.lt(deposits)) {
      return deposits.minus(question8);
    }
    return null;
  }

  @ToString.Exclude
  @JsonIgnore
  @OneToMany(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        updatable = false,
        insertable = false,
        name = "companyId",
        referencedColumnName = "companyId"),
    @JoinColumn(updatable = false, insertable = false, name = "year", referencedColumnName = "year")
  })
  @MapKey(name = "quarter")
  private Map<Quarter, AnnualF940Quarter> question16Quarters;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @OneToMany(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        updatable = false,
        insertable = false,
        name = "companyId",
        referencedColumnName = "companyId"),
    @JoinColumn(updatable = false, insertable = false, name = "year", referencedColumnName = "year")
  })
  private Set<AnnualF940ScheduleAState> f940ScheduleStates;

  @PdfTwoPartEin({
    "topmostSubform[0].Page1[0].EntityArea[0].f1_1[0]",
    "topmostSubform[0].Page1[0].EntityArea[0].f1_2[0]"
  })
  public String getFederalEin() {
    return getCompany().getFederalEin();
  }

  // The PDF has been modified to replace topmostSubform[0].Page2[0].f1_1[0] with Text1
  @PdfTwoPartEin({"Text1", "topmostSubform[0].Page2[0].f1_2[0]"})
  public String getFederalEinPageTwo() {
    return getCompany().getFederalEin();
  }

  @PdfString({
    "topmostSubform[0].Page1[0].EntityArea[0].f1_3[0]",
    "topmostSubform[0].Page2[0].f1_3[0]"
  })
  public String getLegalEntityName() {
    return getCompany().getLegalEntityName();
  }

  @PdfString("topmostSubform[0].Page1[0].EntityArea[0].f1_4[0]")
  public String getDisplayName() {
    return getCompany().getDisplayName();
  }

  @PdfString("topmostSubform[0].Page1[0].EntityArea[0].f1_5[0]")
  public String getCompanyAddressLine1AndLine2() {
    return getCompanyAddress()
        .map(
            companyAddress ->
                Stream.of(companyAddress.getLine1(), companyAddress.getLine2())
                    .filter(StringUtils::hasText)
                    .collect(Collectors.joining(" ")))
        .orElse(null);
  }

  @PdfString("topmostSubform[0].Page1[0].EntityArea[0].f1_6[0]")
  public String getCompanyAddressCity() {
    return getCompanyAddress().map(CompanyAddress::getCity).orElse(null);
  }

  @PdfString("topmostSubform[0].Page1[0].EntityArea[0].f1_7[0]")
  public State getCompanyAddressState() {
    return getCompanyAddress().map(CompanyAddress::getState).orElse(null);
  }

  @PdfString("topmostSubform[0].Page1[0].EntityArea[0].f1_8[0]")
  private String getCompanyAddressZip() {
    return getCompanyAddress().map(CompanyAddress::getPostalCode).orElse(null);
  }

  private Optional<CompanyAddress> getCompanyAddress() {
    return getCompany()
        .getActiveAddress(LocalDate.of(getId().getYear().getValue(), Month.DECEMBER, 31));
  }

  @PdfSplitString({"topmostSubform[0].Page1[0].f1_12[0]", "topmostSubform[0].Page1[0].f1_13[0]"})
  public State getUnemploymentTaxInOnlyOneState() {
    return Optional.of(getF940ScheduleStates())
        .filter(f940ScheduleStates -> f940ScheduleStates.size() == 1)
        .map(f940ScheduleStates -> List.copyOf(f940ScheduleStates).get(0))
        .map(AnnualF940ScheduleAState::getId)
        .map(AnnualF940ScheduleAStateId::getState)
        .orElse(null);
  }

  @PdfString("topmostSubform[0].Page1[0].c1_5[0]")
  public Boolean hadToPayUnemploymentTaxInMoreThanOneState() {
    return getF940ScheduleStates().size() >= 2 ? true : null;
  }

  @PdfString("topmostSubform[0].Page1[0].c1_6[0]")
  public Boolean hadToPayUnemploymentTaxInCreditReductionState() {
    var result =
        getF940ScheduleStates().stream()
            .anyMatch(f940ScheduleStates -> f940ScheduleStates.getCreditReductionRate() != null);
    return result ? true : null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page1[0].f1_30[0]",
      cents = "topmostSubform[0].Page1[0].f1_31[0]")
  public Money getCreditReductionTotalFromScheduleA() {
    return getF940ScheduleStates().stream()
      .map(AnnualF940ScheduleAState::getCreditReductionAmount)
      .filter(Objects::nonNull)
        .reduce(Money::plus)
        .orElse(null);
  }

  private boolean showQuestion16() {
    return zeroOr(getQuestion8FutaTaxBeforeAdjustments()).gteq(Money.valueOf("500.00"));
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_1[0]",
      cents = "topmostSubform[0].Page2[0].f2_2[0]")
  public Money getQuestion16Quarter1() {
    if (showQuestion16()) {
      return getQuarterWithRoundingAdjustment(Quarter.Q1);
    }
    return null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_3[0]",
      cents = "topmostSubform[0].Page2[0].f2_4[0]")
  public Money getQuestion16Quarter2() {
    if (showQuestion16()) {
      return getQuarterWithRoundingAdjustment(Quarter.Q2);
    }
    return null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_5[0]",
      cents = "topmostSubform[0].Page2[0].f2_6[0]")
  public Money getQuestion16Quarter3() {
    if (showQuestion16()) {
      return getQuarterWithRoundingAdjustment(Quarter.Q3);
    }
    return null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_7[0]",
      cents = "topmostSubform[0].Page2[0].f2_8[0]")
  public Money getQuestion16Quarter4() {
    if (showQuestion16()) {
      return getQuarterWithRoundingAdjustment(Quarter.Q4);
    }
    return null;
  }

  @PdfMoney(
      dollars = "topmostSubform[0].Page2[0].f2_9[0]",
      cents = "topmostSubform[0].Page2[0].f2_10[0]")
  public Money getQuestion16AdjustedTotal() {
    if (showQuestion16()) {
      return getQuestion12();
    }
    return null;
  }

  private Money getQuestion16Total() {
    return Stream.of(Quarter.values())
        .map(getQuestion16Quarters()::get)
        .filter(Objects::nonNull)
        .map(AnnualF940Quarter::getFutaLiability)
        .reduce(Money::sum)
        .orElse(Money.ZERO);
  }

  /*
  The federal government's math is a piece of work.  Box 12 and Box 17 need to match, but because of rounding
  they don't match.  So instead of just leaving the rounding error, adjust each quarter of box 16
  so that we get to the same value as box 12.  Because we're using MEF, they validate that box 12 and 17
  are equal.
   */
  private Money getQuarterWithRoundingAdjustment(Quarter quarter) {
    if (showQuestion16()) {
      var quartersLiability =
          new HashMap<>(
              Map.of(
                  Quarter.Q1, quarterLiability(Quarter.Q1),
                  Quarter.Q2, quarterLiability(Quarter.Q2),
                  Quarter.Q3, quarterLiability(Quarter.Q3),
                  Quarter.Q4, quarterLiability(Quarter.Q4)));

      if (quartersLiability.get(quarter).isZero()) {
        return Money.ZERO;
      }

      var question12 = zeroOr(getQuestion12());
      var question16 = zeroOr(getQuestion16Total());
      var difference = question12.minus(question16);

      var centsDiff = (long) (difference.getAmount().doubleValue() * 100);

      var adjustmentCent = Money.valueOf(centsDiff < 0 ? "-0.01" : "0.01");

      centsDiff = Math.abs(centsDiff);
      if (centsDiff > 20000) { // if more than $200 difference, we probably need to look at the data
        log.warn(
            "The difference between box 12 and 17 is {} - NOT adjusting each months values!",
            difference);
        return quartersLiability.get(quarter);
      }

      var currentQuarter = Quarter.Q1;
      while (centsDiff > 0) {
        while (quartersLiability.get(currentQuarter).isZero()
            || quartersLiability.get(currentQuarter).plus(adjustmentCent).isZero()) {
          currentQuarter = Quarter.getNextQuarter(currentQuarter);
        }

        quartersLiability.put(
            currentQuarter, quartersLiability.get(currentQuarter).plus(adjustmentCent));

        centsDiff--;
        currentQuarter = Quarter.getNextQuarter(currentQuarter);
      }

      return quartersLiability.get(quarter);
    }
    return Money.ZERO;
  }

  private Money quarterLiability(Quarter q) {
    return zeroOr(optionally(getQuestion16Quarters().get(q), AnnualF940Quarter::getFutaLiability));
  }
}
