package com.everee.api.annuals.federal;

import static com.everee.api.money.Money.zeroOr;

import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.pdf.PdfString;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.util.Optional;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Table(name = "annualfw3state")
@Immutable
public class AnnualFW3State_2023 implements AnnualFW3State {

  @JsonUnwrapped @EmbeddedId private AnnualFW3StateId id;

  private Long countOfEmployees;

  private Money wagesTipsAndOtherCompensation;

  private Money federalIncomeTaxWithheld;

  private Money socialSecurityWages;

  private Money socialSecurityTaxWithheld;

  private Money medicareWagesAndTips;

  private Money medicareTaxWithheld;

  private Money socialSecurityTips;
  private Money stateWagesTipsEtc;
  private Money stateIncomeTaxWithheld;
  private Money localWagesTipsEtc;
  private Money localIncomeTaxWithheld;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @Transient
  @PdfString("topmostSubform[0].Page1[0].f1_26[0]")
  public Money getStateIncomeTaxWithheldPdf() {
    if (zeroOr(getStateIncomeTaxWithheld()).gt(Money.ZERO)) {
      return stateIncomeTaxWithheld;
    }
    return null;
  }

  @Transient
  @PdfString("topmostSubform[0].Page1[0].f1_28[0]")
  public Money getLocalIncomeTaxWithheldPdf() {
    if (zeroOr(getLocalIncomeTaxWithheld()).gt(Money.ZERO)) {
      return localIncomeTaxWithheld;
    }
    return null;
  }

  @Transient
  @PdfString("topmostSubform[0].Page1[0].f1_27[0]")
  public Money getLocalWagesTipsEtcPdf() {
    if (zeroOr(getLocalIncomeTaxWithheld()).gt(Money.ZERO)) {
      return localWagesTipsEtc;
    }
    return null;
  }

  @Transient
  @PdfString("topmostSubform[0].Page1[0].f1_25[0]")
  public Money getStateWagesTipsEtcPdf() {
    if (zeroOr(getStateIncomeTaxWithheld()).gt(Money.ZERO)) {
      return stateWagesTipsEtc;
    }
    return null;
  }

  @Transient
  @PdfString("topmostSubform[0].Page1[0].f1_24[0]")
  public String getStateTaxAccountNumber() {
    return Optional.ofNullable(getCompany())
        .flatMap(company -> company.getCompanySitInfo(getId().getRecipientState()))
        .map(CompanyTaxJurisdiction::getAccountNumber)
        .orElse(null);
  }
}
