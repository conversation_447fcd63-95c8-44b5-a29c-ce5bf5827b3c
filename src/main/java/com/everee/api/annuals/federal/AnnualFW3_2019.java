package com.everee.api.annuals.federal;

import static com.everee.api.money.Money.zeroOr;
import static com.everee.api.util.ObjectUtils.optionally;

import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.pdf.PdfFillableForm;
import com.everee.api.pdf.PdfString;
import com.everee.api.pdf.PdfTaxpayerIdentifier;
import com.everee.api.pdf.PdfTaxpayerIdentifier.Type;
import com.everee.api.pdf.PdfUnwrapped;
import com.everee.api.tax.state.State;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.time.LocalDate;
import java.time.Month;
import java.util.Optional;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Table(name = "annualfw3")
@Immutable
@PdfFillableForm("taxforms/fw3_2019.pdf")
public class AnnualFW3_2019 implements AnnualFW3 {
  @Transient
  @PdfString("topmostSubform[0].Page1[0].BoxB\\.Payer[0].b941[0].c1_1[0]")
  private final boolean payerType941 = true;

  @Transient
  @PdfString("topmostSubform[0].Page1[0].BoxB\\.KindOfEmployer[0].firstset[0].None[0].c1_2[0]")
  private final boolean employerTypeNone = true;

  @PdfUnwrapped @JsonUnwrapped @EmbeddedId private AnnualFW3Id id;

  @PdfString("topmostSubform[0].Page1[0].BoxesC-h[0].f1_1[0]")
  private Long countOfEmployees;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_6[0]")
  private Money wagesTipsAndOtherCompensation;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_7[0]")
  private Money federalIncomeTaxWithheld;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_8[0]")
  private Money socialSecurityWages;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_9[0]")
  private Money socialSecurityTaxWithheld;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_10[0]")
  private Money medicareWagesAndTips;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_11[0]")
  private Money medicareTaxWithheld;

  @PdfString("topmostSubform[0].Page1[0].Box1-14[0].f1_12[0]")
  private Money socialSecurityTips;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @PdfUnwrapped @Transient private AnnualFW3State_2019 state;

  @PdfTaxpayerIdentifier(
      fieldNames = "topmostSubform[0].Page1[0].BoxesC-h[0].f1_3[0]",
      type = Type.EIN)
  public String getFederalEin() {
    return optionally(getCompany(), DetailedCompany::getFederalEin);
  }

  @PdfString("topmostSubform[0].Page1[0].BoxesC-h[0].f1_4[0]")
  public String getLegalEntityName() {
    return optionally(getCompany(), DetailedCompany::getLegalEntityName);
  }

  @PdfString("topmostSubform[0].Page1[0].BoxesC-h[0].f1_5[0]")
  public String getCompanyAddress() {
    return getCompany()
        .getActiveAddress(LocalDate.of(getId().getYear().getValue(), Month.DECEMBER, 31))
        .map(
            companyAddress ->
                String.join(" ", companyAddress.getLine1(), companyAddress.getLine2()).trim()
                    + "\n"
                    + String.join(
                        " ",
                        companyAddress.getCity(),
                        companyAddress.getState().name(),
                        companyAddress.getPostalCode()))
        .orElse(null);
  }

  @PdfString("topmostSubform[0].Page1[0].f1_26[0]")
  public String getCompanyPhone() {
    return optionally(getCompany(), DetailedCompany::getPhone);
  }

  @PdfString("topmostSubform[0].Page1[0].f1_28[0]")
  public String getCompanyEmail() {
    return optionally(getCompany(), DetailedCompany::getEmail);
  }

  @PdfString("topmostSubform[0].Page1[0].f1_25[0]")
  public String getCompanyContactPerson() {
    return Optional.ofNullable(getCompany())
        .map(company -> company.getFirstName() + ' ' + company.getLastName())
        .map(String::trim)
        .orElse(null);
  }

  @Transient
  public State getRecipientState() {
    return optionally(getState(), state -> state.getId().getRecipientState());
  }

  @PdfString("topmostSubform[0].Page1[0].f1_19[0]")
  @Transient
  public State getRecipientStatePdf() {
    return Optional.ofNullable(getState())
        .map(state -> state.getId().getRecipientState())
        .filter(ignored -> zeroOr(getState().getStateIncomeTaxWithheld()).gt(Money.ZERO))
        .orElse(null);
  }

  @Override
  public AnnualFW3_2019 clone() {
    try {
      return (AnnualFW3_2019) super.clone();
    } catch (CloneNotSupportedException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void setState(AnnualFW3State annualFW3State) {
    this.state = (AnnualFW3State_2019) annualFW3State;
  }
}
