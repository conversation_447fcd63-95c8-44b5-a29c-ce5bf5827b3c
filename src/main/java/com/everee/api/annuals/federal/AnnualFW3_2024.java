package com.everee.api.annuals.federal;

import static com.everee.api.money.Money.zeroOr;
import static com.everee.api.util.ObjectUtils.optionally;

import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.pdf.PdfFillableForm;
import com.everee.api.pdf.PdfString;
import com.everee.api.pdf.PdfTaxpayerIdentifier;
import com.everee.api.pdf.PdfTaxpayerIdentifier.Type;
import com.everee.api.pdf.PdfUnwrapped;
import com.everee.api.tax.state.State;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.time.LocalDate;
import java.time.Month;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Table(name = "annualfw3")
@Immutable
@PdfFillableForm(value = "taxforms/fw3_2024.pdf", setUntagged = true)
public class AnnualFW3_2024 implements AnnualFW3 {

  @Transient
  @PdfString("topmostSubform[0].Page1[0].bKind_ReadOrder[0].b941[0].c1_1[0]")
  private final boolean payerType941 = true;

  @Transient
  @PdfString(
      "topmostSubform[0].Page1[0].bKindOfEmployer_ReadOrder[0].EmployerCheckboxes[0].None[0].c1_2[0]")
  private final boolean employerTypeNone = true;

  @PdfUnwrapped @JsonUnwrapped @EmbeddedId private AnnualFW3Id id;

  @PdfString("topmostSubform[0].Page1[0].BoxesC-H[0].f1_02[0]")
  private Long countOfEmployees;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_08[0]")
  private Money wagesTipsAndOtherCompensation;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_09[0]")
  private Money federalIncomeTaxWithheld;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_10[0]")
  private Money socialSecurityWages;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_11[0]")
  private Money socialSecurityTaxWithheld;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_12[0]")
  private Money medicareWagesAndTips;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_13[0]")
  private Money medicareTaxWithheld;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].f1_14[0]")
  private Money socialSecurityTips;

  @PdfString("topmostSubform[0].Page1[0].Boxes1-14[0].Line12_ReadOrder[0].f1_19[0]")
  private Money deferredCompensation;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @PdfUnwrapped @Transient private AnnualFW3State_2024 state;

  @PdfTaxpayerIdentifier(
      fieldNames = "topmostSubform[0].Page1[0].BoxesC-H[0].f1_04[0]",
      type = Type.EIN)
  public String getFederalEin() {
    return optionally(getCompany(), DetailedCompany::getFederalEin);
  }

  @PdfString("topmostSubform[0].Page1[0].BoxesC-H[0].f1_05[0]")
  public String getLegalEntityName() {
    return optionally(getCompany(), DetailedCompany::getLegalEntityName);
  }

  @PdfString("topmostSubform[0].Page1[0].BoxesC-H[0].f1_06[0]")
  public String getCompanyAddress() {
    return getCompany()
        .getActiveAddress(LocalDate.of(getId().getYear().getValue(), Month.DECEMBER, 31))
        .map(
            companyAddress ->
                List.of(
                            companyAddress.getLine1(),
                            Optional.ofNullable(companyAddress.getLine2()).orElse(""))
                        .stream()
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(" "))
                        .trim()
                    + "\n"
                    + String.join(
                        " ",
                        companyAddress.getCity(),
                        companyAddress.getState().name(),
                        companyAddress.getPostalCode()))
        .orElse(null);
  }

  @PdfString("topmostSubform[0].Page1[0].f1_30[0]")
  public String getCompanyPhone() {
    return optionally(getCompany(), DetailedCompany::getPhone);
  }

  @PdfString("topmostSubform[0].Page1[0].f1_32[0]")
  public String getCompanyEmail() {
    return optionally(getCompany(), DetailedCompany::getEmail);
  }

  @PdfString("topmostSubform[0].Page1[0].f1_29[0]")
  public String getCompanyContactPerson() {
    return Optional.ofNullable(getCompany())
        .map(company -> company.getFirstName() + ' ' + company.getLastName())
        .map(String::trim)
        .orElse(null);
  }

  @Transient
  public State getRecipientState() {
    return optionally(getState(), state -> state.getId().getRecipientState());
  }

  @PdfString("topmostSubform[0].Page1[0].f1_23[0]")
  @Transient
  public String getRecipientStatePdf() {
    return Optional.ofNullable(getState())
        .map(state -> state.getId().getRecipientState())
        .filter(ignored -> zeroOr(getState().getStateIncomeTaxWithheld()).gt(Money.ZERO))
        .map(Objects::toString)
        .orElse("X");
  }

  @Override
  public AnnualFW3_2024 clone() {
    try {
      return (AnnualFW3_2024) super.clone();
    } catch (CloneNotSupportedException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void setState(AnnualFW3State annualFW3State) {
    this.state = (AnnualFW3State_2024) annualFW3State;
  }

  @Override
  public void setCountOfStates(Long countOfStates) {
    this.countOfStates = countOfStates;
  }

  @Transient private Long countOfStates;
}
