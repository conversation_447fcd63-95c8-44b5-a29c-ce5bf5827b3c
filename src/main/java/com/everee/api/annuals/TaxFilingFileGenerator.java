package com.everee.api.annuals;

import static com.everee.api.util.TaxFilingStringUtils.normalize;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.employee.deliverypreference.EmployeeTaxDocumentDeliveryPreferenceService;
import com.everee.api.featureflag.FeatureFlagService;
import com.everee.api.model.EmploymentType;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.payeetype.PayeeTypeRepository;
import com.everee.api.tax.filing.folders.TaxFilingCompanyFileService;
import com.everee.api.user.DetailedUserRepository;
import com.everee.api.user.User;
import java.io.ByteArrayOutputStream;
import java.time.Year;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public abstract class TaxFilingFileGenerator {
  @Autowired private CompanyService companyService;
  @Autowired private DetailedEmployeeRepository detailedEmployeeRepository;
  @Autowired private PayeeTypeRepository payeeTypeRepository;
  @Autowired private DetailedUserRepository detailedUserRepository;
  @Autowired GeneratedFilingStorageService generatedFilingStorageService;
  @Autowired public TaxFilingCompanyFileService taxFilingCompanyFileService;
  @Autowired EmployeeTaxDocumentDeliveryPreferenceService deliveryPreferenceService;
  @Autowired FeatureFlagService featureFlagService;

  protected static final String GENERATE_PAPER_REQUESTED_BUNDLE = "generatePaperRequestedW2DocumentsBundle";

  protected abstract EmploymentType getEmploymentType();

  protected abstract FilingType getFilingType();

  @Transactional
  public int publishEmployeePdfsForCompany(Long companyId, Long employeeId, Year year) {
    return publishEmployeePdfsForCompany(companyId, employeeId, year, getFilingType());
  }

  protected int publishEmployeePdfsForCompany(
      Long companyId, Long employeeId, Year year, FilingType filingType) {
    List<Long> employeeIds =
        employeeId == null ? getEligibleEmployeeIds(companyId, year) : List.of(employeeId);
    var result = generatedFilingStorageService.publishEmployeePdfsForCompany(
        companyId, year, employeeIds, filingType.name().toLowerCase() + "_pdf_" + year.toString());

    // LIME-2218 Generate extra bundle for paperless employee
    if (employeeId == null && isGeneratePaperRequestedBundleEnabled()) {
      var paperlessRequestsEmployeeIds = deliveryPreferenceService.getPaperlessEmployeeIds(companyId);
      var paperRequestsEmployeeIds = employeeIds
        .stream()
        .filter(id -> !paperlessRequestsEmployeeIds.contains(id))
        .collect(Collectors.toList());

      if (!paperRequestsEmployeeIds.isEmpty()) {
        generatedFilingStorageService.publishEmployeePdfsForCompany(
          companyId, year, paperRequestsEmployeeIds, filingType.name().toLowerCase() +"_PaperRequested"+ "_pdf_" + year.toString());
      }
    }

    return result;
  }

  protected DetailedCompany getCompany(Long companyId) {
    return companyService.getCompany(companyId);
  }

  protected List<Long> getEligibleUserIds(Long companyId, Year year) {
    return detailedUserRepository.findUserIdsWithPaymentsByCompanyIdAndYear(
        companyId, getEmploymentType(), year.getValue());
  }

  protected List<Long> getPaperlessUserIds(Long companyId) {
    return  deliveryPreferenceService.getPaperlessUserIds(companyId);
  }

  public List<Long> getEligibleEmployeeIds(Long companyId, Year year) {
    return detailedEmployeeRepository.findEmployeeIdsWithPaymentsByCompanyIdAndYear(
        companyId, year.getValue());
  }

  protected void storeCompanyFile(
      FilingGenerationResults results,
      ByteArrayOutputStream outputStream,
      GeneratedFilingDocumentContext context) {
    generatedFilingStorageService.storeCompanyFile(results, outputStream, context);
  }

  protected void storeUserFile(
      AnnualsPdf pdf,
      Year year,
      FilingType filingType,
      TaxFilingPublishContext taxFilingPublishContext) {
    generatedFilingStorageService.storeUserFile(pdf, year, filingType, taxFilingPublishContext);
  }

  protected String getUserDescription(User user, DetailedCompany company) {
    var name = normalize(user.getLastName()) + ", " + normalize(user.getFirstName());
    var optionalPayeeType = Optional.ofNullable(payeeTypeRepository.findByUserId(user.getId()));

    if (optionalPayeeType.isPresent()) {
      var payeeType = optionalPayeeType.get();
      if (payeeType.getTypeName().equals(PayeeTypeName.BUSINESS)
          && StringUtils.isNotEmpty(payeeType.getBusinessName())) {
        name = normalize(payeeType.getBusinessName());
      }

      if (StringUtils.isNotEmpty(payeeType.getDba())) {
        name = name + " - " + normalize(payeeType.getDba());
      }
    }

    return name + " (" + user.getId() + ") - " + normalize(company.getDisplayName());
  }

  protected boolean isGeneratePaperRequestedBundleEnabled() {
    var generatePaperRequestedBundle = featureFlagService.boolValue(GENERATE_PAPER_REQUESTED_BUNDLE, false);
    return generatePaperRequestedBundle;
  }
}
