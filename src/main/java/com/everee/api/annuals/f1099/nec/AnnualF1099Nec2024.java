package com.everee.api.annuals.f1099.nec;

import static com.everee.api.pdf.PdfTaxpayerIdentifier.Type.EIN;

import com.everee.api.company.DetailedCompany;
import com.everee.api.pdf.PdfCheckbox;
import com.everee.api.pdf.PdfFillableForm;
import com.everee.api.pdf.PdfString;
import com.everee.api.pdf.PdfTaxpayerIdentifier;
import com.everee.api.user.DetailedUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.io.Serializable;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Immutable;
import org.springframework.util.StringUtils;

@Data
@Entity
@Table(name = "firesubmitted1099nec")
@Immutable
@PdfFillableForm(
  value = "taxforms/f1099nec_2024.pdf",
  withoutPages = {1})
@Slf4j
@Accessors(chain = true)
public class AnnualF1099Nec2024 implements f1099Nec {

  private static final String NEW_LINE = "\n";
  private static final String EMPTY_SPACE = " ";
  private static final DateTimeFormatter TEMPLATE_YEAR_FORMATTER =
    DateTimeFormatter.ofPattern("yyyy");
  protected static final String NO_NON_EMPLOYEE_COMPENSATION = "0.00";

  private String payersTin;
  private String payersName;
  private String payersAddress;
  private String payersCity;
  private String payersState;
  private String payersZip;
  private String payersPhone;
  private String recipientsTin;
  private String recipientsTinType;
  private String recipientsName1;
  private String recipientsName2;
  private String recipientsAddress;
  private String recipientsCity;
  private String recipientsState;
  private String recipientsZip;
  private String nonEmployeeCompensation;
  private Long employeeId;

  @JsonUnwrapped @EmbeddedId private AnnualF1099NecSubmissionId id;
  @Transient private List<AnnualF1099Nec.AnnualF1099NecStateInfo> targetStates = new ArrayList<>();

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "userId", insertable = false, updatable = false)
  private DetailedUser user;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @PdfString({
    "topmostSubform[0].CopyA[0].PgHeader[0].f1_1[0]",
    "topmostSubform[0].Copy1[0].Copy1Header[0].CalendarYear[0].f2_1[0]",
    "topmostSubform[0].CopyB[0].CopyBHeader[0].CalendarYear[0].f2_1[0]",
    "topmostSubform[0].Copy2[0].CopyCHeader[0].CalendarYear[0].f2_1[0]"
  })
  protected String getTemplateYear() {
    return id.getYear().format(TEMPLATE_YEAR_FORMATTER);
  }

  @PdfCheckbox({
    "topmostSubform[0].CopyA[0].PgHeader[0].c1_1[1]",
    "topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[1]",
    "topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]",
    "topmostSubform[0].Copy2[0].CopyCHeader[0].c2_1[1]"
  })
  private Boolean corrected;

  @PdfString({
    "topmostSubform[0].CopyA[0].RightColumn[0].f1_9[0]",
    "topmostSubform[0].Copy1[0].RightColumn[0].f2_9[0]",
    "topmostSubform[0].CopyB[0].RightColumn[0].f2_9[0]",
    "topmostSubform[0].Copy2[0].RightColumn[0].f2_9[0]"
  })
  protected String getNonEmployeeCompensation() {
    if (nonEmployeeCompensation == null || nonEmployeeCompensation.matches("^0+$")) {
      return NO_NON_EMPLOYEE_COMPENSATION;
    }

    return new StringBuilder(nonEmployeeCompensation)
      .insert(nonEmployeeCompensation.length() - 2, ".")
      .toString();
  }

  @PdfTaxpayerIdentifier(
    fieldNames = {
      "topmostSubform[0].CopyA[0].LeftColumn[0].f1_3[0]",
      "topmostSubform[0].Copy1[0].LeftColumn[0].f2_3[0]",
      "topmostSubform[0].CopyB[0].LeftColumn[0].f2_3[0]",
      "topmostSubform[0].Copy2[0].LeftColumn[0].f2_3[0]"
    },
    type = EIN)
  public String getPayerTin() {
    return payersTin;
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_5[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_5[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_5[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_5[0]"
  })
  public String getRecipientName() {
    var builder = new StringBuilder().append(recipientsName1);

    if (!StringUtils.isEmpty(recipientsName2)) {
      builder.append(NEW_LINE);
      builder.append(recipientsName2);
    }

    return builder.toString();
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_4[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_4[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_4[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_4[0]"
  })
  public String getRecipientTin() {
    switch (recipientsTinType) {
      case "EIN":
        return new StringBuilder(recipientsTin).insert(2, '-').toString();
      case "SSN":
        return new StringBuilder(recipientsTin).insert(5, '-').insert(3, '-').toString();
      default:
        return recipientsTin;
    }
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_6[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_6[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_6[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_6[0]"
  })
  public String getRecipientAddress() {
    return recipientsAddress;
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_7[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_7[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_7[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_7[0]"
  })
  public String getRecipientCityStateZip() {
    return new StringBuilder()
      .append(recipientsCity)
      .append(EMPTY_SPACE)
      .append(recipientsState)
      .append(EMPTY_SPACE)
      .append(recipientsZip)
      .toString();
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_2[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_2[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_2[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_2[0]"
  })
  public String getPayersDetails() {
    return new StringBuilder()
      .append(payersName)
      .append(NEW_LINE)
      .append(payersAddress)
      .append(NEW_LINE)
      .append(payersCity)
      .append(EMPTY_SPACE)
      .append(payersState)
      .append(EMPTY_SPACE)
      .append(payersZip)
      .append(NEW_LINE)
      .append(payersPhone)
      .toString();
  }

  public Long getFireFileSubmissionId() {
    return id.getFireFileSubmissionId();
  }

  @Override
  public Map<String, ? extends f1099NecState> getStates() {
    return null;
  }

  @Embeddable
  @Data
  @Accessors(chain = true)
  public static class AnnualF1099NecSubmissionId implements Serializable {
    private Year year;
    private Long userId;
    private Long companyId;
    private Long fireFileSubmissionId;
  }
}
