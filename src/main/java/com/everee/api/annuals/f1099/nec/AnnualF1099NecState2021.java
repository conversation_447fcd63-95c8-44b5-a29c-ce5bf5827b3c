package com.everee.api.annuals.f1099.nec;

import com.everee.api.money.Money;
import com.everee.api.pdf.PdfFillableForm;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Table(name = "annualf1099necstate")
@Immutable
@PdfFillableForm("taxforms/f1099nec_2021.pdf")
@Slf4j
public class AnnualF1099NecState2021 implements f1099NecState {
  @Column(insertable = false, updatable = false)
  private String recipientStateCode;

  @JsonUnwrapped @EmbeddedId private AnnualF1099NecState.AnnualF1099NecStateId id;

  private Money stateIncomeWithheld;
  private Money stateOtherIncome;
}
