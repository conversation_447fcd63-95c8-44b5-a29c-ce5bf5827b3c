package com.everee.api.annuals.f1099.misc;

import static com.everee.api.util.ObjectUtils.optionally;
import static java.util.Objects.nonNull;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.money.Money;
import com.everee.api.pdf.PdfFillableForm;
import com.everee.api.pdf.PdfString;
import com.everee.api.pdf.PdfTaxpayerIdentifier;
import com.everee.api.pdf.PdfTaxpayerIdentifier.Type;
import com.everee.api.tax.state.State;
import com.everee.api.user.DetailedUser;
import com.everee.api.user.address.HomeAddress;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Immutable;
import org.springframework.util.StringUtils;

@Data
@Entity
@Immutable
@PdfFillableForm("taxforms/f1099msc_2019.pdf")
@Slf4j
public class AnnualF1099Misc implements f1099Misc {
  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "userId", insertable = false, updatable = false)
  private DetailedUser user;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @ManyToOne(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinColumn(name = "employeeId", insertable = false, updatable = false)
  private DetailedEmployee employee;

  @PdfString({
    "topmostSubform[0].CopyA[0].RightColumn[0].f1_10[0]",
    "topmostSubform[0].Copy1[0].RightCol[0].f2_10[0]",
    "topmostSubform[0].CopyB[0].RightCol[0].f2_10[0]",
    "topmostSubform[0].Copy2[0].RightColumn[0].f2_10[0]",
    "topmostSubform[0].CopyC[0].RightColumn[0].f2_10[0]"
  })
  private Money otherIncome;

  @PdfString({
    "topmostSubform[0].CopyA[0].RightColumn[0].f1_11[0]",
    "topmostSubform[0].Copy1[0].RightCol[0].f2_11[0]",
    "topmostSubform[0].CopyB[0].RightCol[0].f2_11[0]",
    "topmostSubform[0].Copy2[0].RightColumn[0].f2_11[0]",
    "topmostSubform[0].CopyC[0].RightColumn[0].f2_11[0]"
  })
  private Money federalIncomeWithheld;

  @JsonUnwrapped @EmbeddedId private AnnualF1099MiscId id;
  @Transient private State targetState;
  @Transient private String targetStateAccountNumber;

  @OneToMany(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        updatable = false,
        insertable = false,
        name = "companyId",
        referencedColumnName = "companyId"),
    @JoinColumn(
        updatable = false,
        insertable = false,
        name = "year",
        referencedColumnName = "year"),
    @JoinColumn(
        updatable = false,
        insertable = false,
        name = "employeeId",
        referencedColumnName = "employeeId"),
    @JoinColumn(
        updatable = false,
        insertable = false,
        name = "userId",
        referencedColumnName = "userId")
  })
  @MapKey(name = "recipientStateCode")
  private Map<String, AnnualF1099MiscState> states;

  @PdfTaxpayerIdentifier(
      fieldNames = {
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_2[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_2[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_2[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_2[0]",
        "topmostSubform[0].CopyC[0].LeftColumn[0].f2_2[0]"
      },
      type = Type.EIN)
  public String getPayerTin() {
    return getCompany().getFederalEin();
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_4[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_4[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_4[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_4[0]",
    "topmostSubform[0].CopyC[0].LeftColumn[0].f2_4[0]"
  })
  public String getRecipientName() {
    return getUser().getFullName();
  }

  @PdfTaxpayerIdentifier(
      fieldNames = {
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_3[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_3[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_3[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_3[0]",
        "topmostSubform[0].CopyC[0].LeftColumn[0].f2_3[0]"
      },
      type = Type.SSN)
  public String getRecipientTin() {
    return getUser().getTaxpayerIdentifier();
  }

  private HomeAddress getRecipientAddress() {
    return getUser()
        .findActiveHomeAddress(getLastDateOfYear())
        .or(
            () ->
                Optional.ofNullable(getEmployee().getEndDate())
                    .flatMap(getUser()::findActiveHomeAddress))
        .orElseGet(
            () -> {
              log.warn(
                  "Unable to get address for User#{}: {} on LastDayOfYear: {} or EmployeeEndDate: {}",
                  getUser().getId(),
                  getUser().getUsernameOrEmail(),
                  getLastDateOfYear(),
                  getEmployee().getEndDate());
              return null;
            });
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_5[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_5[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_5[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_5[0]",
    "topmostSubform[0].CopyC[0].LeftColumn[0].f2_5[0]"
  })
  public String getRecipientAddressLines() {
    return optionally(
        getRecipientAddress(),
        address ->
            Stream.of(address.getLine1(), address.getLine2())
                .filter(StringUtils::hasText)
                .collect(Collectors.joining("\n")));
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_6[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_6[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_6[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_6[0]",
    "topmostSubform[0].CopyC[0].LeftColumn[0].f2_6[0]"
  })
  public String getRecipientAddressCityStateZip() {
    return optionally(
        getRecipientAddress(),
        address -> address.getCity() + " " + address.getState() + " " + address.getPostalCode());
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].LeftColumn[0].f1_1[0]",
    "topmostSubform[0].Copy1[0].LeftColumn[0].f2_1[0]",
    "topmostSubform[0].CopyB[0].LeftColumn[0].f2_1[0]",
    "topmostSubform[0].Copy2[0].LeftColumn[0].f2_1[0]",
    "topmostSubform[0].CopyC[0].LeftColumn[0].f2_1[0]"
  })
  public String getCompanyNameAddressPhone() {
    return Stream.of(
            getCompany().getLegalEntityName(),
            getCompany()
                .getActiveAddress(getLastDateOfYear())
                .map(CompanyAddress::getDisplayString)
                .orElse(null),
            getCompany().getPhone())
        .filter(StringUtils::hasText)
        .collect(Collectors.joining("\n"));
  }

  private LocalDate getLastDateOfYear() {
    return LocalDate.of(getId().getYear().getValue(), Month.DECEMBER, 31);
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].Boxes15-18[0].f1_25[0]",
    "topmostSubform[0].Copy1[0].Boxes15a-18[0].f2_25[0]",
    "topmostSubform[0].CopyB[0].Boxes15a-18[0].f2_25[0]",
    "topmostSubform[0].Copy2[0].Boxes15a-18[0].f2_25[0]",
    "topmostSubform[0].CopyC[0].Boxes15a-18[0].f2_25[0]"
  })
  public String getStatePayerNumber() {
    if (nonNull(getStateIncomeWithheld())) {
      return targetState.name()
          + Optional.ofNullable(targetStateAccountNumber)
              .map(accountNumber -> "/" + accountNumber)
              .orElse("");
    }
    return null;
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].Boxes15-18[0].f1_23[0]",
    "topmostSubform[0].Copy1[0].Boxes15a-18[0].f2_23[0]",
    "topmostSubform[0].CopyB[0].Boxes15a-18[0].f2_23[0]",
    "topmostSubform[0].Copy2[0].Boxes15a-18[0].f2_23[0]",
    "topmostSubform[0].CopyC[0].Boxes15a-18[0].f2_23[0]"
  })
  // TODO 2020 @PdfString({"topmostSubform[0].CopyA[0].Boxes15-17[0].f1_22[0]"})
  @Transient
  public Money getStateIncomeWithheld() {
    return Optional.ofNullable(getTargetState())
        .map(State::getCode)
        .map(getStates()::get)
        .map(AnnualF1099MiscState::getStateIncomeWithheld)
        .filter(sit -> sit.gt(Money.ZERO))
        .orElse(null);
  }

  // TODO @PdfString({"topmostSubform[0].CopyA[0].Boxes15-17[0].f1_24[0]"})
  @Transient
  public State getStateOnPdf() {
    return Optional.ofNullable(getTargetState())
        .filter(state -> getStates().containsKey(state.getCode()))
        .filter(ignored -> nonNull(getStateIncomeWithheld()))
        .orElse(null);
  }

  @PdfString({
    "topmostSubform[0].CopyA[0].Boxes15-18[0].f1_27[0]",
    "topmostSubform[0].Copy1[0].Boxes15a-18[0].f2_27[0]",
    "topmostSubform[0].CopyB[0].Boxes15a-18[0].f2_27[0]",
    "topmostSubform[0].Copy2[0].Boxes15a-18[0].f2_27[0]",
    "topmostSubform[0].CopyC[0].Boxes15a-18[0].f2_27[0]"
  })
  public Money getStateIncome() {
    return Optional.ofNullable(getTargetState())
        .map(State::getCode)
        .map(getStates()::get)
        .map(AnnualF1099MiscState::getStateOtherIncome)
        .filter(sit -> sit.gt(Money.ZERO))
        .orElse(null);
  }

  @Embeddable
  @Data
  public static class AnnualF1099MiscId implements Serializable {
    private Year year;
    private Long userId;
    private Long employeeId;
    private Long companyId;
  }
}
