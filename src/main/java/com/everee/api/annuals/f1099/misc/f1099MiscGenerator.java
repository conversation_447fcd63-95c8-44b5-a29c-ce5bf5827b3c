package com.everee.api.annuals.f1099.misc;

import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.state.State;
import java.time.Year;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class f1099MiscGenerator {
  private final EntityManager entityManager;

  public f1099Misc getF1099Misc(Year year, long employeeId) {
    if (year.getValue() > 2019) {
      // This form changed after 2019 and we haven't done any work to support it.
      throw new UnsupportedOperationException(
          "No 1099-MISC support has been built beyond tax year 2019");
    }

    try {
      return entityManager
          .createQuery(
              "SELECT annualF1099Misc "
                  + "FROM AnnualF1099Misc annualF1099Misc "
                  + "WHERE annualF1099Misc.id.employeeId = :employeeId "
                  + "AND annualF1099Misc.id.year = :year",
              AnnualF1099Misc.class)
          .setParameter("employeeId", employeeId)
          .setParameter("year", year)
          .getSingleResult();
    } catch (NoResultException ignored) {
      return null;
    }
  }

  public f1099Misc getF1099Misc(Year year, long employeeId, State state) {
    Optional<f1099Misc> annualF1099MiscOptional =
        Optional.ofNullable(getF1099Misc(year, employeeId));
    annualF1099MiscOptional.ifPresent(
        annualF1099Misc -> {
          annualF1099Misc.setTargetState(state);
          Optional.ofNullable(annualF1099Misc.getCompany())
              .flatMap(company -> company.getCompanySitInfo(state))
              .map(CompanyTaxJurisdiction::getAccountNumber)
              .ifPresent(annualF1099Misc::setTargetStateAccountNumber);
        });
    return annualF1099MiscOptional.orElse(null);
  }
}
