package com.everee.api.annuals.w2;

import com.everee.api.money.Money;
import com.everee.api.tax.TaxType;
import com.everee.api.tax.UniqueTaxID;
import com.everee.api.tax.state.State;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.Year;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Immutable;

@Data
@MappedSuperclass
@ToString(callSuper = true)
@EqualsAndHashCode
@Immutable
@Accessors(chain = true)
public abstract class W2NonFedTaxData {

  public enum Group {
    STATE,
    LOCAL,
    OTHER
  }

  @Id @JsonIgnore private String id;

  private Long userId;
  private Long companyId;
  private Year year;
  private String controlNumber;

  @Enumerated(EnumType.STRING)
  private TaxType type;

  @Enumerated(EnumType.STRING)
  private Group group;

  private UniqueTaxID jurisdictionTaxId;
  private String description;
  private Money subjectWages;
  private Money amount;
  @Transient private State state;
  @Transient private String stateEin;
  @Transient private String localityName;

  public boolean isStatutoryEmployee() {
    return false;
  }

  public String getW2Description() {
    return null;
  }
}
