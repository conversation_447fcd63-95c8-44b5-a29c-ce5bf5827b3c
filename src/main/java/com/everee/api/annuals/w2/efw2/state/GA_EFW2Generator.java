package com.everee.api.annuals.w2.efw2.state;

import static com.everee.api.annuals.w2.efw2.Efw2Utils.blank;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.name;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.right;
import static java.time.temporal.TemporalAdjusters.lastDayOfYear;
import static org.apache.commons.lang3.StringUtils.leftPad;
import static org.apache.commons.lang3.StringUtils.truncate;

import com.everee.api.annuals.TaxFilingGenerationException;
import com.everee.api.annuals.w2.W2Data;
import com.everee.api.annuals.w2.W2NonFedTaxData;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.tax.state.State;
import java.time.format.DateTimeFormatter;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class GA_EFW2Generator extends StateEfw2Generator {
  @Getter private final State state = State.GA;

  DateTimeFormatter mm_dd_yyyy = DateTimeFormatter.ofPattern("MM/dd/yyyy");

  protected String getStateEinForStateTotal(W2NonFedTaxData stateData) {
    return blank(20);
  }

  @Override
  protected StringBuffer getOtherStateData(W2Data data, W2NonFedTaxData stateData)
      throws TaxFilingGenerationException {
    return new StringBuffer()
        .append(mm_dd_yyyy.format(data.getYear().atDay(1).with(lastDayOfYear()))) // 298-307
        .append(blank(1)) // 308
        .append(blank(11)) // 309-319
        .append(blank(11)) // 320-330
        .append(
            leftPad(
                truncate(
                    stateData.getStateEin().trim().replaceAll("[^a-zA-Z0-9]", "").toUpperCase(), 9),
                9)); // 331-339
  }

  @Override
  protected StringBuffer getSupplementalStateData(W2Data data, W2NonFedTaxData stateData)
      throws TaxFilingGenerationException {
    return new StringBuffer()
        .append(name(data.getCompany().getLegalEntityName(), 57)) // 340-396
        .append(
            buildCompanyAddress(
                data.getCompany()
                    .getActiveAddress(data.getYear().atDay(1).with(lastDayOfYear()))
                    .get())) // 397-473
        .append(right(data.getCompany().getFederalEin(), 9)) // 474-482
        .append(blank(5)) // 483-487
    ;
  }

  private static StringBuffer buildCompanyAddress(PhysicalAddress address) {
    var postalCodeNumbers = address.getPostalCode().replaceAll("-", "").trim();
    return new StringBuffer()
        .append(right(address.getLine2(), 22))
        .append(right(address.getLine1(), 22))
        .append(right(address.getCity(), 22))
        .append(address.getState().name())
        .append(right(postalCodeNumbers.substring(0, Math.min(5, postalCodeNumbers.length())), 5))
        .append(
            right(
                postalCodeNumbers.length() > 5 ? postalCodeNumbers.substring(5) : "",
                4)); // zip extension
  }
}
