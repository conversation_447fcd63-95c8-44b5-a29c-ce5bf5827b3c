package com.everee.api.annuals.w2.pdf;

import com.everee.api.annuals.w2.W2Data;
import com.everee.api.annuals.w2.W2Data_;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class W2PdfForm2024 extends W2PdfForm {
  static Column HEADER_BOXA = new Column(".BoxA_ReadOrder[0]");
  static Column LEFT = new Column(".Col_Left[0]");
  static Column RIGHT = new Column(".Col_Right[0]");
  static Column BOX1 = RIGHT.plus(".Box1_ReadOrder[0]");
  static Column BOX3 = RIGHT.plus(".Box3_ReadOrder[0]");
  static Column BOX5 = RIGHT.plus(".Box5_ReadOrder[0]");
  static Column BOX7 = RIGHT.plus(".Box7_ReadOrder[0]");
  static Column BOX9 = RIGHT.plus(".Box9_ReadOrder[0]");
  static Column BOX11 = RIGHT.plus(".Box11_ReadOrder[0]");
  static Column RIGHT_LINE_12 = RIGHT.plus(".Box12_ReadOrder[0]");
  static Column BOX_E = LEFT.plus(".FirstName_ReadOrder[0]");
  static Column BOX_E_L = LEFT.plus(".LastName_ReadOrder[0]");
  static Column FOOTER = new Column("");
  static Column BOX15_LEFT = FOOTER.plus(".Boxes15_ReadOrder[0].Box15_ReadOrder[0]");
  static Column BOX15_RIGHT = FOOTER.plus(".Boxes15_ReadOrder[0]");
  static Column BOX16 = FOOTER.plus(".Box16_ReadOrder[0]");
  static Column BOX17 = FOOTER.plus(".Box17_ReadOrder[0]");
  static Column BOX18 = FOOTER.plus(".Box18_ReadOrder[0]");
  static Column BOX19 = FOOTER.plus(".Box19_ReadOrder[0]");

  {
    // Form copies
    formCopies.add(new W2FormCopy("CopyA", 0, 0));
    formCopies.add(new W2FormCopy("Copy1", 0, 0));
    formCopies.add(new W2FormCopy("CopyB", 0, 0));
    formCopies.add(new W2FormCopy("CopyC", 0, 0));
    formCopies.add(new W2FormCopy("Copy2", 0, 0));
    formCopies.add(new W2FormCopy("CopyD", 0, 0));
  }

  @Override
  public List<W2Field> getFields(int currentCopyNum, W2Data w2Data) {
    List<W2Field> fields = new ArrayList<>();

    // Header fields
    fields.add(new W2Field(HEADER_BOXA, FieldType.TEXT, 1, "taxpayerIdentifier", true));

    // Left column fields
    fields.add(new W2Field(LEFT, FieldType.TEXT, 2, "federalEin", true));
    fields.add(new W2Field(LEFT, FieldType.TEXT, 3, "contactInfoER", true));
    fields.add(new W2Field(LEFT, FieldType.TEXT, 4, W2Data_.CONTROL_NUMBER, true));
    fields.add(new W2Field(BOX_E, FieldType.TEXT, 5, "userFirstNameAndMiddleInitial", true));
    fields.add(new W2Field(BOX_E_L, FieldType.TEXT, 6, W2Data_.USER_LAST_NAME, true));
    fields.add(new W2Field(LEFT, FieldType.TEXT, 7, "userSuffix", true));
    fields.add(new W2Field(LEFT, FieldType.TEXT, 8, "contactInfoEE", true));

    if (currentCopyNum == 1) {
      // Right column fields
      fields.add(new W2Field(BOX1, FieldType.TEXT, 9, W2Data_.FIT_WAGES, true));
      fields.add(new W2Field(RIGHT, FieldType.TEXT, 10, W2Data_.FIT, true));
      fields.add(new W2Field(BOX3, FieldType.TEXT, 11, W2Data_.FICA_WAGES, true));
      fields.add(new W2Field(RIGHT, FieldType.TEXT, 12, W2Data_.FICA, true));
      fields.add(new W2Field(BOX5, FieldType.TEXT, 13, W2Data_.MEDI_WAGES, true));
      fields.add(new W2Field(RIGHT, FieldType.TEXT, 14, W2Data_.MEDI, true));
      fields.add(new W2Field(BOX7, FieldType.TEXT, 15, W2Data_.SS_TIPS, true));
      fields.add(new W2Field(RIGHT, FieldType.TEXT, 16, W2Data_.ALLOC_TIPS, true));
      fields.add(new W2Field(BOX9, FieldType.TEXT, 17, W2Data_.DRA, true));
      // 18 dependenctcare
      fields.add(new W2Field(BOX11, FieldType.TEXT, 19, W2Data_.NQ_PLANS, true));

      // Box 12 items
      // TODO: Currently this will only work for the first 4 items.  This is okay
      //       because we don't have any employees with more than 4, but in the future
      //       this will need to be changed to handle more than 4
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 20, "box12Items[0].key", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 21, "box12Items[0].value", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 22, "box12Items[1].key", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 23, "box12Items[1].value", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 24, "box12Items[2].key", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 25, "box12Items[2].value", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 26, "box12Items[3].key", false));
      fields.add(new W2Field(RIGHT_LINE_12, FieldType.TEXT, 27, "box12Items[3].value", false));

      fields.add(
          new W2Field(
              new Column(".Col_Right[0].Statutory_ReadOrder[0]"),
              FieldType.CHECKBOX,
              2,
              W2Data_.STATUTORY_EMPLOYEE,
              true));
      fields.add(
          new W2Field(
              new Column(".Col_Right[0].Retirement_ReadOrder[0]"),
              FieldType.CHECKBOX,
              3,
              W2Data_.RETIREMENT_PLAN,
              true));
      fields.add(new W2Field(RIGHT, FieldType.CHECKBOX, 4, W2Data_.THIRD_PARTY_SICK_PAY, true));
      fields.add(new W2Field(RIGHT, FieldType.TEXT, 28, "other", false));
    }

    var idx = currentCopyNum - 1;
    // Footer fields
    fields.add(
        new W2Field(
            BOX15_LEFT, FieldType.TEXT, 29, "stateAndLocalTaxes[" + idx + "].state", false));
    fields.add(
        new W2Field(
            BOX15_RIGHT, FieldType.TEXT, 30, "stateAndLocalTaxes[" + idx + "].stateEin", false));
    fields.add(
        new W2Field(
            BOX16, FieldType.TEXT, 33, "stateAndLocalTaxes[" + idx + "].subjectWages", false));
    fields.add(
        new W2Field(BOX17, FieldType.TEXT, 35, "stateAndLocalTaxes[" + idx + "].amount", false));
    fields.add(
        new W2Field(
            BOX18, FieldType.TEXT, 37, "stateAndLocalTaxes[" + idx + "].localSubjectWages", false));
    fields.add(
        new W2Field(
            BOX19, FieldType.TEXT, 39, "stateAndLocalTaxes[" + idx + "].localAmount", false));
    fields.add(
        new W2Field(
            FOOTER, FieldType.TEXT, 41, "stateAndLocalTaxes[" + idx + "].localityName", false));
    return fields;
  }

  @Override
  public String getPdfFormFieldName(W2Field field, W2FormCopy formCopy) {
    int position = field.getPosition();

    // Copy A form field names all use "1_"
    // And all other copies use "2_"
    // Not confusing at all
    var numberPrefix = "CopyA".equals(formCopy.getName()) ? "1_" : "2_";

    String fieldName =
        "topmostSubform[0]."
            + formCopy.getName()
            + "[0]"
            + getQualifier(field, formCopy)
            + "."
            + field.getType().getQualifier()
            + numberPrefix
            + (FieldType.TEXT.equals(field.getType())
                ? StringUtils.leftPad(position + "", 2, "0")
                : position)
            + "[0]";

    return fieldName;
  }

  private static final Map<String, Pair<String, String>> replacementsByFormPosition =
      Map.ofEntries(

          // For some reason, Box 11 contains double underscores
          // Not confusing at all
          Map.entry("Copy1__19", Pair.of("Box11_ReadOrder", "Box11__ReadOrder")),
          Map.entry("Copy2__19", Pair.of("Box11_ReadOrder", "Box11__ReadOrder")),
          Map.entry("CopyB__19", Pair.of("Box11_ReadOrder", "Box11__ReadOrder")),
          Map.entry("CopyC__19", Pair.of("Box11_ReadOrder", "Box11__ReadOrder")),
          Map.entry("CopyD__19", Pair.of("Box11_ReadOrder", "Box11__ReadOrder")),
          Map.entry("CopyA__20", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__21", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__22", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__23", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__24", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__25", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__26", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")),
          Map.entry("CopyA__27", Pair.of("Box12_ReadOrder", "Line12_ReadOrder")));

  private String getQualifier(W2Field field, W2FormCopy formCopy) {
    var formPosition = formCopy.getName() + "__" + field.getPosition();
    if (!replacementsByFormPosition.containsKey(formPosition)) {
      return field.getColumn().getQualifier();
    }

    var replacementInfo = replacementsByFormPosition.get(formPosition);
    return field
        .getColumn()
        .getQualifier()
        .replace(replacementInfo.getFirst(), replacementInfo.getSecond());
  }
}
