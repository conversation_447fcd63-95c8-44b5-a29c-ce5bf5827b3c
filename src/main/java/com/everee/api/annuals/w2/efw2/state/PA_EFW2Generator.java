package com.everee.api.annuals.w2.efw2.state;

import static com.everee.api.annuals.w2.efw2.EFW2Generator.EL;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.blank;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.money;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.numericOnly;
import static com.everee.api.annuals.w2.efw2.Efw2Utils.taxpayerId;
import static org.apache.commons.lang3.StringUtils.leftPad;
import static org.apache.commons.lang3.StringUtils.reverse;
import static org.apache.commons.lang3.StringUtils.truncate;

import com.everee.api.annuals.TaxFilingGenerationException;
import com.everee.api.annuals.w2.W2Data;
import com.everee.api.annuals.w2.W2NonFedTaxData;
import com.everee.api.annuals.w2.efw2.Efw2Totaller;
import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.state.State;
import java.time.Year;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PA_EFW2Generator extends StateEfw2Generator {
  @Getter private final State state = State.PA;

  @Override
  public StringBuffer generateStateTotalRecord(
      Efw2Totaller totaller, W2NonFedTaxData stateData, DetailedCompany company, Year year) {
    return new StringBuffer()
        .append("RV")
        .append(getState().getCode())
        .append(year.toString())
        .append(
            leftPad(
                reverse(
                    truncate(
                        reverse(
                            numericOnly(
                                company
                                    .getCompanySitInfo(getState())
                                    .map(CompanyTaxJurisdiction::getAccountNumber)
                                    .orElse(""))),
                        8)),
                8,
                "0"))
        .append(
            leftPad(reverse(truncate(reverse(numericOnly(company.getFederalEin())), 9)), 9, "0"))
        .append(leftPad(totaller.getRecordCount(state) + "", 7, "0"))
        .append(money(stateData.getSubjectWages(), 15))
        .append(money(stateData.getAmount(), 15))
        .append(blank(450))
        .append(EL);
  }

  protected String getStateEinForStateTotal(W2NonFedTaxData stateData) {
    return leftPad(reverse(truncate(reverse(numericOnly(stateData.getStateEin())), 8)), 8, "0")
        + blank(12);
  }

  @Override
  protected StringBuffer getOtherStateData(W2Data data, W2NonFedTaxData stateData)
      throws TaxFilingGenerationException {
    return new StringBuffer()
        .append(blank(10)) // Filler
        .append(blank(1))
        .append(money(Money.ZERO))
        .append(money(Money.ZERO))
        .append(blank(7));
  }

  @Override
  protected StringBuffer getSupplementalStateData(W2Data data, W2NonFedTaxData stateData)
      throws TaxFilingGenerationException {
    return new StringBuffer()
        .append(taxpayerId(data.getUser().getTaxpayerIdentifier()))
        .append(blank(66))
        .append(blank(75));
  }

  public String getFinalRecordData(
      Efw2Totaller totaller, W2NonFedTaxData stateData, DetailedCompany company, Year year) {

    return new StringBuffer()
        .append(leftPad(totaller.getRecordCount(state) + "", 7, "0"))
        .append(money(stateData.getSubjectWages(), 15))
        .append(money(stateData.getAmount(), 15))
        .toString();
  }
}
