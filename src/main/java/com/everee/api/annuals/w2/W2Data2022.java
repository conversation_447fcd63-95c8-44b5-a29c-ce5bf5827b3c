package com.everee.api.annuals.w2;

import com.everee.api.money.Money;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Locale;
import javax.persistence.Entity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Immutable
@Accessors(chain = true)
public class W2Data2022 extends W2Data {
  private Money ffcraSick;
  private Money ffcraCaregiver;
  private Money ffcraFmla;

  @Override
  public String getOther() {
    var sb = new StringBuffer(super.getOther());

    addFFCRA(sb, ffcraSick, "FFCRA Sick ($511/day)");
    addFFCRA(sb, ffcraCaregiver, "FFCRA Caregiver ($200/day)");
    addFFCRA(sb, ffcraFmla, "FFCRA FMLA");

    return sb.toString();
  }

  private void addFFCRA(StringBuffer sb, Money amount, String description) {
    if (amount != null && amount.gt(Money.ZERO)) {
      DecimalFormat df = new DecimalFormat("#0.00", new DecimalFormatSymbols(Locale.getDefault()));
      sb.append(description).append(" ").append(df.format(amount.getAmount())).append("\n");
    }
  }
}
