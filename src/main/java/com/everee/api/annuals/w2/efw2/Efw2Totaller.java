package com.everee.api.annuals.w2.efw2;

import com.everee.api.annuals.w2.W2Data;
import com.everee.api.tax.state.State;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

public class Efw2Totaller extends W2Data {
  @Getter private int recordCount;
  @Getter private int roRecordCount;
  private Map<State, Integer> stateRecordCount = new HashMap<>();

  public Efw2Totaller() {
    setW2NonFedTaxData(new ArrayList<>());
  }

  public W2Data plus(W2Data w2Data, boolean roEligible) {
    recordCount++;
    if (roEligible) roRecordCount++;
    w2Data.getStateTaxes().stream()
        .filter(stateData -> stateData.getSubjectWages().isPlus())
        .forEach(
            stateData -> {
              var cnt = stateRecordCount.getOrDefault(stateData.getState(), 0);
              cnt++;
              stateRecordCount.put(stateData.getState(), cnt);
            });
    return super.plus(w2Data);
  }

  public int getRecordCount(State state) {
    return stateRecordCount.getOrDefault(state, 0);
  }
}
