package com.everee.api.timeclock.bulk;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BulkTimeCardEntryRepository extends JpaRepository<BulkTimeCardEntry, Long> {
  List<BulkTimeCardEntry> findAllByEmployeeId(Long employeeId);

  List<BulkTimeCardEntry> findAllByCompanyIdAndEmployeeIdInAndPayPeriodIdIn(
      Long companyId, Collection<Long> employeeIds, Collection<Long> payPeriodIds);

  Optional<BulkTimeCardEntry> findByEmployeeIdAndPayPeriodId(Long employeeId, Long payPeriodId);
}
