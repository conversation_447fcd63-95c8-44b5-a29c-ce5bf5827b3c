package com.everee.api.timeclock.bulk;

import com.everee.api.http.v2.labor.ManagerTimecardController;
import java.net.URI;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@Deprecated(forRemoval = true)
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class BulkTimeCardEntryController {
  private final BulkTimeCardEntryService bulkTimeCardEntryService;

  /** @deprecated use {@link ManagerTimecardController} */
  @Deprecated
  @PreAuthorize(
      "#currentUser.isCompanyMember(#companyId) && #currentUser.canAccessEmployee(#employeeId)")
  @PostMapping("/companies/{companyId}/employees/{employeeId}/time-worked/raw")
  public ResponseEntity<BulkTimeCardEntry> createPayPeriodTimeWorked(
      @PathVariable Long companyId,
      @PathVariable Long employeeId,
      @Valid @RequestBody PayPeriodTimecardParams request) {
    var payPeriodTimeWorked = bulkTimeCardEntryService.create(employeeId, request);

    URI location =
        ServletUriComponentsBuilder.fromCurrentRequest()
            .query("pay-period-id={payPeriodId}")
            .buildAndExpand(request.getPayPeriodId())
            .toUri();

    return ResponseEntity.created(location).body(payPeriodTimeWorked);
  }

  /** @deprecated use {@link ManagerTimecardController} */
  @Deprecated
  @PreAuthorize(
      "#currentUser.isCompanyMember(#companyId) && #currentUser.canAccessEmployee(#employeeId)")
  @GetMapping("/companies/{companyId}/employees/{employeeId}/time-worked/raw")
  public BulkTimeCardEntry getPayPeriodTimeWorked_deprecated(
      @PathVariable Long companyId,
      @PathVariable Long employeeId,
      @RequestParam(name = "pay-period-id") Long payPeriodId) {
    return bulkTimeCardEntryService.getOne(employeeId, payPeriodId);
  }

  /** @deprecated use {@link ManagerTimecardController} */
  @Deprecated
  @PreAuthorize(
      "#currentUser.isCompanyMember(#companyId) && #currentUser.canAccessEmployee(#employeeId)")
  @PutMapping(
      "/companies/{companyId}/employees/{employeeId}/time-worked/raw/{payPeriodTimeWorkedId}")
  public BulkTimeCardEntry updatePayPeriodTimeWorked(
      @PathVariable Long companyId,
      @PathVariable Long employeeId,
      @PathVariable Long payPeriodTimeWorkedId,
      @Valid @RequestBody PayPeriodTimecardParams request) {
    return bulkTimeCardEntryService.update(payPeriodTimeWorkedId, request);
  }
}
