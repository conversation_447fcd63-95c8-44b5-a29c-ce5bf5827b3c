package com.everee.api.timeclock.bulk;

import static com.everee.api.util.DurationUtils.toApproximateHours;

import com.everee.api.employee.Employee;
import com.everee.api.model.BaseModel;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.phase.Phased;
import com.everee.api.timeclock.DateRangeTimeWorked;
import com.everee.api.timeclock.LegacyWorkedShift;
import com.everee.api.timeclock.TimeWorked;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Collection;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;

@Data
@Entity
@ToString
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class BulkTimeCardEntry extends BaseModel implements TimeWorked, Phased {
  @Transient public Collection<LegacyWorkedShift> shifts;
  @NotNull private Long companyId;
  @NotNull private Long employeeId;
  @NotNull private Long payPeriodId;
  @NotNull private LocalDate startDate;
  @NotNull private LocalDate endDate;
  @NotNull private Duration regularTimeWorked = Duration.ZERO;
  @NotNull private Duration overtimeWorked = Duration.ZERO;
  @NotNull private Duration doubleTimeWorked = Duration.ZERO;
  @Transient private Boolean editable = true;

  @ToString.Exclude
  @JsonIgnore
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "payPeriodId", insertable = false, updatable = false)
  private PayPeriod payPeriod;

  public BulkTimeCardEntry(PayPeriod payPeriod, Employee employee) {
    this.employeeId = employee.getId();
    this.companyId = employee.getCompanyId();
    this.payPeriod = payPeriod;
    this.payPeriodId = payPeriod.getId();
    this.startDate = payPeriod.getStartDate();
    this.endDate = payPeriod.getEndDate();
  }

  @SuppressWarnings("unused")
  public @NotNull BigDecimal getRegularHoursWorked() {
    return toApproximateHours(regularTimeWorked);
  }

  @SuppressWarnings("unused")
  public @NotNull BigDecimal getOvertimeHoursWorked() {
    return toApproximateHours(overtimeWorked);
  }

  @SuppressWarnings("unused")
  public @NotNull BigDecimal getDoubleTimeHoursWorked() {
    return toApproximateHours(doubleTimeWorked);
  }

  @SuppressWarnings("unused")
  public @NotNull BigDecimal getTotalHoursWorked() {
    return toApproximateHours(getTotalTimeWorked());
  }

  public DateRangeTimeWorked toDateRangeTimeWorked() {
    var timeWorked = new DateRangeTimeWorked(getStartDate(), getEndDate(), employeeId);
    timeWorked.setRegularTimeWorked(regularTimeWorked);
    timeWorked.setOvertimeWorked(overtimeWorked);
    timeWorked.setDoubleTimeWorked(doubleTimeWorked);
    return timeWorked;
  }
}
