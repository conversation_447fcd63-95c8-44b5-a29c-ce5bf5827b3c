package com.everee.api.timeclock;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

@Data
@Table(name = "effectiveshiftpunch")
@Entity
@Immutable
public class ShiftPunch {

  @Id @JsonIgnore private String id;

  @Enumerated(EnumType.STRING)
  private Type type;

  @JsonIgnore private Long shiftId;

  @JsonIgnore private Long companyId;

  @JsonIgnore private Long employeeId;

  private String ipAddress;

  private Double latitude;

  private Double longitude;

  private Double radiusMeters;

  private LocalDateTime utcTimestamp;

  private LocalDateTime workLocationTimestamp;

  private ZoneId workLocationTimeZone;

  public enum Type {
    PUNCH_IN,
    PUNCH_OUT
  }

  @Deprecated(forRemoval = true)
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  @Column(name = "utctimestamp", insertable = false, updatable = false)
  private LocalDateTime timestamp;

  @Deprecated(forRemoval = true)
  public LocalDateTime getTimestamp() {
    return utcTimestamp;
  }
}
