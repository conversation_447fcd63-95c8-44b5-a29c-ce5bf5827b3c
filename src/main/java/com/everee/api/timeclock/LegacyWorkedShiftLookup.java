package com.everee.api.timeclock;

import static com.everee.api.timeclock.ShiftLookupPropertyNames.*;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;
import lombok.*;

@Data
@With
@Deprecated
@RequestParams
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class LegacyWorkedShiftLookup implements Lookup {
  @ApiParam(name = ID, example = "1", allowMultiple = true)
  private Set<Long> ids;

  @ApiParam(name = EMPLOYEE_ID, example = "1", allowMultiple = true)
  private Set<Long> employeeIds;

  @ApiParam(name = COMPANY_ID, example = "1", allowMultiple = true)
  private Set<Long> companyIds;

  @ApiParam(name = APPROVAL_GROUP_ID, example = "1", allowMultiple = true)
  private Set<Long> approvalGroupIds;

  @ApiParam(name = VERIFIED)
  private Boolean verified;

  @ApiParam(name = ACTIVE)
  private Boolean active;

  @ApiParam(name = EDITABLE)
  private Boolean editable;

  @ApiParam(name = UTC_PUNCH_IN_START_DATE, example = "2020-01-01")
  private LocalDate utcPunchInStartDate;

  @ApiParam(name = UTC_PUNCH_IN_END_DATE, example = "2020-01-01")
  private LocalDate utcPunchInEndDate;

  @ApiParam(name = UTC_PUNCH_OUT_START_DATE, example = "2020-01-01")
  private LocalDate utcPunchOutStartDate;

  @ApiParam(name = UTC_PUNCH_OUT_END_DATE, example = "2020-01-01")
  private LocalDate utcPunchOutEndDate;

  @ApiParam(name = WORK_LOCATION_PUNCH_IN_START_DATE, example = "2020-01-01")
  private LocalDate workLocationPunchInStartDate;

  @ApiParam(name = WORK_LOCATION_PUNCH_IN_END_DATE, example = "2020-01-01")
  private LocalDate workLocationPunchInEndDate;

  @ApiParam(name = WORK_LOCATION_PUNCH_OUT_START_DATE, example = "2020-01-01")
  private LocalDate workLocationPunchOutStartDate;

  @ApiParam(name = WORK_LOCATION_PUNCH_OUT_END_DATE, example = "2020-01-01")
  private LocalDate workLocationPunchOutEndDate;

  private boolean onlyMyShifts;

  private boolean skipApplyAuthorization;

  LocalDateTime getUtcPunchInStartAt() {
    return Optional.ofNullable(utcPunchInStartDate).map(LocalDate::atStartOfDay).orElse(null);
  }

  LocalDateTime getUtcPunchInEndAt() {
    return Optional.ofNullable(utcPunchInEndDate)
        .map(LocalDate::atStartOfDay)
        .map(l -> l.plusDays(1))
        .orElse(null);
  }

  LocalDateTime getUtcPunchOutStartAt() {
    return Optional.ofNullable(utcPunchOutStartDate).map(LocalDate::atStartOfDay).orElse(null);
  }

  LocalDateTime getUtcPunchOutEndAt() {
    return Optional.ofNullable(utcPunchOutEndDate)
        .map(LocalDate::atStartOfDay)
        .map(l -> l.plusDays(1))
        .orElse(null);
  }

  LocalDateTime getWorkLocationPunchInStartAt() {
    return Optional.ofNullable(workLocationPunchInStartDate)
        .map(LocalDate::atStartOfDay)
        .orElse(null);
  }

  LocalDateTime getWorkLocationPunchInEndAt() {
    return Optional.ofNullable(workLocationPunchInEndDate)
        .map(LocalDate::atStartOfDay)
        .map(l -> l.plusDays(1))
        .orElse(null);
  }

  LocalDateTime getWorkLocationPunchOutStartAt() {
    return Optional.ofNullable(workLocationPunchOutStartDate)
        .map(LocalDate::atStartOfDay)
        .orElse(null);
  }

  LocalDateTime getWorkLocationPunchOutEndAt() {
    return Optional.ofNullable(workLocationPunchOutEndDate)
        .map(LocalDate::atStartOfDay)
        .map(l -> l.plusDays(1))
        .orElse(null);
  }
}
