package com.everee.api.timeclock;

import com.everee.api.company.CompanyService;
import com.everee.api.company.configurations.TimeTrackingMode;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.payperiod.PayPeriodService;
import com.everee.api.timeClassification.LegacyWorkedShiftClassificationService;
import com.everee.api.timeIntervals.Interval;
import com.everee.api.timeclock.bulk.BulkTimeCardEntry;
import com.everee.api.timeclock.bulk.BulkTimeCardEntryRepository;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Collection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Slf4j
@Primary
@Service
@RequiredArgsConstructor
public class HourlyTimeRecordLookupService {
  private final BulkTimeCardEntryRepository bulkTimeCardEntryRepository;
  private final CompanyService companyService;
  private final PayPeriodService payPeriodService;
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final LegacyWorkedShiftClassificationService legacyWorkedShiftClassificationService;

  public WorkWeekTimeWorked getTimeWorkedInWorkWeek(Long employeeId, LocalDate workWeekStart) {
    var employee = detailedEmployeeLookupService.getEmployee(employeeId);
    var company = companyService.getCompany(employee.getCompanyId());

    if (!company
        .getTimeTrackingConfiguration()
        .getTimeTrackingMode()
        .equals(TimeTrackingMode.TIME_CLOCK)) {
      throw new InvalidRequestException(
          "Time tracking is not enabled for this company and is required for workweek-based queries");
    }

    if (workWeekStart.getDayOfWeek() != DayOfWeek.SUNDAY) {
      throw new InvalidRequestException(
          "Parameter invalid: 'for-work-week' must be the first day of a work week");
    }

    var workWeek = WorkWeek.forDate(workWeekStart);
    var timeWorked = new WorkWeekTimeWorked(workWeek);

    var workedShiftDurations =
        legacyWorkedShiftClassificationService.getClassifiedTimeWorkedDurationsPayableInRange(
            employee, workWeek.toInterval());

    var regularTimeWorked = workedShiftDurations.getRegularTimeWorked();
    var overtimeWorked = workedShiftDurations.getOvertimeWorked();
    var doubleTimeWorked = workedShiftDurations.getDoubleTimeWorked();

    timeWorked.setRegularTimeWorked(regularTimeWorked);
    timeWorked.setOvertimeWorked(overtimeWorked);
    timeWorked.setDoubleTimeWorked(doubleTimeWorked);

    return timeWorked;
  }

  public WorkDayTimeWorked getTimeWorkedInWorkDay(Long employeeId, LocalDate date) {
    var employee = detailedEmployeeLookupService.getEmployee(employeeId);
    var company = companyService.getCompany(employee.getCompanyId());

    if (!company
        .getTimeTrackingConfiguration()
        .getTimeTrackingMode()
        .equals(TimeTrackingMode.TIME_CLOCK)) {
      throw new InvalidRequestException(
          "Time tracking is not enabled for this company and is required for workday-based queries");
    }

    var timeWorked = new WorkDayTimeWorked(date, employeeId);

    var workedShiftDurations =
        legacyWorkedShiftClassificationService.getClassifiedTimeWorkedDurationsPayableInRange(
            employee, Interval.ofDay(date, company.getWorkweekConfig()));

    var regularTimeWorked = workedShiftDurations.getRegularTimeWorked();
    var overtimeWorked = workedShiftDurations.getOvertimeWorked();
    var doubleTimeWorked = workedShiftDurations.getDoubleTimeWorked();

    timeWorked.setRegularTimeWorked(regularTimeWorked);
    timeWorked.setOvertimeWorked(overtimeWorked);
    timeWorked.setDoubleTimeWorked(doubleTimeWorked);

    return timeWorked;
  }

  public DateRangeTimeWorked getTimeWorkedInDateRange(
      Long employeeId, LocalDate startDate, LocalDate endDate) {
    var employee = detailedEmployeeLookupService.getEmployee(employeeId);
    var company = companyService.getCompany(employee.getCompanyId());

    if (!company
        .getTimeTrackingConfiguration()
        .getTimeTrackingMode()
        .equals(TimeTrackingMode.TIME_CLOCK)) {
      throw new InvalidRequestException(
          "Time tracking is not enabled for this company and is required for date-range-based queries");
    }

    var timeWorked = new DateRangeTimeWorked(startDate, endDate, employeeId);
    var interval = new Interval(startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay());

    var workedShiftDurations =
        legacyWorkedShiftClassificationService.getClassifiedTimeWorkedDurationsPayableInRange(
            employee, interval);

    var regularTimeWorked = workedShiftDurations.getRegularTimeWorked();
    var overtimeWorked = workedShiftDurations.getOvertimeWorked();
    var doubleTimeWorked = workedShiftDurations.getDoubleTimeWorked();

    timeWorked.setRegularTimeWorked(regularTimeWorked);
    timeWorked.setOvertimeWorked(overtimeWorked);
    timeWorked.setDoubleTimeWorked(doubleTimeWorked);

    return timeWorked;
  }

  public DateRangeTimeWorked getTimeWorkedInPayPeriod(Long employeeId, Long payPeriodId) {
    var employee = detailedEmployeeLookupService.getEmployee(employeeId);
    var company = companyService.getCompany(employee.getCompanyId());
    var payPeriod = payPeriodService.getPayPeriod(payPeriodId);
    var payPeriodStart = payPeriod.getStartDate();
    var payPeriodEnd = payPeriod.getEndDate();

    if (company
        .getTimeTrackingConfiguration()
        .getTimeTrackingMode()
        .equals(TimeTrackingMode.TIME_CLOCK)) {
      var workedShiftDurations =
          legacyWorkedShiftClassificationService.getClassifiedTimeWorkedDurationsPayableInPayPeriod(
              employee, payPeriod);

      var timeWorked = new DateRangeTimeWorked(payPeriodStart, payPeriodEnd, employeeId);

      var regularTimeWorked = workedShiftDurations.getRegularTimeWorked();
      var overtimeWorked = workedShiftDurations.getOvertimeWorked();
      var doubleTimeWorked = workedShiftDurations.getDoubleTimeWorked();

      timeWorked.setRegularTimeWorked(regularTimeWorked);
      timeWorked.setOvertimeWorked(overtimeWorked);
      timeWorked.setDoubleTimeWorked(doubleTimeWorked);

      return timeWorked;
    }

    return bulkTimeCardEntryRepository
        .findByEmployeeIdAndPayPeriodId(employeeId, payPeriodId)
        .map(BulkTimeCardEntry::toDateRangeTimeWorked)
        .orElse(new DateRangeTimeWorked(payPeriodStart, payPeriodEnd, employeeId));
  }

  public static Duration getTotalTimeWorked(Collection<LegacyWorkedShift> shifts) {
    return shifts.stream()
        .map(LegacyWorkedShift::getEffectiveDuration)
        .reduce(Duration.ZERO, Duration::plus);
  }
}
