package com.everee.api.timeclock.overtime;

import java.time.Duration;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Transient;
import lombok.*;

@Data
@Embeddable
public class OvertimeSettings {
  @NonNull
  @Setter(AccessLevel.PRIVATE)
  @Column(name = "overtimemultiplier")
  private Double overtimeRateMultiplier = 1.5;

  @NonNull
  @Setter(AccessLevel.PRIVATE)
  @Column(name = "weeklyovertimehoursthreshold")
  private Duration weeklyOvertimeHoursThreshold = Duration.ofHours(40);

  @NonNull @Transient private Double doubleTimeRateMultiplier = 2.0;
  @NonNull @Transient private Duration dailyDoubleTimeHoursThreshold = Duration.ofHours(12);
}
