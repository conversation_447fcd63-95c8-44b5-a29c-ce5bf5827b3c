package com.everee.api.timeclock.hoursworked;

import com.everee.api.approvalgroup.ApprovalScope;
import com.everee.api.auth.annotation.AnyApproverAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.employeePosition.EmployeePositionLookup;
import com.everee.api.employeePosition.EmployeePositionLookupService;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.phase.Phased;
import com.everee.api.user.DetailedUser;
import com.everee.api.user.UserService;
import com.everee.api.user.lookup.DetailedUserLookupService;
import com.everee.api.user.lookup.UserLookup;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/hours-worked")
@RequiredArgsConstructor
public class HoursWorkedController {
  private final EmployeePositionLookupService positionLookupService;
  private final HoursWorkedLookupService hoursLookupService;
  private final DetailedUserLookupService userLookupService;
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;

  @GetMapping
  @AnyApproverAccess
  public Page<HoursWorked> listHoursWorked(
      @RequestParam(name = "employee-id") Set<Long> employeeIds,
      @RequestParam(name = "start-date") LocalDate startDate,
      @RequestParam(name = "end-date") LocalDate endDate,
      Pageable pageable) {
    var employees =
        detailedEmployeeLookupService.listAll(
            new EmployeeLookup().withIds(employeeIds).withApprovalScope(ApprovalScope.LABOR),
            Pageable.unpaged());

    return hoursLookupService.listHoursWorkedForEmployees(employees, startDate, endDate, pageable);
  }

  @GetMapping("/me")
  public HoursWorked getMyHoursWorked(
      @RequestParam(name = "start-date") LocalDate startDate,
      @RequestParam(name = "end-date") LocalDate endDate) {
    var pageable = Pageable.unpaged();
    var userId = UserService.getAuthenticatedUserId();
    // get user's employee records as page; throw if user not found
    var employees =
        userLookupService
            .findOne(new UserLookup().withIds(Set.of(userId)))
            .map(DetailedUser::getEmployees)
            .map(ArrayList::new)
            .map(PageImpl::new)
            .orElseThrow(ResourceNotFoundException::new);
    // build empty hours for most "active" employee record
    var activeEmployeeId =
        employees
            .get()
            .filter(ee -> ee.getCompanyId().equals(CompanyService.getAuthenticatedCompanyId()))
            .filter(ee -> ee.isActiveInDateRange(startDate, endDate))
            .min(Comparator.comparing(Phased::getStartDate))
            .map(DetailedEmployee::getId)
            .orElse(null);

    var hoursCollector = HoursWorked.empty(activeEmployeeId, startDate, endDate);

    if (activeEmployeeId == null) return hoursCollector;

    return hoursLookupService.listHoursWorkedForEmployees(employees, startDate, endDate, pageable)
        .stream()
        .reduce(hoursCollector, HoursWorked::combine);
  }

  @GetMapping("/summary")
  @AnyApproverAccess
  public Page<HoursWorkedSummary> listHoursSummary(
      @RequestParam(name = "work-location-date-range-start") LocalDate startDate,
      @RequestParam(name = "work-location-date-range-end") LocalDate endDate,
      @RequestParam(name = "search-term", required = false) String searchTerm,
      Pageable pageable) {
    var phaseLookup = new PhaseLookup(Set.of(Phase.ACTIVE), startDate, endDate);

    var positionLookup =
        new EmployeePositionLookup()
            .withCompanyIds(Set.of(CompanyService.getAuthenticatedCompanyId()))
            .withPayTypes(Set.of(PayType.HOURLY))
            .withPhaseLookup(phaseLookup);
    var positions = positionLookupService.listAll(positionLookup, Pageable.unpaged());
    var employeeIds =
        positions.stream().map(EmployeePosition::getEmployeeId).collect(Collectors.toSet());

    var employeeLookup =
        new EmployeeLookup()
            .withCompanyIds(Set.of(CompanyService.getAuthenticatedCompanyId()))
            .withIds(employeeIds)
            .withEmploymentTypes(Set.of(EmploymentType.EMPLOYEE))
            .withSearchTerm(searchTerm)
            .withApprovalScope(ApprovalScope.LABOR)
            .withPhaseLookup(phaseLookup);

    return detailedEmployeeLookupService
        .listAll(employeeLookup, pageable)
        .map(ee -> hoursLookupService.getHoursWorkedSummaryForRange(ee, startDate, endDate));
  }
}
