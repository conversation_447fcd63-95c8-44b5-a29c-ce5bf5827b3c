package com.everee.api.timeclock.hoursworked;

import static com.everee.api.util.DurationUtils.toApproximateHours;

import com.everee.api.money.Money;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import lombok.Value;

@Value
public class HoursWorked {
  Long employeeId;
  LocalDate startDate;
  LocalDate endDate;

  Duration regularTimePayable;
  Duration overtimePayable;
  Duration doubleTimePayable;

  Money grossEarnings;

  public BigDecimal getRegularHoursPayable() {
    return toApproximateHours(regularTimePayable);
  }

  public BigDecimal getOvertimeHoursPayable() {
    return toApproximateHours(overtimePayable);
  }

  public BigDecimal getDoubleTimeHoursPayable() {
    return toApproximateHours(doubleTimePayable);
  }

  @Deprecated(forRemoval = true)
  public BigDecimal getRegularHours() {
    return getRegularHoursPayable();
  }

  @Deprecated(forRemoval = true)
  public BigDecimal getOvertimeHours() {
    return getOvertimeHoursPayable();
  }

  @Deprecated(forRemoval = true)
  public BigDecimal getDoubleTimeHours() {
    return getDoubleTimeHoursPayable();
  }

  public BigDecimal getTotalHoursPayable() {
    return toApproximateHours(regularTimePayable.plus(overtimePayable).plus(doubleTimePayable));
  }

  public static HoursWorked combine(HoursWorked left, HoursWorked right) {
    return new HoursWorked(
        left.getEmployeeId(),
        left.getStartDate(),
        left.getEndDate(),
        left.getRegularTimePayable().plus(right.getRegularTimePayable()),
        left.getOvertimePayable().plus(right.getOvertimePayable()),
        left.getDoubleTimePayable().plus(right.getDoubleTimePayable()),
        left.getGrossEarnings().plus(right.getGrossEarnings()));
  }

  public static HoursWorked empty(Long employeeId, LocalDate startDate, LocalDate endDate) {
    return new HoursWorked(
        employeeId, startDate, endDate, Duration.ZERO, Duration.ZERO, Duration.ZERO, Money.ZERO);
  }
}
