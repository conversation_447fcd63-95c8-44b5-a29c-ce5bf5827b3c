package com.everee.api.timeclock.hoursworked;

import com.everee.api.employee.Employee;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorkedDurations;
import java.time.Duration;
import java.util.Optional;
import lombok.Data;
import lombok.NonNull;

@Data
public class HoursWorkedSummary {
  private Long employeeId;
  private String workerId;
  private String displayFullName;

  private Duration regularTimeHours;
  private Duration overtimeHours;
  private Duration doubleTimeHours;
  private Duration totalWorkedHours;
  private Duration approvedPtoHours;

  public static HoursWorkedSummary from(
      @NonNull Employee employee,
      @NonNull ClassifiedTimeWorkedDurations timeWorkedDurations,
      Duration approvedPtoHours) {
    var summary = new HoursWorkedSummary();

    summary.employeeId = employee.getId();
    summary.workerId = employee.getWorkerId();
    summary.displayFullName = employee.getDisplayFullName();

    summary.regularTimeHours = timeWorkedDurations.getRegularTimeWorked();
    summary.overtimeHours = timeWorkedDurations.getOvertimeWorked();
    summary.doubleTimeHours = timeWorkedDurations.getDoubleTimeWorked();
    summary.totalWorkedHours =
        summary.regularTimeHours.plus(summary.overtimeHours).plus(summary.doubleTimeHours);

    summary.approvedPtoHours = Optional.ofNullable(approvedPtoHours).orElse(Duration.ZERO);

    return summary;
  }

  public static HoursWorkedSummary from(
      @NonNull Employee employee,
      Duration regularTimeHours,
      Duration overtimeHours,
      Duration doubleTimeHours,
      Duration approvedPtoHours) {
    var summary = new HoursWorkedSummary();

    summary.employeeId = employee.getId();
    summary.workerId = employee.getWorkerId();
    summary.displayFullName = employee.getDisplayFullName();

    summary.regularTimeHours = Optional.ofNullable(regularTimeHours).orElse(Duration.ZERO);
    summary.overtimeHours = Optional.ofNullable(overtimeHours).orElse(Duration.ZERO);
    summary.doubleTimeHours = Optional.ofNullable(doubleTimeHours).orElse(Duration.ZERO);
    summary.totalWorkedHours =
        summary.regularTimeHours.plus(summary.overtimeHours).plus(summary.doubleTimeHours);

    summary.approvedPtoHours = Optional.ofNullable(approvedPtoHours).orElse(Duration.ZERO);

    return summary;
  }
}
