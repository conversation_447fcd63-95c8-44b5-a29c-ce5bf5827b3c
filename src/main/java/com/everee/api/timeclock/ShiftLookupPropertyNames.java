package com.everee.api.timeclock;

public abstract class ShiftLookupPropertyNames {
  public static final String ID = "id";
  public static final String EMPLOYEE_ID = "employee-id";
  public static final String COMPANY_ID = "company-id";
  public static final String APPROVAL_GROUP_ID = "approval-group-id";
  public static final String PAY_PERIOD_ID = "pay-period-id";
  public static final String VERIFIED = "verified";
  public static final String ACTIVE = "active";
  public static final String EDITABLE = "editable";
  public static final String UTC_PUNCH_IN_START_DATE = "utc-punch-in-start-date";
  public static final String UTC_PUNCH_IN_END_DATE = "utc-punch-in-end-date";
  public static final String UTC_PUNCH_OUT_START_DATE = "utc-punch-out-start-date";
  public static final String UTC_PUNCH_OUT_END_DATE = "utc-punch-out-end-date";
  public static final String WORK_LOCATION_PUNCH_IN_START_DATE =
      "work-location-punch-in-start-date";
  public static final String WORK_LOCATION_PUNCH_IN_END_DATE = "work-location-punch-in-end-date";
  public static final String WORK_LOCATION_PUNCH_OUT_START_DATE =
      "work-location-punch-out-start-date";
  public static final String WORK_LOCATION_PUNCH_OUT_END_DATE = "work-location-punch-out-end-date";
}
