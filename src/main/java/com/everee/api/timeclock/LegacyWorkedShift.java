package com.everee.api.timeclock;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.model.BaseModelV2;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorked;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorkedDurations;
import com.everee.api.timeIntervals.Interval;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.Formula;

@Data
@Table(name = "effectiveshift")
@Entity
@Deprecated
@EqualsAndHashCode(callSuper = true)
public class LegacyWorkedShift extends BaseModelV2 {

  // <editor-fold desc="internal use fields and properties" >
  @JsonIgnore private Long paymentId;
  @JsonIgnore private Long verifierId;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private boolean active;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  public LocalDateTime utcEffectivePunchInAt;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDateTime utcEffectivePunchOutAt;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDate utcEffectivePunchInDate;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDate utcEffectivePunchOutDate;

  @NonNull
  @JsonIgnore
  @OneToMany(
      mappedBy = "shiftId",
      fetch = FetchType.LAZY,
      orphanRemoval = true,
      cascade = CascadeType.ALL)
  private Set<ClassifiedTimeWorked> classifiedTimeWorked = new HashSet<>();

  // </editor-fold>

  // <editor-fold desc="exposed fields and properties" >
  @NotNull private Long companyId;
  @NotNull private Long employeeId;
  private Long approvalGroupId;
  private boolean editable = true;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDateTime workLocationEffectivePunchInAt;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDateTime workLocationEffectivePunchOutAt;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDate workLocationEffectivePunchInDate;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private LocalDate workLocationEffectivePunchOutDate;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private Duration effectiveDuration;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private boolean verified;

  private ZoneId workLocationTimeZone;
  private String punchInIpAddress;
  private String punchOutIpAddress;
  private Double punchInLatitude;
  private Double punchInLongitude;
  private Double punchInRadiusMeters;
  private Double punchOutLatitude;
  private Double punchOutLongitude;
  private Double punchOutRadiusMeters;
  private LocalDateTime verifiedAt;
  private LocalDateTime utcPunchInAt;
  private LocalDateTime utcPunchOutAt;
  private LocalDateTime utcOverridePunchInAt;
  private LocalDateTime utcOverridePunchOutAt;

  @Formula("(select p.status from payment p where p.id = paymentId)")
  @Enumerated(EnumType.STRING)
  private PaymentStatus paymentStatus;

  @ManyToOne
  @JoinColumn(name = "employeeId", insertable = false, updatable = false)
  private DetailedEmployee employee;

  @Transient private ClassifiedTimeWorkedDurations classifiedDurations;
  // </editor-fold>

  // Property overrides
  public ClassifiedTimeWorkedDurations getClassifiedDurations() {
    return Optional.ofNullable(classifiedDurations)
        .orElseGet(() -> ClassifiedTimeWorkedDurations.fromTimeWorked(getClassifiedTimeWorked()));
  }

  public Duration getElapsedDuration() {
    return active
        ? Duration.between(utcEffectivePunchInAt, LocalDateTime.now())
        : getEffectiveDuration();
  }

  // Helper functions
  public Interval toUtcInterval() {
    if (active) {
      throw new IllegalStateException("Cannot make interval for active shift; end is unknown");
    }

    return new Interval(getUtcEffectivePunchInAt(), getUtcEffectivePunchOutAt());
  }

  @PreUpdate
  @PrePersist
  private void applyTruncation() {
    utcPunchInAt = safelyTruncateToMinute(utcPunchInAt);
    utcPunchOutAt = safelyTruncateToMinute(utcPunchOutAt);
    utcOverridePunchInAt = safelyTruncateToMinute(utcOverridePunchInAt);
    utcOverridePunchOutAt = safelyTruncateToMinute(utcOverridePunchOutAt);
  }

  private static LocalDateTime safelyTruncateToMinute(LocalDateTime value) {
    return Optional.ofNullable(value).map(v -> v.truncatedTo(ChronoUnit.MINUTES)).orElse(null);
  }
}
