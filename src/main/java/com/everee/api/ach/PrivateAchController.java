package com.everee.api.ach;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;

import com.everee.api.auth.annotation.EvereeAdminAccess;
import com.everee.api.payment.PaymentPublishingService;
import com.everee.api.payment.distribution.PaymentDistributionService;
import java.io.IOException;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@EvereeAdminAccess
@RestController
@RequestMapping("/api-private/v1/ach")
@RequiredArgsConstructor
public class PrivateAchController {
  private final AchService achService;
  private final AchInvoiceService achInvoiceService;
  private final PaymentDistributionService paymentDistributionService;
  private final PaymentPublishingService paymentPublishingService;

  @GetMapping("/files")
  public Page<AchFile> listAchFiles(
      @RequestParam(name = "id", required = false) List<Long> ids,
      @RequestParam(name = "company-id", required = false) Long companyId,
      @RequestParam(name = "for-date", required = false) LocalDate forDate,
      @RequestParam(name = "status", required = false) AchFileStatus status,
      Pageable pageable) {
    return achService.listFiles(pageable, ids, companyId, forDate, status);
  }

  @PostMapping("/files")
  public ResponseEntity<AchFile> createAchFile(@Valid @RequestBody AchFile achFile) {
    achFile = achService.createAchFile(achFile);

    var location =
        ServletUriComponentsBuilder.fromCurrentRequestUri()
            .path("/{achFileId}")
            .build(achFile.getId());

    return ResponseEntity.created(location).body(achFile);
  }

  @PostMapping("/files/daily-results-file")
  public ResponseEntity<String> saveDailyResultsFile(@RequestParam MultipartFile doc) {
    var url = achService.saveDailyResultsFile(doc);
    return ResponseEntity.ok().body(url);
  }

  @GetMapping("/files/{achFileId}")
  public AchFile getAchFile(@PathVariable Long achFileId) {
    return achService.getAchFile(achFileId);
  }

  @GetMapping(path = "/files/{achFileId}/invoice", produces = MediaType.APPLICATION_PDF_VALUE)
  public AchInvoice generateInvoice(@PathVariable Long achFileId) throws IOException {
    return achInvoiceService.generateInvoice(achService.getAchFile(achFileId));
  }

  @PutMapping("/files/{achFileId}")
  public AchFile updateAchFile(@PathVariable Long achFileId, @Valid @RequestBody AchFile achFile) {
    var updatedAchFile = achService.updateAchFile(achFileId, achFile, true);
    if (AchFileStatus.PAID.equals(achFile.getStatus())) {
      var paidPaymentIds = achService.getPaidPaymentIds(achFile);
      paymentPublishingService.publishAchMarkedAsPaid(achFile, paidPaymentIds);
    }
    return updatedAchFile;
  }

  @PutMapping("/files/v2/{achFileId}")
  public AchFile updateAchFileSimple(
      @PathVariable Long achFileId, @Valid @RequestBody AchFile achFile) {
    return achService.updateAchFile(achFileId, achFile, false);
  }

  @DeleteMapping("/files/{achFileId}")
  public ResponseEntity<AchFile> deleteAchFile(@PathVariable Long achFileId) {
    achService.deleteAchFile(achFileId);
    return ResponseEntity.noContent().build();
  }

  @PostMapping("/files/{achFileId}/submit")
  public void submitAchFile(@PathVariable Long achFileId) throws IOException {
    achService.submitAchFile(achService.getAchFile(achFileId));
  }

  @PostMapping("/files/{achFileId}/doc")
  public AchFile addDocToAchFile(
      @PathVariable Long achFileId,
      @RequestParam(value = "doc", required = false) MultipartFile doc)
      throws IOException {
    return achService.appendFiles(achFileId, doc);
  }

  @PostMapping("/files/{achFileId}/finalize")
  public AchFile finalizeAchFile(
      @PathVariable Long achFileId,
      @RequestParam(value = "doc", required = false) MultipartFile doc,
      @RequestParam(value = "paymentIds", required = false) Set<Long> paymentIds)
      throws IOException {
    return achService.finalizeAchFile(achFileId, doc, paymentIds);
  }

  @PostMapping("/files/{achFileId}/mark-errors")
  public void markAchFileErrors(@PathVariable Long achFileId) throws IOException {
    achService.markErrors(achService.getAchFile(achFileId));
  }

  @PostMapping("/files/{achFileId}/mark-invoice-errors")
  public void markAchInvoiceErrors(@PathVariable Long achFileId) throws IOException {
    achService.markInvoiceErors(achFileId);
  }

  @DeleteMapping("/files/{achFileId}/doc")
  public AchFile removeFileDocuments(@PathVariable Long achFileId) {
    return achService.removeDocuments(achFileId);
  }

  @PostMapping("/generate")
  public ResponseEntity<?> createAchFiles(
      @RequestParam(name = "company-id", required = false) Long companyId) {
    achService.requestCreateAchFiles(companyId);
    return ResponseEntity.noContent().build();
  }

  @PostMapping("/generate-external")
  public ResponseEntity<?> generateExternalDistributions(
      @RequestParam(name = "company-id") Long companyId) {
    achService.requestPrepareExternalDistributions(companyId);
    return ResponseEntity.noContent().build();
  }

  @GetMapping("/batches")
  public Page<AchFileBatch> listAchFileBatches(
      @RequestParam(name = "id", required = false) List<Long> ids,
      @RequestParam(name = "pay-period-id", required = false) Long payPeriodId,
      @RequestParam(name = "ach-file-id", required = false) Long achFileId,
      Pageable pageable) {
    return achService.listFileBatches(pageable, ids, payPeriodId, achFileId);
  }

  @GetMapping("/batches/{achFileBatchId}")
  public AchFileBatch getAchFileBatch(@PathVariable Long achFileBatchId) throws IOException {
    return achService.getAchFileBatch(achFileBatchId);
  }

  @PostMapping("/batches")
  public ResponseEntity<AchFileBatch> createAchFileBatch(@RequestBody AchFileBatch achFileBatch) {
    achFileBatch = achService.createAchFileBatch(achFileBatch);

    var location =
        ServletUriComponentsBuilder.fromCurrentRequestUri()
            .path("/{achFileId}")
            .build(achFileBatch.getAchFileId());
    return ResponseEntity.created(location).body(achFileBatch);
  }

  @PutMapping("/batches/{achFileBatchId}")
  public AchFileBatch updateAchFileBatch(
      @PathVariable Long achFileBatchId, @RequestBody AchFileBatch achFileBatch) {
    return achService.updateAchFileBatch(achFileBatchId, achFileBatch);
  }

  @PostMapping("/batches/{achFileBatchId}/company-distribution/{companyDistributionId}")
  public void updatePaymentsDistributionsInBatchWithCompanyDistribution(
      @PathVariable Long achFileBatchId, @PathVariable Long companyDistributionId) {
    paymentDistributionService.updatePaymentsDistributionsInBatchWithCompanyDistribution(
        achFileBatchId, companyDistributionId);
  }

  @PutMapping("/batches/{achFileBatchId}/funding-failed")
  public AchFileBatch updateAchFileBatchWithFundingFailed(@PathVariable Long achFileBatchId) {
    return achService.markAchFileBatchFundingFailed(achFileBatchId);
  }

  @PutMapping("/batches/{achFileBatchId}/funded")
  public AchFileBatch markPrefundedPaymentsInBatchFunded(@PathVariable Long achFileBatchId) {
    return achService.markPrefundedPaymentsFunded(achFileBatchId);
  }

  @GetMapping("/records/{achFileRecordId}")
  public AchFileRecord getAchFileRecord(@PathVariable Long achFileRecordId) {
    return achService.getAchFileRecord(achFileRecordId);
  }

  @PostMapping("records")
  public ResponseEntity<AchFileRecord> createAchFileRecord(
      @RequestBody AchFileRecord achFileRecord) {
    achFileRecord = achService.createAchFileRecord(achFileRecord);

    var location =
        ServletUriComponentsBuilder.fromCurrentRequestUri()
            .path("/{achFileId}")
            .build(achFileRecord.getAchFileId());
    return ResponseEntity.created(location).body(achFileRecord);
  }

  @PutMapping("/records/{achFileRecordId}")
  public AchFileRecord updateAchFileRecord(
      @PathVariable Long achFileRecordId, @RequestBody AchFileRecord achFileRecord) {
    return achService.updateAchFileRecord(achFileRecordId, achFileRecord);
  }

  @GetMapping("/can-generate-ach")
  public boolean canGenerateAch(
      @RequestParam(name = "company-id", required = true) Long companyId,
      @RequestParam(name = "context-timestamp", required = false)
          @DateTimeFormat(iso = ISO.DATE_TIME)
          ZonedDateTime contextTimestamp) {
    return achService.canGenerateAchAtTime(
        companyId, Optional.ofNullable(contextTimestamp).orElse(ZonedDateTime.now(UTC_ZONE_ID)));
  }

  @GetMapping("/is-within-ach-window")
  public boolean isWithinAchWindow(
      @RequestParam(name = "company-id", required = true) Long companyId,
      @RequestParam(name = "context-timestamp", required = false)
          @DateTimeFormat(iso = ISO.DATE_TIME)
          ZonedDateTime contextTimestamp) {
    return achService.isWithinAchWindow(
        companyId, Optional.ofNullable(contextTimestamp).orElse(ZonedDateTime.now(UTC_ZONE_ID)));
  }

  // LIME-131 - Get the AchFileBatch for the target settlement date, amount, and batch type
  @GetMapping("/batches/find")
  public List<AchFileBatch> findAchFileBatches(
      @RequestParam(name = "target-settlement-date", required = true) LocalDate date,
      @RequestParam(name = "batch-type", required = true) String batchType) {
    return achService.findAchFileBatches(date, batchType);
  }
}
