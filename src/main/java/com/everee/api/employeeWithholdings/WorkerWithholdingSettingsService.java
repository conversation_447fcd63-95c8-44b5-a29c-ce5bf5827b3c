package com.everee.api.employeeWithholdings;

import static com.everee.api.employeeWithholdings.EmployeeWithholdingsVersion.V2019;
import static com.everee.api.employeeWithholdings.EmployeeWithholdingsVersion.V2020;

import com.everee.api.form.FormService;
import com.everee.api.form.employee.EmployeeFormLookup;
import com.everee.api.form.employee.EmployeeFormLookupService;
import com.everee.api.model.MaritalStatus;
import com.everee.api.money.Money;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.symmetry.MiscParameterNames;
import com.everee.api.tax.TaxType;
import java.util.EnumSet;
import java.util.Objects;
import java.util.Set;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class WorkerWithholdingSettingsService {
  private final EmployeeFormLookupService employeeFormLookupService;
  private final FormService formService;

  public Page<EmployeeWithholdings> getEmployeeWithholdings(
      Long employeeId, Long companyId, PhaseLookup phaseLookup, Pageable pageable) {
    return getEmployeeWithholdings(Set.of(employeeId), companyId, phaseLookup, pageable);
  }

  public Page<EmployeeWithholdings> getEmployeeWithholdings(
      Set<Long> employeeIds, Long companyId, PhaseLookup phaseLookup, Pageable pageable) {
    var w42020Form = formService.get2020w4Form();
    var w42019Form = formService.get2019w4Form();

    var lookup =
        new EmployeeFormLookup()
            .setPhaseLookup(phaseLookup)
            .setFormIds(Set.of(w42020Form.getId(), w42019Form.getId()))
            .setEmployeeIds(employeeIds);

    if (companyId != null) {
      lookup = lookup.setCompanyIds(Set.of(companyId));
    }

    return employeeFormLookupService
        .listAll(lookup, pageable)
        .map(
            employeeForm -> {
              var withholdings = new EmployeeWithholdings();
              withholdings.setEmployeeId(employeeForm.getEmployeeId());
              withholdings.setStartDate(employeeForm.getStartDate());
              withholdings.setEndDate(employeeForm.getEndDate());
              withholdings.setMigrated(true);

              var is2020Form = Objects.equals(employeeForm.getFormId(), w42020Form.getId());
              var form = is2020Form ? w42020Form : w42019Form;
              var formVersion = is2020Form ? V2020 : V2019;

              withholdings.setWithholdingsVersion(formVersion);

              var response = employeeForm.getFormResponse();

              if (response == null) return withholdings;

              if (Objects.equals(
                  "TRUE",
                  response.getAnswerString(
                      form,
                      form.getFormQuestionByName(MiscParameterNames.EXEMPT.getSymmetryName())))) {
                withholdings.setDeferredTaxTypes(EnumSet.of(TaxType.FIT));
              }

              var maritalStatusAbbreviation =
                  response.getAnswerString(
                      form,
                      form.getFormQuestionByName(
                          MiscParameterNames.FILINGSTATUS.getSymmetryName()));

              if (StringUtils.isNotBlank(maritalStatusAbbreviation)) {
                withholdings.setMaritalStatus(
                    MaritalStatus.fromAbbreviation(
                        maritalStatusAbbreviation, formVersion.getAllowedMaritalStatuses()));
              } else {
                withholdings.setMaritalStatus(MaritalStatus.SINGLE);
              }

              if (is2020Form) {
                withholdings.setHaveExactlyTwoJobs(
                    toBoolean(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.TWO_JOBS.getSymmetryName()))));
                withholdings.setExtraWithholdingAmount(
                    toMoney(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.EXTRA_WITHHOLDING.getSymmetryName()))));
                withholdings.setDeductionsAmount(
                    toMoney(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.DEDUCTIONS.getSymmetryName()))));
                withholdings.setOtherIncomeAmount(
                    toMoney(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.OTHER_INCOME.getSymmetryName()))));
                withholdings.setCountOfChildren(
                    toLong(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.QUALIFYING_DEPENDANTS.getSymmetryName()))));
                withholdings.setCountOfOtherDependents(
                    toLong(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.OTHER_DEPENDANTS.getSymmetryName()))));
              } else {
                withholdings.setFederalAllowances(
                    toInteger(
                        response.getAnswerString(
                            form,
                            form.getFormQuestionByName(
                                MiscParameterNames.TOTALALLOWANCES.getSymmetryName()))));
              }
              return withholdings;
            });
  }

  public Page<EmployeeWithholdings> getEmployeeWithholdingsUnsafe(
      Long employeeId, PhaseLookup phaseLookup, Pageable pageable) {
    return getEmployeeWithholdings(employeeId, null, phaseLookup, pageable);
  }

  private Boolean toBoolean(String value) {
    if (value == null) return Boolean.FALSE;
    return Boolean.valueOf(value);
  }

  private Money toMoney(String value) {
    if (value == null) return Money.ZERO;
    return Money.valueOf(value);
  }

  private Integer toInteger(String value) {
    if (value == null) return 0;
    return Integer.valueOf(value);
  }

  private Long toLong(String value) {
    if (value == null) return 0L;
    return Long.valueOf(value);
  }
}
