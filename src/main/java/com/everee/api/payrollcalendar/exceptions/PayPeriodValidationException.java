package com.everee.api.payrollcalendar.exceptions;

import com.everee.api.exception.ValidationException;
import com.everee.api.exception.model.ValidationFailure;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

@Getter
@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class PayPeriodValidationException extends ValidationException {

    public PayPeriodValidationException(String message) {
        super(message);
    }

    public PayPeriodValidationException(List<ValidationFailure> errors) {
        super("payPeriod validation errors", errors);
    }

    public PayPeriodValidationException(String message, List<ValidationFailure> errors) {
        super(message, errors);
    }
}
