package com.everee.api.payrollcalendar.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class InvalidPayrollCalendarRequestException extends RuntimeException {

    private static final long serialVersionUID = -6386002102073130647L;

    public InvalidPayrollCalendarRequestException() {
    super("Invalid Payroll Calendar Request");
  }

  public InvalidPayrollCalendarRequestException(String message) {
    super(message);
  }
}
