package com.everee.api.payrollcalendar.processors.map;

import com.everee.api.exception.DateRangeEndBeforeStartException;
import com.everee.api.exception.PipelineValidationException;
import com.everee.api.payrollcalendar.processors.context.EditPayPeriodContext;
import com.everee.api.pipeline.Process;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EditPayPeriodStartEndDate implements Process<EditPayPeriodContext> {

  @Override
  public void process(EditPayPeriodContext context) {
    var period = context.getDraftPayPeriod();

    if (period == null) {
      throw new PipelineValidationException();
    }

    if (context.getStartDate() != null) {
      period.setAdjustedStartDate(context.getStartDate());
    }

    if (context.getEndDate() != null) {
      period.setAdjustedEndDate(context.getEndDate());
    }

    if (!period.getAssignedEndDate().isAfter(period.getAssignedStartDate())) {
      throw new DateRangeEndBeforeStartException();
    }

    period.setAdjustedByUserId(UserService.getAuthenticatedUserId());

  }
}
