package com.everee.api.payrollcalendar.processors.map;

import com.everee.api.payrollcalendar.models.PayrollCalendarStatus;
import com.everee.api.payrollcalendar.processors.context.CreateCalendarContext;
import com.everee.api.pipeline.Process;
import org.springframework.stereotype.Component;

import static com.everee.api.payrollcalendar.mappers.PayrollCalendarMapper.MAPPER;

@Component
public class MapToDraftCalendar implements Process<CreateCalendarContext> {

    @Override
    public void process(CreateCalendarContext context) {
        var calendar = MAPPER.toEntity(context);

        // Note this will be replaced by a name supplied by the user when companies can have multiple calendars
        calendar.setName("Payroll Calendar");
        calendar.setStatus(PayrollCalendarStatus.DRAFT);
        calendar.addDraftPeriods(context.getDraftPeriods());

        context.setCalendar(calendar);
    }
}
