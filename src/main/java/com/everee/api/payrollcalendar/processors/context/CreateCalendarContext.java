package com.everee.api.payrollcalendar.processors.context;

import com.everee.api.payment.PaymentFundingType;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payrollcalendar.models.PayFrequency;
import com.everee.api.payrollcalendar.models.PayPeriodDTO;
import com.everee.api.payrollcalendar.models.PayrollCalendar;
import com.everee.api.payrollcalendar.models.PayrollCalendarDraftPeriod;
import com.everee.api.user.CoreUser;
import com.everee.api.user.UserService;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@Accessors(chain = true)
public class CreateCalendarContext extends CalendarContextBase implements ICompanyFunding {
    private long companyId;
    private PaymentFundingType paymentFundingType;

    private Set<LocalDate> holidays;

    private PayFrequency frequency;
    private PayPeriodDTO firstPayPeriod;
    private PayPeriodDTO secondPayPeriod;
    private boolean payBeforeBankClosure;

    private PayPeriod latestPayPeriod;

    private long calendarId;
    private PayrollCalendar calendar;

    private List<PayrollCalendarDraftPeriod> draftPeriods;

    public CoreUser getUser(){
        return UserService.getAuthenticatedUser();
    }

    @Override
    public int getForYear() {
        return firstPayPeriod.getPayDate().getYear();
    }

    @Override
    public LocalDate getIntendedStartDate() {
        return firstPayPeriod.getStartDate();
    }
}
