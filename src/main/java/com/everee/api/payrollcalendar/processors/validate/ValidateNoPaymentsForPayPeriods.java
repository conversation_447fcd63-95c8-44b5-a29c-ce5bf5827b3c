package com.everee.api.payrollcalendar.processors.validate;

import com.everee.api.exception.model.ValidationFailure;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payperiod.PayPeriodRepository;
import com.everee.api.payrollcalendar.exceptions.PayPeriodValidationException;
import com.everee.api.payrollcalendar.models.PayrollCalendarExceptionCode;
import com.everee.api.payrollcalendar.processors.context.ICompanyPayPeriods;
import com.everee.api.pipeline.Process;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ValidateNoPaymentsForPayPeriods implements Process<ICompanyPayPeriods> {

    private final PayPeriodRepository payPeriodRepository;
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    @Override
    public void process(ICompanyPayPeriods context) {
        var intendedStartDate = context.getIntendedStartDate();
        var currentAndFuturePayPeriod = context.getCurrentAndFuturePayPeriodsForCompany();

        var validationErrors = new ArrayList<ValidationFailure>();

        if (currentAndFuturePayPeriod == null) {
            return;
        }

        var currentAndFuturePayPeriodIds = currentAndFuturePayPeriod.stream().filter(x -> !x.getStartDate().isBefore(intendedStartDate)).map(PayPeriod::getId).collect(Collectors.toList());

        if (currentAndFuturePayPeriodIds.isEmpty()) {
            return;
        }

        var payPeriodsHavePayments = payPeriodRepository.filterPayPeriodsWithPayments(currentAndFuturePayPeriodIds);

        if (payPeriodsHavePayments.isEmpty()) {
            return;
        }

        payPeriodsHavePayments.forEach(payPeriod -> addValidationError(validationErrors, payPeriod.getStartDate()));

        throw new PayPeriodValidationException("The calendar cannot be published because payments exist for the following pay periods:", validationErrors);
    }

          private HashMap<String, Object> buildErrorArgs(LocalDate startDate){
              HashMap<String, Object> errorArgs = new HashMap<>();
              errorArgs.put("message", "Pay Period with start date:");
              errorArgs.put("startDate", startDate.format(dateFormatter));
              return errorArgs;
         }

          private void addValidationError(List<ValidationFailure> validationResponse, LocalDate startDate) {
              var errorArgs = buildErrorArgs(startDate);
              validationResponse.add(new ValidationFailure(PayrollCalendarExceptionCode.PAY_PERIOD_HAS_PAYMENT.getCode(), errorArgs));
          }
}
