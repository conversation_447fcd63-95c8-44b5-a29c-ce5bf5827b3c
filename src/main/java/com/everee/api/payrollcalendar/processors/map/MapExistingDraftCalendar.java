package com.everee.api.payrollcalendar.processors.map;

import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payrollcalendar.processors.context.EditCalendarContext;
import com.everee.api.pipeline.Process;
import org.springframework.stereotype.Component;


@Component
public class MapExistingDraftCalendar implements Process<EditCalendarContext> {

    @Override
    public void process(EditCalendarContext context) {
        var calendar = context.getCalendar();

        calendar
                .setPayBeforeBankClosure(context.isPayBeforeBankClosure())
                .setFirstPayDate(context.getFirstPayPeriod().getPayDate())
                .setFirstStartDate(context.getFirstPayPeriod().getStartDate())
                .setPayPeriodType(PayPeriodType.valueOf(context.getFrequency().toString()));

        if(context.getSecondPayPeriod() != null){
            calendar.setSecondPayDate(context.getSecondPayPeriod().getPayDate())
                    .setSecondStartDate(context.getSecondPayPeriod().getStartDate());
        }
        else {
            calendar.setSecondPayDate(null)
                    .setSecondStartDate(null);
        }

        calendar.addDraftPeriods(context.getDraftPeriods());
    }
}
