package com.everee.api.payrollcalendar.processors.validate;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.payrollcalendar.processors.context.CalendarContextBase;
import com.everee.api.pipeline.Process;
import org.springframework.stereotype.Component;

@Component
public class ValidateIsInEditMode implements Process<CalendarContextBase> {

    @Override
    public void process(CalendarContextBase context) {
        var calendar = context.getCalendar();

        if (!calendar.isInEditMode()){
            throw new InvalidRequestException("Calendar not in edit mode");
        }

    }
}
