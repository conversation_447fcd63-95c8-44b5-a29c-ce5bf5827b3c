package com.everee.api.payrollcalendar.processors.persist;
import com.everee.api.company.CompanyService;
import com.everee.api.payrollcalendar.models.PayrollCalendar;
import com.everee.api.payrollcalendar.processors.context.CloneCalendarContext;
import com.everee.api.pipeline.Process;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.SerializationUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class ClonePayrollCalendar implements Process<CloneCalendarContext> {

    private final CompanyService companyService;

    @Override
    public void process(CloneCalendarContext input) {

        var company = companyService.getCompany(input.getCompanyId());

        var calendar = input.getCalendar();

        var clone = (PayrollCalendar) SerializationUtils.clone(calendar);

        var userId = UserService.getAuthenticatedUserId();

        clone.setId(null)
                .setCompanyId(company.getId())
                .setUpdatedByUserId(userId)
                .setLastPublishedAt(LocalDateTime.now())
                .setLastPublishedByUserId(userId);

        clone.getPayPeriods()
                    .forEach(period -> {
            period.setId(null);
            period.setPayrollCalendar(clone);
            period.setCompanyId(company.getId());
            period.setCreatedByUserId(userId);
            period.setUpdatedByUserId(userId);
            period.setPayrollCalendar(clone);
        });

        // only include periods that are after the latest active period
        clone.getPayPeriods().removeIf(x -> input.getLatestPayPeriod() != null && x.getStartDate().isBefore(input.getLatestPayPeriod().getEndDate()));

        input.setCalendar(clone);
    }
}
