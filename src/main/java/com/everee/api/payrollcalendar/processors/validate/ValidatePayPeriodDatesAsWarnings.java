package com.everee.api.payrollcalendar.processors.validate;


import com.everee.api.payrollcalendar.models.PayrollCalendarError;
import com.everee.api.payrollcalendar.models.PayrollCalendarExceptionCode;
import com.everee.api.payrollcalendar.processors.context.ValidateCalendarContext;
import com.everee.api.pipeline.Process;
import com.google.logging.type.LogSeverity;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;

@Component
public class ValidatePayPeriodDatesAsWarnings implements Process<ValidateCalendarContext> {

    @Override
    public void process(ValidateCalendarContext context) {

        var warnings = new ArrayList<PayrollCalendarError>();
        var initialPayPeriods = context.getInitialPayPeriods();


        var isPayDateOrSubmissionCutOffBeforeEndDate = initialPayPeriods.stream().anyMatch(periodDTO ->
                periodDTO.getSubmissionCutoff().isBefore(periodDTO.getEndDate()) ||
                        periodDTO.getPayDate().isBefore(periodDTO.getEndDate()));

        if (isPayDateOrSubmissionCutOffBeforeEndDate) {

            warnings.add(new PayrollCalendarError(PayrollCalendarExceptionCode.SUBMISSION_CUTOFF_OR_PAY_DATE_BEFORE_PAY_PERIOD_END_DATE.getCode(),
                    "Your submission cutoff/pay date falls before the end of your pay period. If this sounds accurate, please continue. If not, feel free to make any necessary adjustments.", LogSeverity.WARNING));
        }

        var isPayPeriodInNexYear = initialPayPeriods.stream().anyMatch(periodDTO ->
                periodDTO.getStartDate().getYear() > LocalDate.now().getYear() ||
                        periodDTO.getEndDate().getYear() > LocalDate.now().getYear());

        if (isPayPeriodInNexYear) {

            warnings.add(new PayrollCalendarError(PayrollCalendarExceptionCode.PAY_PERIOD_START_DATE_OR_END_DATE_IS_ON_NEXT_YEAR.getCode(),
                    "There is at least one period where the start or end date is in the next year.", LogSeverity.WARNING));
        }


        context.setWarnings(warnings);
    }
}
