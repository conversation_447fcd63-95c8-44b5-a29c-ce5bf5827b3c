package com.everee.api.payrollcalendar.processors.validate;

import com.everee.api.payrollcalendar.ValidatePayPeriodGapsConstants;
import com.everee.api.payrollcalendar.models.PayPeriodValidationResult;
import com.everee.api.payrollcalendar.models.PayPeriodValidationType;
import com.everee.api.payrollcalendar.models.PayrollCalendarDraftPeriod;
import com.everee.api.payrollcalendar.processors.context.EditPayPeriodContext;
import com.everee.api.pipeline.Process;
import com.everee.api.util.DateUtil;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Component
public class ValidatePayPeriodGaps implements Process<EditPayPeriodContext> {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    @Override
    public void process(EditPayPeriodContext context) {
        var validationResults = new ArrayList<PayPeriodValidationResult>();
        context.setValidationResults(validationResults); // Clear previous errors

        if (context.getCalendar() == null || context.getCalendar().getDraftPayPeriods() == null || context.getDraftPayPeriod() == null) {
            return;
        }
        var payPeriods = context.getCalendar().getDraftPayPeriods(); // contains Future PayPeriod

        // Sorting by ID ensures that the pay periods are in the correct order for validation,
        // as the user may reorder pay periods by changing their start dates.
        payPeriods.sort(Comparator.comparing(PayrollCalendarDraftPeriod::getId));

        var current = context.getDraftPayPeriod(); // the currently edited PayPeriod
        var index = payPeriods.indexOf(current);

        if (index == 0 && context.getLatestPayPeriod() != null) {
            addValidationResults(validationResults, validateFirstPayPeriodAgainstLatest(context));
        }

        if (index > 0) {
            var previous = payPeriods.get(index - 1);
            addValidationResults(validationResults, validateAgainstPreviousPayPeriod(previous, context));
        }

        if (index < payPeriods.size() - 1) {
            var next = payPeriods.get(index + 1);
            addValidationResults(validationResults, validateAgainstNextPayPeriod(next, context));
        }
    }

    private List<PayPeriodValidationResult> validateFirstPayPeriodAgainstLatest(EditPayPeriodContext context) {
        var results = new ArrayList<PayPeriodValidationResult>();
        var latestEnd = context.getLatestPayPeriod().getEndDate();
        var currentStart = context.getDraftPayPeriod().getAssignedStartDate();

        if (latestEnd != null && currentStart != null && !DateUtil.isNextDay(latestEnd, currentStart)) {
            results.add(new PayPeriodValidationResult(
                    context.getPayPeriodId(),
                    PayPeriodValidationType.START_DATE,
                    String.format(ValidatePayPeriodGapsConstants.PAY_PERIOD_GAP_ERROR, latestEnd.format(dateFormatter))
            ));
        }
        if (latestEnd != null && currentStart != null && !DateUtil.isNextDay(latestEnd, currentStart)) {
            results.add(new PayPeriodValidationResult(
                    context.getLatestPayPeriod().getId(),
                    PayPeriodValidationType.END_DATE,
                    String.format(ValidatePayPeriodGapsConstants.PAY_PERIOD_GAP_ERROR_NEXT, currentStart.format(dateFormatter))
            ));
        }
        return results;
    }

    private List<PayPeriodValidationResult> validateAgainstPreviousPayPeriod(PayrollCalendarDraftPeriod previous, EditPayPeriodContext context) {
        var results = new ArrayList<PayPeriodValidationResult>();
        var previousEnd = previous.getAssignedEndDate();
        var currentStart = context.getDraftPayPeriod().getAssignedStartDate();

        if (previousEnd != null && currentStart != null && !DateUtil.isNextDay(previousEnd, currentStart)) {
            results.add(new PayPeriodValidationResult(
                    context.getPayPeriodId(),
                    PayPeriodValidationType.START_DATE,
                    String.format(ValidatePayPeriodGapsConstants.PAY_PERIOD_GAP_ERROR_PREVIOUS, previousEnd.format(dateFormatter))
            ));
            results.add(new PayPeriodValidationResult(
                    previous.getId(),
                    PayPeriodValidationType.END_DATE,
                    String.format(ValidatePayPeriodGapsConstants.PAY_PERIOD_GAP_ERROR_NEXT, currentStart.format(dateFormatter))
            ));
        }

        return results;
    }

    private List<PayPeriodValidationResult> validateAgainstNextPayPeriod(PayrollCalendarDraftPeriod next, EditPayPeriodContext context) {
        var results = new ArrayList<PayPeriodValidationResult>();
        var currentEnd = context.getDraftPayPeriod().getAssignedEndDate();
        var nextStart = next.getAssignedStartDate();

        if (currentEnd != null && nextStart != null && !DateUtil.isNextDay(currentEnd, nextStart)) {
            results.add(new PayPeriodValidationResult(
                    context.getPayPeriodId(),
                    PayPeriodValidationType.END_DATE,
                    String.format(ValidatePayPeriodGapsConstants.PAY_PERIOD_GAP_ERROR_NEXT, nextStart.format(dateFormatter))
            ));
            results.add(new PayPeriodValidationResult(
                    next.getId(),
                    PayPeriodValidationType.START_DATE,
                    String.format(ValidatePayPeriodGapsConstants.PAY_PERIOD_GAP_ERROR_PREVIOUS, currentEnd.format(dateFormatter))
            ));
        }
        return results;
    }

    private void addValidationResults(List<PayPeriodValidationResult> existingResults, List<PayPeriodValidationResult> newResults) {
        if (newResults != null && !newResults.isEmpty()) {
            existingResults.addAll(newResults);
        }
    }
}
