package com.everee.api.payrollcalendar.processors.persist;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.payperiod.*;
import com.everee.api.payperiod.changeschedule.PreferenceChangeScheduleUtils;
import com.everee.api.payperiod.changeschedule.PreferenceScheduleRange;
import com.everee.api.payrollcalendar.models.PayrollCalendar;
import com.everee.api.payrollcalendar.processors.context.ArchiveCalendarContext;
import com.everee.api.payrun.PayRunRepository;
import com.everee.api.payrun.models.PayRun;
import com.everee.api.pipeline.Process;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.Optional;

import static com.everee.api.payrun.models.PayRunType.ON_CYCLE;

@Component
@RequiredArgsConstructor
public class CreateGapPayPeriodIfNeeded implements Process<ArchiveCalendarContext> {

    private final PayPeriodRepository payPeriodRepository;
    private final CompanyService companyService;
    private final PayPeriodPreferenceRepository payPeriodPreferenceRepository;
    private final PayRunRepository payRunRepository;
    private final PayPeriodService payPeriodService;
    private static final Logger log = LoggerFactory.getLogger(CreateGapPayPeriodIfNeeded.class);

    @Override
    public void process(ArchiveCalendarContext context) {
        var calendar = context.getCalendar();
        var company = companyService.getCompany(calendar.getCompanyId());
        var today = LocalDate.now();

        var latestEndDate = getLatestCalendarPayPeriodEndDate(company);
        if (latestEndDate == null) return;

        var defaultPayPeriodType = getDefaultPayPeriodType(calendar);

        var nextPrefStart = defaultPayPeriodType.getNextAvailableStartDate(today);
        var optionalGap = createGapScheduleIfNeeded(latestEndDate, nextPrefStart, company, today);
        if (optionalGap.isEmpty()) {
            return;
        }
        var gapRange = optionalGap.get();

        updatePreferences(calendar, gapRange);
        var gapPreference = createGapPayPeriodPreference(calendar, gapRange);
        createAndAddPayPeriod(calendar, gapRange, gapPreference);
    }

    private PayPeriodType getDefaultPayPeriodType(PayrollCalendar calendar) {
        var preferences = payPeriodPreferenceRepository.findByCompanyIdAndEmployeeIdIsNull(calendar.getCompanyId());
        var defaultPayPeriod = preferences.stream()
                .filter(pref -> pref.getEndDate().isEqual(PayPeriodPreference.MAX_DATE))
                .findFirst();

        // This state must not happen, if happen it means that the system is misconfigured
        if (defaultPayPeriod.isEmpty()) {
            log.warn("Default pay period preference not found for company: {}", calendar.getCompanyId());
            throw new IllegalStateException(String.format("Default pay period preference not found for company: %s", calendar.getCompanyId()));
        }
        return defaultPayPeriod.get().getPayPeriodType();
    }

    private LocalDate getLatestCalendarPayPeriodEndDate(
            DetailedCompany company) {
        var latestPayPeriod = payPeriodRepository.findLatestPayrollCalendarPayPeriod(company.getId(), PageRequest.of(0, 1));
        if (latestPayPeriod.isEmpty()) {
            log.info("No previous pay periods found for company: {}", company.getId());
            return null;
        }
        return latestPayPeriod.get(0).getEndDate();
    }

    private boolean isGapNeeded(LocalDate today, LocalDate nextPrefStart) {
        var dayBeforeNextStart = nextPrefStart.minusDays(1);
        if (!today.isBefore(dayBeforeNextStart)) {
            log.info("No gap needed as today's date is close to the next start date.");
            return false;
        }
        return true;
    }

    private boolean isTodayDirectlyBeforeNextStart(LocalDate today, LocalDate nextPrefStart) {
        var dayBeforeNextStart = nextPrefStart.minusDays(1);
        return today.isEqual(dayBeforeNextStart);
    }

    private Optional<PreferenceScheduleRange> createGapScheduleIfNeeded(LocalDate latestEndDate, LocalDate nextPrefStart, DetailedCompany company, LocalDate today) {
        var optionalGap = Optional.<PreferenceScheduleRange>empty();

        if (isGapNeeded(today, nextPrefStart)) {
            log.info("Creating gap schedule between {} and {}", latestEndDate, nextPrefStart);
            optionalGap = PreferenceChangeScheduleUtils.createGapSchedule(latestEndDate, nextPrefStart);
        } else {
            log.info("No gap schedule needed between {} and {}", latestEndDate, nextPrefStart);
        }

        // Instead of waiting one hour for the cron to create the pay period, we create it immediately
        if (isTodayDirectlyBeforeNextStart(today, nextPrefStart)) {
            try {
                payPeriodService.generatePayPeriods(company, nextPrefStart, true);
            } catch (PayPeriodException ex) { // This fails when the company phase ended or Payments are not enabled
                log.error("Failed to generate pay periods for company {}: {}", company.getId(), ex.getMessage());
            }
        }

        if (optionalGap.isEmpty()) {
            log.info("No gap schedule created between {} and {}", latestEndDate, nextPrefStart);
        }

        return optionalGap;
    }

    private void updatePreferences(PayrollCalendar calendar, PreferenceScheduleRange gapRange) {
        var preferences = payPeriodPreferenceRepository.findByCompanyIdAndEmployeeIdIsNull(calendar.getCompanyId());

        // Move the default pay period after the end date of the custom pay period the date of 2999-12-31
        preferences.stream()
                .filter(pref -> pref.getEndDate().isEqual(PayPeriodPreference.MAX_DATE))
                .findFirst()
                .ifPresent(defaultPref -> {
                    defaultPref.setStartDate(gapRange.getEndDate().plusDays(1));
                    payPeriodPreferenceRepository.save(defaultPref);
                });

        // End the previous preference if it overlaps with the gap

        preferences.stream()
                .filter(pref -> !pref.getEndDate().isBefore(gapRange.getStartDate()))
                .filter(pref -> !pref.getStartDate().isAfter(gapRange.getStartDate().minusDays(1)))
                .max(Comparator.comparing(PayPeriodPreference::getStartDate))
                .ifPresent(prev -> {
                    LocalDate newEndDate = gapRange.getStartDate().minusDays(1);
                    prev.setEndDate(newEndDate);
                    payPeriodPreferenceRepository.save(prev);
                });
    }

    private PayPeriodPreference createGapPayPeriodPreference(PayrollCalendar calendar, PreferenceScheduleRange gapRange) {
        return new PayPeriodPreference()
                .setCompanyId(calendar.getCompanyId())
                .setStartDate(gapRange.getStartDate())
                .setEndDate(gapRange.getEndDate())
                .setPayPeriodType(PayPeriodType.CUSTOM);
    }

    private void createAndAddPayPeriod(PayrollCalendar calendar, PreferenceScheduleRange gapRange, PayPeriodPreference gapPreference) {
        var userId = UserService.getAuthenticatedUserId();

        payPeriodPreferenceRepository.save(gapPreference);

        var gapPeriod = new PayPeriod()
                .setCompanyId(calendar.getCompanyId())
                .setStartDate(gapRange.getStartDate())
                .setEndDate(gapRange.getEndDate())
                .setPayPeriodType(PayPeriodType.CUSTOM)
                .setPayPeriodPreferenceId(gapPreference.getId())
                .setCreatedByUserId(calendar.getCreatedByUserId())
                .setUpdatedByUserId(calendar.getCreatedByUserId())
                .setCalculatedEndDate(gapRange.getEndDate())
                .setCalculatedStartDate(gapRange.getStartDate())
                .setCreatedByUserId(userId)
                .setUpdatedByUserId(userId);

        gapPeriod = payPeriodRepository.save(gapPeriod);

        PayRun payRun = new PayRun()
                .setCreatedByUserId(userId)
                .setUpdatedByUserId(userId)
                .setPayPeriodId(gapPeriod.getId())
                .setPayDate(gapPeriod.getPayDate())
                .setCompanyId(gapPeriod.getCompanyId())
                .setDescription(gapPeriod.getPayPeriodType().name())
                .setType(ON_CYCLE);

        payRunRepository.save(payRun);
        calendar.getPayPeriods().add(gapPeriod);
        log.info("Created and added new pay period with ID {} for company {}", gapPeriod.getId(), gapPeriod.getCompanyId());
    }
}
