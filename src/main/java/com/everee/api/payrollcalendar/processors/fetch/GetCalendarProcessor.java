package com.everee.api.payrollcalendar.processors.fetch;

import com.everee.api.exception.PipelineValidationException;
import com.everee.api.payrollcalendar.PayrollCalendarRepository;
import com.everee.api.payrollcalendar.processors.context.ICalendar;
import com.everee.api.pipeline.Process;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityNotFoundException;

@Component
@RequiredArgsConstructor
public class GetCalendarProcessor implements Process<ICalendar> {

    @Autowired
    private final PayrollCalendarRepository payrollCalendarRepository;

    @Override
    public void process(ICalendar calendar) {
        if(calendar.getCalendarId() == 0){
            throw new PipelineValidationException();
        }
        var calendarEntity = payrollCalendarRepository.findById(calendar.getCalendarId())
                .orElseThrow(() -> new EntityNotFoundException("Payroll Calendar not found"));
        calendar.setCalendar(calendarEntity);
    }
}
