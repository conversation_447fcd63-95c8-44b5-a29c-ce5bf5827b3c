package com.everee.api.payrollcalendar.processors.validate;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.payrollcalendar.models.PayrollCalendarStatus;
import com.everee.api.payrollcalendar.processors.context.CalendarContextBase;
import com.everee.api.pipeline.Process;
import org.springframework.stereotype.Component;

@Component
public class ValidateCalendarIsNotArchived implements Process<CalendarContextBase> {

    @Override
    public void process(CalendarContextBase context) {
        var calendar = context.getCalendar();

        if (calendar.getStatus().equals(PayrollCalendarStatus.ARCHIVED)){
            throw new InvalidRequestException("The Calendar has been archived");
        }
    }
}
