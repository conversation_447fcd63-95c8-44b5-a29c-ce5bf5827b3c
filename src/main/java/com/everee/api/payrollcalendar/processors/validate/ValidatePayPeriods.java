package com.everee.api.payrollcalendar.processors.validate;

import com.everee.api.bankingday.Weekdays;
import com.everee.api.exception.model.ValidationFailure;
import com.everee.api.payrollcalendar.exceptions.PayPeriodValidationException;
import com.everee.api.payrollcalendar.models.PayPeriodValidationType;
import com.everee.api.payrollcalendar.models.PayrollCalendarDraftPeriod;
import com.everee.api.payrollcalendar.models.PayrollCalendarExceptionCode;
import com.everee.api.payrollcalendar.processors.context.PublishCalendarContext;
import com.everee.api.pipeline.Process;
import com.everee.api.util.DateUtil;
import com.google.logging.type.LogSeverity;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

@Component
public class ValidatePayPeriods implements Process<PublishCalendarContext> {

  private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

  @Override
  public void process(PublishCalendarContext input) {

    if (input.getCalendar() == null || input.getCalendar().getDraftPayPeriods() == null) {
      return;
    }

    var payPeriods = input.getCalendar().getDraftPayPeriods();
    // Sort pay periods by start date
    payPeriods.sort(Comparator.comparing(PayrollCalendarDraftPeriod::getAssignedStartDate));

    // Validate the first pay period against the latest pay period
    var result = validateFirstPayPeriodAgainstLatest(input, payPeriods);
    var validationFailures = new ArrayList<>(result.getFailures());

    // Iterate over the list and find validation issues
    for (int i = 0; i < payPeriods.size() - 1; i++) {
      var currentPayPeriod = payPeriods.get(i);
      var nextPayPeriod = payPeriods.get(i + 1);

      if (!DateUtil.isNextDay(currentPayPeriod.getAssignedEndDate(), nextPayPeriod.getAssignedStartDate())) {
        validationFailures.addAll(validateGapOrOverlap(currentPayPeriod, nextPayPeriod).getFailures());
      }

      if (Weekdays.isWeekend(currentPayPeriod.getAssignedPayDate())) {
        validationFailures.addAll(validatePayDateOnWeekend(currentPayPeriod).getFailures());
      }
      if (input.getHolidays() != null && input.getHolidays().contains(currentPayPeriod.getAssignedPayDate())) {
        validationFailures.addAll(validatePayDateOnHoliday(currentPayPeriod).getFailures());
      }
    }

    // Validate the last pay period
    if (!payPeriods.isEmpty()) {
      var lastPayPeriod = payPeriods.get(payPeriods.size() - 1);

      if (Weekdays.isWeekend(lastPayPeriod.getAssignedPayDate())) {
        validationFailures.addAll(validatePayDateOnWeekend(lastPayPeriod).getFailures());
      }
      if (input.getHolidays() != null && input.getHolidays().contains(lastPayPeriod.getAssignedPayDate())) {
        validationFailures.addAll(validatePayDateOnHoliday(lastPayPeriod).getFailures());
      }
    }

    if (!validationFailures.isEmpty()) {
      throw new PayPeriodValidationException(validationFailures);
    }
  }

  private PayPeriodValidationResult validateFirstPayPeriodAgainstLatest(PublishCalendarContext input, List<PayrollCalendarDraftPeriod> payPeriods) {
    var result = new PayPeriodValidationResult();

    if (payPeriods.isEmpty()) {
      return result;
    }
    var firstPayPeriod = payPeriods.get(0);

    if (input.getLatestPayPeriod() != null && firstPayPeriod.getAdjustedStartDate() != null) {

      var latestPayPeriodEndDate = input.getLatestPayPeriod().getEndDate();
      var latestPayPeriodStartDate = input.getLatestPayPeriod().getStartDate();

      if (!firstPayPeriod.getAdjustedStartDate().equals(latestPayPeriodStartDate) && !DateUtil.isNextDay(latestPayPeriodEndDate, firstPayPeriod.getAssignedStartDate())) {
        result.addFailure(buildValidationFailure(firstPayPeriod.getId(),
          String.format("Gap/overlap between pay periods - latest pay period ends on %s", latestPayPeriodEndDate.format(dateFormatter)),
          PayrollCalendarExceptionCode.PAY_PERIOD_INVALID_START_DATE,
          PayPeriodValidationType.START_DATE,
          "priorEndDate", latestPayPeriodEndDate.format(dateFormatter)));
      }
    }

    return result;
  }

  private PayPeriodValidationResult validateGapOrOverlap(PayrollCalendarDraftPeriod current, PayrollCalendarDraftPeriod next) {
    var result = new PayPeriodValidationResult();

    result.addFailure(buildValidationFailure(current.getId(),
      String.format("Gap/overlap between pay periods - next pay period starts on %s", next.getAssignedStartDate().format(dateFormatter)),
      PayrollCalendarExceptionCode.PAY_PERIOD_INVALID_END_DATE,
      PayPeriodValidationType.END_DATE, "nextStartDate", next.getAssignedStartDate().format(dateFormatter)));

    result.addFailure(buildValidationFailure(next.getId(),
      String.format("Gap/overlap between pay periods - prior pay period ends on %s", current.getAssignedEndDate().format(dateFormatter)),
      PayrollCalendarExceptionCode.PAY_PERIOD_INVALID_START_DATE,
      PayPeriodValidationType.START_DATE, "priorEndDate", current.getAssignedEndDate().format(dateFormatter)));

    return result;
  }

  private PayPeriodValidationResult validatePayDateOnWeekend(PayrollCalendarDraftPeriod period) {
    var result = new PayPeriodValidationResult();
    var args = buildErrorArgs(period.getId(), "Pay date cannot be on a weekend", PayPeriodValidationType.PAY_DATE);
    result.addFailure(new ValidationFailure(PayrollCalendarExceptionCode.PAY_PERIOD_PAY_DATE_ON_WEEKEND.getCode(), LogSeverity.ERROR, args));
    return result;
  }

  private PayPeriodValidationResult validatePayDateOnHoliday(PayrollCalendarDraftPeriod period) {
    var result = new PayPeriodValidationResult();
    var args = buildErrorArgs(period.getId(), "Pay date cannot be on a holiday", PayPeriodValidationType.PAY_DATE);
    result.addFailure(new ValidationFailure(PayrollCalendarExceptionCode.PAY_PERIOD_PAY_DATE_ON_HOLIDAY.getCode(), LogSeverity.ERROR, args));
    return result;
  }

  private ValidationFailure buildValidationFailure(Long periodId, String message, PayrollCalendarExceptionCode exceptionCode, PayPeriodValidationType type, String key, String value) {
    var errorArgs = buildErrorArgs(periodId, message, type);
    errorArgs.put(key, value);
    return new ValidationFailure(exceptionCode.getCode(), errorArgs);
  }

  private HashMap<String, Object> buildErrorArgs(Long periodId, String message, PayPeriodValidationType type) {
    HashMap<String, Object> errorArgs = new HashMap<>();
    errorArgs.put("periodId", periodId);
    errorArgs.put("message", message);
    errorArgs.put("type", type);
    return errorArgs;
  }
}
