package com.everee.api.payrollcalendar.processors.validate;

import com.everee.api.company.CompanyService;
import com.everee.api.payrollcalendar.processors.context.CalendarContextBase;
import com.everee.api.pipeline.Process;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;

@Component
public class ValidateAuthorizeToEditPayrollCalendar implements Process<CalendarContextBase> {

    @Override
    public void process(CalendarContextBase context) {
        var calendar = context.getCalendar();

        if (!calendar.getCompanyId().equals(CompanyService.getAuthenticatedCompanyId())) {
            throw new AccessDeniedException("Unable to edit calendar");
        }
    }
}
