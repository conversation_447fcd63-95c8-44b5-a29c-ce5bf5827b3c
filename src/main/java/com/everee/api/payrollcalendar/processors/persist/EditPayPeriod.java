package com.everee.api.payrollcalendar.processors.persist;

import com.everee.api.exception.PipelineValidationException;
import com.everee.api.payrollcalendar.PayrollCalendarDraftPeriodRepository;
import com.everee.api.payrollcalendar.processors.context.IDraftPayPeriodToDto;
import com.everee.api.pipeline.Process;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EditPayPeriod implements Process<IDraftPayPeriodToDto> {

  private final PayrollCalendarDraftPeriodRepository payrollCalendarDraftPeriodRepository;

  @Override
  public void process(IDraftPayPeriodToDto context) {
    var period = context.getDraftPayPeriod();
    if(period == null){
      throw new PipelineValidationException();
    }

    payrollCalendarDraftPeriodRepository.save(period);
  }
}
