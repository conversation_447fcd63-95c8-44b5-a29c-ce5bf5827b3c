package com.everee.api.payrollcalendar.mappers;

import com.everee.api.payment.PaymentFundingType;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payrollcalendar.models.*;
import com.everee.api.payrollcalendar.processors.context.CreateCalendarContext;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.everee.api.payrollcalendar.PayrollCalendarUtil.offsetWorkingDays;
import static com.launchdarkly.shaded.com.google.common.base.MoreObjects.firstNonNull;

@Mapper(uses = {PayrollCalendarDraftPeriodsMapper.class, PayrollCalendarPeriodsMapper.class})
public interface PayrollCalendarMapper {
    PayrollCalendarMapper MAPPER = Mappers.getMapper(PayrollCalendarMapper.class);

    @Mapping(target = "frequency", source = "calendar.payPeriodType")
    @Mapping(target = "nextPayDate", source = "calendar.payPeriods", qualifiedByName = "getNextPayDate")
    @Mapping(target = "firstPayPeriod", expression = "java(getFirstPeriodDTO(calendar))")
    @Mapping(target = "secondPayPeriod", expression = "java(getSecondPeriodDTO(calendar))")
    @Mapping(target = "draftPayPeriods", source = "calendar.draftPayPeriods", qualifiedByName = "toDTO")
    @Mapping(target = "payPeriods", source = "calendar.payPeriods", qualifiedByName = "toDTO")
    @Mapping(target = "createdBy", source = "createByUserName")
    PayrollCalendarDTO toDTO(PayrollCalendar calendar, String createByUserName, @Context PaymentFundingType companyFunding, @Context Set<LocalDate> holidays);

    @Mapping(target = "firstStartDate", source = "firstPayPeriod.startDate")
    @Mapping(target = "firstPayDate", source = "firstPayPeriod.payDate")
    @Mapping(target = "secondStartDate", source = "secondPayPeriod.startDate")
    @Mapping(target = "secondPayDate", source = "secondPayPeriod.payDate")
    @Mapping(target = "payPeriodType", expression = "java(com.everee.api.payperiod.PayPeriodType.valueOf(request.getFrequency().toString()))")
    @Mapping(target = "forYear", source = "firstPayPeriod.payDate.year")
    @Mapping(target = "companyId", expression = "java(request.getCompanyId())")
    @Mapping(target = "createdByUserId", expression = "java(request.getUser().getId())")
    @Mapping(target = "updatedByUserId", expression = "java(request.getUser().getId())")
    PayrollCalendar toEntity(CreateCalendarContext request);

    @Mapping(source = "payPeriodType", target = "frequency")
    @Mapping(target = "nextPayDate", source = "payPeriods", qualifiedByName = "getNextPayDate")
    @Mapping(source = "firstPayDate", target = "firstPayDate")
    @Mapping(source = "lastPublishedAt", target = "lastPublishedAt")
    ViewCalendarDTO toViewCalendarDTO(PayrollCalendar entity);

    default List<ViewCalendarDTO> toDTO(List<PayrollCalendar> calendars) {
        return calendars.stream()
                .map(this::toViewCalendarDTO)
                .collect(Collectors.toList());
    }

    @Named("getNextPayDate")
    default LocalDate getNextPayDate(List<PayPeriod> periods) {
        return periods
                .stream()
                .filter(p -> p.getPayDate().isAfter(LocalDate.now()))
                .findFirst().map(PayPeriod::getPayDate)
                .orElse(null);
    }

    default PayPeriodDTO getFirstPeriodDTO(PayrollCalendar entity) {
        var payPeriods = entity.getPayPeriods();
        var startDate = !payPeriods.isEmpty() ? payPeriods.get(0).getStartDate() : entity.getFirstStartDate();
        var payDate = !payPeriods.isEmpty() ? payPeriods.get(0).getPayDate() : entity.getFirstPayDate();

        return PayPeriodDTO.builder().startDate(startDate).payDate(payDate).build();
    }

    default PayPeriodDTO getSecondPeriodDTO(PayrollCalendar entity) {
        if (entity.getSecondStartDate() == null) {
            return null;
        }
        return PayPeriodDTO.builder()
                .startDate(entity.getSecondStartDate())
                .payDate(entity.getSecondPayDate())
                .build();
    }


}
