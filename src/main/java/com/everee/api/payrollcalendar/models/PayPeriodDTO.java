package com.everee.api.payrollcalendar.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@AllArgsConstructor
@Getter
@Builder
@EqualsAndHashCode
public class PayPeriodDTO {

    @NotNull
    private final LocalDate startDate;

    @NotNull
    private final LocalDate payDate;
}
