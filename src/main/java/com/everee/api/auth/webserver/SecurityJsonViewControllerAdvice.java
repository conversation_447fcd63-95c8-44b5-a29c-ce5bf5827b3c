package com.everee.api.auth.webserver;

import com.everee.api.auth.role.InternalRole;
import com.everee.api.config.JsonViews.Default;
import com.everee.api.config.JsonViews.EvereeAdmin;
import com.everee.api.config.JsonViews.EvereeSystem;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJacksonValue;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.AbstractMappingJacksonResponseBodyAdvice;

@RestControllerAdvice
public class SecurityJsonViewControllerAdvice extends AbstractMappingJacksonResponseBodyAdvice {

  @Override
  protected void beforeBodyWriteInternal(
      MappingJacksonValue bodyContainer,
      MediaType contentType,
      MethodParameter returnType,
      ServerHttpRequest request,
      ServerHttpResponse response) {

    applySecurityJsonView(bodyContainer);
  }

  private void applySecurityJsonView(MappingJacksonValue bodyContainer) {
    bodyContainer.setSerializationView(Default.class);

    Set<String> grantedAuthorities =
        Optional.ofNullable(SecurityContextHolder.getContext())
            .map(SecurityContext::getAuthentication).map(Authentication::getAuthorities).stream()
            .flatMap(Collection::stream)
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toSet());

    if (grantedAuthorities.contains(InternalRole.EVEREE_SYSTEM.getSpringIdentifier())) {
      bodyContainer.setSerializationView(EvereeSystem.class);
    } else if (grantedAuthorities.contains(InternalRole.EVEREE_ADMIN.getSpringIdentifier())) {
      bodyContainer.setSerializationView(EvereeAdmin.class);
    }
  }
}
