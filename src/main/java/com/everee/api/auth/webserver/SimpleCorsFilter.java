package com.everee.api.auth.webserver;

import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SimpleCorsFilter extends CorsFilter {
  public SimpleCorsFilter(
      @Autowired(required = false) @Qualifier("EvereeCorsConfigurationSource")
          CorsConfigurationSource corsConfigurationSource) {
    super(
        Optional.ofNullable(corsConfigurationSource)
            .orElseGet(UrlBasedCorsConfigurationSource::new));
  }
}
