package com.everee.api.auth.webserver;

import com.everee.api.model.BaseModelV2;
import javax.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Immutable
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OidcBlacklistedSession extends BaseModelV2<OidcBlacklistedSession> {
  private String sessionKey;
  private String sessionData;
}
