package com.everee.api.earnings;

import com.everee.api.earnings.grossearnings.payloads.GrossEarningPayload;
import com.everee.api.earnings.grossearnings.utils.EarningsConverter;
import com.everee.api.money.Money;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.Value;

@Value
public class GrossEarnings {
  @NonNull Long employeeId;
  @NonNull LocalDate startDate;
  @NonNull LocalDate endDate;

  @NonNull List<GrossEarningPayload> earningList;

  @NonNull Set<PtoTransaction> ptoTransactions;

  @SuppressWarnings("unused")
  public Money getGrossAmount() {
    return getEarningList().stream()
        .map(GrossEarningPayload::getGrossAmount)
        .reduce(Money.ZERO, Money::plus);
  }

  public List<GrossEarningPayload> getEarningList() {
    return earningList.stream()
        .collect(
            Collectors.groupingBy(
                GrossEarningPayload::getGroupKey,
                Collectors.reducing(GrossEarningPayload::aggregate)))
        .values()
        .stream()
        .flatMap(Optional::stream)
        .collect(Collectors.toList());
  }

  @Deprecated(forRemoval = true)
  public List<Earning> getEarnings() {
    return getEarningList().stream().map(EarningsConverter::toLegacy).collect(Collectors.toList());
  }
}
