package com.everee.api.earnings.grossearnings;

import com.everee.api.earnings.EarningType;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentEarning;
import java.util.Objects;

public class GrossEarningCompositeKey {
  private Long approvalGroupId;
  private Long workLocationId;
  private Long workerRoleId;
  private Money unitRate;
  private EarningType type;

  public GrossEarningCompositeKey(GrossEarning grossEarning) {
    this.approvalGroupId = grossEarning.getApprovalGroupId();
    this.workLocationId = grossEarning.getWorkLocationId();
    this.workerRoleId = grossEarning.getWorkerRoleId();
    this.unitRate = grossEarning.getUnitRate();
    this.type = grossEarning.getType();
  }

  public GrossEarningCompositeKey(PaymentEarning paymentEarning) {
    this.approvalGroupId = paymentEarning.getApprovalGroupId();
    this.workLocationId = paymentEarning.getWorkLocationId();
    this.workerRoleId = paymentEarning.getWorkerRoleId();
    this.unitRate = paymentEarning.getUnitRate();
    this.type = paymentEarning.getType();
  }

  @Override
  public boolean equals(Object o) {
    if (o == null) return false;
    if (this == o) {
      return true;
    }
    if (!(o instanceof GrossEarningCompositeKey)) {
      return false;
    }

    var that = (GrossEarningCompositeKey) o;
    return Objects.equals(approvalGroupId, that.approvalGroupId)
        && Objects.equals(workLocationId, that.workLocationId)
        && Objects.equals(workerRoleId, that.workerRoleId)
        && Objects.equals(unitRate, that.unitRate)
        && type == that.type;
  }

  @Override
  public int hashCode() {
    return Objects.hash(approvalGroupId, workLocationId, workerRoleId, unitRate, type);
  }
}
