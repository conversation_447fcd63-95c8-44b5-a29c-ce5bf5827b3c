package com.everee.api.earnings.grossearnings;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.everee.api.earnings.EarningSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GrossEarningRepository extends JpaRepository<GrossEarning, Long> {
  @Query(
      "select"
          + "   ge.externalId"
          + " from GrossEarning ge"
          + " where ge.companyId = :companyId and ge.externalId in :externalIds")
  Page<String> listExistingExternalIds(
      @Param("companyId") Long companyId,
      @Param("externalIds") Set<String> externalIds,
      Pageable pageable);

  @Modifying
  @Query(
      "delete from GrossEarning ge"
          + " where ge.companyId = :companyId"
          + "   and ge.paid = false"
          + "   and ge.earningDate between :startDate and :endDate")
  void deleteUnpaidGrossEarningsInDateRangeInclusive(
      @Param("companyId") Long companyId,
      @Param("startDate") LocalDate startDate,
      @Param("endDate") LocalDate endDate);

  @Query(
      // Find gross earnings that were used to make up the provided journal entry
      "SELECT ge"
          + " FROM GrossEarning ge"
          + " JOIN Payment p ON p.id = ge.paymentId"
          + " JOIN AchFile ach ON ach.id = p.achFileId"
          + " JOIN JournalEntry je ON je.achFileId = ach.id"
          + " WHERE je.id = ?1")
  List<GrossEarning> findEarningsByJournalEntryId(Long journalEntryId);

  @Query(
      // Find gross earnings based on paymentId
      "SELECT ge FROM GrossEarning ge where paymentId in ?1")
  List<GrossEarning> findEarningsByPaymentIds(Collection<Long> paymentIds);

  @Query(
      // Find gross earnings based on paymentId
      "SELECT ge FROM GrossEarning ge where paymentId = ?1")
  List<GrossEarning> findEarningsByPaymentId(Long paymentId);

  @Query(
      "SELECT "
          + "COUNT(e) > 0"
          + " FROM GrossEarning e"
          + " WHERE e.employeeId = :employeeId"
          + "   AND e.workLocationId = :workLocationId"
          + "   AND e.earningDate = :earningDate")
  boolean exists(
      @Param("employeeId") Long employeeId,
      @Param("workLocationId") Long workLocationId,
      @Param("earningDate") LocalDate earningDate);

  @Modifying
  @Query("DELETE FROM GrossEarning ge WHERE ge.id IN :ids")
  void deleteAllByIdIn(@Param("ids") Set<Long> ids);

  @Query("SELECT ge" + " FROM GrossEarning ge" + " WHERE ge.importId IN :importIds")
  List<GrossEarning> findEarningsByImportIds(@Param("importIds") List<String> importIds);

  @Modifying
    @Query(
        "update GrossEarning ge"
            + " set ge.payRunId = :payRunId"
            + " where ge.paymentId in :paymentIds")
  void updateGrossEarningsWithPayRunId(@Param("payRunId") Long payRunId, @Param("paymentIds") List<Long> paymentIds);

  @Modifying
  @Query("DELETE FROM GrossEarning ge WHERE ge.payRunId = ?1")
  void deleteByPayRunId(Long payRunId);

  @Modifying
  @Query("DELETE FROM GrossEarning ge WHERE ge.payRunId = :payRunId AND ge.source IN :sources")
  void deleteByPayRunIdAndSource(@Param("payRunId") Long payRunId, @Param("sources") List<EarningSource> sources);
}
