package com.everee.api.earnings.grossearnings;

import com.everee.api.earnings.EarningType;
import com.everee.api.lookup.DateRangeLookup;
import com.everee.api.util.BooleanApiParam;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.Set;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class GrossEarningLookup implements DateRangeLookup {
  private Set<Long> companyIds;
  private Set<Long> employeeIds;

  @ApiParam(name = "min-reference-date")
  @NonNull
  private LocalDate minReferenceDate;

  @ApiParam(name = "max-reference-date")
  @NonNull
  private LocalDate maxReferenceDate;

  private Set<EarningType> types;
  private Set<Long> approvalGroupIds;
  private Set<Long> workLocationIds;
  private Set<Long> workerRoleIds;

  private Set<Long> paymentIds;
  private Set<Long> payRunIds;
  private BooleanApiParam paid = BooleanApiParam.UNSPECIFIED;
}
