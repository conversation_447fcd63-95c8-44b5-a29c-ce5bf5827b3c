package com.everee.api.earnings.grossearnings;

import com.everee.api.featureflag.FeatureFlag;
import com.everee.api.featureflag.FeatureFlagProvider;
import com.everee.api.payment.PaymentType;
import com.everee.api.payment.event.PaymentDeletedEvent;
import com.everee.api.payment.event.PaymentStatusChangedEvent;
import com.everee.api.payment.event.PaymentUpdatedEvent;
import com.everee.api.util.BooleanApiParam;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GrossEarningEventListener {
  private final GrossEarningLookupService grossEarningLookupService;
  private final GrossEarningService grossEarningService;
  private final FeatureFlagProvider featureFlagProvider;

  @EventListener
  public void handlePaymentUpdatedEvent(PaymentUpdatedEvent event) {
    var payment = event.getPayment();
    var payRunsEnabled = isPayrollPayRunsEnabled(payment.getCompanyId());

    if (!isSupportedPaymentType(payment.getType(), payRunsEnabled)) {
      return;
    }

    var paymentId = payment.getId();
    var employeeId = payment.getEmployeeId();
    var payRunId = payment.getPayRunId();

    var lookup = new GrossEarningLookup()
      .setEmployeeIds(Set.of(employeeId))
      .setPaid(BooleanApiParam.EXCLUDE_ALL)
      .setMinReferenceDate(payment.getPayPeriodStartDate())
      .setMaxReferenceDate(payment.getPayPeriodEndDate());

    if (payRunId != null && payRunsEnabled) {
      lookup.setPayRunIds(Set.of(payRunId));
    }

    grossEarningLookupService
      .listAll(
        lookup,
        Pageable.unpaged())
      .forEach(earning -> earning.setPaymentId(paymentId));
  }

  @EventListener
  public void handlePaymentStatusChangedEvent(PaymentStatusChangedEvent event) {
    var payment = event.getPayment();
    var payRunsEnabled = isPayrollPayRunsEnabled(payment.getCompanyId());

    if (!isSupportedPaymentType(payment.getType(), payRunsEnabled)) {
      return;
    }

    var paymentId = payment.getId();
    var employeeId = payment.getEmployeeId();
    var earningsArePaid = payment.getStatus().isFinalized();

    grossEarningLookupService
        .listAll(
            new GrossEarningLookup()
                .setEmployeeIds(Set.of(employeeId))
                .setPaymentIds(Set.of(paymentId)),
            Pageable.unpaged())
        .forEach(earning -> earning.setPaid(earningsArePaid));
  }

  @EventListener
  public void handlePaymentDeletedEvent(PaymentDeletedEvent event) {
    var payment = event.getPayment();

    var paymentId = payment.getId();

    grossEarningService
        .deleteGrossEarningsByPaymentIds(Set.of(paymentId));
  }

  private boolean isPayrollPayRunsEnabled(Long companyId) {
    var payrollPayRunsSettingValue = featureFlagProvider.intValue(
      FeatureFlag.PAYROLL_PAY_RUNS_ENABLED, companyId, 1
    );
    return payrollPayRunsSettingValue > 1;
  }

  private boolean isSupportedPaymentType(PaymentType paymentType, boolean payRunsEnabled) {
    var allowedTypes = payRunsEnabled
      ? Set.of(PaymentType.PAYROLL, PaymentType.AD_HOC)
      : Set.of(PaymentType.PAYROLL);

    return allowedTypes.contains(paymentType);
  }
}
