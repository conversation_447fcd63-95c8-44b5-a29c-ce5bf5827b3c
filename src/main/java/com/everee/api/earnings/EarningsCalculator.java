package com.everee.api.earnings;

import static com.everee.api.timeoff.types.TimeOffType.*;

import com.everee.api.company.DetailedCompany;
import com.everee.api.earnings.grossearnings.GrossEarning;
import com.everee.api.employee.Employee;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.holiday.employee.EmployeeHoliday;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.timeoff.request.TimeOffRequestDayAmount;
import java.time.LocalDate;
import java.util.Collection;
import java.util.EnumSet;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public abstract class EarningsCalculator {
  public abstract List<GrossEarning> getEarningsForPeriod(
      @NonNull DetailedCompany company,
      @NonNull Employee employee,
      @NonNull Optional<Long> payRunId,
      @NonNull PayPeriod baseEmployeePayPeriod,
      @NonNull EmployeePosition position,
      @NonNull List<LocalDate> days,
      @NonNull Function<LocalDate, Collection<ClassifiedTimeWorkedForDate>> getTimeWorkedForDate,
      @NonNull Function<LocalDate, Optional<TimeOffRequestDayAmount>> getTimeOffForDate,
      @NonNull Function<LocalDate, Optional<EmployeeHoliday>> getHolidayForDate,
      @NonNull List<GrossEarning> earningList);

  protected static Function<TimeOffRequestDayAmount, GrossEarning> makeTimeOffEarning(
      @NonNull DetailedCompany company,
      @NonNull Employee employee,
      @NonNull LocalDate earningDate,
      @NonNull EmployeePosition position) {
    return (@NonNull TimeOffRequestDayAmount dayAmount) -> {
      var timeOffType = dayAmount.getRequest().getTimeOffType();
      var earningType = timeOffType.getEarningType();
      var payRate = position.getHourlyRate();
      var payableHours = dayAmount.getHours();

      if (EnumSet.of(FFCRA_FMLA, FFCRA_SICK_CAREGIVER, FFCRA_SICK_INDIVIDUAL)
          .contains(timeOffType)) {
        throw new IllegalArgumentException("FFCRA earning types are no longer supported.");
      }

      return GrossEarning.from(
          company, employee, earningDate, earningType, payRate, payableHours, null);
    };
  }
}
