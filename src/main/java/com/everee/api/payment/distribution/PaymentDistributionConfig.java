package com.everee.api.payment.distribution;

import com.everee.api.bankaccount.BankAccountType;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Data;
import lombok.experimental.Accessors;

@Embeddable
@Data
@Accessors(chain = true)
public class PaymentDistributionConfig {
  private Long userBankAccountId;
  private String bankName;
  private String accountName;
  private String routingNumber;
  private String accountNumber;

  @Enumerated(EnumType.STRING)
  private BankAccountType accountType;

  private Long taxTypeAccumulationId;

  private String tabapayAccountId;
  private String galileoAccountId;
  private String cardLast4;
  private String cardNetwork;
}
