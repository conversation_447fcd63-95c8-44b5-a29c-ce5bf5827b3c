package com.everee.api.payment.distribution.report;

import static com.everee.api.payment.distribution.PaymentDistributionType.PAY_CARD;
import static com.everee.api.payment.distribution.PaymentDistributionType.PTPC;
import static com.everee.api.util.SqlQueryUtils.*;
import static com.everee.api.util.SqlQueryUtils.pageableFragment;

import com.everee.api.money.Money;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.util.DateUtil;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
@RequiredArgsConstructor
public class PaymentDistributionReportDAO {
  private final NamedParameterJdbcTemplate jdbcTemplate;

  public PaymentDistributionReportSummaryTotal getTotalSummaries(
      PaymentDistributionType type,
      LocalDate startDate,
      LocalDate endDate,
      String nameSearch,
      Boolean demoCompany) {
    Assert.notNull(startDate, "'startDate' must be set");
    Assert.notNull(endDate, "'endDate' must be set");

    var startDateTime = DateUtil.getMountainTimeAsUtcTime(startDate, 0);
    var endDateTime = DateUtil.getMountainTimeAsUtcTime(endDate, 0);

    var sql = buildSummariesQuery(true, nameSearch, demoCompany, null);

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("startDateTime", startDateTime);
    parameters.addValue("endDateTime", endDateTime);
    parameters.addValue("type", type.name());
    Optional.ofNullable(demoCompany).map(c -> parameters.addValue("demoCompany", c));
    Optional.ofNullable(nameSearch)
        .map(c -> parameters.addValue("nameSearch", wrapInWildcard(c.toLowerCase())));

    return jdbcTemplate.queryForObject(
        sql,
        parameters,
        (rs, rowNum) ->
            new PaymentDistributionReportSummaryTotal(
                rs.getInt("countCompanies"),
                rs.getInt("countWorkerInitiated"),
                Money.valueOf(rs.getBigDecimal("workerInitiatedAmount")),
                rs.getInt("countAdminInitiated"),
                Money.valueOf(rs.getBigDecimal("adminInitiatedAmount")),
                rs.getInt("totalCount"),
                Money.valueOf(rs.getBigDecimal("totalAmount"))));
  }

  public Page<PaymentDistributionReportSummary> getSummaries(
      PaymentDistributionType type,
      LocalDate startDate,
      LocalDate endDate,
      String nameSearch,
      Boolean demoCompany,
      Pageable pageable) {
    Assert.notNull(startDate, "'startDate' must be set");
    Assert.notNull(endDate, "'endDate' must be set");

    var startDateTime = DateUtil.getMountainTimeAsUtcTime(startDate, 0);
    var endDateTime = DateUtil.getMountainTimeAsUtcTime(endDate, 0);

    var sql = buildSummariesQuery(false, nameSearch, demoCompany, pageable);

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("startDateTime", startDateTime);
    parameters.addValue("endDateTime", endDateTime);
    parameters.addValue("type", type.name());
    Optional.ofNullable(demoCompany).map(c -> parameters.addValue("demoCompany", c));
    Optional.ofNullable(nameSearch)
        .map(c -> parameters.addValue("nameSearch", wrapInWildcard(c.toLowerCase())));

    var result =
        jdbcTemplate.query(
            sql,
            parameters,
            (rs, rowNum) ->
                new PaymentDistributionReportSummary(
                    rs.getLong("companyId"),
                    rs.getString("companyDisplayName"),
                    rs.getString("companyLegalName"),
                    rs.getInt("countWorkerInitiated"),
                    Money.valueOf(rs.getBigDecimal("workerInitiatedAmount")),
                    rs.getInt("countAdminInitiated"),
                    Money.valueOf(rs.getBigDecimal("adminInitiatedAmount")),
                    rs.getInt("totalCount"),
                    Money.valueOf(rs.getBigDecimal("totalAmount"))));

    var totals = getTotalSummaries(type, startDate, endDate, nameSearch, demoCompany);
    var total = totals.getCountCompanies();

    return new PageImpl<>(result, pageable, total);
  }

  public List<PaymentDistributionReportTransaction> getTransactions(
      PaymentDistributionType type,
      LocalDate startDate,
      LocalDate endDate,
      String nameSearch,
      Boolean demoCompany) {
    var startDateTime = DateUtil.getMountainTimeAsUtcTime(startDate, 0);
    var endDateTime = DateUtil.getMountainTimeAsUtcTime(endDate, 0);

    var sql = buildTransactionsQuery(type, nameSearch, demoCompany);

    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("startDateTime", startDateTime);
    parameters.addValue("endDateTime", endDateTime);
    parameters.addValue("type", type.name());
    Optional.ofNullable(demoCompany).map(c -> parameters.addValue("demoCompany", c));
    Optional.ofNullable(nameSearch)
        .map(c -> parameters.addValue("nameSearch", wrapInWildcard(c.toLowerCase())));

    return jdbcTemplate.query(
        sql,
        parameters,
        (rs, rowNum) ->
            new PaymentDistributionReportTransaction(
                rs.getLong("companyId"),
                rs.getString("companyDisplayName"),
                rs.getTimestamp("attemptedAt").toLocalDateTime(),
                rs.getString("firstName"),
                rs.getString("lastName"),
                Money.valueOf(rs.getBigDecimal("amount")),
                rs.getString("transactionId")));
  }

  private String buildTransactionsQuery(
      PaymentDistributionType type, String nameSearch, Boolean demoCompany) {
    return ""
        + "select "
        + "    c.id as companyId, "
        + "    c.displayName as companyDisplayName, "
        + "    pd.attemptedAt, "
        + "    u.firstName, "
        + "    u.lastName, "
        + "    pd.amount, "
        + includeIf(type == PTPC, "pd.tabapayTransactionId as transactionId ")
        + includeIf(type == PAY_CARD, "pd.galileoTransactionId as transactionId ")
        + "from PaymentDistribution pd "
        + "join Payment p on p.id = pd.paymentId "
        + "join company c on c.id = pd.companyId "
        + "left outer join employee e on e.id = p.employeeId "
        + "left outer join appUser u on u.id = e.userid "
        + "where "
        + "    pd.companyDistributionId is not null "
        + "    and pd.type = :type "
        + "    and pd.status NOT IN ('FAILED', 'CREATED_NEW_DISTRIBUTION') "
        + "    and pd.amount <> 0 "
        + "    and pd.attemptedAt between :startDateTime and :endDateTime "
        + includeIfNotNull(demoCompany, "and c.demoCompany = :demoCompany ")
        + includeIfNotNull(
            nameSearch,
            "and (lower(c.displayName) like :nameSearch or lower(c.legalEntityName) like :nameSearch) ")
        + "order by pd.attemptedAt asc";
  }

  private String buildSummariesQuery(
      boolean totalsQuery, String nameSearch, Boolean demoCompany, Pageable pageable) {
    var query =
        ""
            + "with paymentsByCompanyDistributionId as ( "
            + "select "
            + "    pd.companyDistributionId, "
            + "    max(pd.attemptedAt) as attemptedAt, "
            + "    p.employeeId, "
            + "    c.id as companyId, "
            + "    c.displayName as companyDisplayName, "
            + "    c.legalEntityName as companyLegalName, "
            + "    c.demoCompany, "
            + "    count(1) as numPayments, "
            + "    sum(pd.amount) as amount, "
            + "    max(pd.distributionInitiatedByType) as distributionInitiatedByType "
            + "from PaymentDistribution pd "
            + "join Payment p on p.id = pd.paymentId "
            + "join Company c on c.id = pd.companyId "
            + "where "
            + "    pd.companyDistributionId is not null "
            + "    and pd.type = :type "
            + "    and pd.status NOT IN ('FAILED', 'CREATED_NEW_DISTRIBUTION') "
            + "    and pd.amount <> 0 "
            + "group by c.id, c.displayName, c.legalEntityName, c.demoCompany, p.employeeId, pd.companyDistributionId "
            + ") ";

    if (totalsQuery) {
      query +=
          ""
              + "select "
              + "    count(distinct pbcd.companyId) as countCompanies, "
              + "    count(case when pbcd.distributionInitiatedByType = 'WORKER' then pbcd.companyDistributionId end) as countWorkerInitiated, "
              + "    COALESCE(sum(case when pbcd.distributionInitiatedByType = 'WORKER' then pbcd.amount end),0) as workerInitiatedAmount, "
              + "    count(case when pbcd.distributionInitiatedByType = 'ADMIN' then pbcd.companyDistributionId end) as countAdminInitiated, "
              + "    COALESCE(sum(case when pbcd.distributionInitiatedByType = 'ADMIN' then pbcd.amount end),0) as adminInitiatedAmount, "
              + "    count(1) as totalCount, "
              + "    COALESCE(sum(pbcd.amount),0) as totalAmount "
              + "from paymentsByCompanyDistributionId pbcd "
              + "where "
              + "    pbcd.attemptedAt between :startDateTime and :endDateTime "
              + includeIfNotNull(demoCompany, "and pbcd.demoCompany = :demoCompany ")
              + includeIfNotNull(
                  nameSearch,
                  "    and ( "
                      + "    lower(pbcd.companyDisplayName) like :nameSearch "
                      + "    or "
                      + "    lower(pbcd.companyLegalName) like :nameSearch "
                      + "    ) ");
    } else {
      query +=
          ""
              + "select "
              + "    pbcd.companyId, "
              + "    pbcd.companyDisplayName, "
              + "    pbcd.companyLegalName, "
              + "    count(case when pbcd.distributionInitiatedByType = 'WORKER' then pbcd.companyDistributionId end) as countWorkerInitiated, "
              + "    COALESCE(sum(case when pbcd.distributionInitiatedByType = 'WORKER' then pbcd.amount end),0) as workerInitiatedAmount, "
              + "    count(case when pbcd.distributionInitiatedByType = 'ADMIN' then pbcd.companyDistributionId end) as countAdminInitiated, "
              + "    COALESCE(sum(case when pbcd.distributionInitiatedByType = 'ADMIN' then pbcd.amount end),0) as adminInitiatedAmount, "
              + "    count(1) as totalCount, "
              + "    COALESCE(sum(pbcd.amount),0) as totalAmount "
              + "from paymentsByCompanyDistributionId pbcd "
              + "where "
              + "    pbcd.attemptedAt between :startDateTime and :endDateTime "
              + includeIfNotNull(demoCompany, "and pbcd.demoCompany = :demoCompany ")
              + includeIfNotNull(
                  nameSearch,
                  "and (lower(pbcd.companyDisplayName) like :nameSearch or lower(pbcd.companyLegalName) like :nameSearch) ")
              + "group by pbcd.companyId, pbcd.companyDisplayName, pbcd.companyLegalName "
              + pageableFragment(pageable, "pbcd.companyDisplayName");
    }

    return query;
  }
}
