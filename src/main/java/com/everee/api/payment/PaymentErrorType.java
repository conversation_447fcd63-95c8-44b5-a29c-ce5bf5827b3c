package com.everee.api.payment;

import com.everee.api.i18n.LocalizedString;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum PaymentErrorType {
  MISSING_OR_INVALID_ADDRESS(
      1L,
      "pfail.MISSING_OR_INVALID_ADDRESS.title",
      "pfail.MISSING_OR_INVALID_ADDRESS.description",
      "pfail.MISSING_OR_INVALID_ADDRESS.recoverysuggestion"),
  MISSING_BANK_ACCOUNT(
      2L,
      "pfail.MISSING_BANK_ACCOUNT.title",
      "pfail.MISSING_BANK_ACCOUNT.description",
      "pfail.MISSING_BANK_ACCOUNT.recoverysuggestion"),
  INVALID_BANK_ACCOUNT(
      3L,
      "pfail.INVALID_BANK_ACCOUNT.title",
      "pfail.INVALID_BANK_ACCOUNT.description",
      "pfail.INVALID_BANK_ACCOUNT.recoverysuggestion"),
  MISSING_WITHHOLDINGS(
      4L,
      "pfail.MISSING_WITHHOLDINGS.title",
      "pfail.MISSING_WITHHOLDINGS.description",
      "pfail.MISSING_WITHHOLDINGS.recoverysuggestion"),
  MISSING_EMPLOYEE_POSITION(
      5L,
      "pfail.MISSING_EMPLOYEE_POSITION.title",
      "pfail.MISSING_EMPLOYEE_POSITION.description",
      "pfail.MISSING_EMPLOYEE_POSITION.recoverysuggestion"),
  MISSING_TAX_PAYER_IDENTIFIER(
      6L,
      "pfail.MISSING_TAX_PAYER_IDENTIFIER.title",
      "pfail.MISSING_TAX_PAYER_IDENTIFIER.description",
      "pfail.MISSING_TAX_PAYER_IDENTIFIER.recoverysuggestion"),
  MISSING_OR_INVALID_PAY_PERIOD(
      7L,
      "pfail.MISSING_OR_INVALID_PAY_PERIOD.title",
      "pfail.MISSING_OR_INVALID_PAY_PERIOD.description",
      "pfail.MISSING_OR_INVALID_PAY_PERIOD.recoverysuggestion"),
  MISSING_UNEMPLOYMENT_RATES(
      8L,
      "pfail.MISSING_UNEMPLOYMENT_RATES.title",
      "pfail.MISSING_UNEMPLOYMENT_RATES.description",
      "pfail.MISSING_UNEMPLOYMENT_RATES.recoverysuggestion"),
  MISSING_CONTRIBUTION_DEDUCTION_LIMITS(
      9L,
      "pfail.MISSING_CONTRIBUTION_DEDUCTION_LIMITS.title",
      "pfail.MISSING_CONTRIBUTION_DEDUCTION_LIMITS.description",
      "pfail.MISSING_CONTRIBUTION_DEDUCTION_LIMITS.recoverysuggestion"),
  FAILED_VALIDATION(
      10L,
      "pfail.FAILED_VALIDATION.title",
      "pfail.FAILED_VALIDATION.description",
      "pfail.FAILED_VALIDATION.recoverysuggestion"),
  NEGATIVE_NET(
      11L,
      "pfail.NEGATIVE_NET.title",
      "pfail.NEGATIVE_NET.description",
      "pfail.NEGATIVE_NET.recoverysuggestion"),
  FUTURE_FINALIZED(
      12L,
      "pfail.FUTURE_FINALIZED.title",
      "pfail.FUTURE_FINALIZED.description",
      "pfail.FUTURE_FINALIZED.recoverysuggestion"),
  FUNDING_FAILED(
      13L,
      "pfail.FUNDING_FAILED.title",
      "pfail.FUNDING_FAILED.description",
      "pfail.FUNDING_FAILED.recoverysuggestion"),
  PREVIOUS_NOT_FINALIZED(
      14L,
      "pfail.PREVIOUS_NOT_FINALIZED.title",
      "pfail.PREVIOUS_NOT_FINALIZED.description",
      "pfail.PREVIOUS_NOT_FINALIZED.recoverysuggestion"),
  UNEXPECTED(
      999L,
      "pfail.UNEXPECTED.title",
      "pfail.UNEXPECTED.description",
      "pfail.UNEXPECTED.recoverysuggestion"),
  MISSING_PAY_CARD(
      15L,
      "pfail.MISSING_PAY_CARD.title",
      "pfail.MISSING_PAY_CARD.description",
      "pfail.MISSING_PAY_CARD.recoverysuggestion"),
  INSUFFICIENT_NET_FOR_ACH_FEE(
      16L,
      "pfail.INSUFFICIENT_NET_FOR_ACH_FEE.title",
      "pfail.INSUFFICIENT_NET_FOR_ACH_FEE.description",
      "pfail.INSUFFICIENT_NET_FOR_ACH_FEE.recoverysuggestion"),
  // AAPL-10 Net Shortfall
  NET_SHORTFALL(
      101001L,
      "pwarn.NET_SHORTFALL.title",
      "pwarn.NET_SHORTFALL.description",
      "pwarn.NET_SHORTFALL.recoverysuggestion"),
  ANOTHER_PAYMENT_CALCULATION_IN_PROGRESS(
      17L,
      "pfail.ANOTHER_PAYMENT_CALCULATION_IN_PROGRESS.title",
      "pfail.ANOTHER_PAYMENT_CALCULATION_IN_PROGRESS.description",
      "pfail.ANOTHER_PAYMENT_CALCULATION_IN_PROGRESS.recoverysuggestion"),
  MISSING_TAX_JURISDICTION_DATA(
      18L,
      "pfail.MISSING_TAX_JURISDICTION_DATA.title",
      "pfail.MISSING_TAX_JURISDICTION_DATA.description",
      "pfail.MISSING_TAX_JURISDICTION_DATA.recoverysuggestion"),
  TAX_AMOUNT_LARGER_THAN_WAGES(
      19L,
      "pfail.TAX_AMOUNT_LARGER_THAN_WAGES.title",
      "pfail.TAX_AMOUNT_LARGER_THAN_WAGES.description",
      "pfail.TAX_AMOUNT_LARGER_THAN_WAGES.recoverysuggestion"),
  MISSING_MARITAL_STATUS_ABBREVIATION(
      20L,
      "pfail.MISSING_MARITAL_STATUS_ABBREVIATION.title",
      "pfail.MISSING_MARITAL_STATUS_ABBREVIATION.description",
      "pfail.MISSING_MARITAL_STATUS_ABBREVIATION.recoverysuggestion"),
  CONFLICTING_OTP_EARNING_TAX_CALCULATION_METHODS(
      21L,
      "pfail.CONFLICTING_OTP_EARNING_TAX_CALCULATION_METHODS.title",
      "pfail.CONFLICTING_OTP_EARNING_TAX_CALCULATION_METHODS.description",
      "pfail.CONFLICTING_OTP_EARNING_TAX_CALCULATION_METHODS.recoverysuggestion"),
  CONFLICTING_OTP_TAX_CALCULATION_METHODS(
      22L,
      "pfail.CONFLICTING_OTP_TAX_CALCULATION_METHODS.title",
      "pfail.CONFLICTING_OTP_TAX_CALCULATION_METHODS.description",
      "pfail.CONFLICTING_OTP_TAX_CALCULATION_METHODS.recoverysuggestion"),
  FAILED_LOADING_PAYMENT_DETAILS(
      23L,
      "pfail.FAILED_LOADING_PAYMENT_DETAILS.title",
      "pfail.FAILED_LOADING_PAYMENT_DETAILS.description",
      "pfail.FAILED_LOADING_PAYMENT_DETAILS.recoverysuggestion"),
  NEGATIVE_XTD(
      24L,
      "pfail.NEGATIVE_XTD.title",
      "pfail.NEGATIVE_XTD.description",
      "pfail.NEGATIVE_XTD.recoverysuggestion"
  ),
  NEGATIVE_DEDUCTION_XTD(
    25L,
    "pfail.NEGATIVE_DEDUCTION_XTD.title",
    "pfail.NEGATIVE_DEDUCTION_XTD.description",
    "pfail.NEGATIVE_DEDUCTION_XTD.recoverysuggestion"
  ),
  GARNISHMENT_SHORTFALL(
    26L,
    "pwarn.GARNISHMENT_SHORTFALL.title",
    "pwarn.GARNISHMENT_SHORTFALL.description",
    "pwarn.GARNISHMENT_SHORTFALL.recoverysuggestion"
  );

  private final Long code;
  private final String titleKey;
  private final String descriptionKey;
  private final String recoverySuggestionKey;

  /**
   * @return A short, numeric code meant to uniquely identify the error that is being represented.
   *     This code should be a short number that appears random, but is repeatable for all errors of
   *     a certain type. This code can assist in debugging procedures and helps provide context for
   *     API consumers.
   */
  public Long getCode() {
    return code;
  }

  /**
   * @return A short title, meant to describe the failure in 1-2 words. Examples include "Blocked",
   *     "Waiting", and "Needs Info"
   */
  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of(titleKey);
  }

  /**
   * @return A medium-length message, meant to adequately describe the problem in simple terms that
   *     any user could understand. This message should avoid using technical terminology and should
   *     not provide recovery suggestions.
   */
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.of(descriptionKey);
  }

  /**
   * @return A medium-length message, meant to provide a concise suggestion to the user of how to
   *     quickly correct the problem.
   */
  public LocalizedString getLocalizedRecoverySuggestion() {
    return LocalizedString.of(recoverySuggestionKey);
  }
}
