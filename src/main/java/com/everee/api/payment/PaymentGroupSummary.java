package com.everee.api.payment;

import com.everee.api.money.Money;
import com.everee.api.payment.group.PaymentGroup;
import java.util.*;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PaymentGroupSummary {
  private final @NotNull String id;
  private final @NotNull Long companyId;
  private final @NotNull String localizedDateRange;
  private final @NotNull String localizedPayDateRange;

  private @NotNull Long paymentCount = 0L;
  private @NotNull Money grossEarnings = Money.ZERO;
  private @NotNull Set<Long> paymentIds;

  public static PaymentGroupSummary fromGroup(PaymentGroup paymentGroup) {
    var paymentGroupSummary =
        new PaymentGroupSummary(
            paymentGroup.getId(),
            paymentGroup.getCompanyId(),
            paymentGroup.getLocalizedDateRange(),
            paymentGroup.getLocalizedPayDateRange());

    paymentGroupSummary.setPaymentCount(paymentGroup.getPaymentCount());
    paymentGroupSummary.setGrossEarnings(paymentGroup.getTotal().getGrossEarnings());

    return paymentGroupSummary;
  }
}
