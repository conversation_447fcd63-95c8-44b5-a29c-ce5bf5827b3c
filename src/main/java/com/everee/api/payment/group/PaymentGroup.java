package com.everee.api.payment.group;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.DateRange;
import com.everee.api.payment.PaymentQueryStatus;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.total.PaymentTotals;
import java.time.LocalDate;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.lang.NonNull;

@Data
@NoArgsConstructor
public class PaymentGroup {
  private String id;
  private Long companyId;
  private Set<LocalizedString> localizedTitles = new LinkedHashSet<>();
  private Set<LocalizedString> localizedDescriptions = new LinkedHashSet<>();
  private String localizedDateRange;
  private String localizedPayDateRange;
  private boolean custom;
  private PaymentTotals totals;
  private PaymentLookup lookup;
  private LocalDate scheduleDate;

  @Deprecated
  public LocalizedString getLocalizedTitle() {
    return getLocalizedTitles().stream()
        .findFirst()
        .orElse(LocalizedString.of("paymentgroup.default.title"));
  }

  @Deprecated
  public LocalizedString getLocalizedDescription() {
    return getLocalizedDescriptions().stream()
        .findFirst()
        .orElse(LocalizedString.of("paymentgroup.default.description"));
  }

  PaymentGroup(
      @NonNull String id,
      @NonNull PaymentLookup paymentLookup,
      @NonNull PaymentTotals paymentTotals,
      @NotNull Object payPeriodOrRequest,
      boolean custom,
      Set<LocalizedString> localizedTitles,
      Set<LocalizedString> localizedDescriptions,
      String localizedDateRange) {
    this.id = id;
    this.lookup = paymentLookup;
    this.companyId = paymentLookup.getCompanyId();
    this.custom = custom;
    this.totals = paymentTotals;
    this.localizedTitles.addAll(localizedTitles);
    this.localizedDescriptions.addAll(localizedDescriptions);
    this.scheduleDate = totals.getScheduleDate();

    this.localizedDateRange =
        Optional.ofNullable(localizedDateRange)
            .orElseGet(
                () ->
                    (totals.getPaymentCount() == 0)
                        ? ""
                        : DateRange.of(totals.getMinForDate(), totals.getMaxForDate())
                            .getLocalizedDescription(LocaleContextHolder.getLocale()));

    this.localizedPayDateRange =
        Optional.ofNullable(scheduleDate)
            .map(s -> DateRange.of(s, s).getLocalizedDescription(LocaleContextHolder.getLocale()))
            .orElseGet(
                () ->
                    (totals.getPaymentCount() == 0)
                        ? ""
                        : DateRange.of(totals.getMinPayDate(), totals.getMaxPayDate())
                            .getLocalizedDescription(LocaleContextHolder.getLocale()));
  }

  /** @return the most appropriate status to represent the entire cumulative payment group */
  public PaymentQueryStatus getStatus() {
    return Stream.of(
            PaymentQueryStatus.SCHEDULED,
            PaymentQueryStatus.AUTO_APPROVED,
            PaymentQueryStatus.PROCESSING,
            PaymentQueryStatus.PENDING_APPROVAL,
            PaymentQueryStatus.READY_TO_CALCULATE,
            PaymentQueryStatus.PENDING_VERIFICATION,
            PaymentQueryStatus.ERROR,
            PaymentQueryStatus.UNPAYABLE_WORKER,
            PaymentQueryStatus.PAID,
            PaymentQueryStatus.PENDING_PAYMENT,
            PaymentQueryStatus.PENDING_FUNDING)
        .filter(totals.getStatusCounts()::containsKey)
        .filter(s -> totals.getStatusCounts().get(s) > 0)
        .findFirst()
        .orElse(null);
  }

  public LocalizedString getLocalizedStatusTitle() {
    return Optional.ofNullable(getStatus()).map(PaymentQueryStatus::getLocalizedTitle).orElse(null);
  }

  public LocalizedString getLocalizedStatusDescription() {
    return Optional.ofNullable(getStatus())
        .map(PaymentQueryStatus::getLocalizedDescription)
        .orElse(null);
  }

  @Deprecated
  public PaymentTotals getTotal() {
    return totals;
  }

  @Deprecated
  public long getPaymentCount() {
    return (getTotal() != null) ? getTotal().getPaymentCount() : 0l;
  }
}
