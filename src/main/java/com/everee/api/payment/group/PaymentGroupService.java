package com.everee.api.payment.group;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.lookup.LookupIdEncoder;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.payment.request.lookup.PaymentRequestLookup;
import com.everee.api.payment.request.lookup.PaymentRequestLookupService;
import com.everee.api.payment.total.PaymentTotalService;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payperiod.lookup.PayPeriodLookup;
import com.everee.api.payperiod.lookup.PayPeriodLookupService;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentGroupService {
  private final OutstandingByDayGroupingStrategy outstandingByDayGroupingStrategy;
  private final OutstandingByDayAndStatusGroupingStrategy outstandingByDayAndStatusGroupingStrategy;
  private final OutstandingByDayAndStatusProcessingGroupingStrategy
      outstandingByDayAndStatusProcessingGroupingStrategy;
  private final PaymentLookupService paymentLookupService;
  private final LookupIdEncoder lookupIdEncoder;
  private final PayPeriodLookupService payPeriodLookupService;
  private final PaymentRequestLookupService paymentRequestLookupService;
  private final PaymentTotalService paymentTotalService;
  private final PaymentGroupDAO paymentGroupDAO;
  private final PaymentGroupRowConverter paymentGroupRowConverter;

  public int getPaymentGroupCount(@NotNull PaymentGroupLookup lookup) {
    return paymentLookupService.count(PaymentGroupLookupMapper.MAPPER.toPaymentLookup(lookup));
  }

  public PaymentGroup getPaymentGroup(@NotNull PaymentGroupLookup lookup) {
    var localizedTitles = new HashSet<LocalizedString>();
    var localizedDescriptions = new HashSet<LocalizedString>();
    var localizedDateRange = new AtomicReference<String>();
    var custom = false;

    if (lookup.getPayPeriodIds() != null && !lookup.getPayPeriodIds().isEmpty()) {
      var payPeriodId = lookup.getPayPeriodIds().stream().findFirst().orElseThrow();
      var payPeriodLookup = new PayPeriodLookup().withIds(Set.of(payPeriodId));
      var payPeriod = payPeriodLookupService.findOneOrThrow(payPeriodLookup);
      PaymentGroupDescriptor paymentGroupDescriptor = payPeriod;

      if (payPeriod.getPayPeriodType() == PayPeriodType.AD_HOC) {
        var paymentRequestLookup =
            new PaymentRequestLookup().withPayPeriodIds(Set.of(payPeriod.getId()));
        paymentGroupDescriptor = paymentRequestLookupService.findOneOrThrow(paymentRequestLookup);
        custom = true;
      }

      localizedTitles.add(paymentGroupDescriptor.getLocalizedTitle());
      localizedDescriptions.add(paymentGroupDescriptor.getLocalizedDescription());
      localizedDateRange.set(paymentGroupDescriptor.getLocalizedDateRange());
    }

    var paymentLookup = PaymentGroupLookupMapper.MAPPER.toPaymentLookup(lookup);
    var totals = paymentTotalService.getTotals(paymentLookup);

    localizedTitles.addAll(totals.getLocalizedTitles());
    localizedDescriptions.addAll(totals.getLocalizedDescriptions());

    return new PaymentGroup(
        lookupIdEncoder.toId(paymentLookup),
        paymentLookup,
        totals,
        null,
        custom,
        localizedTitles,
        localizedDescriptions,
        localizedDateRange.get());
  }

  public Page<PaymentGroup> listGroups(
      PaymentGroupingStrategyType strategyType,
      Sort.Direction direction,
      boolean usePerformance,
      Pageable pageable,
      PaymentGroupLookup paymentGroupLookup,
      Long companyId) {
    if (usePerformance) {
      Optional.ofNullable(companyId).ifPresent(paymentGroupLookup::setCompanyId);
      var result = paymentGroupDAO.listGroupSummaries(strategyType, pageable, paymentGroupLookup);
      return result.map(
          row -> paymentGroupRowConverter.convertRow(row, strategyType, paymentGroupLookup));
    }
    switch (strategyType) {
      case DAILY:
        return listDailyGroups(pageable, paymentGroupLookup);

      case OUTSTANDING:
        return outstandingByDayGroupingStrategy.listPaymentGroups(
            pageable, direction, paymentGroupLookup, companyId);

      case OUTSTANDING_BY_DAY_AND_STATUS:
        return outstandingByDayAndStatusGroupingStrategy.listPaymentGroups(
            pageable, direction, paymentGroupLookup, companyId);

      case OUTSTANDING_BY_DAY_AND_STATUS_PROCESSING_GROUPED:
        return outstandingByDayAndStatusProcessingGroupingStrategy.listPaymentGroups(
            pageable, direction, paymentGroupLookup, companyId);

      default:
        throw new IllegalArgumentException("Missing or invalid 'strategyType'");
    }
  }

  public Page<PaymentGroup> listDailyGroups(
      @NotNull Pageable pageable, PaymentGroupLookup paymentGroupLookup) {
    var offset = pageable.isPaged() ? pageable.getOffset() : 0;
    var pageSize = pageable.isPaged() ? pageable.getPageSize() : 20;

    var endDate = paymentGroupLookup.getMaxForDateInclusive();
    var startDate = paymentGroupLookup.getMinForDateInclusive();

    if (endDate == null) {
      endDate = LocalDate.now().minusDays(offset);
    }
    if (startDate == null) {
      startDate = endDate.minusDays(pageSize);
    }

    if (startDate.isAfter(endDate)) {
      throw new InvalidRequestException("Given start date cannot be after end date");
    }

    if (startDate.until(endDate, ChronoUnit.DAYS) > 31) {
      throw new InvalidRequestException("Maximum Range for Daily groups is 31 days");
    }

    var groups =
        startDate
            .datesUntil(endDate.plusDays(1))
            .sorted(Comparator.reverseOrder())
            .map(
                date ->
                    paymentGroupLookup.withMinForDateInclusive(date).withMaxForDateInclusive(date))
            .map(this::getPaymentGroup)
            .collect(Collectors.toList());

    return new PageImpl<>(groups);
  }
}
