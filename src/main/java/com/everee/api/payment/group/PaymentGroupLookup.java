package com.everee.api.payment.group;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import com.everee.api.config.LookupReportCriteria;
import com.everee.api.config.RequestParams;
import com.everee.api.config.resolver.DeprecatedApiParamName;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.payment.PaymentQueryStatus;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.PaymentType;
import com.everee.api.payperiod.PayPeriodType;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@With
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = true)
@RequestParams
@JsonInclude(NON_NULL)
public class PaymentGroupLookup {
  @JsonProperty("company-id")
  @ApiParam(name = "company-id", example = "1")
  private Long companyId;

  @JsonProperty("payment-type")
  @ApiParam(name = "payment-type", allowMultiple = true)
  private Set<PaymentType> paymentTypes;

  @JsonProperty("payment-status")
  @ApiParam(name = "payment-status", allowMultiple = true)
  private Set<PaymentStatus> paymentStatuses;

  public Set<PaymentStatus> getPaymentStatuses() {
    return Optional.ofNullable(paymentStatuses).orElse(new HashSet<>(PaymentStatus.VISIBLE));
  }

  @JsonAlias({"query-status", "status"})
  @JsonProperty("query-status")
  @LookupReportCriteria(name = "Payment Status(es)")
  @ApiParam(name = "query-status", allowMultiple = true)
  @DeprecatedApiParamName(name = {"status"})
  private Set<PaymentQueryStatus> queryStatuses;

  @Deprecated
  @JsonProperty(value = "status", access = JsonProperty.Access.READ_ONLY)
  public Set<PaymentQueryStatus> getQueryStatusesDeprecated() {
    return queryStatuses;
  }

  @JsonProperty("payment-request-id")
  @ApiParam(name = "payment-request-id", allowMultiple = true)
  private Long paymentRequestId;

  @JsonProperty("payable-payment-request-id")
  @ApiParam(name = "payable-payment-request-id", allowMultiple = true)
  private Set<Long> payablePaymentRequestIds;

  @JsonProperty("min-pay-date")
  @ApiParam(name = "min-pay-date")
  private LocalDate minPayDateInclusive;

  public LocalDate getMinPayDateInclusive() {
    validate(minPayDateInclusive, maxPayDateInclusive);
    return minPayDateInclusive;
  }

  @JsonProperty("max-pay-date")
  @ApiParam(name = "max-pay-date")
  private LocalDate maxPayDateInclusive;

  public LocalDate getMaxPayDateInclusive() {
    validate(minPayDateInclusive, maxPayDateInclusive);
    return maxPayDateInclusive;
  }

  @JsonProperty("min-for-date")
  @ApiParam(name = "min-for-date")
  private LocalDate minForDateInclusive;

  public LocalDate getMinForDateInclusive() {
    validate(minForDateInclusive, maxForDateInclusive);
    return minForDateInclusive;
  }

  @JsonProperty("max-for-date")
  @ApiParam(name = "max-for-date")
  private LocalDate maxForDateInclusive;

  public LocalDate getMaxForDateInclusive() {
    validate(minForDateInclusive, maxForDateInclusive);
    return maxForDateInclusive;
  }

  @JsonProperty("pay-period-id")
  @ApiParam(name = "pay-period-id", allowMultiple = true)
  private Set<Long> payPeriodIds;

  @JsonProperty("pay-period-type")
  @ApiParam(name = "pay-period-type", allowMultiple = true)
  @LookupReportCriteria(name = "Pay Period Type(s)")
  private Set<PayPeriodType> payPeriodTypes;

  @JsonProperty("min-schedule-date")
  @ApiParam(name = "min-schedule-date")
  private LocalDate minScheduleDateInclusive;

  public LocalDate getMinScheduleDateInclusive() {
    validate(minScheduleDateInclusive, maxScheduleDateInclusive);
    return minScheduleDateInclusive;
  }

  @JsonProperty("max-schedule-date")
  @ApiParam(name = "max-schedule-date")
  private LocalDate maxScheduleDateInclusive;

  public LocalDate getMaxScheduleDateInclusive() {
    validate(minScheduleDateInclusive, maxScheduleDateInclusive);
    return maxScheduleDateInclusive;
  }

  @JsonProperty("demoCompany")
  @ApiParam(name = "demoCompany")
  private Boolean demoCompany;

  public void validate(LocalDate min, LocalDate max) {
    if (min != null && max != null && min.isAfter(max)) {
      throw new InvalidRequestException("Min date cannot be later than max date");
    }
  }
}
