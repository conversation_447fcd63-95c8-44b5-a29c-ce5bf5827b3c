package com.everee.api.payment;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.BaseModelV2;
import com.everee.api.money.Money;
import com.everee.api.tax.PayerType;
import com.everee.api.tax.TaxType;
import com.everee.api.tax.UniqueTaxID;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PaymentTax extends BaseModelV2<PaymentTax> {

  @NotNull private Long paymentId;

  @NotNull
  @Enumerated(EnumType.STRING)
  private TaxType type;

  @NotNull
  @Setter(AccessLevel.PRIVATE)
  private String qualifiedType;

  public String getQualifiedType() {
    if (qualifiedType == null) qualifiedType = jurisdictionTaxId;
    return qualifiedType;
  }

  @NotNull
  @Deprecated
  @Enumerated(EnumType.STRING)
  private PayerType payerType;

  @Deprecated private String description;

  @NotNull private String jurisdictionTaxId;

  private boolean exempt = false;

  public PaymentTax setJurisdictionTaxId(String jurisdictionTaxId) {
    this.jurisdictionTaxId = jurisdictionTaxId;
    if (this.qualifiedType == null) this.qualifiedType = jurisdictionTaxId;
    return this;
  }

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "amount")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdAmount")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdAmount")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdAmount"))
  })
  private PaymentAmounts amounts = new PaymentAmounts();

  @Deprecated
  public Money getAmount() {
    return Optional.ofNullable(amounts).map(PaymentAmounts::getAmount).orElse(null);
  }

  @Deprecated
  public Money getYtdAmount() {
    return Optional.ofNullable(amounts).map(PaymentAmounts::getYtdAmount).orElse(null);
  }

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "subjectWages")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdSubjectWages")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdSubjectWages")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdSubjectWages"))
  })
  private PaymentAmounts subjectWageAmounts = new PaymentAmounts();

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "overrideSubjectWages")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdOverrideSubjectWages")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdOverrideSubjectWages")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdOverrideSubjectWages"))
  })
  private PaymentAmounts overrideSubjectWageAmounts = new PaymentAmounts();

  @Deprecated
  public Money getSubjectWages() {
    return Optional.ofNullable(subjectWageAmounts).map(PaymentAmounts::getAmount).orElse(null);
  }

  @Deprecated
  public Money getYtdSubjectWages() {
    return Optional.ofNullable(subjectWageAmounts).map(PaymentAmounts::getYtdAmount).orElse(null);
  }

  private Money uncappedSubjectWages = Money.ZERO;

  private Long taxTypeAccumulationId;

  /**
   * AAPL-10 The original amount for this payment tax. The original amount is the amount that the
   * employee was expected to pay based on their earnings in this payment. In the event of a net
   * shortfall, the actual amount will be reduced, but this value will reflect the original tax
   * liability. The original amount minus the carryover balance should equal the current amount.
   */
  private Money originalAmount = Money.ZERO;

  /**
   * AAPL-10 The carryover balance for this payment tax. A carryover occurs when an employee's gross
   * earnings are insufficient to cover the taxes due. This can happen in cases where the gross
   * earnings contain a significant amount of previously paid earnings, such as cash tips that have
   * already been taken home.
   */
  private Money carryOverBalance = Money.ZERO;

  @SuppressWarnings("unused")
  public PayerType getPayerType() {
    return Optional.ofNullable(type).map(TaxType::getPayerType).orElse(null);
  }

  @SuppressWarnings("unused")
  public LocalizedString getLocalizedDescription() {
    return Optional.ofNullable(type).map(TaxType::getLocalizedTitle).orElse(null);
  }

  public String getState() {
    return Optional.ofNullable(new UniqueTaxID(this.qualifiedType).getState())
        .map(state -> state.name())
        .orElse("");
  }

  public static PaymentTax combine(@NonNull PaymentTax a, @NonNull PaymentTax b) {
    var result = new PaymentTax();
    result.setType(Objects.requireNonNullElse(a.type, b.type));
    result.setJurisdictionTaxId(
        Objects.requireNonNullElse(a.jurisdictionTaxId, b.jurisdictionTaxId));
    result.setAmounts(PaymentAmounts.combine(a.amounts, b.amounts));
    result.setSubjectWageAmounts(
        PaymentAmounts.combine(a.subjectWageAmounts, b.subjectWageAmounts));
    result.setUncappedSubjectWages(
        Money.zeroOr(a.uncappedSubjectWages).plus(Money.zeroOr(b.uncappedSubjectWages)));
      result.setOverrideSubjectWageAmounts(
          PaymentAmounts.combine(a.overrideSubjectWageAmounts, b.overrideSubjectWageAmounts));

    return result;
  }

  public static Collector<PaymentTax, ?, Map<String, PaymentTax>> groupingByQualifiedType() {
    var seed = new PaymentTax();
    var reducer = Collectors.reducing(seed, PaymentTax::combine);
    return Collectors.groupingBy(PaymentTax::getQualifiedType, reducer);
  }
}
