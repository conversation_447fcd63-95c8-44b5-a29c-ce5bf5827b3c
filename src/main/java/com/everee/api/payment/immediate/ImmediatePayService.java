package com.everee.api.payment.immediate;

import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.integration.galileo.service.GalileoService;
import com.everee.api.integration.tabapay.dto.RetrieveTransactionResponse;
import com.everee.api.integration.tabapay.dto.TransactionCreateResponse;
import com.everee.api.integration.tabapay.service.TabapayService;
import com.everee.api.payment.distribution.PaymentDistribution;
import com.everee.api.payment.distribution.PaymentDistributionRepository;
import com.everee.api.payment.immediate.dto.PayCardImmediatePayResponse;
import com.everee.api.payment.immediate.mapper.PayCardImmediatePayResponseMapper;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ImmediatePayService {
  private final TabapayService tabapayService;
  private final GalileoService galileoService;
  private final PaymentDistributionRepository paymentDistributionRepository;
  private final PayCardImmediatePayResponseMapper payCardImmediatePayResponseMapper =
      PayCardImmediatePayResponseMapper.MAPPER;

  public TransactionCreateResponse createTransactionForRequest(
      @NotNull ImmediatePayRequest immediatePayRequest) {
    var paymentDistributions =
        immediatePayRequest.getPaymentDistributionIds().stream()
            .map(this::getDistributionOrThrow)
            .collect(Collectors.toList());
    return tabapayService.sendDistributionsAsSingleTransaction(paymentDistributions);
  }

  public RetrieveTransactionResponse retrieveTransaction(Long paymentDistributionId) {
    var paymentDistribution = getDistributionOrThrow(paymentDistributionId);
    return tabapayService.retrieveTransaction(paymentDistribution);
  }

  public PayCardImmediatePayResponse createPayCardPayment(
      @NotNull ImmediatePayRequest immediatePayRequest) {
    var paymentDistributions =
        immediatePayRequest.getPaymentDistributionIds().stream()
            .map(this::getDistributionOrThrow)
            .collect(Collectors.toList());

    return payCardImmediatePayResponseMapper.adjustmentToDto(
        galileoService.createAdjustment(paymentDistributions));
  }

  private PaymentDistribution getDistributionOrThrow(Long paymentDistributionId) {
    return paymentDistributionRepository
        .findById(paymentDistributionId)
        .orElseThrow(ResourceNotFoundException::new);
  }
}
