package com.everee.api.payment.total.v2;

import com.everee.api.auth.tenant.TenantSecurityContextHolder;
import com.everee.api.payment.PaymentQueryStatus;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.payment.request.lookup.PaymentRequestLookupService;
import com.everee.api.payment.total.BasePaymentTotalService;
import com.everee.api.payment.total.v3.PaymentTotals;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Deprecated
@Service(value = "paymentTotalServiceV2")
@RequiredArgsConstructor
@Slf4j
public class PaymentTotalService implements BasePaymentTotalService {
  private static final int MAX_BATCH_SIZE = 2000;

  private final EntityManager entityManager;
  private final PaymentLookupService paymentLookupService;
  private final PaymentRequestLookupService paymentRequestLookupService;

  private PaymentTotals getTotals(Set<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return new PaymentTotals();
    }

    var paymentIds = new ArrayList<>(ids);
    var size = paymentIds.size();
    return IntStream.range(1, (paymentIds.size() / MAX_BATCH_SIZE) + 2)
        .mapToObj(
            i -> paymentIds.subList(MAX_BATCH_SIZE * (i - 1), Math.min(MAX_BATCH_SIZE * i, size)))
        .map(pids -> getTotals(new PaymentLookup().setIds(new HashSet<>(pids))))
        .reduce(PaymentTotals::plus)
        .orElse(new PaymentTotals());
  }

  public PaymentTotals getTotals(PaymentLookup lookup) {
    // Break up if necessary
    if (!CollectionUtils.isEmpty(lookup.getIds())) {
      if (lookup.getIds().size() > MAX_BATCH_SIZE) {
        return getTotals(lookup.getIds());
      }
    }

    // Auth
    TenantSecurityContextHolder.getContext()
        .ifPresent(paymentLookupService.applyAuthorization(lookup));

    lookup.validate();

    var where = new StringBuffer(" WHERE 1 = 1");
    var parameters = new HashMap<String, Object>();

    if (lookup.getCompanyId() != null) {
      where.append("  AND p.companyid = :companyid");
      parameters.put("companyid", lookup.getCompanyId());
    }

    if (lookup.getEmployeeId() != null) {
      where.append("  AND p.employeeid = :employeeid");
      parameters.put("employeeid", lookup.getEmployeeId());
    }

    if (lookup.getMinForDateInclusive() != null) {
      where.append("  AND p.fordate >= :minfordate");
      parameters.put("minfordate", lookup.getMinForDateInclusive());
    }

    if (lookup.getMaxForDateInclusive() != null) {
      where.append("  AND p.fordate <= :maxfordate");
      parameters.put("maxfordate", lookup.getMaxForDateInclusive());
    }

    if (lookup.getMinPayDateInclusive() != null) {
      where.append("  AND p.paydate >= :minpaydate");
      parameters.put("minpaydate", lookup.getMinPayDateInclusive());
    }

    if (lookup.getMaxPayDateInclusive() != null) {
      where.append("  AND p.paydate <= :maxpaydate");
      parameters.put("maxpaydate", lookup.getMaxPayDateInclusive());
    }

    if (!CollectionUtils.isEmpty(lookup.getEmployeeIds())) {
      where.append("  AND p.employeeid in :employeeids");
      parameters.put("employeeids", lookup.getEmployeeIds());
    }

    if (!CollectionUtils.isEmpty(lookup.getApprovalGroupIds())) {
      where.append("  AND p.approvalgroupid in :approvalgroupids");
      parameters.put("approvalgroupids", lookup.getApprovalGroupIds());
    }

    if (!CollectionUtils.isEmpty(lookup.getPayPeriodIds())) {
      where.append("  AND p.payperiodid in :payperiodids");
      parameters.put("payperiodids", lookup.getPayPeriodIds());
    }

    if (!CollectionUtils.isEmpty(lookup.getIds())) {
      where.append("  AND p.id in :ids");
      parameters.put("ids", lookup.getIds());
    }

    if (!CollectionUtils.isEmpty(lookup.getQueryStatuses())) {
      where.append("  AND p.querystatus IN :querystatus");
      parameters.put(
          "querystatus",
          lookup.getQueryStatuses().stream()
              .map(PaymentQueryStatus::name)
              .collect(Collectors.toSet()));
    }

    if (!CollectionUtils.isEmpty(lookup.getPaymentStatuses())) {
      where.append("  AND p.status IN :paymentstatus");
      parameters.put(
          "paymentstatus",
          lookup.getPaymentStatuses().stream()
              .map(PaymentStatus::name)
              .collect(Collectors.toSet()));
    }

    if (!CollectionUtils.isEmpty(lookup.getPayablePaymentRequestIds())) {
      where.append("  AND payable.payablePaymentRequestId IN :payablePaymentRequestIds");
      parameters.put("payablePaymentRequestIds", lookup.getPayablePaymentRequestIds());
    }

    var query =
        "SELECT"
            + "   COUNT(p) as paymentcount, "
            + "   COUNT(DISTINCT p.employeeid) as workercount, "
            + "   COALESCE(SUM(CASE WHEN pd.amount > 0 OR prr.amount > 0 THEN 1 ELSE 0 END), 0) AS ptpcpaymentcount, "
            + "   MIN(p.fordate) AS minfordate, "
            + "   MAX(p.fordate) AS maxfordate, "
            + "   COALESCE(MIN(p.paydate), MIN(p.fordate)) AS minpaydate, "
            + "   COALESCE(MAX(p.paydate), MAX(p.fordate)) AS maxpaydate, "
            + "   SUM(p.grossearnings) as grossearnings, "
            + "   SUM(p.netearnings) as netearnings, "
            + "   SUM(p.totaltaxesee) as totaltaxesee, "
            + "   SUM(p.totaltaxeser) as totaltaxeser, "
            + "   SUM(p.totaltaxfunding) as totaltaxfunding, "
            + "   SUM(p.pretaxdeductions) as pretaxdeductions, "
            + "   SUM(p.posttaxdeductions) as posttaxdeductions, "
            + "   SUM(p.deferredcompensation) as deferredcompensation, "
            + "   SUM(p.previouslypaidearnings) as previouslypaidearnings, "
            + "   SUM(p.pretaxdeductions + p.deferredcompensation + p.posttaxdeductions) "
            + "       AS totaldeductionsee, "
            + "   SUM(p.totalcontributionser) AS totalcontributionser, "
            + "   SUM(p.pretaxdeductions + p.deferredcompensation + p.posttaxdeductions + "
            + "       p.totalcontributionser) AS totalcontributiondeductions, "
            + "   SUM(pcd.remittedamount) as totalcontributiondeductionfunding, "
            + "   SUM(p.totalfunding) as totalfunding, "
            + "   SUM(p.totalexpense) as totalexpense "
            + "FROM decoratedpayment p "
            + "LEFT JOIN (SELECT "
            + "   paymentid, "
            + "   SUM(amountee + amounter) as amount, "
            + "   SUM(amounter) as amounter, "
            + "   SUM(amountee) as amountee, "
            + "   SUM(CASE WHEN fundandremit=true THEN amountee+amounter ELSE 0.00 END) as remittedAmount "
            + "  FROM paymentcontributiondeduction group by paymentid) pcd "
            + "    ON pcd.paymentid=p.id "
            + "LEFT JOIN (SELECT "
            + "   paymentid, "
            + "   type, "
            + "   SUM(amount) as amount "
            + "  FROM paymentdistribution WHERE status NOT IN ('FAILED', 'CREATED_NEW_DISTRIBUTION') group by paymentid, type) pd "
            + "    ON pd.paymentid=p.id AND pd.type='PTPC'"
            + "LEFT JOIN (SELECT "
            + "   paymentid, "
            + "   SUM(amount) as amount "
            + "  FROM paymentrequestrecipient WHERE paymentmethod='INSTANT_DEPOSIT' group by paymentid) prr "
            + "    ON prr.paymentid=p.id "
            + "LEFT OUTER JOIN (select paymentid, payablepaymentrequestid from payable group by paymentid, payablepaymentrequestid) payable ON payable.paymentid = p.id "
            + where;

    var nativeQuery = entityManager.createNativeQuery(query);
    parameters.forEach((k, v) -> nativeQuery.setParameter(k, v));
    var results = nativeQuery.getResultList();

    if (results.isEmpty()) {
      return new PaymentTotals();
    }

    var result = (Object[]) results.get(0);
    var i = 0;

    var totals = new PaymentTotals();
    totals
        .setPaymentCount(((BigInteger) result[i++]).longValue())
        .setWorkerCount(((BigInteger) result[i++]).longValue())
        .setPtpcPaymentCount(((BigInteger) result[i++]).longValue())
        .setMinForDate(toDate(result[i++]))
        .setMaxForDate(toDate(result[i++]))
        .setMinPayDate(toDate(result[i++]))
        .setMaxPayDate(toDate(result[i++]))
        .setGrossEarnings(toMoney(result[i++]))
        .setNetEarnings(toMoney(result[i++]))
        .setTotalTaxesEE(toMoney(result[i++]))
        .setTotalTaxesER(toMoney(result[i++]))
        .setTotalTaxFunding(toMoney(result[i++]))
        .setPreTaxDeductions(toMoney(result[i++]))
        .setPostTaxDeductions(toMoney(result[i++]))
        .setDeferredCompensation(toMoney(result[i++]))
        .setPreviouslyPaidEarnings(toMoney(result[i++]))
        .setTotalDeductionsEE(toMoney(result[i++]))
        .setTotalContributionsER(toMoney(result[i++]))
        .setTotalContributionDeductions(toMoney(result[i++]))
        .setTotalContributionDeductionFunding(toMoney(result[i++]))
        .setTotalFunding(toMoney(result[i++]))
        .setTotalExpense(toMoney(result[i++]));

    return totals;
  }
}
