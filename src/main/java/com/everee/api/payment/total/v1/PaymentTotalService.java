package com.everee.api.payment.total.v1;

import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.payment.total.BasePaymentTotalService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Deprecated
@Service(value = "paymentTotalServiceV1")
@RequiredArgsConstructor
@Slf4j
public class PaymentTotalService implements BasePaymentTotalService {

  private final PaymentLookupService paymentLookupService;

  public PaymentTotal getTotals(@NonNull PaymentLookup lookup) {
    lookup.validate();
    var paymentTotal = new PaymentTotal();
    var payments = paymentLookupService.getConfiguredQuery(lookup).streamAll();
    payments.forEach(paymentTotal::accumulate);
    return paymentTotal;
  }
}
