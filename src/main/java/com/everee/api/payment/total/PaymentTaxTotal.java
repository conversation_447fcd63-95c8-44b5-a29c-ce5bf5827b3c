package com.everee.api.payment.total;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentTax;
import com.everee.api.tax.PayerType;
import com.everee.api.tax.TaxType;
import java.util.Optional;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PaymentTaxTotal implements Comparable<PaymentTaxTotal> {
  private final @NotNull TaxType taxType;
  private final @NotNull String jurisdictionTaxId;

  private @NotNull Money amount = Money.ZERO;
  private @NotNull Money ytdAmount = Money.ZERO;
  private @NotNull Money subjectWages = Money.ZERO;
  private @NotNull Money ytdSubjectWages = Money.ZERO;
  private @NotNull Money ytdOverrideSubjectWages = Money.ZERO;

  public PaymentTaxTotal(TaxType taxType, String jurisdictionTaxId) {
    this.taxType = taxType;
    this.jurisdictionTaxId = jurisdictionTaxId;
  }

  public void accumulate(PaymentTax tax) {
    if (taxType != tax.getType()) {
      throw new IllegalArgumentException("Tax types must match");
    }

    this.amount = accumulate(amount, tax.getAmounts().getAmount());
    this.ytdAmount = accumulate(ytdAmount, tax.getAmounts().getYtdAmount());
    this.subjectWages = accumulate(subjectWages, tax.getSubjectWageAmounts().getAmount());
    this.ytdSubjectWages = accumulate(ytdSubjectWages, tax.getSubjectWageAmounts().getYtdAmount());
    if (tax.getOverrideSubjectWageAmounts() != null && tax.getOverrideSubjectWageAmounts().getYtdAmount() != null) {
      this.ytdOverrideSubjectWages = accumulate(ytdOverrideSubjectWages, tax.getOverrideSubjectWageAmounts().getYtdAmount());
    } else {
      this.ytdOverrideSubjectWages = accumulate(ytdOverrideSubjectWages, Money.ZERO);
    }
  }

  public void accumulate(PaymentTaxTotal otherTotal) {
    if (taxType != otherTotal.getTaxType()) {
      throw new IllegalArgumentException("Tax types must match");
    }

    this.amount = accumulate(amount, otherTotal.getAmount());
    this.subjectWages = accumulate(subjectWages, otherTotal.getSubjectWages());
    this.ytdOverrideSubjectWages = accumulate(ytdOverrideSubjectWages, otherTotal.getYtdOverrideSubjectWages());
  }

  private Money accumulate(@NotNull Money left, Money right) {
    return Optional.ofNullable(right).map(left::plus).orElse(left);
  }

  @SuppressWarnings("unused")
  public PayerType getPayerType() {
    return Optional.ofNullable(taxType).map(TaxType::getPayerType).orElse(null);
  }

  @SuppressWarnings("unused")
  public LocalizedString getLocalizedDescription() {
    return Optional.ofNullable(taxType).map(TaxType::getLocalizedTitle).orElse(null);
  }

  public String getQualifiedType() {
    return this.jurisdictionTaxId;
  }

  @Override
  public int compareTo(PaymentTaxTotal o) {
    return this.getQualifiedType().compareTo(o.getQualifiedType());
  }
}
