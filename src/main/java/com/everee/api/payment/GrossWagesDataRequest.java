package com.everee.api.payment;

import java.math.BigInteger;
import java.util.List;

public class GrossWagesDataRequest {
  private List<Long> companiesIds;

  private int year;

  private List<BigInteger> locationIds;

  public List<Long> getCompaniesIds() {
    return companiesIds;
  }

  public void setCompaniesIds(List<Long> companyIds) {
    this.companiesIds = companyIds;
  }

  public int getYear() {
    return year;
  }

  public void setYear(int year) {
    this.year = year;
  }

  public List<BigInteger> getLocationIds() {
    return locationIds;
  }

  public void setLocationIds(List<BigInteger> locationIds) {
    this.locationIds = locationIds;
  }
}
