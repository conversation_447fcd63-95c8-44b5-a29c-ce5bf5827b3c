package com.everee.api.payment.funding;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import com.everee.api.payment.PaymentFundingType;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@RequestParams
public class PaymentFundingLookup implements Lookup {
  @ApiParam(name = "company-id", allowMultiple = true)
  private Set<Long> companyIds;

  @ApiParam(name = "company-funding-id", allowMultiple = true)
  private Set<Long> companyFundingIds;

  @ApiParam(name = "status", allowMultiple = true)
  private Set<PaymentFundingStatus> statuses;

  @ApiParam(name = "type", allowMultiple = true)
  private Set<PaymentFundingType> types;

  @ApiParam(name = "date-on-or-before")
  private LocalDate fundingDateOnOrBefore;

  @ApiParam(name = "payment-id", allowMultiple = true)
  private Set<Long> paymentIds;
}
