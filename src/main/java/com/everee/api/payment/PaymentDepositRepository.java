package com.everee.api.payment;

import com.everee.api.payment.distribution.WorkerDepositSummary;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface PaymentDepositRepository extends JpaRepository<PaymentDeposit, Long> {
  @Query(
      "SELECT "
          + "pd.id as distributionId, "
          + "pd.paymentId as paymentId, "
          + "pd.amounts.amount as depositAmount, "
          + "concat(u.firstName, ' ', u.lastName) as workerName, "
          + "pd.updatedAt as lastDepositUpdate"
          + " FROM Payment p"
          + " JOIN PaymentDeposit pd ON pd.paymentId=p.id"
          + " JOIN CoreEmployee e ON e.id=p.employeeId"
          + " JOIN CoreUser u ON u.id=e.userId"
          + " WHERE pd.id IN ?1 AND p.status <> 'DELETED'")
  List<WorkerDepositSummary> getWorkerDepositSummaries(Collection<Long> depositIds);

  @Modifying(clearAutomatically = true)
  @Query("UPDATE PaymentDeposit SET status=:status WHERE id IN :depositIds")
  @Transactional
  void setStatusForDepositIds(
      @Param("status") PaymentDepositStatus status,
      @Param("depositIds") Collection<Long> depositIds);

  @Modifying
  @Query("UPDATE PaymentDeposit pd SET pd.status = :status, pd.updatedAt=CURRENT_TIMESTAMP WHERE pd.paymentId IN :paymentIds")
  void updateStatusByPaymentIds(
      @Param("status") PaymentDepositStatus status,
      @Param("paymentIds") Collection<Long> paymentIds);

}
