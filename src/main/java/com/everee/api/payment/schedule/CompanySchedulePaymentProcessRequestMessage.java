package com.everee.api.payment.schedule;

import static com.everee.api.config.LogContextKeyNames.REQUEST_HEADER_ID;

import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

@Slf4j
@Data
@Accessors(chain = true)
public class CompanySchedulePaymentProcessRequestMessage {
  // Used for logging
  private String requestId;
  private Long companyId;
  private LocalDate scheduleDate;

  public CompanySchedulePaymentProcessRequestMessage() {
    try {
      requestId = MDC.get(REQUEST_HEADER_ID);
    } catch (Exception ex) {
      log.info("No " + REQUEST_HEADER_ID + " exists on the MDC.", ex);
    }
  }
}
