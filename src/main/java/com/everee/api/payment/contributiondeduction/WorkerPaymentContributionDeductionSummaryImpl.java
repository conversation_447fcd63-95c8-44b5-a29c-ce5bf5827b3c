package com.everee.api.payment.contributiondeduction;

import com.everee.api.money.Money;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@Data
@Accessors(chain = true)
public class WorkerPaymentContributionDeductionSummaryImpl implements WorkerPaymentContributionDeductionSummary {
    private String typeGuid;
    private String companyDisplayName;
    private String companyLegalName;
    private String firstName;
    private String middleName;
    private String lastName;
    private String workerId;
    private String externalWorkerId;
    private Money fundingAmount;
    private Money amountEE;
    private Money amountER;
    private String agency;
    private String agencyAddress;
    private String orderId;
    private String caseId;
    private String remittanceId;
    private String taxpayerIdLast4;
    private String taxpayerId;
}
