package com.everee.api.payment;

import static java.util.Optional.ofNullable;

import com.everee.api.companyemployeeimport.ExcelService;
import com.everee.api.money.Money;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.WillClose;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReturnedDepositsReportService {
  private final ExcelService excelService;
  private final PaymentLookupService paymentLookupService;

  private static final String SHEET_TITLE = "Returned Deposits";
  private static final List<String> HEADER_NAMES =
      List.of(
          "Worker",
          "Failed deposit amount",
          "Payment type",
          "Earning date",
          "Deposit last tried",
          "Everee Worker ID",
          "External Worker ID");

  public void generateReport(Sort sort, @WillClose OutputStream outputStream) throws IOException {
    var mutator = excelService.create();
    mutator.sheet(SHEET_TITLE);
    var row = new AtomicInteger(0);
    var col = new AtomicInteger(0);
    HEADER_NAMES.forEach((colName) -> setExcelCell(mutator, row, col, colName));

    // default sort
    if (sort == null || sort.isEmpty()) {
      sort = Sort.by(Sort.Order.asc(Payment_.UPDATED_AT));
    }

    var lookup = new PaymentLookup().setDepositStatuses(Set.of(PaymentDepositStatus.FAILED));

    paymentLookupService
        .streamAll(lookup, Pageable.unpaged(), sort)
        .forEach(
            item -> {
              col.set(0);
              row.getAndIncrement();
              // Worker
              setExcelCell(mutator, row, col, item.getPayeeDisplayFullName());

              // Failed deposit amount
              var failedDepositAmount =
                  item.getDeposits().stream()
                      .filter((deposit) -> deposit.getStatus() == PaymentDepositStatus.FAILED)
                      .map(PaymentDeposit::getAmounts)
                      .map(PaymentAmounts::getAmount)
                      .reduce(Money.ZERO, Money::plus);
              setExcelCell(mutator, row, col, failedDepositAmount.toDisplayString());

              // Payment type
              var paymentType = item.getType();
              setExcelCell(mutator, row, col, paymentType.getDisplayName());

              // Earning date
              var earningDate = ofNullable(item.getForDate()).map(LocalDate::toString).orElse(null);
              setExcelCell(mutator, row, col, earningDate);

              // Deposit last tried date
              var depositLastTriedDate = item.getUpdatedAt().toLocalDate().toString();
              setExcelCell(mutator, row, col, depositLastTriedDate);

              // IDs
              setExcelCell(mutator, row, col, item.getEmployee().getWorkerId());
              setExcelCell(mutator, row, col, item.getEmployee().getExternalWorkerId());
            });
    mutator.writeTo(outputStream);
  }

  private void setExcelCell(
      ExcelService.ExcelMutator mutator, AtomicInteger row, AtomicInteger col, String value) {
    mutator.cell(row.get(), col.getAndIncrement()).set(value);
  }
}
