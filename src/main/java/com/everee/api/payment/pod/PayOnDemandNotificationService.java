package com.everee.api.payment.pod;

import com.everee.api.companyrole.CompanyRole;
import com.everee.api.companyrole.CompanyRoleRepository;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.employee.CoreEmployee;
import com.everee.api.employee.CoreEmployeeRepository;
import com.everee.api.employee.Employee;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.storage.StorageService;
import com.everee.api.user.User;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PayOnDemandNotificationService {
  private final MessageSource messageSource;
  private final CoreEmployeeRepository coreEmployeeRepository;
  private final NotificationService notificationService;
  private final UserService userService;
  private final CompanyRoleRepository companyRoleRepository;
  private final PartnerRepository partnerRepository;
  private final StorageService storageService;

  @Async
  public void notifyNewPayOnDemandRequest(PayOnDemandRequest payOnDemandRequest) {
    final CoreEmployee requesterEmployee =
        coreEmployeeRepository.getOne(payOnDemandRequest.getEmployeeId());

    companyRoleRepository
        .streamAllByCompanyIdAndType(
            requesterEmployee.getCompanyId(), CompanyRoleType.FINANCIAL_MANAGER)
        .map(CompanyRole::getUserId)
        .map(userService::getUser)
        .distinct()
        .forEach(
            accountOwner ->
                sendNotificationFor(payOnDemandRequest, requesterEmployee, accountOwner));
  }

  private void sendNotificationFor(
      PayOnDemandRequest payOnDemandRequest, Employee requesterEmployee, User accountOwner) {
    var partner =
        partnerRepository.findByCompanyId(payOnDemandRequest.getCompanyId()).orElseThrow();
    var payOnDemandId = payOnDemandRequest.getId();
    var companyId = requesterEmployee.getCompanyId();
    var requesterName = requesterEmployee.getDisplayFullName();
    var notification =
        new PayOnDemandNotification(
            partner, companyId, requesterName, payOnDemandId, messageSource, storageService);

    notificationService.enqueueUserNotification(notification, accountOwner.getId());
  }
}
