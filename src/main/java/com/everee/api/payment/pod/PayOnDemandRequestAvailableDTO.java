package com.everee.api.payment.pod;

import static com.everee.api.util.ObjectUtils.allPresent;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.DateRange;
import com.everee.api.money.Money;
import java.time.Duration;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.context.i18n.LocaleContextHolder;

@Data
@Accessors(chain = true)
public class PayOnDemandRequestAvailableDTO {
  private LocalDate startDate;

  private LocalDate endDate;

  private Duration totalTimeWorked = Duration.ZERO;

  private Duration availableTimeWorked = Duration.ZERO;

  private Money grossEarnings = Money.ZERO;

  private LocalizedString message;

  public PayOnDemandRequestAvailableDTO setEarningsZeroWithMessage(LocalizedString message) {
    setGrossEarnings(Money.ZERO);
    setMessage(message);

    return this;
  }

  @SuppressWarnings("unused")
  public String getLocalizedDateRange() {
    if (allPresent(startDate, endDate)) {
      return DateRange.of(startDate, endDate)
          .getLocalizedDescription(LocaleContextHolder.getLocale());
    } else {
      return null;
    }
  }
}
