package com.everee.api.payment.tax;

import com.everee.api.payment.PaymentTax;
import com.everee.api.payment.PaymentTaxGroup;
import com.everee.api.tax.PayerType;
import com.everee.api.tax.TaxType;
import com.everee.api.tax.UniqueTaxID;
import com.everee.api.taxauthority.jurisdiction.TaxJurisdiction;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaymentTaxService {
  private final PaymentTaxRepository paymentTaxRepository;

  public void movePaymentTaxesForEmployee(
      Long employeeId, TaxJurisdiction fromJurisdiction, TaxJurisdiction toJurisdiction) {
    paymentTaxRepository.movePaymentTaxes(
        fromJurisdiction.getId(), toJurisdiction.getId(), employeeId);
  }

  public void deletePaymentTaxesForEmployee(Long employeeId, TaxJurisdiction toDeleteJurisdiction) {
    paymentTaxRepository.removePaymentTaxes(toDeleteJurisdiction.getId(), employeeId);
  }

  /**
   * Generates a grouping key for a given {@link PaymentTax} based on its jurisdiction tax id and tax type code excluding ER_ prefix.
   *
   * <p>The grouping is payer-type agnostic. i.e. Taxes with similar attributes but different payer types (e.g., {@code TaxType.CITY} and {@code TaxType.ER_CITY})
   * will be grouped together under the same key.
   *
   * <p>The grouping key consists of the following components:
   * <ul>
   *   <li>State code</li>
   *   <li>County code</li>
   *   <li>Feature code</li>
   *   <li>{@link TaxType} Tax type code excluding ER_ prefix</li>
   *   <li>School district code</li>
   * </ul>
   *
   * <p>Example:
   * <ul>
   *   <li>Jurisdiction Tax ID: "01-**********-ER_CITY-000"</li>
   *   <li>Tax Type Code: "ER_CITY"</li>
   *   <li>Generated Key: "01-**********-CITY-000"</li>
   * </ul>
   *
   * @param tax the {@link PaymentTax} for which the grouping key is generated
   * @return a {@code String} key for grouping taxes in a payer-type agnostic manner
   */
  private String getGroupingKey(PaymentTax tax) {
    var parts = new UniqueTaxID(tax.getJurisdictionTaxId()).getParts(); // e.g. "01-**********-ER_CITY-000" --> ["01", "000", "122701", "ER_CITY", "000"]

    var taxTypeExcludingER = parts[UniqueTaxID.Part.TAX_TYPE_CODE.ordinal()]
      .replace("ER_", ""); // e.g. "ER_CITY" --> "CITY"

    return String.join(
      UniqueTaxID.getDELIM(), // "-"
      parts[UniqueTaxID.Part.STATE_CODE.ordinal()], // e.g. "01"
      parts[UniqueTaxID.Part.COUNTY_CODE.ordinal()], // e.g. "000"
      parts[UniqueTaxID.Part.FEATURE_CODE.ordinal()], // e.g. "122701"
      taxTypeExcludingER,  // e.g. "CITY"
      parts[UniqueTaxID.Part.SCHOOL_DISTRICT_CODE.ordinal()] // e.g. "000"
    );
  }

  /**
   * Groups a set of {@link PaymentTax} objects. Each group is represented by a {@link PaymentTaxGroup} containing a
   * description, state, and employee and employer tax details.
   *
   * <p>Examples:
   * <ul>
   *   <li>For federal taxes like {@code TaxType.FICA} or {@code TaxType.ER_FICA}, the generated description
   *       is "Social Security" because federal taxes do not have a feature code.</li>
   *   <li>For local taxes like {@code TaxType.CITY} or {@code TaxType.ER_CITY}, the generated description
   *       includes the feature code, e.g., "1234 City Tax", because local taxes have a feature code.</li>
   * </ul>
   *
   * @param taxes the set of {@link PaymentTax} objects to be grouped
   * @return a {@code Set} of {@link PaymentTaxGroup} objects representing grouped taxes
   */
  public Set<PaymentTaxGroup> groupTaxesByJurisdictionId(Set<PaymentTax> taxes) {
    var taxGroupMap = taxes.stream()
      .collect(Collectors.groupingBy(this::getGroupingKey));

    return taxGroupMap.entrySet().stream()
      .map(group -> {
        var employeeTax = group.getValue().stream()
          .filter(tax -> tax.getPayerType() == PayerType.EMPLOYEE)
          .findFirst()
          .orElse(null);

        var employerTax = group.getValue().stream()
          .filter(tax -> tax.getPayerType() == PayerType.EMPLOYER)
          .findFirst()
          .orElse(null);

        var tax = employeeTax != null ? employeeTax : employerTax;

        if (tax == null) {
          return null;
        }

        // feature code is needed for local taxes (e.g. City, County, etc.)
        var featureCode = new UniqueTaxID(tax.getJurisdictionTaxId())
          .getPart(UniqueTaxID.Part.FEATURE_CODE)
          .replaceAll("0000", "");

        var localizedTitle = tax.getType().getLocalizedTitleExcludingER();

        var description = featureCode.isEmpty() ? localizedTitle : featureCode + " " + localizedTitle;

        return PaymentTaxGroup.of(
          group.getKey(),
          description,
          tax.getState(),
          employeeTax,
          employerTax
        );
      })
      .collect(Collectors.toSet());
  }
}
