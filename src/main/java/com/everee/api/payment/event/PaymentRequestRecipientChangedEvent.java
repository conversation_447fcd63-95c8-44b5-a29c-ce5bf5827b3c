package com.everee.api.payment.event;

import com.everee.api.event.ResourceEvent;
import com.everee.api.payment.request.entity.PaymentRequestRecipient;

public class PaymentRequestRecipientChangedEvent extends ResourceEvent {

  private boolean _recalculateExistingPayment = true;

  public PaymentRequestRecipientChangedEvent(PaymentRequestRecipient recipient) {
    super(recipient.getCompanyId(), recipient);
  }

  public PaymentRequestRecipientChangedEvent(
      PaymentRequestRecipient recipient, boolean recalculateExistingPayment) {
    super(recipient.getCompanyId(), recipient);
    _recalculateExistingPayment = recalculateExistingPayment;
  }

  public PaymentRequestRecipient getRecipient() {
    return (PaymentRequestRecipient) source;
  }

  @Override
  public String getResourceName() {
    return "payment";
  }

  @Override
  public String getResourceId() {
    return getRecipient().getId().toString();
  }

  @Override
  public String getActionName() {
    return "requested";
  }

  public boolean getRecalculateExistingPayment() {
    return _recalculateExistingPayment;
  }
}
