package com.everee.api.payment.event;

import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.time.WorkerLocalTimeService;
import com.everee.api.worker.event.WorkerApprovalGroupChangedEvent;
import java.time.ZonedDateTime;
import java.util.Set;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentEventListener {
  private final ApplicationEventPublisher eventPublisher;
  private final PaymentLookupService paymentLookupService;
  private final WorkerLocalTimeService workerLocalTimeService;

  @EventListener
  public void handleApprovalGroupChangedEvent(@NonNull WorkerApprovalGroupChangedEvent event) {
    var employee = event.getEmployee();
    var approvalGroupId = employee.getApprovalGroupId();
    var minEffectDate =
        workerLocalTimeService.localTimestamp(employee, ZonedDateTime.now()).toLocalDate();
    var lookup =
        new PaymentLookup()
            .setEmployeeIds(Set.of(employee.getId()))
            .setMinForDateInclusive(minEffectDate)
            .setSkipApplyAuthorization(true);

    // find and update any affected payments
    paymentLookupService
        .streamAll(lookup, Pageable.unpaged())
        .forEach(t -> t.setApprovalGroupId(approvalGroupId));
  }

  @Async
  @EventListener
  public void publishPaymentPaidEvent(PaymentStatusChangedEvent event) {
    var payment = event.getPayment();
    var companyId = payment.getCompanyId();

    if (payment.getStatus() != PaymentStatus.PAID) return;

    var payload = new PaymentWebhookEventPayload();
    payload.setCompanyId(payload.getCompanyId());
    payload.setWorkerId(payment.getEmployee().getWorkerId());
    payload.setExternalWorkerId(payment.getEmployee().getExternalWorkerId());
    payload.setPaymentId(payment.getId());

    eventPublisher.publishEvent(new PaymentPaidEvent(companyId, payment, payload));
  }
}
