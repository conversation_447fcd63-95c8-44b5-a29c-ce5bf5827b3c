package com.everee.api.payment.event;

import com.everee.api.event.ResourceEvent;
import com.everee.api.payment.request.entity.PaymentRequest;
import com.everee.api.payment.request.entity.PaymentRequestRecipient;
import java.util.List;

public class PaymentRequestRecipientsAddedEvent extends ResourceEvent {

  private final List<PaymentRequestRecipient> addedRecipients;

  public PaymentRequestRecipientsAddedEvent(
      PaymentRequest request, List<PaymentRequestRecipient> addedRecipients) {
    super(request.getCompanyId(), request);
    this.addedRecipients = addedRecipients;
  }

  public PaymentRequest getRequest() {
    return (PaymentRequest) getSource();
  }

  public List<PaymentRequestRecipient> getAddedRecipients() {
    return this.addedRecipients;
  }

  @Override
  public String getResourceId() {
    return getRequest().getId().toString();
  }

  @Override
  public String getResourceName() {
    return "payment-batch";
  }

  @Override
  public String getActionName() {
    return "recipients-added";
  }
}
