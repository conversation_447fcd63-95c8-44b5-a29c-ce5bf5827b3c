package com.everee.api.payment.request.lookup;

import static com.everee.api.model.BaseModel_.ID;
import static com.everee.api.payment.request.entity.PaymentRequest_.COMPANY_ID;
import static com.everee.api.payment.request.entity.PaymentRequest_.PAY_PERIOD_ID;
import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.payment.request.entity.PaymentRequest;
import com.everee.api.query.Query;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentRequestLookupService
    implements LookupService<PaymentRequest, PaymentRequestLookup, Query<PaymentRequest>> {
  private final EntityManager entityManager;

  @Override
  public void configureQuery(PaymentRequestLookup lookup, Query<PaymentRequest> query) {
    query
        .where(property(ID).in(lookup.getIds()))
        .where(property(COMPANY_ID).in(lookup.getCompanyIds()))
        .where(property(PAY_PERIOD_ID).in(lookup.getPayPeriodIds()));
  }

  @Override
  public Query<PaymentRequest> createQuery() {
    return new Query<>(entityManager, PaymentRequest.class);
  }
}
