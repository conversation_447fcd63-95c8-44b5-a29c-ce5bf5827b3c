package com.everee.api.payment.request.entity;

import com.everee.api.earnings.EarningType;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.BaseModel;
import com.everee.api.model.DateRange;
import com.everee.api.payment.group.PaymentGroupDescriptor;
import com.everee.api.payment.request.PaymentRequestPaymentMethod;
import com.everee.api.payperiod.PayPeriod;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.context.i18n.LocaleContextHolder;

/** <AUTHOR> */
@Data
@Entity
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PaymentRequest extends BaseModel implements PaymentGroupDescriptor {

  @NotNull private Long companyId;

  private Long payPeriodId;

  @ToString.Exclude
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "payPeriodId", insertable = false, updatable = false)
  private PayPeriod payPeriod;

  public void setPayPeriod(PayPeriod payPeriod) {
    this.payPeriod = payPeriod;
    this.payPeriodId = (payPeriod != null) ? payPeriod.getId() : null;
  }

  @NotNull
  @Enumerated(EnumType.STRING)
  private EarningType type;

  @NotNull private String title;

  @NotNull private Long requestedByUserId;

  @Deprecated
  @NotNull
  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate targetPaymentDate;

  @ToString.Exclude
  @OneToMany(mappedBy = "paymentRequestId", cascade = CascadeType.ALL, orphanRemoval = true)
  private List<PaymentRequestRecipient> recipients = new ArrayList<>();

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentRequestPaymentTimeframe paymentTimeframe;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentRequestPaymentMethod paymentMethod;

  @Override
  public LocalDate getEffectiveDate() {
    return targetPaymentDate;
  }

  @Override
  public LocalizedString getLocalizedTitle() {
    return type.getLocalizedTitle();
  }

  @Override
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.ofUnlocalized(title);
  }

  @Override
  public String getLocalizedDateRange() {
    return DateRange.of(targetPaymentDate, targetPaymentDate)
        .getLocalizedDescription(LocaleContextHolder.getLocale());
  }

  private LocalDate scheduleDate;
}
