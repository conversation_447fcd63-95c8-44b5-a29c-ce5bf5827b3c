package com.everee.api.payment.request.entity;

import static javax.persistence.CascadeType.*;

import com.everee.api.earnings.EarningType;
import com.everee.api.earnings.EarningUnitBasis;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeduction;
import com.everee.api.model.BaseModelV2;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentQueryStatus;
import com.everee.api.payment.request.PaymentRequestPaymentMethod;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.tax.TaxCalcMethod;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Formula;

/** <AUTHOR> */
@Accessors(chain = true)
@Data
@Entity
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PaymentRequestRecipient extends BaseModelV2<PaymentRequestRecipient> {

  @NotNull private Long companyId;
  @NotNull private Long employeeId;
  @NotNull private Long paymentRequestId;

  @Formula("(select e.integrationId from Employee e where e.id = employeeid)")
  private String workerId;

  @Formula("(select e.externalWorkerId from Employee e where e.id = employeeid)")
  private String externalWorkerId;

  @Setter(AccessLevel.NONE)
  @Formula(
      "(select concat("
          + "    u.firstname, ' ',"
          + "    case when u.middlename is null or u.middlename = '' then null else concat(substring(u.middlename from 1 for 1), ' ') end,"
          + "    u.lastname)"
          + " from appuser u join employee e on e.userid = u.id where e.id = employeeid)")
  private String workerFullName;

  @Setter(AccessLevel.NONE)
  @Formula("(select e.employmenttype = 'EMPLOYEE' from employee e where e.id = employeeid)")
  private Boolean workerTaxable;

  @Setter(AccessLevel.NONE)
  @Formula("(select pr.type from PaymentRequest pr where pr.id = paymentRequestId)")
  @Enumerated(EnumType.STRING)
  private EarningType earningType;

  @Deprecated
  @Setter(AccessLevel.NONE)
  @Formula("(select pr.targetPaymentDate from PaymentRequest pr where pr.id = paymentRequestId)")
  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate targetPaymentDate;

  @Formula("(select pr.paymentTimeframe from PaymentRequest pr where pr.id = paymentRequestId)")
  @Enumerated(EnumType.STRING)
  private PaymentRequestPaymentTimeframe paymentTimeframe;

  @Enumerated(EnumType.STRING)
  private PaymentRequestPaymentMethod paymentMethod;

  @Setter(AccessLevel.NONE)
  @Formula("(select pr.title from PaymentRequest pr where pr.id = paymentRequestId)")
  private String title;

  @Setter(AccessLevel.NONE)
  @Formula("(select p.queryStatus from decoratedpayment p where p.id = paymentId)")
  @Enumerated(EnumType.STRING)
  private PaymentQueryStatus paymentStatus;

  @Setter(AccessLevel.NONE)
  @Formula("(select p.payDate from decoratedpayment p where p.id = paymentId)")
  private LocalDate payDate;

  private Long paymentId;

  @NotNull private Money amount;

  @NotNull
  @Enumerated(EnumType.STRING)
  private TaxCalcMethod taxCalcMethod;

  private String note;

  private Long payPeriodId;

  @ToString.Exclude
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "payPeriodId", insertable = false, updatable = false)
  private PayPeriod payPeriod;

  public void setPayPeriod(PayPeriod payPeriod) {
    this.payPeriod = payPeriod;
    this.payPeriodId = (payPeriod != null) ? payPeriod.getId() : null;
  }

  @Fetch(FetchMode.SUBSELECT)
  @OneToMany(
      fetch = FetchType.EAGER,
      cascade = {PERSIST, MERGE, REFRESH})
  @JoinColumn(name = "recipientId")
  private Set<EmployeeContributionDeduction> employeeContributionDeductions = new HashSet<>();

  @Setter(AccessLevel.NONE)
  @Formula(
      "(SELECT c.ptpcEnabled AND tba.accountid is not null FROM employee e JOIN company c ON c.id=e.companyid LEFT JOIN tabapayaccount tba ON tba.userid=e.userId AND tba.accountType='CARD' WHERE e.id=employeeId)")
  private Boolean ptpcEnabled;

  @Setter(AccessLevel.NONE)
  @Formula(
      "(SELECT coalesce(SUM(pd.amount), 0.00) FROM paymentdistribution pd WHERE pd.paymentId = paymentId AND pd.type='PTPC' AND pd.status IN ('DISTRIBUTED', 'SUBMITTED'))")
  private Money ptpcDistributedTotal;

  @Enumerated(EnumType.STRING)
  @Transient
  private EarningUnitBasis unitBase;

  public EarningUnitBasis getUnitBasis() {
    return EarningUnitBasis.COST;
  }
}
