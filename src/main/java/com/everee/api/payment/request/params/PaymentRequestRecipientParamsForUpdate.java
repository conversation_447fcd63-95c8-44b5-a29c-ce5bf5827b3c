package com.everee.api.payment.request.params;

import com.everee.api.employee.contributiondeduction.ContributionDeductionParams;
import com.everee.api.money.Money;
import com.everee.api.payment.request.PaymentRequestPaymentMethod;
import com.everee.api.tax.TaxCalcMethod;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PaymentRequestRecipientParamsForUpdate {
  private Long id;
  @NotNull private Money amount;
  @NotNull private TaxCalcMethod taxCalcMethod;
  private PaymentRequestPaymentMethod paymentMethod;
  private String note;
  private Set<ContributionDeductionParams> employeeContributionDeductions;
}
