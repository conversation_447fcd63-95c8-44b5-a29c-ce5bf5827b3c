package com.everee.api.payment.notification;

import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import org.springframework.context.MessageSource;

@Value
@EqualsAndHashCode(callSuper = true)
public class SchedulePaymentErrorNotification extends TransactionalSingleActionEmailTemplate {
  @NonNull Partner partner;
  @NonNull String companyName;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;
  @NonNull LocalDate scheduleDate;
  @NonNull String paymentDescription;
  @NonNull String buttonUrl;

  @Override
  public String getEmailSubject() {
    return getLocalizedMessage("un.schedulepayment.error.email.subject");
  }

  @Override
  public String getPreheader() {
    return null;
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage("un.schedulepayment.error.email.banner-text");
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage(
        "un.schedulepayment.error.email.body-paragraph1-text", getLocalizedDate(scheduleDate));
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage(
        "un.schedulepayment.error.email.body-paragraph2-text", paymentDescription);
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("un.schedulepayment.error.email.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    return buttonUrl;
  }
}
