package com.everee.api.payment.notification.submitted;

import com.everee.api.money.Money;
import com.everee.api.payment.lookup.PaymentLookup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class PaymentSubmittedNotificationRequest {
  @NonNull Long companyId;

  PaymentLookup paymentGroupLookup;

  int payeeCount;
  Money totalToFund;
  Money grossPay;
  Money taxes;
  Money contributionsAndDeductions;
  Money overtime;
  Money pto;
  Money holiday;

  private boolean autoPayroll;
}
