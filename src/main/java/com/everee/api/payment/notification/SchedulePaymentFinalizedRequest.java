package com.everee.api.payment.notification;

import com.everee.api.money.Money;
import com.everee.api.payment.lookup.PaymentLookup;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SchedulePaymentFinalizedRequest {
  @NonNull Long companyId;
  @NonNull Long scheduledByUserId;

  @NonNull
  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  LocalDate scheduleDate;

  @NonNull String paymentDescription;

  PaymentLookup paymentGroupLookup;
  PaymentLookup errorGroupLookup;

  int payeeCount;
  int errorCount;
  Money totalToFund;
  Money grossPay;
  Money taxes;
  Money contributionsAndDeductions;
  Money overtime;
  Money pto;
  Money holiday;
}
