package com.everee.api.payment.notification.generated;

import static com.everee.api.payment.PaymentQueryStatus.*;
import static com.everee.api.payment.notification.SchedulePaymentNotificationService.buildDetailPaymentPageLink;

import com.everee.api.company.CompanyService;
import com.everee.api.company.autopayroll.CompanyAutoPayrollSettingsRepository;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.money.Money;
import com.everee.api.notification.email.EmailRecipient;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.payment.PaymentQueryStatus;
import com.everee.api.payment.PaymentType;
import com.everee.api.payment.funding.PaymentFunding;
import com.everee.api.payment.funding.PaymentFundingStatus;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payperiod.lookup.PayPeriodLookup;
import com.everee.api.payperiod.lookup.PayPeriodLookupService;
import com.everee.api.storage.StorageService;
import com.everee.api.user.DetailedUserRepository;
import com.everee.api.util.DateUtil;
import java.time.LocalDate;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentGeneratedNotificationService {

  private final CompanyService companyService;
  private final NotificationService notificationService;
  private final PartnerRepository partnerRepository;
  private final MessageSource messageSource;
  private final StorageService storageService;
  private final PayPeriodLookupService payPeriodLookupService;
  private final PaymentLookupService paymentLookupService;
  private final CompanyAutoPayrollSettingsRepository companyAutoPayrollSettingsRepository;
  private final DetailedUserRepository userRepository;
  private static Set VALID_PAYMENT_TYPES = Set.of(PaymentType.PAYROLL, PaymentType.IMPORT);
  private static Set DISQUALIFIED_PAYMENT_STATUS_TYPES =
      Set.of(PENDING_APPROVAL, READY_TO_CALCULATE, ERROR);
  private static Set UNVERIFIED_PAYMENT_STATUS_TYPES =
      Set.of(PaymentQueryStatus.PENDING_VERIFICATION);
  private static Set VALID_PAY_PERIOD_TYPES =
      Set.of(
          PayPeriodType.DAILY,
          PayPeriodType.WEEKLY,
          PayPeriodType.BI_WEEKLY,
          PayPeriodType.SEMI_MONTHLY,
          PayPeriodType.MONTHLY,
          PayPeriodType.ANNUAL);

  @Transactional
  public void sendPaymentGeneratedNotifications(LocalDate contextDate) {
    companyAutoPayrollSettingsRepository
        .streamAllByEnabled(true)
        .forEach(
            companyAutoPayrollSettings ->
                sendPaymentGeneratedNotifications(
                    companyAutoPayrollSettings.getCompanyId(), contextDate));
  }

  private void sendPaymentGeneratedNotifications(Long companyId, LocalDate contextDate) {

    var company = companyService.getCompany(companyId);
    var partner = partnerRepository.findByCompanyId(companyId).orElseThrow();

    // Generate unique email for each different pay period type
    VALID_PAY_PERIOD_TYPES.stream()
        .forEach(
            payPeriodType -> {
              var totalPayees = new AtomicLong(0);
              var disqualifiedPayees = new AtomicLong(0);
              var unverifiedPayees = new AtomicLong(0);

              var totalToFund = new AtomicReference<Money>(new Money("0"));
              var grossPay = new AtomicReference<Money>(new Money("0"));
              var taxes = new AtomicReference<Money>(new Money("0"));
              var benefits = new AtomicReference<Money>(new Money("0"));
              var overtime = new AtomicReference<Money>(new Money("0"));
              var pto = new AtomicReference<Money>(new Money("0"));
              var holiday = new AtomicReference<Money>(new Money("0"));

              var payPeriodLookup =
                  new PayPeriodLookup()
                      .withPayPeriodTypes(Set.of((PayPeriodType) payPeriodType))
                      .withMinEndDateInclusive(contextDate.minusDays(1))
                      .withMaxEndDateInclusive(contextDate.minusDays(1))
                      .withCompanyIds(Set.of(companyId));

              var payPeriodIds =
                  payPeriodLookupService
                      .listAll(payPeriodLookup, Pageable.unpaged())
                      .map(payPeriod -> payPeriod.getId())
                      .toSet();

              if (payPeriodIds.isEmpty()) {
                return;
              }

              // Lookup payments
              var paymentLookup =
                  new PaymentLookup()
                      .withPayPeriodIds(payPeriodIds)
                      .withPaymentTypes(VALID_PAYMENT_TYPES);

              var payments = paymentLookupService.findAll(paymentLookup);

              if (payments.isEmpty()) {
                return;
              }

              var minFundingDate = new AtomicReference<LocalDate>();
              var minPayDate = new AtomicReference<LocalDate>();
              // Accumulate data from payments
              payments.stream()
                  .forEach(
                      payment -> {
                        if (DISQUALIFIED_PAYMENT_STATUS_TYPES.contains(payment.getQueryStatus())) {
                          disqualifiedPayees.getAndAdd(1L);
                          return;
                        } else if (UNVERIFIED_PAYMENT_STATUS_TYPES.contains(
                            payment.getQueryStatus())) {
                          unverifiedPayees.getAndAdd(1L);
                          return;
                        }

                        totalPayees.getAndAdd(1L);
                        totalToFund.set(
                            totalToFund.get().plus(Money.zeroOr(payment.getTotalFunding())));
                        grossPay.set(
                            grossPay.get().plus(payment.getGrossEarningsAmounts().getAmount()));
                        taxes.set(taxes.get().plus(Money.zeroOr(payment.getTotalTaxesER())));
                        overtime.set(
                            overtime.get().plus(Money.zeroOr(payment.getOvertimeEarnings())));
                        pto.set(pto.get().plus(Money.zeroOr(payment.getPtoEarnings())));
                        holiday.set(holiday.get().plus(Money.zeroOr(payment.getHolidayEarnings())));
                        benefits.set(
                            benefits
                                .get()
                                .plus(Money.zeroOr(payment.getTotalContributionsER()))
                                .plus(Money.zeroOr(payment.getTotalDeductionsEE())));
                        minPayDate.set(DateUtil.minDate(minPayDate.get(), payment.getPayDate()));
                        minFundingDate.set(
                            DateUtil.minDate(
                                minFundingDate.get(),
                                payment.getFundings().stream()
                                    .filter(
                                        pf ->
                                            pf.getStatus()
                                                != PaymentFundingStatus.CREATED_NEW_FUNDING)
                                    .map(PaymentFunding::getFundingDate)
                                    .findFirst()
                                    .orElse(minFundingDate.get())));
                      });

              // Send emails to all financial managers
              userRepository
                  .streamAllByCompanyRole(companyId, CompanyRoleType.FINANCIAL_MANAGER)
                  .forEach(
                      user -> {
                        var email =
                            new PaymentGeneratedNotification(
                                partner,
                                companyId,
                                company.getDisplayName(),
                                user.getDisplayFullName(),
                                messageSource,
                                storageService,
                                contextDate.minusDays(1),
                                minFundingDate.get(),
                                minPayDate.get(),
                                disqualifiedPayees.get(),
                                unverifiedPayees.get(),
                                ((PayPeriodType) payPeriodType).getDisplayName(),
                                totalPayees.get(),
                                totalToFund.get(),
                                grossPay.get(),
                                taxes.get(),
                                benefits.get(),
                                overtime.get(),
                                pto.get(),
                                holiday.get(),
                                buildDetailPaymentPageLink(
                                    new PaymentLookup()
                                        .withQueryStatuses(Set.of(AUTO_APPROVED))
                                        .withPayPeriodIds(payPeriodIds)),
                                buildDetailPaymentPageLink(
                                    new PaymentLookup()
                                        .withQueryStatuses(DISQUALIFIED_PAYMENT_STATUS_TYPES)
                                        .withPayPeriodIds(payPeriodIds)),
                                buildDetailPaymentPageLink(
                                    new PaymentLookup()
                                        .withQueryStatuses(UNVERIFIED_PAYMENT_STATUS_TYPES)
                                        .withPayPeriodIds(payPeriodIds)));
                        var recipient = new EmailRecipient(user.getFullName(), user.getEmail());
                        notificationService.enqueueEmailNotification(email, recipient);
                      });
            });
  }
}
