package com.everee.api.payment.paymentdeductionoverride;

import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class PaymentDeductionOverrideException extends RuntimeException {
  private List invalidData;

  public PaymentDeductionOverrideException(String message, List invalidData) {
    super(message);
    this.invalidData = invalidData;
  }

  public PaymentDeductionOverrideException(String message) {
    super(message);
  }

  public List getInvalidData() {
    return invalidData;
  }
}
