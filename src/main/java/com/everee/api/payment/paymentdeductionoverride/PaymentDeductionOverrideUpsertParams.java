package com.everee.api.payment.paymentdeductionoverride;

import com.everee.api.money.Money;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PaymentDeductionOverrideUpsertParams {
  private Long paymentId;
  private Long employeeContributionDeductionId;
  private Money amountEE;
  private Money originalAmountEE;
  private Money amountER;
  private Money originalAmountER;
  private String importId;
}
