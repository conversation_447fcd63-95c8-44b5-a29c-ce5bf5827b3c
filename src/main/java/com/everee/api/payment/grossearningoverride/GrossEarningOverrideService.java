package com.everee.api.payment.grossearningoverride;

import com.everee.api.earnings.grossearnings.GrossEarning;
import com.everee.api.model.BaseModelV2;
import com.google.common.base.Functions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class GrossEarningOverrideService {

  private final GrossEarningOverrideRepository grossEarningOverrideRepository;

  public Set<GrossEarningOverride> findAllByGrossEarningId(Set<Long> grossEarningIds) {
    return grossEarningOverrideRepository.findAllByGrossEarningId(grossEarningIds);
  }

  @Transactional
  public List<GrossEarningOverride> saveAll(
    Iterable<GrossEarningOverride> grossEarningOverrides
  ) {
    return grossEarningOverrideRepository.saveAll(grossEarningOverrides);
  }

  @Transactional
  public void softDeleteAllByIdIn(Set<Long> grossEarningIds) {
    grossEarningOverrideRepository.softDeleteAllByIdIn(
      grossEarningIds,
      LocalDateTime.now()
    );
  }

  /**
   * Applies the given overrides on the provided earnings.
   * @param earnings
   *  Original earning collection, any overrides will be applied
   *  to these. If not overridden, an earning is returned as is.
   * @param earningOverrideMap
   *  A map that uses GrossEarningIds as keys and
   *  override records for those earnings as values.
   * @return A new collection that contains the original earnings with any
   * overrides already applied to them, if any. An original earning will be
   * missing in the returned collection if it happens to have a defined override
   * with a non-null deletion date.
   */
  public List<GrossEarning> applyGrossEarningOverrides(
    List<GrossEarning> earnings,
    Map<Long, GrossEarningOverride> earningOverrideMap
  ) {
    return earnings
      .stream()
      .map(
        earning -> {
          if (!earning.isOverridable()) {
            return Optional.of(earning);
          }

          var earningOverride = earningOverrideMap.get(earning.getId());

          if (earningOverride == null) {
            return Optional.of(earning);
          }

          if (earningOverride.getDeletedAt() != null) {
            return Optional.<GrossEarning>empty();
          }

          earning.setGrossAmount(earningOverride.getGrossAmount());
          earning.setUnitCount(earningOverride.getUnitCount());

          return Optional.of(earning);
        }
      )
      .filter(Optional::isPresent)
      .map(Optional::get)
      .collect(Collectors.toList());
  }

  /**
   * Fetches any overrides defined to the provided earnings and applies them.
   * @param earnings
   *  Original earning collection, any overrides will be applied
   *  to these. If not overridden, an earning is returned as is.
   * @return A new collection that contains the original earnings with any
   * overrides already applied to them, if any. An original earning will be
   * missing in the returned collection if it happens to have a defined override
   * with a non-null deletion date.
   */
  public List<GrossEarning> applyGrossEarningOverrides(List<GrossEarning> earnings) {
    if (earnings.isEmpty()) {
      return earnings;
    }

    var earningIds =
      earnings
        .stream()
        .map(BaseModelV2::getId)
        .collect(Collectors.toSet());

    var earningOverrideMap =
      grossEarningOverrideRepository
        .findAllByGrossEarningId(earningIds)
        .stream()
        .collect(Collectors.toMap(GrossEarningOverride::getGrossEarningId, Functions.identity()));

    return applyGrossEarningOverrides(earnings, earningOverrideMap);
  }
}
