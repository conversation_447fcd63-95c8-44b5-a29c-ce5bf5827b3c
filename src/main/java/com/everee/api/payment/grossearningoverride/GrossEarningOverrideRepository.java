package com.everee.api.payment.grossearningoverride;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Set;

@Repository
public interface GrossEarningOverrideRepository
    extends JpaRepository<GrossEarningOverride, Long> {

  @Query(
    value =
      "SELECT geo " +
      "FROM GrossEarningOverride geo " +
      "WHERE geo.grossEarningId IN :grossEarningIds"
  )
  Set<GrossEarningOverride> findAllByGrossEarningId(
    @Param("grossEarningIds") Set<Long> grossEarningIds
  );

  @Modifying
  @Query(
    value =
      "UPDATE GrossEarningOverride geo " +
      "SET geo.deletedAt = :deletedAt " +
      "WHERE geo.grossEarningId IN :grossEarningIds"
  )
  void softDeleteAllByIdIn(
    @Param("grossEarningIds") Set<Long> grossEarningIds,
    @Param("deletedAt") LocalDateTime deletedAt
  );
}
