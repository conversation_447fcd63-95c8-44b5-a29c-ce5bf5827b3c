package com.everee.api.payment.deposit;

import com.everee.api.link.DynamicLinkBuilder;
import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import lombok.*;
import org.springframework.context.MessageSource;

@Value
@EqualsAndHashCode(callSuper = true)
public class PaymentDepositFailedAdminNotification extends TransactionalSingleActionEmailTemplate {
  @NonNull Partner partner;
  @NonNull String paymentDate;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;

  @Override
  public boolean isDeliveryCritical() {
    return true;
  }

  @Override
  public String getEmailSubject() {
    return getLocalizedMessage("un.depositfailedadmin.email.subject");
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage("un.depositfailedadmin.email.preheader");
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage("un.depositfailedadmin.email.banner-text");
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage("un.depositfailedadmin.email.body-paragraph1-text", paymentDate);
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage("un.depositfailedadmin.email.body-paragraph2-text");
  }

  @Override
  public String getBodyParagraph3Text() {
    return getLocalizedMessage("un.depositfailedadmin.email.body-paragraph3-text");
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("un.depositfailedadmin.email.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    return new DynamicLinkBuilder().pathSegment("payroll", "current", "groups").build().toString();
  }

  @Override
  public EmailType getEmailType() {
      return EmailType.PAYMENT_DEPOSIT_FAILED;
  }
}
