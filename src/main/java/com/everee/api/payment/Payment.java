package com.everee.api.payment;

import static com.everee.api.money.Money.zeroOr;
import static com.everee.api.payment.PaymentDepositStatus.DEPOSITED;
import static com.everee.api.util.DateUtil.isDateWithinRange;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.reducing;

import com.everee.api.earnings.EarningType;
import com.everee.api.employee.Employee;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.payment.approval.PaymentAutoApproval;
import com.everee.api.payment.distribution.PaymentDistribution;
import com.everee.api.payment.distribution.PaymentDistributionConfig;
import com.everee.api.payment.distribution.PaymentDistributionStatus;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.payment.funding.PaymentFunding;
import com.everee.api.payment.funding.PaymentFundingStatus;
import com.everee.api.payment.paymentdeductionoverride.PaymentDeductionOverride;
import com.everee.api.payment.request.entity.PaymentRequestRecipient;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payrun.models.PayRun;
import com.everee.api.tax.state.State;
import com.everee.api.util.DurationUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Formula;

@Data
@Accessors(chain = true)
@Table(name = "decoratedpayment")
@Entity
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class Payment extends BasePayment {

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentType type;

  @Enumerated(EnumType.STRING)
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  public PaymentQueryStatus queryStatus;

  @NotNull private Long employeeId;
  @NotNull private Long payPeriodId;
  private Long approvalGroupId;

  private Long companyPayPeriodId;

  @Formula("(select pp.enddate from payperiod pp where pp.id = companypayperiodid)")
  @Setter(AccessLevel.NONE)
  private LocalDate companyPayPeriodEndDate;

  private Long prevPaymentId;
  @Deprecated private Long achFileId;

  private Long fundingAchFileRecordId;

  @Enumerated(EnumType.STRING)
  private PaymentFundingType fundingType;

  @Embedded private PaymentError error;

  // LIME-2633
  @Enumerated(EnumType.STRING)
  private PaymentDistributionType paymentDistributionType;

  // LIME-224
  @Enumerated(EnumType.STRING)
  private PaymentDistributionType paymentDistributionTypeOverride;

  @ToString.Exclude
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "payPeriodId", insertable = false, updatable = false)
  private PayPeriod payPeriod;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PayPeriodType payPeriodType;

  @ToString.Exclude
  @OneToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "id")
  private PaymentEmployee employee;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate payDate;

  @Enumerated(EnumType.STRING)
  private PaymentTimeframeType timeframe = PaymentTimeframeType.DAILY_CUTOFF;

  private Long payRunId;

  @ToString.Exclude
  @ManyToOne(fetch = FetchType.LAZY)
  @JsonIgnore
  @JoinColumn(name = "payRunId", insertable = false, updatable = false)
  private PayRun payRun;

  private Long paymentImportId;
  

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "preTaxDeductions")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdPreTaxDeductions")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdPreTaxDeductions")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdPreTaxDeductions"))
  })
  private PaymentAmounts preTaxDeductionAmounts = new PaymentAmounts();

  @Deprecated
  public Money getPreTaxDeductions() {
    return Optional.ofNullable(preTaxDeductionAmounts).map(PaymentAmounts::getAmount).orElse(null);
  }

  @Deprecated
  public Money getYtdPreTaxDeductions() {
    return Optional.ofNullable(preTaxDeductionAmounts)
        .map(PaymentAmounts::getYtdAmount)
        .orElse(null);
  }

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "postTaxDeductions")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdPostTaxDeductions")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdPostTaxDeductions")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdPostTaxDeductions"))
  })
  private PaymentAmounts postTaxDeductionAmounts = new PaymentAmounts();

  @Deprecated
  public Money getPostTaxDeductions() {
    return Optional.ofNullable(postTaxDeductionAmounts).map(PaymentAmounts::getAmount).orElse(null);
  }

  @Deprecated
  public Money getYtdPostTaxDeductions() {
    return Optional.ofNullable(postTaxDeductionAmounts)
        .map(PaymentAmounts::getYtdAmount)
        .orElse(null);
  }

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "deferredCompensation")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdDeferredCompensation")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdDeferredCompensation")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdDeferredCompensation"))
  })
  private PaymentAmounts deferredCompensationAmounts = new PaymentAmounts();

  @Deprecated
  public Money getDeferredCompensation() {
    return Optional.ofNullable(deferredCompensationAmounts)
        .map(PaymentAmounts::getAmount)
        .orElse(null);
  }

  @Transient
  public Money getTotalTaxOriginalAmount() {
    return getCarryOverTotals().getEmployeeTaxesOriginalAmount();
  }

  @Transient
  public Money getPreTaxDeductionsOriginalAmount() {
    return getCarryOverTotals().getPreTaxDeductionOriginalAmount();
  }

  @Transient
  public Money getPostTaxDeductionsOriginalAmount() {
    return getCarryOverTotals().getPostTaxDeductionOriginalAmount();
  }

  @Deprecated
  public Money getYtdDeferredCompensation() {
    return Optional.ofNullable(deferredCompensationAmounts)
        .map(PaymentAmounts::getYtdAmount)
        .orElse(null);
  }

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "grossEarnings")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdGrossEarnings")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdGrossEarnings")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdGrossEarnings"))
  })
  private PaymentAmounts grossEarningsAmounts = new PaymentAmounts();

  @Deprecated
  public Money getGrossEarnings() {
    return Optional.ofNullable(grossEarningsAmounts).map(PaymentAmounts::getAmount).orElse(null);
  }

  @Deprecated
  public Money getYtdGrossEarnings() {
    return Optional.ofNullable(grossEarningsAmounts).map(PaymentAmounts::getYtdAmount).orElse(null);
  }

  @SuppressWarnings("unused")
  public Money getPtoEarnings() {
    return Stream.ofNullable(earnings)
        .flatMap(Collection::stream)
        .filter(e -> EarningType.PTO_TYPES.contains(e.getType()))
        .map(PaymentEarning::getAmounts)
        .map(PaymentAmounts::getAmount)
        .filter(Objects::nonNull)
        .reduce(Money::sum)
        .orElse(Money.ZERO);
  }

  @SuppressWarnings("unused")
  public Money getHolidayEarnings() {
    return Stream.ofNullable(earnings)
        .flatMap(Collection::stream)
        .filter(e -> EarningType.HOLIDAY_TYPES.contains(e.getType()))
        .map(PaymentEarning::getAmounts)
        .map(PaymentAmounts::getAmount)
        .filter(Objects::nonNull)
        .reduce(Money::sum)
        .orElse(Money.ZERO);
  }

  public Money getOvertimeEarnings() {
    return Stream.ofNullable(earnings)
        .flatMap(Collection::stream)
        .filter(e -> EarningType.OVERTIME_TYPES.contains(e.getType()))
        .map(PaymentEarning::getAmounts)
        .map(PaymentAmounts::getAmount)
        .filter(Objects::nonNull)
        .reduce(Money::sum)
        .orElse(Money.ZERO);
  }

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "amount", column = @Column(name = "netEarnings")),
    @AttributeOverride(name = "mtdAmount", column = @Column(name = "mtdNetEarnings")),
    @AttributeOverride(name = "qtdAmount", column = @Column(name = "qtdNetEarnings")),
    @AttributeOverride(name = "ytdAmount", column = @Column(name = "ytdNetEarnings"))
  })
  private PaymentAmounts netEarningsAmounts = new PaymentAmounts();

  @Deprecated
  public Money getNetEarnings() {
    return Optional.ofNullable(netEarningsAmounts).map(PaymentAmounts::getAmount).orElse(null);
  }

  @Deprecated
  public Money getYtdNetEarnings() {
    return Optional.ofNullable(netEarningsAmounts).map(PaymentAmounts::getYtdAmount).orElse(null);
  }

  private Money previouslyPaidEarnings;
  private Money externallyPaidCheckEarnings;
  private Money totalTaxesEE;
  private Money totalTaxesER;
  private Money totalContributionsER;
  private Money totalExpense;
  private Money totalTaxFunding;
  private Money totalFunding;
  private Money disposableIncome;

  private Money ffcraTaxCredit = Money.ZERO;
  private Money ffcraCreditableTax = Money.ZERO;

  public LocalDate getPayPeriodStartDate() {
    return Optional.ofNullable(payPeriod).map(PayPeriod::getStartDate).orElse(null);
  }

  public LocalDate getPayPeriodEndDate() {
    return Optional.ofNullable(payPeriod).map(PayPeriod::getEndDate).orElse(null);
  }

  @SuppressWarnings("unused")
  public BigDecimal getTotalHours() {
    return DurationUtils.toApproximateHours(getTotalTimeWorked());
  }

  @SuppressWarnings("unused")
  public BigDecimal getRegularHours() {
    return DurationUtils.toApproximateHours(getRegularTimeWorked());
  }

  @SuppressWarnings("unused")
  public BigDecimal getOvertimeHours() {
    return DurationUtils.toApproximateHours(getOvertimeWorked());
  }

  @SuppressWarnings("unused")
  public BigDecimal getDoubleTimeHours() {
    return DurationUtils.toApproximateHours(getDoubleTimeWorked());
  }

  public Duration getTotalTimeWorked() {
    var regularTime = Optional.ofNullable(getRegularTimeWorked()).orElse(Duration.ZERO);
    var overTime = Optional.ofNullable(getOvertimeWorked()).orElse(Duration.ZERO);
    var doubleTime = Optional.ofNullable(getDoubleTimeWorked()).orElse(Duration.ZERO);
    return regularTime.plus(overTime).plus(doubleTime);
  }

  public Duration getRegularTimeWorked() {
      return Stream.ofNullable(earnings)
        .flatMap(Collection::stream)
        .map(PaymentEarning::getRegularTimeWorked)
        .filter(Objects::nonNull)
        .reduce(Duration::plus)
        .orElse(Duration.ZERO);
  }

    public Map<EarningType, BigDecimal> calculateHours() {
        Map<EarningType, BigDecimal> hoursByType = new EnumMap<>(EarningType.class);

        EnumSet.allOf(EarningType.class).forEach(type -> hoursByType.put(type, BigDecimal.ZERO));

        if (earnings != null) {
            for (PaymentEarning earning : earnings) {
                var type = earning.getType();
                var count = earning.getUnitCount();

                if (count != null && hoursByType.containsKey(type)) {
                    count = count.setScale(2, RoundingMode.HALF_UP);
                    hoursByType.merge(type, count, BigDecimal::add);
                }
            }
        }

        return hoursByType;
    }

  public Duration getOvertimeWorked() {
    return Stream.ofNullable(earnings)
        .flatMap(Collection::stream)
        .map(PaymentEarning::getOvertimeWorked)
        .filter(Objects::nonNull)
        .reduce(Duration::plus)
        .orElse(Duration.ZERO);
  }

  public Duration getDoubleTimeWorked() {
    return Stream.ofNullable(earnings)
        .flatMap(Collection::stream)
        .map(PaymentEarning::getDoubleTimeWorked)
        .filter(Objects::nonNull)
        .reduce(Duration::plus)
        .orElse(Duration.ZERO);
  }

  @Deprecated
  public Duration getTotalOvertimeWorked() {
    return getOvertimeWorked();
  }

  @Deprecated
  public Duration getTotalRegularTimeWorked() {
    return getRegularTimeWorked();
  }

  @Deprecated
  public Duration getTotalDoubleTimeWorked() {
    return getDoubleTimeWorked();
  }

  @JsonProperty("earningList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentEarning> earnings = new HashSet<>();

  @JsonProperty("taxList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentTax> taxes = new HashSet<>();

  @JsonProperty("taxGroupList")
  @Transient
  private Set<PaymentTaxGroup> taxGroups = new HashSet<>();

  @JsonProperty("contributionDeductionList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  @OrderBy(value = "ordinal asc NULLS LAST")
  private Set<PaymentContributionDeduction> contributionDeductions = new HashSet<>();

  @JsonProperty("ptoTransactionList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentPtoTransaction> ptoTransactions = new HashSet<>();

  @JsonProperty("depositList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentDeposit> deposits = new HashSet<>();

  @JsonProperty("taxCreditList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentTaxCredit> paymentTaxCredits = new HashSet<>();

  @JsonProperty("distributionList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentDistribution> distributions = new HashSet<>();

  @Enumerated(EnumType.STRING)
  private PaymentImportType importType;

  @JsonIgnore
  @Transient
  public Set<PaymentDistribution> getUpdatableDistributions() {
    return distributions.stream()
        .filter(d -> !PaymentDistributionStatus.DISTRIBUTED.equals(d.getStatus()))
        .filter(d -> !PaymentDistributionStatus.CREATED_NEW_DISTRIBUTION.equals(d.getStatus()))
        .collect(Collectors.toSet());
  }

  @JsonProperty("fundingList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentFunding> fundings = new HashSet<>();

  @JsonIgnore
  @Transient
  public Set<PaymentFunding> getUpdatableFundings() {
    return fundings.stream()
        .filter(d -> !PaymentFundingStatus.FUNDED.equals(d.getStatus()))
        .filter(d -> !PaymentFundingStatus.CREATED_NEW_FUNDING.equals(d.getStatus()))
        .collect(Collectors.toSet());
  }

  @JsonProperty("autoApprovalsList")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentAutoApproval> autoApprovals = new HashSet<>();

  @JsonIgnore
  @ToString.Exclude
  @Setter(AccessLevel.NONE)
  @OneToMany(mappedBy = "paymentId")
  private Set<PaymentRequestRecipient> paymentRecipients = new HashSet<>();

  // LIME-1782 Payment Deductions Overrides
  @JsonProperty("paymentDeductionOverrideList")
  @OneToMany(mappedBy = "id.paymentId", fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentDeductionOverride> paymentDeductionOverrides = new HashSet<>();

  // AAPL-10 Net Shortfalls
  @JsonProperty("warnings")
  @OneToMany(
      mappedBy = "paymentId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Fetch(FetchMode.SELECT)
  private Set<PaymentWarning> warnings = new HashSet<>();

  // AAPL-264 Carryover totals
  @Transient
  public CarryOverPaymentTotals getCarryOverTotals() {
    return CarryOverPaymentTotals.create(this.contributionDeductions, this.taxes);
  }

  @JsonProperty("earnings")
  @Deprecated
  public Map<String, PaymentEarning> getEarningsMap() {
    return earnings.stream()
        .filter(earning -> earning.getQualifiedType() != null)
        .collect(
            groupingBy(
                PaymentEarning::getQualifiedType,
                reducing(new PaymentEarning(), PaymentEarning::combine)));
  }

  @JsonProperty("taxes")
  @Deprecated
  public Map<String, PaymentTax> getTaxesMap() {
    return taxes.stream()
        .filter(tax -> tax.getQualifiedType() != null)
        .collect(
            groupingBy(
                PaymentTax::getQualifiedType, reducing(new PaymentTax(), PaymentTax::combine)));
  }

  @JsonProperty("contributionsDeductions")
  @Deprecated
  public Map<String, PaymentContributionDeduction> getContributionsDeductionsMap() {
    return contributionDeductions.stream()
        .filter(deduction -> deduction.getQualifiedType() != null)
        .collect(
            groupingBy(
                PaymentContributionDeduction::getQualifiedType,
                reducing(
                    new PaymentContributionDeduction(), PaymentContributionDeduction::combine)));
  }

  @Transient
  @JsonIgnore
  public Money getTotalDeductionsEE() {
    return getPreTaxDeductionAmounts()
        .getAmount()
        .plus(
            getDeferredCompensationAmounts()
                .getAmount()
                .plus(getPostTaxDeductionAmounts().getAmount()));
  }

  @Transient
  @JsonIgnore
  public Money getTotalContributionDeductions() {
    return getContributionDeductions().stream()
        .map(PaymentContributionDeduction::getTotalAmount)
        .reduce(Money::plus)
        .orElse(Money.ZERO);
  }

  @Transient
  @JsonIgnore
  public Money getTotalContributionDeductionFunding() {
    return getContributionDeductions().stream()
        .filter(pcd -> pcd.isFundAndRemit())
        .map(PaymentContributionDeduction::getTotalAmount)
        .reduce(Money::plus)
        .orElse(Money.ZERO);
  }

  public boolean isFinalized() {
    return status.isFinalized();
  }

  public boolean isErrored() {
   return status.isErrored();
  }

  public boolean isFinalPayrollPayment() {
    var payPeriodStart = getPayPeriod().getStartDate();
    var payPeriodEnd = getPayPeriod().getEndDate();
    var terminationDate = getEmployee().getEndDate();

    if (terminationDate == null) {
      return false;
    }

    return type == PaymentType.PAYROLL
        && isDateWithinRange(terminationDate, payPeriodStart, payPeriodEnd);
  }

  @Enumerated(EnumType.STRING)
  private State residentState;

  @Enumerated(EnumType.STRING)
  @Deprecated // use PaymentEarning.workLocation.state
  private State workLocationState;

  @SuppressWarnings("unused")
  public String getPayeeDisplayFullName() {
    return Optional.ofNullable(getEmployee()).map(Employee::getDisplayFullName).orElse(null);
  }

  public String getPayeeFirstName() {
    return Optional.ofNullable(getEmployee()).map(Employee::getFirstName).orElse(null);
  }

  @Deprecated(forRemoval = true)
  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private Long employeePositionId;

  @Deprecated(forRemoval = true)
  @ToString.Exclude
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "employeePositionId", insertable = false, updatable = false)
  private EmployeePosition employeePosition;

  @Deprecated(forRemoval = true)
  public Money getPayRate() {
    return Optional.ofNullable(getEmployeePosition())
        .map(EmployeePosition::getPayRate)
        .orElse(null);
  }

  @Deprecated(forRemoval = true)
  public PayType getPayType() {
    return Optional.ofNullable(getEmployeePosition())
        .map(EmployeePosition::getPayType)
        .orElse(null);
  }

  @Deprecated(forRemoval = true)
  public PayType getWageType() {
    return getPayType();
  }

  @Transient
  public String getEarningNotes() {
    return String.join(
        ", ",
        earnings.stream()
            .map(PaymentEarning::getNote)
            .filter((note) -> !StringUtils.isBlank(note))
            .collect(Collectors.toSet()));
  }

  public BigDecimal percentOfMonth;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime deletedAt;

  private Long deletedByUserId;

  private String deletedNote;

  @Enumerated(EnumType.STRING)
  private PaymentDeletedReason deletedReason;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime unapprovedAt;

  private Long unapprovedByUserId;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime calculationRequestedAt;

  @Enumerated(EnumType.STRING)
  @NotNull
  private PaymentDepositStatus depositStatus;

  public boolean isReadyToDeposit() {
    var depositStatuses =
        new HashSet(
            getDeposits().stream().map(PaymentDeposit::getStatus).collect(Collectors.toSet()));
    var hasReadyToDepositDeposits = depositStatuses.remove(PaymentDepositStatus.READY_TO_DEPOSIT);
    var areOtherDepositsDeposited =
        depositStatuses.isEmpty()
            || (depositStatuses.size() == 1 && depositStatuses.contains(DEPOSITED));

    return hasReadyToDepositDeposits && areOtherDepositsDeposited;
  }

  @JsonIgnore
  public PaymentDeposit getDeposit(PaymentDistribution distribution, boolean includeDeposited) {
    var accountNumber =
        Optional.ofNullable(distribution.getDistributionConfig())
            .map(PaymentDistributionConfig::getAccountNumber)
            .orElse(null);
    var routingNumber =
        Optional.ofNullable(distribution.getDistributionConfig())
            .map(PaymentDistributionConfig::getRoutingNumber)
            .orElse(null);
    var accountType =
        Optional.ofNullable(distribution.getDistributionConfig())
            .map(PaymentDistributionConfig::getAccountType)
            .orElse(null);
    var amount = zeroOr(distribution.getAmounts().getAmount());

    return deposits.stream()
        .filter(pd -> Objects.equals(pd.getAccountNumber(), accountNumber))
        .filter(pd -> Objects.equals(pd.getRoutingNumber(), routingNumber))
        .filter(pd -> Objects.equals(pd.getAccountType(), accountType))
        .filter(pd -> amount.equals(pd.getAmounts().getAmount()))
        .filter(pd -> includeDeposited || DEPOSITED != pd.getStatus())
        .findFirst()
        .orElse(null);
  }

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonSerialize(using = LocalDateSerializer.class)
  private LocalDate scheduleDate;

  @Transient
  public boolean isScheduled() {
    return scheduleDate != null && scheduleApprovedByUserId != null;
  }

  @Transient
  public boolean isPulledBack() {
    return status == PaymentStatus.DELETED && deletedReason == PaymentDeletedReason.PULLBACK;
  }


  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime scheduleApprovedAt;

  @Enumerated(EnumType.STRING)
  private ScheduleSource scheduleSource;

  private Long scheduleApprovedByUserId;

  private boolean hasSymmetryPayload;

  public void clearSchedule() {
    scheduleApprovedByUserId = null;
    scheduleApprovedAt = null;
    scheduleDate = null;
  }

  public Money getTipsEarnings() {
    return Stream.ofNullable(earnings)
      .flatMap(Collection::stream)
      .filter(e -> EarningType.TIPS.equals(e.getType()))
      .map(PaymentEarning::getAmounts)
      .map(PaymentAmounts::getAmount)
      .filter(Objects::nonNull)
      .reduce(Money::sum)
      .orElse(Money.ZERO);
  }

  public Money getPreviouslyPaidTipsEarnings() {
    return Stream.ofNullable(earnings)
      .flatMap(Collection::stream)
      .filter(e -> EarningType.PREVIOUSLY_PAID_TIPS.equals(e.getType()))
      .map(PaymentEarning::getAmounts)
      .map(PaymentAmounts::getAmount)
      .filter(Objects::nonNull)
      .reduce(Money::sum)
      .orElse(Money.ZERO);
  }
}
