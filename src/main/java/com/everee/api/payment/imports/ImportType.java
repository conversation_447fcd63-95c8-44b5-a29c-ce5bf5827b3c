package com.everee.api.payment.imports;

public enum ImportType {
    POST_ONLY,
    HISTORICAL,
    INVALID;

    public static ImportType valueOfIgnoreCase(String value) {
      if(isPostOnly(value)) {
        return POST_ONLY;
      } else if (isHistorical(value)) {
        return HISTORICAL;
      }

      return INVALID;
    }

  private static boolean isPostOnly(String value) {
    return value != null && value.trim().toLowerCase().matches("post\\s*only|postonly|post_only");
  }

  private static boolean isHistorical(String value) {
    return value != null && value.trim().toLowerCase().matches("historical");
  }
}
