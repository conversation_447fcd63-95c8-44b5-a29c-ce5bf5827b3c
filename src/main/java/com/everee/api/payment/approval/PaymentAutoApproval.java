package com.everee.api.payment.approval;

import com.everee.api.model.BaseModelV2;
import com.everee.api.payment.Payment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Entity
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PaymentAutoApproval extends BaseModelV2<PaymentAutoApproval> {
  @NotNull private Long paymentId;

  @ToString.Exclude
  @EqualsAndHashCode.Exclude
  @ManyToOne(fetch = FetchType.LAZY)
  @JsonIgnore
  @JoinColumn(name = "paymentId", insertable = false, updatable = false)
  private Payment payment;

  @Enumerated(EnumType.STRING)
  @NotNull
  private PayrollAutoApprovalRule rule;

  @Enumerated(EnumType.STRING)
  @NotNull
  private PayrollAutoApprovalStatus status;

  private String inputs;
  private String outputs;
}
