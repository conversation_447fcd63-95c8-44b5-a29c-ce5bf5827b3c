package com.everee.api.payment.lookup;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class PaymentAccessDeniedException extends LocalizedRuntimeException {
  public PaymentAccessDeniedException() {
    super("payment.PaymentAccessDeniedException.message");
  }
}
