package com.everee.api.companyrole;

import lombok.RequiredArgsConstructor;

// NOTE: update `api.auth.annotation.*Access` annotation interfaces when changing these
// roles as they are currently stringly-typed and cannot be verified by the compiler.

@RequiredArgsConstructor
public enum CompanyRoleType {
  OWNER,
  FINANCIAL_MANAGER;

  public String getSpringIdentifier() {
    return "ROLE_" + name();
  }
}
