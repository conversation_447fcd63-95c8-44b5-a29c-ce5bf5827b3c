package com.everee.api.companyrole;

import static org.hibernate.jpa.QueryHints.HINT_FETCH_SIZE;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import javax.persistence.QueryHint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

@Repository
public interface CompanyRoleRepository extends JpaRepository<CompanyRole, Long> {

  Page<CompanyRole> findAllByIdIn(List<Long> ids, Pageable pageable);

  List<CompanyRole> findAllByUserId(Long id);

  void deleteAllByCompanyIdAndUserIdAndType(Long companyId, Long userId, CompanyRoleType type);

  @QueryHints(value = @QueryHint(name = HINT_FETCH_SIZE, value = "50"))
  Stream<CompanyRole> streamAllByCompanyIdAndType(Long companyId, CompanyRoleType type);

  Optional<CompanyRole> findByCompanyIdAndUserIdAndType(
      Long companyId, Long userId, CompanyRoleType type);
}
