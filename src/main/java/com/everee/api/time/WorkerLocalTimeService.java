package com.everee.api.time;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.CoreEmployeeRepository;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.employee.Employee;
import com.everee.api.employeePosition.EmployeesLocalDateRequest;
import com.everee.api.util.DateUtil;
import java.math.BigInteger;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WorkerLocalTimeService {
  private final CompanyService companyService;
  private final CoreEmployeeRepository coreEmployeeRepository;
  private final DetailedEmployeeRepository detailedEmployeeRepository;

  public LocalDateTime localNowTimestamp(@NonNull Long employeeId) {
    return localTimestamp(employeeId, ZonedDateTime.now(UTC_ZONE_ID));
  }

  public LocalDateTime localTimestamp(@NonNull Long employeeId, @NonNull ZonedDateTime timestamp) {
    var employee = coreEmployeeRepository.getOne(employeeId);
    return localTimestamp(employee, timestamp);
  }

  public LocalDateTime localTimestamp(
      @NonNull Employee employee, @NonNull ZonedDateTime timestamp) {
    return zonedTimestamp(employee, timestamp).toLocalDateTime();
  }

  public ZonedDateTime zonedTimestamp(@NonNull Long employeeId, @NonNull ZonedDateTime timestamp) {
    var employee = coreEmployeeRepository.getOne(employeeId);
    return zonedTimestamp(employee, timestamp);
  }

  public ZonedDateTime zonedTimestamp(
      @NonNull Employee employee, @NonNull ZonedDateTime timestamp) {
    return timestamp.withZoneSameInstant(getTimezone(employee, timestamp));
  }

  public ZonedDateTime getStartOfDay(@NonNull Employee employee, @NonNull LocalDate forDate) {
    var workweekConfig = companyService.getCompany(employee.getCompanyId()).getWorkweekConfig();
    var employeeTimezone = getTimezoneForDate(employee, forDate);

    return forDate.atTime(workweekConfig.getWorkdayStart()).atZone(employeeTimezone);
  }

  public ZonedDateTime getEndOfDay(@NonNull Employee employee, @NonNull LocalDate forDate) {
    return getStartOfDay(employee, forDate).plusDays(1).minusNanos(1);
  }

  public ZoneId getTimezone(@NonNull Employee employee, @NonNull ZonedDateTime timestamp) {
    var workweekConfig = companyService.getCompany(employee.getCompanyId()).getWorkweekConfig();
    var companyStartAt = workweekConfig.getStartOfWorkweek(timestamp);
    var employeeStartAt = workweekConfig.getStartOfWorkday(employee.getStartDate());

    var lookupDate = DateUtil.maxDateTime(companyStartAt, employeeStartAt).toLocalDate();

    return ZoneId.of(
        detailedEmployeeRepository.getEmployeeTimezone(
            employee.getCompanyId(), employee.getUserId(), employee.getId(), lookupDate));
  }

  public Map<Long, LocalDate> getLocalDatePerEmployee(Page<DetailedEmployee> employees, Map<Long, DetailedCompany> companiesDetailsMap)
  {
      var timestamp = ZonedDateTime.now();
      var timezonePerEmployee = getTimezonePerEmployee(employees, companiesDetailsMap, timestamp);

      return timezonePerEmployee.entrySet().stream()
              .collect(Collectors.toMap(
                      Map.Entry::getKey,
                      entry -> timestamp.withZoneSameInstant(ZoneId.of(entry.getValue())).toLocalDate()
              ));
  }

  public Map<Long, String> getTimezonePerEmployee(@NonNull Page<DetailedEmployee> employees, @NonNull Map<Long, DetailedCompany> companiesDetailsMap, @NonNull ZonedDateTime timestamp)
  {
      var employeeRequests = employees.getContent().stream()
              .map(employee -> createEmployeesLocalDateRequest(employee, companiesDetailsMap, timestamp))
              .collect(Collectors.toList());

      var employeeIds = employeeRequests.stream().map(EmployeesLocalDateRequest::getEmployeeId).distinct().collect(Collectors.toList());
      var companyIds = employeeRequests.stream().map(EmployeesLocalDateRequest::getCompanyId).distinct().collect(Collectors.toList());
      var userIds = employeeRequests.stream().map(EmployeesLocalDateRequest::getUserId).distinct().collect(Collectors.toList());

      var timezoneByEmployeeIds = detailedEmployeeRepository.findWorkLocationTimezoneByEmployeeIds(employeeIds,timestamp.toLocalDate());
      var timezoneByCompanyIds = detailedEmployeeRepository.findCompanyTimezoneByCompanyIds(companyIds);
      var timezoneByUserIds = detailedEmployeeRepository.findUserAddressTimezoneByUserIds(userIds,timestamp.toLocalDate());

      var employeeTimezoneMap = timezoneByEmployeeIds.stream().collect(Collectors.toMap(result -> (BigInteger) result[0], result -> (String) result[1]));
      var companyTimezoneMap = timezoneByCompanyIds.stream().collect(Collectors.toMap(result -> (BigInteger) result[0], result -> (String) result[1]));
      var userTimezoneMap = timezoneByUserIds.stream().collect(Collectors.toMap(result -> (BigInteger) result[0], result -> (String) result[1]));

      Map<Long, String> timezonePerEmployee = new HashMap<>();

      for (EmployeesLocalDateRequest employeeRequest : employeeRequests)
      {
          var employeeId = employeeRequest.getEmployeeId();
          var timezone = employeeTimezoneMap.get(BigInteger.valueOf(employeeId));

          if (timezone == null)
          {
              var companyId = employeeRequest.getCompanyId();
              timezone = companyTimezoneMap.get(BigInteger.valueOf(companyId));
          }
          if (timezone == null)
          {
              var userId = employeeRequest.getUserId();
              timezone = userTimezoneMap.get(BigInteger.valueOf(userId));
          }

          timezonePerEmployee.put(employeeId, timezone);
      }

      return timezonePerEmployee;
    }

  private EmployeesLocalDateRequest createEmployeesLocalDateRequest(DetailedEmployee detailedEmployee, Map<Long, DetailedCompany> companiesDetailsMap, ZonedDateTime timestamp)
  {
      EmployeesLocalDateRequest request = new EmployeesLocalDateRequest();

      request.setEmployeeId(detailedEmployee.getId());
      request.setUserId(detailedEmployee.getUserId());
      request.setCompanyId(detailedEmployee.getCompanyId());

      var workweekConfig = companiesDetailsMap.get(detailedEmployee.getCompanyId()).getWorkweekConfig();
      var companyStartAt = workweekConfig.getStartOfWorkweek(timestamp);
      var employeeStartAt = workweekConfig.getStartOfWorkday(detailedEmployee.getStartDate());

      var lookupDate = DateUtil.maxDateTime(companyStartAt, employeeStartAt).toLocalDate();

      request.setReferenceDate(lookupDate);
      return request;
    }

  /**
   * This method pulls the worker's timezone that was in effect at the beginning of the workweek
   * containing the given date. For the purposes of time-worked classification, it is assumed that a
   * worker's work location timezone is constant throughout the week
   */
  public ZoneId getTimezoneForDate(@NonNull Employee employee, @NonNull LocalDate forDate) {
    var workweekConfig = companyService.getCompany(employee.getCompanyId()).getWorkweekConfig();
    var lookupAt = workweekConfig.getStartOfWorkday(forDate);

    return getTimezone(employee, lookupAt);
  }
}
