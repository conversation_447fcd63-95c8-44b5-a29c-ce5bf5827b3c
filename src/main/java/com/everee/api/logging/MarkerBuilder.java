package com.everee.api.logging;

import java.util.LinkedHashMap;
import java.util.Map;
import net.logstash.logback.marker.Markers;
import org.slf4j.Marker;

public class MarkerBuilder {
  private final Map<String, Object> entries = new LinkedHashMap<>();

  public MarkerBuilder() {}

  public MarkerBuilder(String key, Object value) {
    entries.put(key, value);
  }

  public boolean containsKey(String key) {
    return entries.containsKey(key);
  }

  public Object get(String key) {
    return entries.get(key);
  }

  public MarkerBuilder set(String key, Object value) {
    entries.put(key, value);
    return this;
  }

  public void clear() {
    entries.clear();
  }

  public Marker buildMarker() {
    return toMarker(entries);
  }

  public static Marker toMarker(String k1, Object v1) {
    return Markers.appendEntries(Map.of(k1, v1));
  }

  public static Marker toMarker(String k1, Object v1, String k2, Object v2) {
    return Markers.appendEntries(Map.of(k1, v1, k2, v2));
  }

  public static Marker toMarker(String k1, Object v1, String k2, Object v2, String k3, Object v3) {
    return Markers.appendEntries(Map.of(k1, v1, k2, v2, k3, v3));
  }

  public static Marker toMarker(Map<String, Object> entries) {
    return Markers.appendEntries(entries);
  }
}
