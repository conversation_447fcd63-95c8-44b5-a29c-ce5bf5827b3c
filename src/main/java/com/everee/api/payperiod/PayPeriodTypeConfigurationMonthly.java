package com.everee.api.payperiod;

import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.i18n.LocalizedString;
import java.time.LocalDate;

class PayPeriodTypeConfigurationMonthly extends AbstractPayPeriodTypeConfiguration {

  public PayPeriodTypeConfigurationMonthly() {}

  public PayPeriodTypeConfigurationMonthly(PayPeriodTypeConfigProvider provider) {
    super(provider);
  }

  @Override
  public PayPeriod generatePayPeriod(
      LocalDate startDate, PayPeriodPreference pref, EmployeePosition position)
      throws PayPeriodException {
    if (pref.getMonthlyStartDay() == null) {
      throw new PayPeriodException("Missing PayPeriodPreference.MonthlyStartDay");
    }

    var nextStartDate = startDate.plusMonths(1);

    if (nextStartDate.getDayOfMonth() != pref.getMonthlyStartDay()) {
      throw new PayPeriodException(
          "StartDate "
              + nextStartDate
              + " DayOfMonth "
              + nextStartDate.getDayOfMonth()
              + " does not match PayPeriodPreference StartDay of "
              + pref.getMonthlyStartDay());
    }

    return new PayPeriod(startDate, nextStartDate.minusDays(1), pref);
  }

  @Override
  public LocalDate getNextAvailableStartDate(LocalDate after) {
    var next = after.withDayOfMonth(getMonthlyStartDay());
    if (after.getDayOfMonth() >= getMonthlyStartDay()) {
      next = next.plusMonths(1);
    }
    return next;
  }

  @Override
  public LocalDate getStartDateForPeriodContaining(LocalDate refDate) {
    var start = refDate.withDayOfMonth(getMonthlyStartDay());
    if (refDate.getDayOfMonth() < getMonthlyStartDay()) {
      start = start.minusMonths(1);
    }
    return start;
  }

  @Override
  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of("pptype.MONTHLY.title");
  }

  @Override
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.of("pptype.MONTHLY.description");
  }

  public int getPeriodsPerYear() {
    return 12;
  }

  @Override
  public boolean isVariableLength() {
    return true;
  }

  @Override
  public boolean isRecurring() {
    return true;
  }
}
