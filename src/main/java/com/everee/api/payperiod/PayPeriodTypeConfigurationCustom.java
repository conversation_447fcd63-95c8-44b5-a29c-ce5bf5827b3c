package com.everee.api.payperiod;

import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.i18n.LocalizedString;
import java.time.LocalDate;

class PayPeriodTypeConfigurationCustom extends AbstractPayPeriodTypeConfiguration {

  public PayPeriodTypeConfigurationCustom() {}

  public PayPeriodTypeConfigurationCustom(PayPeriodTypeConfigProvider provider) {
    super(provider);
  }

  @Override
  public PayPeriod generatePayPeriod(
      LocalDate startDate, PayPeriodPreference pref, EmployeePosition position)
      throws PayPeriodException {
    return new PayPeriod(pref.getStartDate(), pref.getEndDate(), pref);
  }

  @Override
  public LocalDate getNextAvailableStartDate(LocalDate after) {
    throw new UnsupportedOperationException();
  }

  @Override
  public LocalDate getStartDateForPeriodContaining(LocalDate refDate) {
    throw new UnsupportedOperationException();
  }

  @Override
  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of("pptype.CUSTOM.title");
  }

  @Override
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.of("pptype.CUSTOM.description");
  }

  public int getPeriodsPerYear() {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isVariableLength() {
    return true;
  }

  @Override
  public boolean isRecurring() {
    return false;
  }
}
