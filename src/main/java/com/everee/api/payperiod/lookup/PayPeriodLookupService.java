package com.everee.api.payperiod.lookup;

import static com.everee.api.model.BaseModel_.ID;
import static com.everee.api.payperiod.PayPeriod_.COMPANY_ID;
import static com.everee.api.payperiod.PayPeriod_.EMPLOYEE_ID;
import static com.everee.api.payperiod.PayPeriod_.END_DATE;
import static com.everee.api.payperiod.PayPeriod_.PAY_PERIOD_PREFERENCE_ID;
import static com.everee.api.payperiod.PayPeriod_.PAY_PERIOD_TYPE;
import static com.everee.api.payperiod.PayPeriod_.START_DATE;
import static com.everee.api.payperiod.PayPeriod_.PAY_DATE;
import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.query.Query;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PayPeriodLookupService
    implements LookupService<PayPeriod, PayPeriodLookup, Query<PayPeriod>> {
  private final EntityManager entityManager;

  @Override
  public void configureQuery(PayPeriodLookup lookup, Query<PayPeriod> query) {
    query
        .where(property(ID).in(lookup.getIds()))
        .where(property(COMPANY_ID).in(lookup.getCompanyIds()))
        .where(property(PAY_PERIOD_PREFERENCE_ID).in(lookup.getPayPeriodPreferenceIds()))
        .where(property(PAY_PERIOD_TYPE).in(lookup.getPayPeriodTypes()))
        .where(property(START_DATE).lessThanOrEqual(lookup.getForDate()))
        .where(property(END_DATE).greaterThanOrEqual(lookup.getForDate()))
        .where(property(START_DATE).greaterThanOrEqual(lookup.getMinStartDateInclusive()))
        .where(property(START_DATE).lessThanOrEqual(lookup.getMaxStartDateInclusive()))
        .where(property(END_DATE).greaterThanOrEqual(lookup.getMinEndDateInclusive()))
        .where(property(END_DATE).lessThanOrEqual(lookup.getMaxEndDateInclusive()))
        .where(property(PAY_DATE).equal(lookup.getPayDate()));

    if (lookup.isNullEmployeeId()) {
      query.where(property(EMPLOYEE_ID).isNull());
    } else {
      query.where(property(EMPLOYEE_ID).in(lookup.getEmployeeIds()));
    }
  }

  @Override
  public Query<PayPeriod> createQuery() {
    return new Query<>(entityManager, PayPeriod.class);
  }
}
