package com.everee.api.payperiod.lookup;

import com.everee.api.lookup.Lookup;
import com.everee.api.payperiod.PayPeriodType;
import java.time.LocalDate;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@With
@AllArgsConstructor
@NoArgsConstructor
public class PayPeriodLookup implements Lookup {
  private Set<Long> ids;
  private Set<Long> companyIds;
  private boolean nullEmployeeId;
  private Set<Long> employeeIds;
  private Set<Long> payPeriodPreferenceIds;
  private Set<PayPeriodType> payPeriodTypes;
  private LocalDate forDate;
  private LocalDate minStartDateInclusive;
  private LocalDate maxStartDateInclusive;
  private LocalDate minEndDateInclusive;
  private LocalDate maxEndDateInclusive;
  private LocalDate payDate;
}
