package com.everee.api.payperiod;

import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.i18n.LocalizedString;
import java.time.LocalDate;

class PayPeriodTypeConfigurationPayOnDemandRemainder extends AbstractPayPeriodTypeConfiguration {

  public PayPeriodTypeConfigurationPayOnDemandRemainder() {}

  public PayPeriodTypeConfigurationPayOnDemandRemainder(PayPeriodTypeConfigProvider provider) {
    super(provider);
  }

  @Override
  public PayPeriod generatePayPeriod(
      LocalDate startDate, PayPeriodPreference pref, EmployeePosition position)
      throws PayPeriodException {
    throw new UnsupportedOperationException();
  }

  @Override
  public LocalDate getNextAvailableStartDate(LocalDate after) {
    throw new UnsupportedOperationException();
  }

  @Override
  public LocalDate getStartDateForPeriodContaining(LocalDate refDate) {
    throw new UnsupportedOperationException();
  }

  @Override
  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of("pptype.PAY_ON_DEMAND_REMAINDER.title");
  }

  @Override
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.of("pptype.PAY_ON_DEMAND_REMAINDER.description");
  }

  @Override
  public int getPeriodsPerYear() {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isVariableLength() {
    return true;
  }

  @Override
  public boolean isRecurring() {
    return false;
  }
}
