package com.everee.api.payperiod;

import com.everee.api.config.jpa.RefreshingSave;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PayPeriodPreferenceRepository
    extends JpaRepository<PayPeriodPreference, Long>, RefreshingSave<PayPeriodPreference> {
  List<PayPeriodPreference> findByCompanyIdAndEmployeeIdIsNull(Long companyId);

  Page<PayPeriodPreference> findByCompanyIdAndEmployeeIdIsNull(Long companyId, Pageable pageable);

  @Query(
      "SELECT ppp FROM PayPeriodPreference ppp \n"
          + " WHERE companyId = :companyId \n"
          + "   AND employeeId IS NULL \n"
          + "   AND :referenceDate < endDate")
  List<PayPeriodPreference> findActiveAndUpcomingForCompany(
      @Param("companyId") Long companyId, @Param("referenceDate") LocalDate referenceDate);

  List<PayPeriodPreference> findByCompanyIdAndEmployeeId(Long companyId, Long employeeId);

  @Query(
      "SELECT ppp FROM PayPeriodPreference ppp \n"
          + " WHERE companyId = ?1 \n"
          + "   AND employeeId IS NULL \n"
          + "   AND ?2 BETWEEN startDate AND endDate")
  Optional<PayPeriodPreference> findByCompanyIdAndForDate(Long companyId, LocalDate forDate);

  @Query(
      "SELECT ppp FROM PayPeriodPreference ppp \n"
          + " WHERE companyId = ?1 \n"
          + "   AND employeeId = ?2 \n"
          + "   AND ?3 BETWEEN startDate AND endDate")
  Optional<PayPeriodPreference> findByEmployeeIdAndForDate(
      Long companyId, Long employeeId, LocalDate forDate);
}
