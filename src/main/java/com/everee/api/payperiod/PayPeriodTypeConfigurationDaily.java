package com.everee.api.payperiod;

import com.everee.api.bankingday.Weekdays;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.PayType;
import java.time.LocalDate;

class PayPeriodTypeConfigurationDaily extends AbstractPayPeriodTypeConfiguration {

  public PayPeriodTypeConfigurationDaily() {}

  public PayPeriodTypeConfigurationDaily(PayPeriodTypeConfigProvider provider) {
    super(provider);
  }

  @Override
  public PayPeriod generatePayPeriod(
      LocalDate startDate, PayPeriodPreference pref, EmployeePosition position)
      throws PayPeriodException {
    // Don't generate pay periods for salaried employees on weekends
    if (position == null)
      throw new PayPeriodException(
          "Missing EmployeePosition for employeeId "
              + pref.getEmployeeId()
              + " and date "
              + startDate);

    if (position.getPayType() == PayType.SALARY && Weekdays.isWeekend(startDate)) {
      return null;
    }
    return new PayPeriod(startDate, startDate, pref);
  }

  @Override
  public LocalDate getNextAvailableStartDate(LocalDate after) {
    return after.plusDays(1);
  }

  @Override
  public LocalDate getStartDateForPeriodContaining(LocalDate refDate) {
    return refDate;
  }

  @Override
  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of("pptype.DAILY.title");
  }

  @Override
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.of("pptype.DAILY.description");
  }

  public int getPeriodsPerYear() {
    return 260;
  }

  @Override
  public boolean isVariableLength() {
    return false;
  }

  @Override
  public boolean isRecurring() {
    return true;
  }
}
