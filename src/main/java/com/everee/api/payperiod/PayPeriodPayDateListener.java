package com.everee.api.payperiod;

import com.everee.api.bankingday.BankingDays;
import com.everee.api.util.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.PrePersist;

@Component
public class PayPeriodPayDateListener {

  @PrePersist
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public void beforeCreation(PayPeriod payPeriod) {
    var bankingDays = BeanUtil.getBean(BankingDays.class);
    if (payPeriod.getPayDate() != null || payPeriod.getEndDate() == null ||
      payPeriod.getPayPeriodType() == PayPeriodType.IMPORT ||
      payPeriod.getPayPeriodType() == PayPeriodType.AD_HOC) {
      return;
    }
    payPeriod.setPayDate(bankingDays.nextBankingDay(payPeriod.getEndDate()));
  }
}
