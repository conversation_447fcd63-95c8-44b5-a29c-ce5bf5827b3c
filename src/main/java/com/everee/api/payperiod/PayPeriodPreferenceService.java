package com.everee.api.payperiod;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.exception.ActionNotSupportedForEndedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.Employee;
import com.everee.api.employee.PayPeriodPreferenceOption;
import com.everee.api.employee.R365PayrollFrequencyUpdateRequest;
import com.everee.api.employee.event.WorkerUpdatedPayrollFrequencyEvent;
import com.everee.api.employee.exception.ActionNotSupportedForTerminatedWorker;
import com.everee.api.featureflag.FeatureFlagService;
import com.everee.api.payperiod.exception.CompanyIsMissingPayPeriodPreference;
import com.everee.api.payperiod.lookup.*;
import com.everee.api.phase.Phase;
import com.everee.api.r365.R365HttpClient;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.util.DateUtil;
import com.everee.api.util.SetUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import lombok.*;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class PayPeriodPreferenceService {
  private final ApplicationEventPublisher eventPublisher;
  private final CompanyPayPeriodPreferenceLookupService companyPayPeriodPreferenceLookupService;
  private final EmployeePayPeriodPreferenceLookupService employeePayPeriodPreferenceLookupService;
  private final PayPeriodPreferenceRepository payPeriodPreferenceRepository;
  private final CompanyLocalTimeService companyLocalTimeService;
  private final PayPeriodLookupService payPeriodLookupService;
  private final PayPeriodService payPeriodService;
  private final CompanyService companyService;

  private final R365HttpClient r365HttpClient;

  private final FeatureFlagService featureFlagService;

  public static final String SYNC_PAY_FREQUENCY_TO_R365 = "syncPayFrequencyTo365Enabled";

  public PayPeriodPreference getCompanyActivePreference(
      @NonNull Long companyId, @NonNull LocalDate forDate) {
    var prefLookup =
        new CompanyPayPeriodPreferenceLookup()
            .withCompanyIds(Set.of(companyId))
            .withForDate(forDate);
    return companyPayPeriodPreferenceLookupService
        .findOne(prefLookup)
        .orElseThrow(
            () -> {
              var msg =
                  String.format(
                      "Company %s (%s) is missing Traditional Cycle Preference.",
                      companyService.getCompany(companyId).getDisplayName(), companyId);
              throw new IllegalStateException(msg);
            });
  }

  public Optional<PayPeriodPreference> findActiveEmployeePreference(
      Long companyId, Long employeeId, LocalDate forDate) {
    var prefLookup =
        new EmployeePayPeriodPreferenceLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(Set.of(employeeId))
            .withForDate(forDate);
    return employeePayPeriodPreferenceLookupService.findOne(prefLookup);
  }

  public List<PayPeriodPreference> listUpcomingPayPeriodPreferences(
      Long companyId, Long employeeId, LocalDate startDate) {
    var prefLookup =
        new EmployeePayPeriodPreferenceLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(Set.of(employeeId))
            .withMinStartDateInclusive(startDate);
    return employeePayPeriodPreferenceLookupService.listAll(prefLookup, Pageable.unpaged()).stream()
        .collect(Collectors.toList());
  }

  public PayPeriodPreference getSelectedPayPeriodPreference(
      Long companyId, Long employeeId, LocalDate forDate) {
    var prefLookup =
        new EmployeePayPeriodPreferenceLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(Set.of(employeeId))
            .withMinEndDateInclusive(forDate);
    // Finds the future pref for employee, falls back to active
    var futureOrActivePref =
        employeePayPeriodPreferenceLookupService.listAll(prefLookup, Pageable.unpaged()).stream()
            .max(Comparator.comparing(PayPeriodPreference::getEndDate));
    var companyPref = getCompanyActiveOrUpcomingPreference(companyId, forDate);

    // If active or future pref is empty, company traditional is selected
    if (futureOrActivePref.isEmpty()) {
      return companyPref;
    }

    // If end date is set (not MAX_DATE) the employee has selected traditional cycle
    var eePref = futureOrActivePref.get();
    if (eePref.getEndDate().isBefore(PayPeriodPreference.MAX_DATE)) {
      return companyPref;
    }

    // Employee has selected a PYW option
    return eePref;
  }

  public SelectedPreferenceType getSelectedPreferenceType(
      Long companyId, Long employeeId, LocalDate forDate) {
    var prefLookup =
        new EmployeePayPeriodPreferenceLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(Set.of(employeeId))
            .withMinEndDateInclusive(forDate);
    // Finds the future pref for employee, falls back to active
    var futureOrActivePref =
        employeePayPeriodPreferenceLookupService.listAll(prefLookup, Pageable.unpaged()).stream()
            .max(Comparator.comparing(PayPeriodPreference::getEndDate));
    var companyPref = getCompanyActiveOrUpcomingPreference(companyId, forDate);

    // If active or future pref is empty, company traditional is selected
    if (futureOrActivePref.isEmpty()) {
      return SelectedPreferenceType.of(companyPref.getPayPeriodType(), forDate);
    }

    // If end date is set (not MAX_DATE) the employee has selected traditional cycle
    var eePref = futureOrActivePref.get();
    if (eePref.getEndDate().isBefore(PayPeriodPreference.MAX_DATE)) {
      return SelectedPreferenceType.of(
          companyPref.getPayPeriodType(), eePref.getEndDate().plusDays(1));
    }

    // Employee has selected a PYW option (starts in future or is already active)
    var effectiveDate = DateUtil.maxDate(eePref.getStartDate(), forDate);
    return SelectedPreferenceType.of(eePref.getPayPeriodType(), effectiveDate);
  }

  public PayPeriodPreference getCurrentOrPreviousNonCustomPreference(
      DetailedCompany company, Employee employee, LocalDate startDate) {
    var companyPref = getCompanyActiveOrUpcomingPreference(company.getId(), startDate);

    // get active and ended preferences in descending order
    var lookup =
        new EmployeePayPeriodPreferenceLookup()
            .withEmployeeIds(Set.of(employee.getId()))
            .withMaxStartDateInclusive(startDate);
    var preferences =
        employeePayPeriodPreferenceLookupService.listAll(lookup, Pageable.unpaged()).stream()
            .sorted(Comparator.comparing(PayPeriodPreference::getStartDate).reversed())
            .collect(Collectors.toList());
    // if no current or ended preferences found -> fall back to traditional cycle
    if (preferences.size() == 0) return companyPref;

    // find the most recent preference is active (empty if not)
    var activePreference =
        Optional.of(preferences.get(0)).filter(p -> startDate.isBefore(p.getEndDate()));

    // if the most recent preference isn't active, worker is on company preference
    if (activePreference.isEmpty()) return companyPref;

    // if the active preference isn't CUSTOM return that preference
    if (!activePreference.get().getPayPeriodType().equals(PayPeriodType.CUSTOM)) {
      return activePreference.get();
    }

    // iterating backwards -> find first discontiguous or non-Custom preference
    var contiguousCompareDate = activePreference.get().getEndDate();
    for (var p : preferences) {
      // preference isn't contiguous with last pref
      if (!p.getEndDate().equals(contiguousCompareDate)) return companyPref;

      // preference found isn't CUSTOM
      if (!p.getPayPeriodType().equals(PayPeriodType.CUSTOM)) return p;

      contiguousCompareDate = p.getStartDate().minusDays(1);
    }

    // all preferences were contiguous and custom... must have started on traditional cycle
    return companyPref;
  }

  public UpdatedPayPeriodPreferences updateEmployeePayPeriodPreference(
      DetailedCompany company, DetailedEmployee employee, PayPeriodPreferenceUpdateRequest request)
      throws PayPeriodException {

    var result = applyPayPeriodPreferenceUpdate(company, employee, request);
    eventPublisher.publishEvent(new WorkerUpdatedPayrollFrequencyEvent(employee));

    if (featureFlagService.boolValue(SYNC_PAY_FREQUENCY_TO_R365, false)) {

      // Get All PayPeriods for employee starting from Company StartDate
      var prefLookup =
          new EmployeePayPeriodPreferenceLookup()
              .withCompanyIds(Set.of(company.getId()))
              .withEmployeeIds(Set.of(employee.getId()));

      var payPeriodPreferences =
          employeePayPeriodPreferenceLookupService.listAll(prefLookup, Pageable.unpaged()).stream()
              .collect(Collectors.toList());

      var hasActive = false;
      for (var payPref : payPeriodPreferences) {
        if (payPref.isActive()) {
          hasActive = true;
          break;
        }
      }

      if (payPeriodPreferences.size() == 0 || !hasActive) {
        payPeriodPreferences.add(result.getCurrentPayPeriodPreference());
      }

      publishUpdateR365EmployeePayFrequency(
          payPeriodPreferences, employee.getWorkerId(), company.getId());
    }

    return result;
  }

  private void publishUpdateR365EmployeePayFrequency(
      List<PayPeriodPreference> ppPreferences, String workerId, Long companyId) {

    var payload = new PublishUpdateR365EmployeePayFrequencyPayload(ppPreferences);

    r365HttpClient.publishQueueMessage(
      "employee-pay-frequency",
      companyId,
      payload
    );
  }

  /**
   * Creates the payPeriodPreference at the start of the current payPeriod. Should be used by
   * onboarding only!!!
   */
  @Transactional
  public void tryCreateBackdatedPayPeriodPreference(
      @NonNull DetailedCompany company,
      @NonNull DetailedEmployee employee,
      PayPeriodType payPeriodType)
      throws PayPeriodException {
    var companyNow = companyLocalTimeService.companyLocalNowDate(company.getId());

    if (payPeriodType == null
        || getCompanyActiveOrUpcomingPreference(company.getId(), companyNow)
            .getPayPeriodType()
            .equals(payPeriodType)) {
      return; // Do nothing, employee selected the same thing as the company preference
    }

    var companyStartDate = company.getStartDate();
    var request = new PayPeriodPreferenceUpdateRequest(payPeriodType, companyNow);
    if (companyStartDate.isAfter(companyNow)) {
      // Company is onboarding, just schedule the change
      //  Note: pay periods will be created automatically per the preference schedule
      updateEmployeePayPeriodPreference(company, employee, request);
      return;
    }

    var startPayingEmployeeDate = DateUtil.maxDate(companyStartDate, employee.getStartDate());

    if (companyNow.isAfter(startPayingEmployeeDate)) {
      // User is onboarding late, they have to wait for change preferences to take effect
      //  Note: they will be paid per the active period already created for company
      updateEmployeePayPeriodPreference(company, employee, request);
      return;
    }
    // Company is active && employee starts on or after today

    // FIXME: to get an updated preference to be active today, we need to pass in yesterday.
    //  This is confusing and needs to be reworked
    request.setStartDate(companyNow.minusDays(1));
    updateEmployeePayPeriodPreference(company, employee, request);

    payPeriodService.generatePayPeriod(employee, companyNow);
  }

  public PayPeriodPreference getCompanyActiveOrUpcomingPreference(
      Long companyId, LocalDate startDate) {
    var lookup =
        new CompanyPayPeriodPreferenceLookup()
            .withCompanyIds(Set.of(companyId))
            .withMinEndDateInclusive(startDate);
    return companyPayPeriodPreferenceLookupService.listAll(lookup, Pageable.unpaged()).stream()
        .filter(preference -> !preference.getPayPeriodType().equals(PayPeriodType.CUSTOM))
        .min(Comparator.comparing(PayPeriodPreference::getStartDate))
        .orElseThrow(() -> new CompanyIsMissingPayPeriodPreference(companyId, startDate));
  }

  private UpdatedPayPeriodPreferences applyPayPeriodPreferenceUpdate(
      DetailedCompany company, Employee employee, PayPeriodPreferenceUpdateRequest request)
      throws PayPeriodException {
    var companyId = company.getId();
    var employeeId = employee.getId();
    var selectedPeriodType = request.getPayPeriodType();
    var requestEffectiveDate = request.getStartDate();
    var earliestStartDateForRequest =
        DateUtil.maxDate(requestEffectiveDate, company.getStartDate(), employee.getStartDate());

    // Delete all future employee pay periods and pay preferences
    validateUpdateIsPermitted(company, employee, requestEffectiveDate);
    deleteUpcomingPayPeriods(companyId, employeeId, requestEffectiveDate);
    deleteUpcomingPayPreferences(companyId, employeeId, requestEffectiveDate);

    var updatePrefResult = new UpdatedPayPeriodPreferences();

    // find preferences for company and employee
    var activeLookupDate = requestEffectiveDate.minusDays(1);
    var companyPref = getCompanyActiveOrUpcomingPreference(companyId, activeLookupDate);
    var eeActivePref = findActiveEmployeePreference(companyId, employeeId, activeLookupDate);
    var eeCurrentNonCustomPref =
        getCurrentOrPreviousNonCustomPreference(company, employee, activeLookupDate);
    // determine effective preference for ee: falls back to companyPref if empty
    var eeEffectivePref = eeActivePref.orElse(companyPref);
    if (eeEffectivePref.getPayPeriodType().equals(selectedPeriodType)) {
      // already on selected preference; remove endDate and populate results.
      //  - this can happen if a person changes "back" to an active preference
      if (!eeEffectivePref.getPayPeriodType().equals(companyPref.getPayPeriodType())) {
        eeEffectivePref.setEndDate(PayPeriodPreference.MAX_DATE);
      }
      updatePrefResult.setCurrentPayPeriodPreference(eeEffectivePref);
      updatePrefResult.setSelectedPayPeriodType(
          SelectedPreferenceType.of(
              eeEffectivePref.getPayPeriodType(), earliestStartDateForRequest));

      updatePrefResult.setPayPeriodPreferenceOptions(
          PayPeriodPreferenceOptionService.buildListOfPayPreferenceOptions(
              company,
              employee,
              companyPref.getPayPeriodType(),
              eeCurrentNonCustomPref.getPayPeriodType(),
              updatePrefResult.getSelectedPayPeriodType()));
      return updatePrefResult;
    }

    // determine end date for active preference and start date for selected preference
    var prefChangeDates =
        determineActiveEndDateAndSelectedStartDate(
            companyPref, eeEffectivePref, request, earliestStartDateForRequest);
    var activePrefEndDate = prefChangeDates.getActivePrefEndDate();
    var selectedPrefStartDate = prefChangeDates.getSelectedPrefStartDate();
    updatePrefResult.setSelectedPayPeriodType(
        SelectedPreferenceType.of(selectedPeriodType, selectedPrefStartDate));

    // set endDate for active preference if it exists
    eeActivePref.ifPresent(
        active -> {
          active.setEndDate(activePrefEndDate);
          payPeriodPreferenceRepository.saveAndRefresh(active);
        });
    // set the current preference on the response object
    updatePrefResult.setCurrentPayPeriodPreference(eeEffectivePref);

    // create gap if needed
    if (activePrefEndDate.isBefore(selectedPrefStartDate.minusDays(1))) {
      validateGapPreferenceRequired(
          eeEffectivePref.getPayPeriodType(),
          selectedPeriodType,
          activePrefEndDate,
          selectedPrefStartDate);
      updatePrefResult.setGapPayPeriodPreference(
          createNewPreference(
              new PayPeriodPreferenceUpdateRequest(
                  PayPeriodType.CUSTOM,
                  companyId,
                  employeeId,
                  activePrefEndDate.plusDays(1),
                  selectedPrefStartDate.minusDays(1))));
    }

    // don't create a preference that is the same as the company traditional period
    if (companyPref.getPayPeriodType().equals(selectedPeriodType)) {
      updatePrefResult.setFuturePayPeriodPreference(companyPref);

      updatePrefResult.setPayPeriodPreferenceOptions(
          PayPeriodPreferenceOptionService.buildListOfPayPreferenceOptions(
              company,
              employee,
              companyPref.getPayPeriodType(),
              eeCurrentNonCustomPref.getPayPeriodType(),
              updatePrefResult.getSelectedPayPeriodType()));

      return updatePrefResult;
    }

    // create the future preference
    updatePrefResult.setFuturePayPeriodPreference(
        createNewPreference(
            new PayPeriodPreferenceUpdateRequest(
                selectedPeriodType,
                companyId,
                employeeId,
                selectedPrefStartDate,
                PayPeriodPreference.MAX_DATE,
                request.getWeeklyStartDay(),
                request.getFirstSemiMonthlyStartDay(),
                request.getMonthlyStartDay())));

    updatePrefResult.setPayPeriodPreferenceOptions(
        PayPeriodPreferenceOptionService.buildListOfPayPreferenceOptions(
            company,
            employee,
            companyPref.getPayPeriodType(),
            eeCurrentNonCustomPref.getPayPeriodType(),
            updatePrefResult.getSelectedPayPeriodType()));
    return updatePrefResult;
  }

  private LocalDate determineNextPeriodEndDate(PayPeriodPreference preference, LocalDate after) {
    var payPeriodType = preference.getPayPeriodType();
    if (payPeriodType.equals(PayPeriodType.CUSTOM)) {
      var endDate = preference.getEndDate();
      if (after.isAfter(endDate)) {
        return PayPeriodPreference.MAX_DATE;
      }
      return endDate;
    }
    if (!payPeriodType.equals(PayPeriodType.BI_WEEKLY)) {
      return payPeriodType.getNextAvailableStartDate(preference, after).minusDays(1);
    }

    var periodsLookup =
        new PayPeriodLookup()
            .withCompanyIds(Set.of(preference.getCompanyId()))
            .withNullEmployeeId(preference.getEmployeeId() == null)
            .withEmployeeIds(SetUtils.toSetOrNull(preference.getEmployeeId()))
            .withPayPeriodTypes(Set.of(PayPeriodType.BI_WEEKLY));
    var period =
        payPeriodLookupService.listAll(periodsLookup, Pageable.unpaged()).stream()
            .max(Comparator.comparing(PayPeriod::getEndDate));

    if (period.isEmpty()) {
      return PayPeriodType.BI_WEEKLY.getNextAvailableStartDate(preference, after);
    }

    var nextEndDate =
        Optional.ofNullable(period.get().getEndDate())
            .orElseThrow(() -> new RuntimeException("Pay Period is missing an EndDate"));
    if (nextEndDate.isBefore(after)) {
      for (var date : nextEndDate.datesUntil(after).collect(Collectors.toList())) {
        if (date.equals(nextEndDate) && nextEndDate.isBefore(after)) {
          nextEndDate = nextEndDate.plusWeeks(2);
        }
      }
    }

    return nextEndDate;
  }

  private LocalDate determineSelectedStartDate(
      PayPeriodPreference companyPref,
      PayPeriodPreferenceUpdateRequest request,
      LocalDate earliestStartDate) {
    var after = earliestStartDate.minusDays(1);
    var selectedType = request.getPayPeriodType();
    // if selecting traditional cycle and it's BI_WEEKLY, we need to reference the generated period
    if (selectedType.equals(PayPeriodType.BI_WEEKLY)
        && companyPref.getPayPeriodType().equals(selectedType)) {
      return determineNextPeriodEndDate(companyPref, after).plusDays(1);
    }
    return selectedType.getNextAvailableStartDate(request, after);
  }

  private PreferenceTransitionDates determineActiveEndDateAndSelectedStartDate(
      PayPeriodPreference companyPref,
      PayPeriodPreference eeEffectivePref,
      PayPeriodPreferenceUpdateRequest request,
      LocalDate earliestStartDateForSelectedPref) {
    var after = request.getStartDate().minusDays(1);
    var selectedType = request.getPayPeriodType();
    var selectedPrefStartDate =
        determineSelectedStartDate(companyPref, request, earliestStartDateForSelectedPref);
    var effectivePrefEndDate = determineNextPeriodEndDate(eeEffectivePref, after);
    // clamp the effectivePrefEndDate to the day before the earliest StartDate for the selected pref
    if (request.getStartDate().isBefore(earliestStartDateForSelectedPref)) {
      effectivePrefEndDate =
          DateUtil.minDate(effectivePrefEndDate, earliestStartDateForSelectedPref.minusDays(1));
    }

    // push out startDate for selected pref until it is after the effective pref endDate
    if (selectedPrefStartDate.isBefore(effectivePrefEndDate.plusDays(1))) {
      for (var date :
          selectedPrefStartDate
              .datesUntil(effectivePrefEndDate.plusDays(1))
              .collect(Collectors.toList())) {
        if (date.equals(selectedPrefStartDate)
            && selectedPrefStartDate.isBefore(effectivePrefEndDate.plusDays(1))) {
          selectedPrefStartDate =
              selectedType.getNextAvailableStartDate(request, selectedPrefStartDate);
        }
      }
    }

    // Squeeze EffectiveEnd toward SelectedStart to minimize gap period
    var nextEffectiveEndDate =
        determineNextPeriodEndDate(eeEffectivePref, effectivePrefEndDate.plusDays(1));
    if (nextEffectiveEndDate.isBefore(selectedPrefStartDate)) {
      for (var date :
          nextEffectiveEndDate.datesUntil(selectedPrefStartDate).collect(Collectors.toList())) {
        if (date.equals(nextEffectiveEndDate)
            && nextEffectiveEndDate.isBefore(selectedPrefStartDate)) {
          effectivePrefEndDate = nextEffectiveEndDate;
          nextEffectiveEndDate =
              determineNextPeriodEndDate(eeEffectivePref, effectivePrefEndDate.plusDays(1));
        }
      }
    }

    var latestEffectiveEndDate =
        DateUtil.maxDate(effectivePrefEndDate, earliestStartDateForSelectedPref.minusDays(1));
    return new PreferenceTransitionDates(latestEffectiveEndDate, selectedPrefStartDate);
  }

  private void validateGapPreferenceRequired(
      PayPeriodType activeType, PayPeriodType newType, LocalDate oldEndDate, LocalDate newStartDate)
      throws PayPeriodException {
    var validTypes = Set.of(PayPeriodType.WEEKLY, PayPeriodType.BI_WEEKLY, PayPeriodType.CUSTOM);
    if (!validTypes.contains(activeType) && !validTypes.contains(newType)) {
      throw new PayPeriodException(
          "Next PayPeriodPreference startDate ("
              + newStartDate
              + ") is not exactly one day after previous PayPeriod endDate ("
              + oldEndDate
              + ") while trying to switch from "
              + activeType
              + " to "
              + newType
              + "!");
    }
  }

  private void validateUpdateIsPermitted(
      DetailedCompany company, Employee employee, LocalDate forDate) {
    if (company.getPhase(forDate).equals(Phase.ENDED)) {
      throw new ActionNotSupportedForEndedCompany();
    }
    if (employee.getPhase(forDate).equals(Phase.ENDED)) {
      throw new ActionNotSupportedForTerminatedWorker();
    }
  }

  private void deleteUpcomingPayPeriods(Long companyId, Long employeeId, LocalDate startDate) {
    var futurePeriodsLookup =
        new PayPeriodLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(Set.of(employeeId))
            .withMinStartDateInclusive(startDate);
    var futurePeriods =
        payPeriodLookupService.listAll(futurePeriodsLookup, Pageable.unpaged()).stream()
            .collect(Collectors.toList());

    if (futurePeriods.size() > 0) {
      payPeriodService.deletePayPeriods(futurePeriods);
    }
  }

  private void deleteUpcomingPayPreferences(Long companyId, Long employeeId, LocalDate startDate) {
    var futurePrefs = listUpcomingPayPeriodPreferences(companyId, employeeId, startDate);
    if (futurePrefs.size() > 0) {
      payPeriodPreferenceRepository.deleteAll(futurePrefs);
      payPeriodPreferenceRepository.flush();
    }
  }

  private PayPeriodPreference createNewPreference(PayPeriodPreferenceUpdateRequest request)
      throws PayPeriodException {

    if (request.getStartDate().isAfter(request.getEndDate())) {
      throw new PayPeriodException(
          "PayPeriodPreference startDate ("
              + request.getStartDate()
              + ") cannot be after PayPeriodPreference endDate ("
              + request.getEndDate()
              + ")");
    }

    // Create a new preference
    var newPref = new PayPeriodPreference();
    newPref.setCompanyId(request.getCompanyId());
    newPref.setEmployeeId(request.getEmployeeId());
    newPref.setPayPeriodType(request.getPayPeriodType());
    newPref.setWeeklyStartDay(request.getWeeklyStartDay());
    newPref.setFirstSemiMonthlyStartDay(request.getFirstSemiMonthlyStartDay());
    newPref.setMonthlyStartDay(request.getMonthlyStartDay());
    newPref.setStartDate(request.getStartDate());
    newPref.setEndDate(request.getEndDate());

    // Save it
    newPref = payPeriodPreferenceRepository.saveAndRefresh(newPref);

    return newPref;
  }

  @Data
  @ToString
  public static class UpdatedPayPeriodPreferences {
    // used by: mobile & webapp
    private SelectedPreferenceType selectedPayPeriodType;

    @JsonIgnore private List<PayPeriodPreferenceOption> payPeriodPreferenceOptions;

    @JsonIgnore @Deprecated // used for testing only
    private PayPeriodPreference currentPayPeriodPreference;

    @JsonIgnore @Deprecated // used for testing only
    private PayPeriodPreference gapPayPeriodPreference;

    @JsonIgnore @Deprecated // used for testing only
    private PayPeriodPreference futurePayPeriodPreference;
  }

  @Data
  private static class PreferenceTransitionDates {
    private final LocalDate activePrefEndDate;
    private final LocalDate selectedPrefStartDate;
  }
}
