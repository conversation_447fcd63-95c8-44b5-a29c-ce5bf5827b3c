package com.everee.api.payperiod;

import static com.everee.api.util.ObjectUtils.allPresent;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.BaseModel;
import com.everee.api.model.DateRange;
import com.everee.api.payment.group.PaymentGroupDescriptor;
import com.everee.api.payrollcalendar.models.PayDateAdjustmentReason;
import com.everee.api.payrollcalendar.models.PayrollCalendar;
import com.everee.api.payrun.models.PayRun;
import com.everee.api.phase.Phased;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;

import java.io.Serializable;
import java.time.LocalDate;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.context.i18n.LocaleContextHolder;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Entity
@EntityListeners(PayPeriodPayDateListener.class)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PayPeriod extends BaseModel implements Phased, PaymentGroupDescriptor, Serializable {

    private static final long serialVersionUID = -4067309533231557901L;

    @NotNull
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate startDate;

    @NotNull
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate endDate;

    @NotNull
    private Long companyId;

    private Long employeeId;

    private Long payPeriodPreferenceId;

    private Long payrollCalendarId;

    @ToString.Exclude
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "payPeriodPreferenceId", insertable = false, updatable = false)
    private PayPeriodPreference payPeriodPreference;

    @ToString.Exclude
    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "payrollCalendarId", insertable = false, updatable = false)
    private PayrollCalendar payrollCalendar;

    @ToString.Exclude
    @JsonIgnore
    @OneToOne(mappedBy = "payPeriod", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private PayRun payRun;

    private LocalDate payDate;

    private LocalDate calculatedPayDate;

    private Long adjustedByUserId;

    @Enumerated(EnumType.STRING)
    private PayDateAdjustmentReason adjustedPayDateReason;

    @NotNull
    @Enumerated(EnumType.STRING)
    private PayPeriodType payPeriodType;

    private LocalDate calculatedStartDate;

    private LocalDate calculatedEndDate;

    private Long createdByUserId;

    private Long updatedByUserId;

    public PayPeriod(
            LocalDate startDate, LocalDate endDate, PayPeriodPreference payPeriodPreference) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.payPeriodPreferenceId = payPeriodPreference.getId();
        this.payPeriodType = payPeriodPreference.getPayPeriodType();
        this.companyId = payPeriodPreference.getCompanyId();
        this.employeeId = payPeriodPreference.getEmployeeId();
    }

    @Override
    public LocalDate getEffectiveDate() {
        return endDate;
    }

    @Override
    public LocalizedString getLocalizedTitle() {
        return payPeriodType.getLocalizedTitle();
    }

    @Override
    public LocalizedString getLocalizedDescription() {
        return payPeriodType.getLocalizedTitle();
    }

    @Override
    public String getLocalizedDateRange() {
        if (allPresent(startDate, endDate)) {
            if (payPeriodType == PayPeriodType.PAY_ON_DEMAND_REMAINDER) {
                return DateRange.of(endDate, endDate)
                        .getLocalizedDescription(LocaleContextHolder.getLocale());
            }
            return DateRange.of(startDate, endDate)
                    .getLocalizedDescription(LocaleContextHolder.getLocale());
        } else {
            return null;
        }
    }
}
