package com.everee.api.predict;

import com.everee.api.predict.models.IPredictionModel;
import com.everee.api.predict.models.LinearRegressionModel;
import com.everee.api.predict.models.LinearRegressionWithoutOutliersModel;
import com.everee.api.predict.models.MeanPredictionModel;
import com.everee.api.predict.models.MeanWithoutOutliers;
import com.everee.api.predict.models.MedianPredictionModel;
import com.everee.api.predict.models.WeightedAveragePredictionModel;

public enum PredictionModel {
  MEAN {
    @Override
    public IPredictionModel prepare(PredictionInput input) {
      return new MeanPredictionModel();
    }
  },
  MEAN_WITHOUT_OUTLIERS {
    @Override
    public IPredictionModel prepare(PredictionInput input) {
      return new MeanWithoutOutliers();
    }
  },
  MEDIAN {
    @Override
    public IPredictionModel prepare(PredictionInput input) {
      return new MedianPredictionModel();
    }
  },
  LINEAR_REGRESSION {
    @Override
    public IPredictionModel prepare(PredictionInput input) {
      return new LinearRegressionModel();
    }
  },
  LINEAR_REGRESSION_WITHOUT_OUTLIERS {
    @Override
    public IPredictionModel prepare(PredictionInput input) {
      return new LinearRegressionWithoutOutliersModel();
    }
  },
  WEIGHTED_AVERAGE {
    @Override
    public IPredictionModel prepare(PredictionInput input) {
      return new WeightedAveragePredictionModel();
    }
  },
  ;

  public abstract IPredictionModel prepare(PredictionInput input);
}
