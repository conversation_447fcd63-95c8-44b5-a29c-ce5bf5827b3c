package com.everee.api.predict.models;

import com.everee.api.predict.PredictionInput;
import lombok.NonNull;
import org.apache.commons.math3.stat.regression.SimpleRegression;

public class LinearRegressionWithoutOutliersModel implements IPredictionModel {

  private SimpleRegression r = new SimpleRegression();

  @Override
  public double predict(@NonNull PredictionInput input, double nextX) {
    Double[] x = PredictionModelUtils.replaceOutliersWithNull(input.getXValues());
    double[] y = input.getYValues();
    if (x.length != y.length) {
      throw new IllegalArgumentException("array lengths are not equal");
    }

    for (int i = 0; i < x.length; i++) {
      if (x[i] != null) {
        r.addData(x[i], y[i]);
      }
    }

    return r.predict(nextX);
  }
}
