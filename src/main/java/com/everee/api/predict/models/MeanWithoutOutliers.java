package com.everee.api.predict.models;

import com.everee.api.predict.PredictionInput;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.descriptive.moment.Mean;

@Slf4j
public class MeanWithoutOutliers implements IPredictionModel {

  @Override
  public double predict(@NonNull PredictionInput input, double ignored) {
    double[] values = PredictionModelUtils.removeOutliers(input.getYValues());
    return new Mean().evaluate(values);
  }
}
