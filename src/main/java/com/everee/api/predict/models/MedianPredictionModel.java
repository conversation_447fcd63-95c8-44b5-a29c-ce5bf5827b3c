package com.everee.api.predict.models;

import com.everee.api.predict.PredictionInput;
import lombok.NonNull;
import org.apache.commons.math3.stat.descriptive.rank.Median;

public class MedianPredictionModel implements IPredictionModel {
  private Median median = new Median();

  @Override
  public double predict(@NonNull PredictionInput input, double ignored) {
    return median.evaluate(input.getYValues());
  }
}
