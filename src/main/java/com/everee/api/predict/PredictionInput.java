package com.everee.api.predict;

import java.util.Collection;
import java.util.function.Function;
import lombok.Data;

@Data
public class PredictionInput {

  private final Collection<PredictionInputData> inputs;

  public double[] getXValues() {
    return getValues(PredictionInputData::getX);
  }

  public double[] getYValues() {
    return getValues(PredictionInputData::getY);
  }

  private double[] getValues(Function<PredictionInputData, Double> mapper) {
    return getInputs().stream().map(mapper).mapToDouble(x -> x).toArray();
  }
}
