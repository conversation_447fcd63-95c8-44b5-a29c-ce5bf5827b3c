package com.everee.api.keycloak.event;

import com.everee.api.user.DetailedUser;
import lombok.Getter;

public class UserUpdatedEvent extends UserEvent {

  @Getter private final String rawPassword;

  public UserUpdatedEvent(DetailedUser user) {
    this(user, null);
  }

  public UserUpdatedEvent(DetailedUser user, String rawPassword) {
    super(user);
    this.rawPassword = rawPassword;
  }

  @Override
  public String getActionName() {
    return "update";
  }
}
