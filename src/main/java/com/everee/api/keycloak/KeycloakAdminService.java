package com.everee.api.keycloak;

import com.everee.api.keycloak.event.UserCreatedEvent;
import com.everee.api.keycloak.event.UserDeletedEvent;
import com.everee.api.keycloak.event.UserUpdatedEvent;
import com.everee.api.keycloak.event.UserUpdatedPasswordEvent;
import com.everee.api.user.DetailedUser;
import java.util.List;
import java.util.Optional;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@RequiredArgsConstructor
@Slf4j
public class KeycloakAdminService {

  private final KeycloakProperties properties;

  private Keycloak keycloak;

  public Keycloak getKeycloak() {
    if (keycloak == null) {
      keycloak =
          KeycloakBuilder.builder()
              .clientId(properties.getClientId())
              .clientSecret(properties.getClientSecret())
              .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
              .realm(properties.getRealm())
              .serverUrl(properties.getAuthServerUrl())
              .build();
    }
    keycloak.tokenManager().getAccessToken();
    return keycloak;
  }

  @EventListener
  public void handleEvent(UserCreatedEvent event) {
    log.debug("handleEvent(userCreatedEvent)");
    if (!properties.getEnabled()) {
      return;
    }

    var user = event.getUser();
    if (!StringUtils.hasLength(user.getOidcSubject())) {
      var keycloakId = createUser(user, event.getRawPassword());
      user.setOidcSubject(keycloakId);
    }
  }

  @EventListener
  public void handleEvent(UserDeletedEvent event) {
    log.debug("handleEvent(userDeletedEvent)");
    if (!properties.getEnabled()) {
      return;
    }

    var user = event.getUser();
    if (StringUtils.hasLength(user.getOidcSubject())) {
      deleteUser(user.getOidcSubject());
    }
  }

  @EventListener
  public void handleEvent(UserUpdatedEvent event) {
    log.debug("handleEvent(userUpdatedEvent)");
    if (!properties.getEnabled()) {
      return;
    }

    var user = event.getUser();
    if (StringUtils.hasLength(user.getOidcSubject())) {
      updateUser(user, event.getRawPassword());
    } else {
      var keycloakId = createUser(user, event.getRawPassword());
      user.setOidcSubject(keycloakId);
    }
  }

  @EventListener
  public void handleEvent(UserUpdatedPasswordEvent event) {
    log.debug("handleEvent(userUpdatedPasswordEvent)");
    if (!properties.getEnabled()) {
      return;
    }

    var user = event.getUser();
    if (StringUtils.hasLength(user.getOidcSubject())) {
      if (StringUtils.hasLength(event.getRawPassword())) {
        updatePassword(user.getOidcSubject(), event.getRawPassword());
      }
    } else {
      var keycloakId = createUser(user, event.getRawPassword());
      user.setOidcSubject(keycloakId);
    }
  }

  private String createUser(DetailedUser user, String rawPassword) {
    log.debug("createUser(user: {})", user.getId());

    var representation = toUserRepresentation(user);
    if (StringUtils.hasLength(rawPassword)) {
      representation.setCredentials(List.of(toCredentialRepresentation(rawPassword)));
    }

    try (var response = getKeycloak().realm(properties.getRealm()).users().create(representation)) {
      if (response.getStatus() == HttpStatus.CREATED.value()) {
        return getCreatedId(response);
      } else {
        log.error(
            "Unexpected server response "
                + response.getStatus()
                + " "
                + response.getStatusInfo().getReasonPhrase()
                + ", expected 201 Created");
        log.error("Response: {}", response);
      }
    } catch (Exception e) {
      log.error("Exception in createUser", e);
    }
    return null;
  }

  private void deleteUser(String id) {
    log.debug("deleteUser(id: {})", id);
    try (var response = getKeycloak().realm(properties.getRealm()).users().delete(id)) {
      if (response.getStatus() != HttpStatus.NO_CONTENT.value()) {
        throw new RuntimeException(
            "Unexpected response deleting keycloak user response: " + response);
      }
    } catch (Exception e) {
      log.error("Exception in deleteUser", e);
    }
  }

  private void updatePassword(String id, String password) {
    log.debug("updatePassword(id: {})", id);
    try {
      getKeycloak()
          .realm(properties.getRealm())
          .users()
          .get(id)
          .resetPassword(toCredentialRepresentation(password));
    } catch (Exception e) {
      log.error("Exception in updatePassword", e);
    }
  }

  private void updateUser(DetailedUser user, String rawPassword) {
    log.debug("updateUser(user: {})", user.getId());

    var representation = toUserRepresentation(user);
    if (StringUtils.hasLength(rawPassword)) {
      representation.setCredentials(List.of(toCredentialRepresentation(rawPassword)));
    }

    try {
      getKeycloak()
          .realm(properties.getRealm())
          .users()
          .get(user.getOidcSubject())
          .update(representation);
    } catch (Exception e) {
      log.error("Exception in updateUser", e);
    }
  }

  private CredentialRepresentation toCredentialRepresentation(String password) {
    var to = new CredentialRepresentation();
    to.setTemporary(false);
    to.setType(CredentialRepresentation.PASSWORD);
    to.setValue(password);
    return to;
  }

  private UserRepresentation toUserRepresentation(DetailedUser from) {
    var to = new UserRepresentation();
    to.setEmail(from.getEmail());
    to.setEmailVerified(!StringUtils.hasLength(from.getUnverifiedEmail()));
    to.setEnabled(true);
    to.setFirstName(from.getFirstName());
    to.setLastName(from.getLastName());
    if (StringUtils.hasLength(from.getPhoneNumber())) {
      to.singleAttribute("phoneNumber", from.getPhoneNumber());
    }
    to.setUsername(from.getUsername());
    return to;
  }

  private String getCreatedId(Response response) {
    return Optional.ofNullable(response.getLocation())
        .map(
            location -> {
              var path = location.getPath();
              return path.substring(path.lastIndexOf('/') + 1);
            })
        .orElse(null);
  }
}
