package com.everee.api.terminalsession;

import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Version;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

@Data
@Entity
public class TerminalSession {
  @NotNull @Id private String id;

  @NotNull private Long companyId;

  @NotNull private Long locationId;

  @CreatedBy @NotNull private Long createdByUserId;

  @NotBlank private String nickname;

  @NotBlank private String ipAddress;

  @NotNull private LocalDateTime lastActiveAt;

  @CreatedDate protected LocalDateTime createdAt;

  @Version @LastModifiedDate protected LocalDateTime updatedAt;
}
