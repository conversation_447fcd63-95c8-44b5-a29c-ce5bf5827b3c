package com.everee.api.terminalsession;

import com.everee.api.auth.annotation.AccountOwnerAccess;
import com.everee.api.auth.annotation.FinancialManagerAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.terminalsession.lookup.TerminalSessionLookup;
import com.everee.api.terminalsession.lookup.TerminalSessionLookupService;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AccountOwnerAccess
@FinancialManagerAccess
@RequestMapping("/api/v2/terminal-sessions")
@RequiredArgsConstructor
public class TerminalSessionController {
  private final TerminalSessionService terminalSessionService;
  private final TerminalSessionLookupService terminalSessionLookupService;
  private final CompanyService companyService;

  @GetMapping
  public Page<TerminalSession> listTerminalSessions(
      Pageable pageable, @RequestParam(name = "location-id") Long locationId) {
    var companyId = CompanyService.getAuthenticatedCompanyId();

    var lookup =
        new TerminalSessionLookup()
            .setCompanyIds(Set.of(companyId))
            .setLocationIds(Set.of(locationId));

    return terminalSessionLookupService.listAll(lookup, pageable);
  }

  @PostMapping
  public TerminalSessionCreateResponse createTerminalSession(
      HttpServletRequest request,
      @Valid @RequestBody CreateTerminalSessionRequest createTerminalSessionRequest) {
    createTerminalSessionRequest.setIpAddress(request.getRemoteAddr());
    var terminalSession =
        terminalSessionService.createTerminalSession(createTerminalSessionRequest);
    var company = companyService.getCompany(terminalSession.getCompanyId());

    return new TerminalSessionCreateResponse(
        terminalSession.getId(),
        terminalSession.getNickname(),
        company.getId(),
        company.getDisplayName());
  }

  @DeleteMapping("/{terminalSessionId}")
  public void deleteTerminalSession(
      @PathVariable(name = "terminalSessionId") String terminalSessionId) {

    var companyId = CompanyService.getAuthenticatedCompanyId();
    var lookup =
        new TerminalSessionLookup()
            .withCompanyIds(Set.of(companyId))
            .withIds(Set.of(terminalSessionId));

    terminalSessionService.deleteTerminalSession(lookup);
  }
}
