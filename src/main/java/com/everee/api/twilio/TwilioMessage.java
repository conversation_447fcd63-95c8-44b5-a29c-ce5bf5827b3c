package com.everee.api.twilio;

import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Currency;
import java.util.Optional;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;
import lombok.NonNull;
import org.joda.time.DateTime;

@Data
@Entity
public class TwilioMessage {

  @Id private String id;

  private String uri;

  private String body;

  private String status;

  private String direction;

  private Integer errorCode;

  private String accountSid;

  private String apiVersion;

  private String errorMessage;

  private String toPhoneNumber;

  private String fromPhoneNumber;

  private String messagingServiceSid;

  private String numMedia;

  private String numSegments;

  private String price;

  private String priceUnit;

  private LocalDateTime sentAt;

  private LocalDateTime createdAt;

  private LocalDateTime updatedAt;

  public static TwilioMessage of(@NonNull Message source) {
    var target = new TwilioMessage();
    target.setId(source.getSid());
    target.setUri(source.getUri());
    target.setBody(source.getBody());
    target.setErrorCode(source.getErrorCode());
    target.setAccountSid(source.getAccountSid());
    target.setApiVersion(source.getApiVersion());
    target.setErrorMessage(source.getErrorMessage());
    target.setToPhoneNumber(source.getTo());
    target.setMessagingServiceSid(source.getMessagingServiceSid());
    target.setNumMedia(source.getNumMedia());
    target.setNumSegments(source.getNumSegments());
    target.setPrice(source.getPrice());

    Optional.ofNullable(source.getStatus())
        .map(Message.Status::toString)
        .ifPresent(target::setStatus);

    Optional.ofNullable(source.getDirection())
        .map(Message.Direction::toString)
        .ifPresent(target::setStatus);

    Optional.ofNullable(source.getFrom())
        .map(PhoneNumber::toString)
        .ifPresent(target::setFromPhoneNumber);

    Optional.ofNullable(source.getPriceUnit())
        .map(Currency::getCurrencyCode)
        .ifPresent(target::setPriceUnit);

    Optional.ofNullable(source.getDateCreated())
        .map(DateTime::getMillis)
        .map(Instant::ofEpochMilli)
        .map(i -> i.atZone(ZoneId.systemDefault()))
        .map(ZonedDateTime::toLocalDateTime)
        .ifPresent(target::setCreatedAt);

    Optional.ofNullable(source.getDateUpdated())
        .map(DateTime::getMillis)
        .map(Instant::ofEpochMilli)
        .map(i -> i.atZone(ZoneId.systemDefault()))
        .map(ZonedDateTime::toLocalDateTime)
        .ifPresent(target::setUpdatedAt);

    Optional.ofNullable(source.getDateSent())
        .map(DateTime::getMillis)
        .map(Instant::ofEpochMilli)
        .map(i -> i.atZone(ZoneId.systemDefault()))
        .map(ZonedDateTime::toLocalDateTime)
        .ifPresent(target::setSentAt);

    return target;
  }
}
