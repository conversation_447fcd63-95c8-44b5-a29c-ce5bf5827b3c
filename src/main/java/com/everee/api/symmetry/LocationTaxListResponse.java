package com.everee.api.symmetry;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JsonTypeName("LocationTaxResponse")
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
@EqualsAndHashCode(callSuper = true)
public class LocationTaxListResponse extends SymmetryResponse {
  @JsonProperty("taxItemFull")
  private List<TaxItem> taxItems = new ArrayList<TaxItem>();
}
