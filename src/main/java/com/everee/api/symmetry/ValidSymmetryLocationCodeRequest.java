package com.everee.api.symmetry;

import com.everee.api.model.PhysicalAddress;
import com.everee.api.tax.state.State;
import com.everee.api.user.address.HomeAddress;
import com.everee.api.util.ObjectUtils;
import java.time.ZoneId;

public class ValidSymmetryLocationCodeRequest implements PhysicalAddress {

  private String line1;
  private String line2;
  private String city;
  private State state;
  private String postalCode;
  private Double latitude;
  private Double longitude;
  private ZoneId timeZone;
  private String symmetryLocationCode;
  private String symmetryMunicipalityCode;
  private String symmetrySchoolCode;
  private String symmetryAddressNormalized;
  private String symmetryCityCode;
  private String symmetryCityName;
  private String symmetryCountyCode;
  private String symmetryCountyName;
  private String symmetryStateAbbreviation;
  private String symmetryStateCode;
  private String symmetryStateName;
  private String symmetryMunicipalityName;
  private String symmetrySchoolName;
  private String symmetryTransitDistrictCode;
  private String symmetryTransitDistrictName;
  private String symmetryPsdCode;
  private String symmetryPsdRate;

  public static PhysicalAddress from(ValidSymmetryLocationCodeRequest validAddressRequest) {
    return new HomeAddress()
        .setLine1(validAddressRequest.getLine1())
        .setLine2(validAddressRequest.getLine2())
        .setCity(validAddressRequest.getCity())
        .setState(validAddressRequest.getState())
        .setPostalCode(validAddressRequest.getPostalCode())
        .setLatitude(validAddressRequest.getLatitude())
        .setLongitude(validAddressRequest.getLongitude())
        .setTimeZone(validAddressRequest.getTimeZone())
        .setSymmetryLocationCode(validAddressRequest.getSymmetryLocationCode())
        .setSymmetryMunicipalityCode(validAddressRequest.getSymmetryMunicipalityCode())
        .setSymmetrySchoolCode(validAddressRequest.getSymmetrySchoolCode())
        .setSymmetryAddressNormalized(validAddressRequest.getSymmetryAddressNormalized())
        .setSymmetryCityCode(validAddressRequest.getSymmetryCityCode())
        .setSymmetryCityName(validAddressRequest.getSymmetryCityName())
        .setSymmetryCountyCode(validAddressRequest.getSymmetryCountyCode())
        .setSymmetryCountyName(validAddressRequest.getSymmetryCountyName())
        .setSymmetryStateAbbreviation(validAddressRequest.getSymmetryStateAbbreviation())
        .setSymmetryStateCode(validAddressRequest.getSymmetryStateCode())
        .setSymmetryStateName(validAddressRequest.getSymmetryStateName())
        .setSymmetryMunicipalityName(validAddressRequest.getSymmetryMunicipalityName())
        .setSymmetrySchoolName(validAddressRequest.getSymmetrySchoolName())
        .setSymmetryTransitDistrictCode(validAddressRequest.getSymmetryTransitDistrictCode())
        .setSymmetryTransitDistrictName(validAddressRequest.getSymmetryTransitDistrictName())
        .setSymmetryPsdCode(validAddressRequest.getSymmetryPsdCode())
        .setSymmetryPsdRate(validAddressRequest.getSymmetryPsdRate());
  }

  @Override
  public String getLine1() {
    return line1;
  }

  public void setLine1(String line1) {
    this.line1 = line1;
  }

  @Override
  public String getLine2() {
    return line2;
  }

  public void setLine2(String line2) {
    this.line2 = line2;
  }

  @Override
  public String getCity() {
    return city;
  }

  public void setCity(String city) {
    this.city = city;
  }

  @Override
  public State getState() {
    return state;
  }

  public void setState(State state) {
    this.state = state;
  }

  @Override
  public String getPostalCode() {
    return postalCode;
  }

  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }

  @Override
  public Double getLatitude() {
    return latitude;
  }

  public void setLatitude(Double latitude) {
    this.latitude = latitude;
  }

  @Override
  public Double getLongitude() {
    return longitude;
  }

  public void setLongitude(Double longitude) {
    this.longitude = longitude;
  }

  @Override
  public ZoneId getTimeZone() {
    return timeZone;
  }

  public void setTimeZone(ZoneId timeZone) {
    this.timeZone = timeZone;
  }

  public String getSymmetryLocationCode() {
    return symmetryLocationCode;
  }

  public void setSymmetryLocationCode(String symmetryLocationCode) {
    this.symmetryLocationCode = symmetryLocationCode;
  }

  public String getSymmetryMunicipalityCode() {
    return symmetryMunicipalityCode;
  }

  public void setSymmetryMunicipalityCode(String symmetryMunicipalityCode) {
    this.symmetryMunicipalityCode = symmetryMunicipalityCode;
  }

  public String getSymmetrySchoolCode() {
    return symmetrySchoolCode;
  }

  public void setSymmetrySchoolCode(String symmetrySchoolCode) {
    this.symmetrySchoolCode = symmetrySchoolCode;
  }

  public String getSymmetryAddressNormalized() {
    return symmetryAddressNormalized;
  }

  public void setSymmetryAddressNormalized(String symmetryAddressNormalized) {
    this.symmetryAddressNormalized = symmetryAddressNormalized;
  }

  public String getSymmetryCityCode() {
    return symmetryCityCode;
  }

  public void setSymmetryCityCode(String symmetryCityCode) {
    this.symmetryCityCode = symmetryCityCode;
  }

  public String getSymmetryCityName() {
    return symmetryCityName;
  }

  public void setSymmetryCityName(String symmetryCityName) {
    this.symmetryCityName = symmetryCityName;
  }

  public String getSymmetryCountyCode() {
    return symmetryCountyCode;
  }

  public void setSymmetryCountyCode(String symmetryCountyCode) {
    this.symmetryCountyCode = symmetryCountyCode;
  }

  public String getSymmetryCountyName() {
    return symmetryCountyName;
  }

  public void setSymmetryCountyName(String symmetryCountyName) {
    this.symmetryCountyName = symmetryCountyName;
  }

  public String getSymmetryStateAbbreviation() {
    return symmetryStateAbbreviation;
  }

  public void setSymmetryStateAbbreviation(String symmetryStateAbbreviation) {
    this.symmetryStateAbbreviation = symmetryStateAbbreviation;
  }

  public String getSymmetryStateCode() {
    return symmetryStateCode;
  }

  public void setSymmetryStateCode(String symmetryStateCode) {
    this.symmetryStateCode = symmetryStateCode;
  }

  public String getSymmetryStateName() {
    return symmetryStateName;
  }

  public void setSymmetryStateName(String symmetryStateName) {
    this.symmetryStateName = symmetryStateName;
  }

  public String getSymmetryMunicipalityName() {
    return symmetryMunicipalityName;
  }

  public void setSymmetryMunicipalityName(String symmetryMunicipalityName) {
    this.symmetryMunicipalityName = symmetryMunicipalityName;
  }

  public String getSymmetrySchoolName() {
    return symmetrySchoolName;
  }

  public void setSymmetrySchoolName(String symmetrySchoolName) {
    this.symmetrySchoolName = symmetrySchoolName;
  }

  public String getSymmetryTransitDistrictCode() {
    return symmetryTransitDistrictCode;
  }

  public void setSymmetryTransitDistrictCode(String symmetryTransitDistrictCode) {
    this.symmetryTransitDistrictCode = symmetryTransitDistrictCode;
  }

  public String getSymmetryTransitDistrictName() {
    return symmetryTransitDistrictName;
  }

  public void setSymmetryTransitDistrictName(String symmetryTransitDistrictName) {
    this.symmetryTransitDistrictName = symmetryTransitDistrictName;
  }

  public String getSymmetryPsdCode() {
    return symmetryPsdCode;
  }

  public void setSymmetryPsdCode(String symmetryPsdCode) {
    this.symmetryPsdCode = symmetryPsdCode;
  }

  public String getSymmetryPsdRate() {
    return symmetryPsdRate;
  }

  public void setSymmetryPsdRate(String symmetryPsdRate) {
    this.symmetryPsdRate = symmetryPsdRate;
  }

  public boolean isGeocoded() {
    return ObjectUtils.allPresent(latitude, longitude);
  }
}
