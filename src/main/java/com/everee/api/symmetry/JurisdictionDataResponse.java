package com.everee.api.symmetry;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JsonTypeName("JurisdictionDataResponse")
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
@EqualsAndHashCode(callSuper = true)
public class JurisdictionDataResponse extends SymmetryResponse {
  private List<JurisdictionCollection> jurisdictionCollection;
}
