package com.everee.api.symmetry;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.symmetry.location.SymmetryApiAddress;
import com.everee.api.symmetry.location.SymmetryApiGeo;
import com.everee.api.symmetry.location.SymmetryLocationCodeJurisdiction;
import com.everee.api.symmetry.location.SymmetryLocationCodeRequestLocation;
import com.everee.api.symmetry.location.SymmetryLocationCodeRequestV2;
import com.everee.api.tax.TaxType;
import io.jsonwebtoken.lang.Collections;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class SymmetryLocationCodeLookupService {

  private final SymmetryClient symmetryClient;

  public SymmetryLocationCodeInfo getLocationCode(@NonNull PhysicalAddress address) {
    var request = new SymmetryLocationCodeRequestV2();
    var location = new SymmetryLocationCodeRequestLocation().setLocationName("address");
    request.setLocations(List.of(location));
    if (address.getLatitude() != null && address.getLongitude() != null) {
      var geo = new SymmetryApiGeo();
      location.setGeoCoordinates(geo);
      geo.setLatitude(address.getLatitude());
      geo.setLongitude(address.getLongitude());
    }

    var symmetryAddress = new SymmetryApiAddress();
    location.setAddress(symmetryAddress);
    symmetryAddress.setStreetAddress1(address.getLine1());
    symmetryAddress.setStreetAddress2(address.getLine2());
    symmetryAddress.setCity(address.getCity());
    symmetryAddress.setState(address.getState().getCode());
    symmetryAddress.setZipCode(address.getPostalCode());

    var response = symmetryClient.getLocationCode(request);

    if (response == null || Collections.isEmpty(response.getLocations())) {
      var msg =
          String.format(
              "Failed to retrieve Symmetry Location Code for %f/%f",
              address.getLatitude(), address.getLongitude());
      throw new InvalidRequestException(msg);
    }

    var symmetryLocation = response.getLocations().get(0);
    var locationCodeDetails = symmetryLocation.getLocationCodeDetails();
    var locationCodeInfo = new SymmetryLocationCodeInfo();
    if (locationCodeDetails == null){ // KIWI-112 Bug fix for null pointer
        return locationCodeInfo;
    }

    locationCodeInfo.setLocationCode(locationCodeDetails.getLocationCode());
    locationCodeInfo.setLocationCodeFull(locationCodeDetails.getLocationCodeFull());
    locationCodeInfo.setGeocoded(symmetryLocation.isGeocoded());
    locationCodeInfo.setLatitude(
        Optional.ofNullable(symmetryLocation.getLatitude()).map(d -> d.toString()).orElse(null));
    locationCodeInfo.setLongitude(
        Optional.ofNullable(symmetryLocation.getLongitude()).map(d -> d.toString()).orElse(null));
    locationCodeInfo.setAddressNormalized(
        Optional.ofNullable(symmetryLocation.getNormalizedAddress())
            .map(SymmetryApiAddress::toString)
            .orElse(null));
    locationCodeInfo.setStateAbbreviation(
        Optional.ofNullable(symmetryLocation.getNormalizedAddress())
            .map(SymmetryApiAddress::getState)
            .orElse(null));

    locationCodeInfo.setStateCode(
        locationCodeDetails
            .getJurisdiction("STATE")
            .map(SymmetryLocationCodeJurisdiction::getCode)
            .orElse(null));
    locationCodeInfo.setStateName(
        locationCodeDetails
            .getJurisdiction("STATE")
            .map(SymmetryLocationCodeJurisdiction::getName)
            .orElse(null));

    locationCodeInfo.setCountyCode(
        locationCodeDetails
            .getJurisdiction(TaxType.CNTY.name())
            .map(SymmetryLocationCodeJurisdiction::getCode)
            .orElse(null));
    locationCodeInfo.setCountyName(
        locationCodeDetails
            .getJurisdiction(TaxType.CNTY.name())
            .map(SymmetryLocationCodeJurisdiction::getName)
            .orElse(null));

    locationCodeInfo.setCityCode(
        locationCodeDetails
            .getJurisdiction(TaxType.CITY.name())
            .map(SymmetryLocationCodeJurisdiction::getCode)
            .orElse(null));
    locationCodeInfo.setCityName(
        locationCodeDetails
            .getJurisdiction(TaxType.CITY.name())
            .map(SymmetryLocationCodeJurisdiction::getName)
            .orElse(null));

    locationCodeInfo.setSchoolCode(
        locationCodeDetails
            .getJurisdiction(TaxType.SCHL.name())
            .map(SymmetryLocationCodeJurisdiction::getCode)
            .orElse(null));
    locationCodeInfo.setSchoolName(
        locationCodeDetails
            .getJurisdiction(TaxType.SCHL.name())
            .map(SymmetryLocationCodeJurisdiction::getName)
            .orElse(null));

    locationCodeInfo.setTransitDistrictCode(
        locationCodeDetails
            .getJurisdiction(TaxType.TRANS.name())
            .map(SymmetryLocationCodeJurisdiction::getCode)
            .orElse(null));
    locationCodeInfo.setTransitDistrictName(
        locationCodeDetails
            .getJurisdiction(TaxType.TRANS.name())
            .map(SymmetryLocationCodeJurisdiction::getName)
            .orElse(null));

    locationCodeInfo.setMunicipalityCode(
        locationCodeDetails
            .getJurisdiction("MUNI")
            .map(SymmetryLocationCodeJurisdiction::getCode)
            .orElse(null));
    locationCodeInfo.setMunicipalityName(
        locationCodeDetails
            .getJurisdiction("MUNI")
            .map(SymmetryLocationCodeJurisdiction::getName)
            .orElse(null));

    return locationCodeInfo;
  }

  public <T extends SymmetryLocation> T refreshLocationCode(@NonNull T location) {
    var response = getLocationCode(location);

    location.setSymmetryRefreshedAt(LocalDateTime.now());
    location.setSymmetryMunicipalityCode(response.getMunicipalityCode());
    location.setSymmetrySchoolCode(response.getSchoolCode());
    location.setSymmetryAddressNormalized(response.getAddressNormalized());
    location.setSymmetryCityCode(response.getCityCode());
    location.setSymmetryCityName(response.getCityName());
    location.setSymmetryStateAbbreviation(response.getStateAbbreviation());
    location.setSymmetryStateCode(response.getStateCode());
    location.setSymmetryStateName(response.getStateName());
    location.setSymmetryCountyCode(response.getCountyCode());
    location.setSymmetryCountyName(response.getCountyName());
    location.setSymmetryMunicipalityName(response.getMunicipalityName());
    location.setSymmetrySchoolName(response.getSchoolName());
    location.setSymmetryTransitDistrictCode(response.getTransitDistrictCode());
    location.setSymmetryTransitDistrictName(response.getTransitDistrictName());
    location.setSymmetryPsdCode(response.getPsdCode());
    location.setSymmetryPsdRate(response.getPsdRate());
    location.setSymmetryLocationCode(response.getLocationCode());
    location.setSymmetryLocationCodeFull(response.getLocationCodeFull());

    return location;
  }
}
