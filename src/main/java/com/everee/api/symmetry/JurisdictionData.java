package com.everee.api.symmetry;

import com.everee.api.tax.UniqueTaxID;
import com.everee.api.tax.state.State;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class JurisdictionData extends SymmetryResponse {
  @RequiredArgsConstructor
  public enum RegexType {
    PICK_LIST("PickList"),
    SINGLE_VALUE("SingleValue");

    private final String value;

    public static RegexType fromValue(String value) {
      return Arrays.stream(RegexType.values())
          .filter(r -> r.value.equals(value))
          .findFirst()
          .orElseThrow(
              () -> new NoSuchElementException("Unable to find RegexType by value: " + value));
    }

    public String getValue() {
      return value;
    }
  }

  @RequiredArgsConstructor
  public enum DataType {
    STRING("String"),
    INTEGER("Integer"),
    BOOLEAN("Boolean"),
    DOLLAR("Dollar"),
    PERCENTAGE("Percentage");

    private final String value;

    public static DataType fromValue(String value) {
      return Arrays.stream(DataType.values())
          .filter(r -> r.value.equals(value))
          .findFirst()
          .orElseThrow(
              () -> new NoSuchElementException("Unable to find DataType by value: " + value));
    }

    public String getValue() {
      return value;
    }
  }

  private UniqueTaxID uniqueTaxID;
  private String stateCode;
  private String parameterName;
  private String description;
  private String regex;
  private String regexType;
  private String regexDescription;
  private String certificateLineNo;
  private String listValues;
  private String defaultValue = "";
  private String helpText;
  private String dataType;

  @JsonProperty("isOptional")
  private boolean optional;

  public Optional<State> getState() {
    try {
      return Optional.of(State.fromCode(getStateCode()));
    } catch (NoSuchElementException ex) {
      return Optional.empty();
    }
  }

  public Map<String, String> getListValues() {
    return toMap(listValues);
  }

  public RegexType getRegexType() {
    return RegexType.fromValue(regexType);
  }

  public DataType getDataType() {
    return DataType.fromValue(dataType);
  }

  private List<String> toList(String string) {
    if (StringUtils.isBlank(string)) return List.of();
    return List.of(string.split(",")).stream().map(String::trim).collect(Collectors.toList());
  }

  private Map<String, String> toMap(String string) {
    var list = toList(string);
    return list.stream().map(s -> s.split("=")).collect(Collectors.toMap(s -> s[0], s -> s[1]));
  }
}
