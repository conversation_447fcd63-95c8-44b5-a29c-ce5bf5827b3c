package com.everee.api.symmetry.location;

import com.everee.api.symmetry.SymmetryRequest;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
@JsonTypeName("LocationCodeRequest")
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class SymmetryLocationCodeRequestV2 extends SymmetryRequest {
  private boolean verbose = true;
  private List<SymmetryLocationCodeRequestLocation> locations = new ArrayList();
}
