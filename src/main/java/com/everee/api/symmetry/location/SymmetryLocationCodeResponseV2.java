package com.everee.api.symmetry.location;

import com.everee.api.symmetry.SymmetryResponse;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.util.List;
import lombok.Data;

@Data
@JsonTypeName("LocationCodeResponse")
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class SymmetryLocationCodeResponseV2 extends SymmetryResponse {
  private List<SymmetryLocationCodeResponseLocation> locations;
}
