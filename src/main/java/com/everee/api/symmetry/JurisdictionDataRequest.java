package com.everee.api.symmetry;

import com.everee.api.tax.UniqueTaxID;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.util.HashSet;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JsonTypeName("JurisdictionDataRequest")
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
@EqualsAndHashCode(callSuper = true)
public class JurisdictionDataRequest extends SymmetryRequest {
  private String payDate;

  @JsonProperty("uniqueTaxID")
  private Set<UniqueTaxID> uniqueTaxIDs = new HashSet<>();
}
