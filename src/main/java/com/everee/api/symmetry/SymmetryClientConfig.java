package com.everee.api.symmetry;

import com.everee.api.logging.FeignTimingLogger;
import feign.Logger;
import feign.Logger.Level;
import feign.RequestInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;

@RequiredArgsConstructor
public class SymmetryClientConfig {
  private final SymmetryProperties properties;

  @Bean
  Logger.Level feignLoggerLevel() {
    return Level.BASIC;
  }

  @Bean
  RequestInterceptor feignRequestInterceptor() {
    return template -> {
      template.header("Authorization", properties.getAuthToken());
    };
  }

  @Bean
  Logger symmetryClientLogger() {
    return new FeignTimingLogger("symmetry");
  }
}
