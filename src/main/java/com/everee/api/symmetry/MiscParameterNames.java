package com.everee.api.symmetry;

import java.util.Arrays;
import java.util.NoSuchElementException;

public enum MiscParameterNames {
  ADDITIONALALLOWANCES,
  ADDITIONALCOUNTYWITHHOLDING,
  ADDITIONALWH_NYC {
    @Override
    public String getSymmetryName() {
      return "ADDITIONALWH-NYC";
    }
  },
  ADDITIONALWH_YONKERS {
    @Override
    public String getSymmetryName() {
      return "ADDITIONALWH-YONKERS";
    }
  },
  AGEANDBLINDNESSEXEMPTIONS,
  BASICALLOWANCES,
  CALC_WITH_EMPLOYEE_PAYROLL,
  COURTESY,
  DEDUCTIONS,
  DEPENDENTEXEMPTIONS,
  DEPENDENTS,
  DEPENDENTS_AMT,
  FILINGSTATUS,
  FULLTIMESTUDENT,
  HOURS_WORKED,
  IS_CORPORATE_OFFICER,
  IS_PENSION_PAYMENT,
  MINIMUMRATE,
  MOST_RECENT_WH,
  MUNI_WH_YTD,
  NUMBER_EMPLOYEES,
  NUMBER_W2_WORKERS,
  OTHER_INCOME,
  <PERSON><PERSON>SO<PERSON><PERSON>XEMPTIONS,
  PERCENTSTATE,
  PREMIUM_COST,
  QUARTERLY_GROSS_PAYROLL_AMT,
  RATETABLE,
  REDUCED_WH_AMT,
  REGULARALLOWANCES,
  SCHOOL_WH_YTD,
  SUPPLEMENTAL,
  TEXARKANARESIDENT,
  TOTAL_ALLOWANCES,
  TOTAL_CURRENT_WAGES,
  TOTAL_EMPLOYEES,
  TOTAL_HOURS,
  TOTALALLOWANCES,
  TOTALDEPENDENTS,
  TWO_JOBS,
  WH_METHOD,
  WITHHOLDINGCODE,
  YOUTH_EXEMPTION,
  YTD_WAGES,
  _2020_W4 {
    @Override
    public String getSymmetryName() {
      return "2020_W4";
    }
  },
  QUALIFYING_DEPENDANTS,
  OTHER_DEPENDANTS,
  EXTRA_WITHHOLDING,
  EXEMPT;

  public String getSymmetryName() {
    return name();
  }

  public static MiscParameterNames fromSymmetryName(String name) {
    return Arrays.stream(MiscParameterNames.values())
        .filter(miscParameterName -> miscParameterName.getSymmetryName().equals(name))
        .findFirst()
        .orElseThrow(NoSuchElementException::new);
  }
}
