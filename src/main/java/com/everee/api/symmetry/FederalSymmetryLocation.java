package com.everee.api.symmetry;

import com.everee.api.tax.TaxType;
import com.everee.api.tax.state.State;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class FederalSymmetryLocation implements SymmetryLocation {
  public static final FederalSymmetryLocation instance = new FederalSymmetryLocation();

  public static FederalSymmetryLocation getInstance() {
    return instance;
  }

  @Override
  public Double getLatitude() {
    return null;
  }

  @Override
  public <T> T setLatitude(Double latitude) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Double getLongitude() {
    return null;
  }

  @Override
  public <T> T setLongitude(Double longitude) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryLocationCode() {
    return TaxType.FIT.getUniqueTaxID().getLocationCode();
  }

  @Override
  public <T> T setSymmetryLocationCode(String symmetryLocationCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryLocationCodeFull() {
    return TaxType.FIT.getUniqueTaxID().getLocationCodeFull();
  }

  @Override
  public <T> T setSymmetryLocationCodeFull(String symmetryLocationCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryMunicipalityCode() {
    return null;
  }

  @Override
  public <T> T setSymmetryMunicipalityCode(String symmetryMunicipalityCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetrySchoolCode() {
    return null;
  }

  @Override
  public <T> T setSymmetrySchoolCode(String symmetrySchoolCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public LocalDateTime getSymmetryRefreshedAt() {
    return null;
  }

  @Override
  public <T> T setSymmetryRefreshedAt(LocalDateTime symmetryRefreshedAt) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryAddressNormalized() {
    return null;
  }

  @Override
  public <T> T setSymmetryAddressNormalized(String symmetryAddressNormalized) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryCityCode() {
    return null;
  }

  @Override
  public <T> T setSymmetryCityCode(String symmetryCityCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryCityName() {
    return null;
  }

  @Override
  public <T> T setSymmetryCityName(String symmetryCityName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryStateAbbreviation() {
    return null;
  }

  @Override
  public <T> T setSymmetryStateAbbreviation(String symmetryStateAbbreviation) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryStateCode() {
    return null;
  }

  @Override
  public <T> T setSymmetryStateCode(String symmetryStateCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryStateName() {
    return null;
  }

  @Override
  public <T> T setSymmetryStateName(String symmetryStateName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryCountyCode() {
    return null;
  }

  @Override
  public <T> T setSymmetryCountyCode(String symmetryCountyCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryCountyName() {
    return null;
  }

  @Override
  public <T> T setSymmetryCountyName(String symmetryCountyName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isGeocoded() {
    return false;
  }

  @Override
  public String getSymmetryMunicipalityName() {
    return null;
  }

  @Override
  public <T> T setSymmetryMunicipalityName(String symmetryMunicipalityName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetrySchoolName() {
    return null;
  }

  @Override
  public <T> T setSymmetrySchoolName(String symmetrySchoolName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryTransitDistrictCode() {
    return null;
  }

  @Override
  public <T> T setSymmetryTransitDistrictCode(String symmetryTransitDistrictCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryTransitDistrictName() {
    return null;
  }

  @Override
  public <T> T setSymmetryTransitDistrictName(String symmetryTransitDistrictName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryPsdCode() {
    return null;
  }

  @Override
  public <T> T setSymmetryPsdCode(String symmetryPsdCode) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getSymmetryPsdRate() {
    return null;
  }

  @Override
  public <T> T setSymmetryPsdRate(String symmetryPsdRate) {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getLine1() {
    return null;
  }

  @Override
  public String getLine2() {
    return null;
  }

  @Override
  public String getCity() {
    return null;
  }

  @Override
  public State getState() {
    return null;
  }

  @Override
  public String getPostalCode() {
    return null;
  }

  // TODO: does this need to implement Physical Address?
  public ZoneId getTimeZone() {
    return null;
  }
}
