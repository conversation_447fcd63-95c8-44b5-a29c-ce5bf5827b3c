package com.everee.api.symmetry;

import java.util.Optional;
import lombok.Data;

@Data
public class SymmetryLocationCodeInfo {
  private String addressNormalized;
  private String cityCode;
  private String cityName;
  private String countyCode;
  private String countyName;

  private boolean geocoded;

  private String latitude;
  private String longitude;
  private String stateAbbreviation;
  private String stateCode;
  private String stateName;
  private String zipcode;
  private String municipalityCode;
  private String municipalityName;
  private String schoolCode;
  private String schoolName;
  private String transitDistrictCode;
  private String transitDistrictName;
  private String psdCode;
  private String psdRate;
  private String locationCode;
  private String locationCodeFull;

  /**
   * Get Symmetry location code in format stateCode-countyCode-cityCode ie. "49-035-1432728"
   *
   * @return Symmetry location code
   */
  public String getLocationCode() {
    return Optional.ofNullable(locationCode)
        .orElse(getStateCode() + "-" + getCountyCode() + "-" + getCityCode());
  }
}
