package com.everee.api.symmetry;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.EnumDTO;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum StateCalcOverride {
  RESIDENT_ONLY("stateCalcOverride.RESIDENT_ONLY.title"),
  NON_RESIDENT_ONLY("stateCalcOverride.NON_RESIDENT_ONLY.title"),
  ZERO_IN_BOTH("stateCalcOverride.ZERO_IN_BOTH.title"),
  NONE("stateCalcOverride.NONE.title"),
  ALL("stateCalcOverride.ALL.title"),
  ALL_WITH_NO_CREDIT("stateCalcOverride.ALL_WITH_NO_CREDIT.title"),
  DIFFERENCE("stateCalcOverride.DIFFERENCE.title"),
  DIFFERENCE_DEPRECATED("stateCalcOverride.DIFFERENCE_DEPRECATED.title"),
  FULL("stateCalcOverride.FULL.title"),
  REMOVE("stateCalcOverride.REMOVE.title");

  private final String localizationKey;

  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of(localizationKey);
  }

  public EnumDTO toEnumDTO(){
    return new EnumDTO(this.name(), this.getLocalizedTitle());
  }
}
