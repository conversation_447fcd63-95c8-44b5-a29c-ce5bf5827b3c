package com.everee.api.worklocation.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class WorkLocationInvalidNameException extends LocalizedRuntimeException {
  public WorkLocationInvalidNameException(String locationName) {
    super("workLocation.exception.WorkLocationInvalidNameException.message", locationName);
  }
}
