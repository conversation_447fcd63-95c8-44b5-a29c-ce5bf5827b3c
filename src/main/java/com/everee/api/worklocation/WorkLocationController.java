package com.everee.api.worklocation;

import com.everee.api.auth.annotation.AnyApproverAccess;
import com.everee.api.auth.annotation.FinancialManagerAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocationLookup;
import java.time.LocalDate;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/work-locations")
@RequiredArgsConstructor
public class WorkLocationController {
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final WorkLocationService workLocationService;
  private final WorkLocationLookupService lookupService;

  @GetMapping
  @AnyApproverAccess
  public Page<WorkLocation> listWorkLocations(
      @RequestParam(name = "id", required = false) Set<Long> ids,
      @RequestParam(name = "for-scheduling", required = false) Boolean isScheduleLocation,
      @RequestParam(name = "include-archived", required = false) Boolean includeArchived,
      Pageable pageable) {
    var lookup =
        new WorkLocationLookup()
            .setCompanyIds(Set.of(CompanyService.getAuthenticatedCompanyId()))
            .setIds(ids)
            .setScheduleLocation(isScheduleLocation)
            .setIncludeArchived(includeArchived);
    return lookupService.listAll(lookup, pageable);
  }

  @GetMapping("/{workLocationId}")
  @AnyApproverAccess
  public WorkLocation getWorkLocation(@PathVariable Long workLocationId) {
    var lookup =
        new WorkLocationLookup()
            .setCompanyIds(Set.of(CompanyService.getAuthenticatedCompanyId()))
            .setIds(Set.of(workLocationId))
            .setIncludeArchived(true);
    return lookupService.findOneOrThrow(lookup);
  }

  @PostMapping
  @FinancialManagerAccess
  public WorkLocation createWorkLocation(@RequestBody WorkLocationParams workLocationParams) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    return workLocationService.createWorkLocation(companyId, workLocationParams);
  }

  @DeleteMapping("/{workLocationId}")
  @FinancialManagerAccess
  public void deleteWorkLocation(
      @PathVariable(name = "workLocationId") Long workLocationId,
      @RequestParam(name = "force", required = false) Boolean forceDelete) {
    var lookup =
        new WorkLocationLookup()
            .setCompanyIds(Set.of(CompanyService.getAuthenticatedCompanyId()))
            .setIds(Set.of(workLocationId));
    workLocationService.deleteWorkLocation(lookup, forceDelete);
  }

  @PutMapping("/{workLocationId}")
  @FinancialManagerAccess
  public WorkLocation updateWorkLocation(
      @PathVariable(name = "workLocationId") Long workLocationId,
      @RequestBody WorkLocationParams workLocationParams) {
    var lookup =
        new WorkLocationLookup()
            .setCompanyIds(Set.of(CompanyService.getAuthenticatedCompanyId()))
            .setIds(Set.of(workLocationId));
    return workLocationService.updateWorkLocation(lookup, workLocationParams);
  }

  @GetMapping("/{workLocationId}/employees")
  @AnyApproverAccess
  public Page<DetailedEmployee> getLegalLocationEmployees(
      @PathVariable Long workLocationId,
      @RequestParam(name = "for-date", required = false) LocalDate forDate,
      Pageable pageable) {
    if (forDate == null) {
      var company = CompanyService.getAuthenticatedCompany();
      forDate = company.getNowDate();
    }

    var locationLookup =
        new WorkerLegalWorkLocationLookup()
            .setWorkLocationId(workLocationId)
            .setPhaseLookup(new PhaseLookup(Phase.ACTIVE, forDate));
    var employeeLookup =
        new EmployeeLookup()
            .withPhaseLookup(new PhaseLookup(Phase.ACTIVE, forDate))
            .withLegalWorkLocationLookup(locationLookup);

    return detailedEmployeeLookupService.listAll(employeeLookup, pageable);
  }
}
