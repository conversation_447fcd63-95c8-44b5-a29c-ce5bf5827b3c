package com.everee.api.worklocation;

import com.everee.api.tax.state.State;
import java.time.ZoneId;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class WorkLocationParams {
  @NotBlank private String name;

  @NotBlank private String postalCode;

  @NotNull private Double latitude;

  @NotNull private Double longitude;

  @NotBlank private String line1;

  private String line2;

  @NotBlank private String city;

  @NotNull
  @Enumerated(EnumType.STRING)
  private State state;

  @NotNull private ZoneId timeZone;

  private boolean scheduleLocation;
}
