package com.everee.api.aws;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.rekognition.AmazonRekognition;
import com.amazonaws.services.rekognition.AmazonRekognitionClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.sns.AmazonSNS;
import com.amazonaws.services.sns.AmazonSNSClientBuilder;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import lombok.RequiredArgsConstructor;
import org.apache.tika.utils.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class AWSConfiguration {

  private final AWSProperties awsProperties;

  @Bean
  public AmazonSQS amazonSQS(AWSCredentialsProvider awsCredentialsProvider) {
    if (!StringUtils.isBlank(awsProperties.getLocalstackEndpoint())) {
      AwsClientBuilder.EndpointConfiguration endpoint =
          new AwsClientBuilder.EndpointConfiguration(
              awsProperties.getLocalstackEndpoint(), awsProperties.getRegion());
      BasicAWSCredentials awsCreds = new BasicAWSCredentials("test", "test");
      return AmazonSQSClientBuilder.standard()
          .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
          .withEndpointConfiguration(endpoint)
          .build();
    } else {
      return AmazonSQSClientBuilder.standard()
          .withCredentials(awsCredentialsProvider)
          .withRegion(awsProperties.getRegion())
          .build();
    }
  }

  @Bean
  public AmazonSNS amazonSNS(AWSCredentialsProvider awsCredentialsProvider) {
    if (!StringUtils.isBlank(awsProperties.getLocalstackEndpoint())) {
      AwsClientBuilder.EndpointConfiguration endpoint =
          new AwsClientBuilder.EndpointConfiguration(
              awsProperties.getLocalstackEndpoint(), awsProperties.getRegion());
      BasicAWSCredentials awsCreds = new BasicAWSCredentials("test", "test");
      return AmazonSNSClientBuilder.standard()
          .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
          .withEndpointConfiguration(endpoint)
          .build();
    } else {
      return AmazonSNSClientBuilder.standard()
          .withCredentials(awsCredentialsProvider)
          .withRegion(awsProperties.getRegion())
          .build();
    }
  }

  @Bean
  public AmazonS3 amazonS3(AWSCredentialsProvider awsCredentialsProvider) {
    if (!StringUtils.isBlank(awsProperties.getLocalstackEndpoint())) {
      AwsClientBuilder.EndpointConfiguration endpoint =
          new AwsClientBuilder.EndpointConfiguration(
              awsProperties.getLocalstackEndpoint(), awsProperties.getRegion());
      BasicAWSCredentials awsCreds = new BasicAWSCredentials("test", "test");
      return AmazonS3ClientBuilder.standard()
          .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
          .withEndpointConfiguration(endpoint)
          .withPathStyleAccessEnabled(true)
          .build();
    } else {
      return AmazonS3ClientBuilder.standard()
          .withCredentials(awsCredentialsProvider)
          .withRegion(awsProperties.getRegion())
          .build();
    }
  }

  @Bean
  public AWSCredentialsProvider awsCredentialsProvider(AWSCredentials awsCredentials) {
    return new AWSStaticCredentialsProvider(awsCredentials);
  }

  @Bean
  public AWSCredentials awsCredentials() {
    return new BasicAWSCredentials(awsProperties.getAccessKey(), awsProperties.getSecretKey());
  }

  @Bean
  public AmazonRekognition amazonRekognitionClient(AWSCredentialsProvider awsCredentialsProvider) {
    if (!StringUtils.isBlank(awsProperties.getLocalstackEndpoint())) {
      AwsClientBuilder.EndpointConfiguration endpoint =
          new AwsClientBuilder.EndpointConfiguration(
              awsProperties.getLocalstackEndpoint(), awsProperties.getRegion());
      BasicAWSCredentials awsCreds = new BasicAWSCredentials("test", "test");
      return AmazonRekognitionClientBuilder.standard()
          .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
          .withEndpointConfiguration(endpoint)
          .build();
    } else {
      return AmazonRekognitionClientBuilder.standard()
          .withCredentials(awsCredentialsProvider)
          .withRegion(awsProperties.getRegion())
          .build();
    }
  }
}
