package com.everee.api.payeetype;

import com.everee.api.auth.annotation.PeopleApproverAccess;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.user.UserService;
import java.util.Optional;
import javax.validation.Valid;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/payee-type")
@RequiredArgsConstructor
public class PayeeTypeController {

  private final PayeeTypeService payeeTypeService;

  @GetMapping
  @PeopleApproverAccess
  public PayeeTypeDTO getPayeeTypeForUser(@NonNull @RequestParam Long userId) {
    return payeeTypeService
        .findByUserId(userId)
        .map(PayeeTypeMapper.MAPPER::payeeTypeToDto)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @PutMapping
  @PeopleApproverAccess
  public PayeeTypeDTO updatePayeeTypeForUser(
      @Valid @RequestBody PayeeTypeUpdateParams updateParams) {
    if (PayeeTypeName.BUSINESS.equals(updateParams.getTypeName())
        && StringUtils.isEmpty(updateParams.getBusinessName())) {
      throw new IllegalArgumentException("Business name is required");
    }

    return Optional.of(updateParams)
        .flatMap(payeeTypeService::createOrUpdate)
        .map(PayeeTypeMapper.MAPPER::payeeTypeToDto)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @GetMapping("/me")
  public PayeeTypeDTO getPayeeTypeForMe() {
    var userId = UserService.getAuthenticatedUserId();
    return this.getPayeeTypeForUser(userId);
  }

  @PutMapping("/me")
  public PayeeTypeDTO updatePayeeTypeForMe(@Valid @RequestBody PayeeTypeUpdateParams updateParams) {
    updateParams.setUserId(UserService.getAuthenticatedUserId());
    return this.updatePayeeTypeForUser(updateParams);
  }
}
