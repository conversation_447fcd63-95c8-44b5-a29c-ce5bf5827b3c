package com.everee.api.integration;

import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ConfiguredIntegrationService {
  private final ConfiguredIntegrationLookupService lookupService;
  private final ConfiguredIntegrationRepository repository;

  public ConfiguredIntegration create(
      Long companyId, SupportedIntegration integration, IntegrationHealth health) {
    var configuredIntegration = new ConfiguredIntegration();

    configuredIntegration.setCompanyId(companyId);
    configuredIntegration.setIntegration(integration);
    configuredIntegration.setHealth(health);

    return repository.save(configuredIntegration);
  }

  public ConfiguredIntegration updateHealth(
      ConfiguredIntegrationLookup lookup, IntegrationHealth health) {
    var integration = lookupService.findOneOrThrow(lookup);

    integration.setHealth(health);

    if (health == IntegrationHealth.DISABLED_BY_CUSTOMER) {
      integration.setDisabledAt(LocalDateTime.now());
    }

    return repository.save(integration);
  }
}
