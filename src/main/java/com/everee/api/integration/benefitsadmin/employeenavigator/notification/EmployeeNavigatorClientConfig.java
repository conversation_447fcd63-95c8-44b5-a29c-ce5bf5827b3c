package com.everee.api.integration.benefitsadmin.employeenavigator.notification;

import com.everee.api.integration.benefitsadmin.employeenavigator.EmployeeNavigatorProperties;
import com.everee.api.logging.FeignTimingLogger;
import feign.Logger;
import feign.Logger.Level;
import feign.RequestInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;

@RequiredArgsConstructor
public class EmployeeNavigatorClientConfig {
  private final EmployeeNavigatorProperties properties;

  @Bean
  Logger.Level feignLoggerLevel() {
    return Level.BASIC;
  }

  @Bean
  RequestInterceptor feignRequestInterceptor() {
    return template -> template.header("Authorization", "Basic " + properties.getAuthToken());
  }

  @Bean
  Logger employeeNavigatorClientLogger() {
    return new FeignTimingLogger("employee-navigator");
  }
}
