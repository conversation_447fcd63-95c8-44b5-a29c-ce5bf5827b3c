package com.everee.api.integration.benefitsadmin.employeenavigator.notification;

import com.everee.api.company.CompanyService;
import com.everee.api.company.ExternalBenefitsProvider;
import com.everee.api.employee.CoreEmployeeRepository;
import com.everee.api.employee.Employee;
import com.everee.api.integration.benefitsadmin.employeenavigator.EmployeeNavigatorProperties;
import com.everee.api.model.EmploymentType;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeNavigatorNotificationService {
  private final CompanyService companyService;
  private final CoreEmployeeRepository coreEmployeeRepository;
  private final EmployeeNavigatorClient employeeNavigatorClient;
  private final EmployeeNavigatorProperties properties;

  public void sendNotification(Long companyId, String workerId) {
    if (properties.isDryRunEnabled()) {
      return;
    }

    var company = companyService.getCompany(companyId);
    if (company.getExternalBenefitsProvider() != ExternalBenefitsProvider.EMPLOYEE_NAVIGATOR) {
      return;
    }

    var employmentType =
        coreEmployeeRepository
            .findByWorkerIdAndCompanyId(workerId, companyId)
            .map(Employee::getEmploymentType)
            .orElseThrow();

    if (employmentType != EmploymentType.EMPLOYEE) {
      return;
    }

    var request = new EmployeeNavigatorNotificationRequest(companyId.toString(), workerId);

    try {
      log.info(
          String.format(
              "Attempting Employee Navigator notification; companyId=%d, workerId=%s",
              companyId, workerId));

      employeeNavigatorClient.pushNotification(companyId, workerId, request);
    } catch (FeignException.BadRequest ex) {
      log.info(
          String.format(
              "Employee Navigator notification failed (HTTP 400); companyId=%d, workerId=%s",
              companyId, workerId),
          ex);
    } catch (FeignException ex) {
      log.warn(
          String.format(
              "Employee Navigator notification failed (HTTP %s); companyId=%d, workerId=%s",
              ex.status(), companyId, workerId),
          ex);
    }
  }
}
