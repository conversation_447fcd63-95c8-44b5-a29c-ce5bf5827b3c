package com.everee.api.integration.benefitsadmin.employeenavigator.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class EmployeeNavigatorCannotCreateEmployeeException extends RuntimeException {
  public EmployeeNavigatorCannotCreateEmployeeException() {
    super("Unable to create employee due to missing required property");
  }
}
