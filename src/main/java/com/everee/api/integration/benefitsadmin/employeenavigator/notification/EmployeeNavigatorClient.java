package com.everee.api.integration.benefitsadmin.employeenavigator.notification;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
    name = "employeenavigator",
    url = "${app.employeenavigator.base-url}",
    configuration = EmployeeNavigatorClientConfig.class)
public interface EmployeeNavigatorClient {
  @PostMapping("/notification/{companyId}/employee/{employeeId}")
  EmployeeNavigatorNotificationResponse pushNotification(
      @PathVariable("companyId") Long companyId,
      @PathVariable("employeeId") String integrationId,
      @RequestBody EmployeeNavigatorNotificationRequest request);
}
