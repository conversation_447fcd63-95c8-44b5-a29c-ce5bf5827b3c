package com.everee.api.integration.benefitsadmin.employeenavigator;

import static com.everee.api.phase.Phase.*;
import static com.everee.api.storage.StorageAccess.CUSTOMER_FACING;
import static java.math.RoundingMode.HALF_UP;

import com.everee.api.company.CompanyService;
import com.everee.api.companyemployeeimport.ExcelService.ExcelMutator;
import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.employee.contributiondeduction.ContributionDeductionApplicationBasis;
import com.everee.api.employee.contributiondeduction.ContributionDeductionFixedMethod;
import com.everee.api.employee.contributiondeduction.ContributionDeductionLookup;
import com.everee.api.employee.contributiondeduction.ContributionDeductionLookupService;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.model.EmploymentType;
import com.everee.api.money.Money;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.payperiod.PayPeriodPreferenceService;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.report.ReportExporter;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.util.ObjectUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeNavigatorExportService {
  private final CompanyLocalTimeService companyLocalTimeService;
  private final CompanyService companyService;
  private final ContributionDeductionLookupService contributionDeductionLookupService;
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final DetailedEmployeeRepository detailedEmployeeRepository;
  private final PayPeriodPreferenceService payPeriodPreferenceService;
  private final StorageService storageService;

  @Transactional(readOnly = true)
  public StoredFileLink generateReportForDemographics(Long companyId) throws IOException {
    var company = companyService.getCompany(companyId);
    var exporter = new ReportExporter();
    var mutator = exporter.getMutator();
    var row = exporter.getRow();
    var col = exporter.getCol();

    exporter.createSheet("Demographic Report");

    var headers =
        List.of(
            "CompanyId",
            "EmployeeId",
            "PayrollId",
            "SSN",
            "Payee Type",
            "Business Legal Name",
            "Doing Business As (DBA)",
            "FirstName",
            "MiddleName",
            "LastName",
            "DOB",
            "JobTitle",
            "Department",
            "HireDate",
            "TerminationDate",
            "PayEffectiveDate",
            "CompensationBasis",
            "AnnualBaseSalary",
            "BaseHourlyRate",
            "AnnualBenefitSalary",
            "Address1",
            "Address2",
            "City",
            "State",
            "ZIP",
            "Email",
            "Phone");
    exporter.writeHeaderCells(headers);

    try (var censusItemStream =
        detailedEmployeeRepository.getWorkerCensusItems(
            companyId, company.getNowDate(), Set.of(ACTIVE.toString(), UPCOMING.toString()))) {

      censusItemStream
          .filter(item -> item.getWorkerType().equals(EmploymentType.EMPLOYEE))
          .forEach(
              item -> {
                var annualBenefitSalary = Money.ZERO;

                if (ObjectUtils.allPresent(
                    item.getPayType(), item.getPayRate(), item.getTypicalWeeklyHours())) {
                  switch (item.getPayType()) {
                    case SALARY:
                      annualBenefitSalary = item.getPayRate();
                      break;

                    case HOURLY:
                      annualBenefitSalary =
                          item.getPayRate().times(item.getTypicalWeeklyHours()).times(52);
                      break;
                  }
                }

                var payeeType =
                    Optional.ofNullable(item.getPayeeType())
                        .orElse(PayeeTypeName.INDIVIDUAL.toString());

                setExcelCell(mutator, row, col, companyId);
                setExcelCell(mutator, row, col, "");
                setExcelCell(mutator, row, col, item.getWorkerId());
                setExcelCell(mutator, row, col, item.getTaxpayerIdentifier());
                setExcelCell(mutator, row, col, payeeType);
                setExcelCell(mutator, row, col, item.getBusinessName());
                setExcelCell(mutator, row, col, item.getDba());
                setExcelCell(mutator, row, col, item.getFirstName());
                setExcelCell(mutator, row, col, item.getMiddleName());
                setExcelCell(mutator, row, col, item.getLastName());
                setExcelCell(mutator, row, col, item.getDateOfBirth());
                setExcelCell(mutator, row, col, item.getTitle());
                setExcelCell(mutator, row, col, item.getApprovalGroupName());
                setExcelCell(mutator, row, col, item.getHireDate());
                setExcelCell(mutator, row, col, item.getTerminationDate());
                setExcelCell(mutator, row, col, item.getPayRateEffectiveDate());

                if (item.getPayType() == null) {
                  setExcelCell(mutator, row, col, "");
                  setExcelCell(mutator, row, col, "");
                  setExcelCell(mutator, row, col, "");
                } else {
                  switch (item.getPayType()) {
                    case SALARY:
                      setExcelCell(mutator, row, col, "Salary"); // Pay type
                      setExcelCell(mutator, row, col, item.getPayRate()); // Annual base salary
                      setExcelCell(mutator, row, col, ""); // Base hourly rate
                      break;

                    case HOURLY:
                      setExcelCell(mutator, row, col, "Hourly"); // Pay type
                      setExcelCell(mutator, row, col, ""); // Annual base salary
                      setExcelCell(mutator, row, col, item.getPayRate()); // Base hourly rate
                      break;
                  }
                }

                setExcelCell(mutator, row, col, annualBenefitSalary);
                setExcelCell(mutator, row, col, item.getAddressLine1());
                setExcelCell(mutator, row, col, item.getAddressLine2());
                setExcelCell(mutator, row, col, item.getAddressCity());
                setExcelCell(mutator, row, col, item.getAddressState());
                setExcelCell(mutator, row, col, item.getAddressPostalCode());

                setExcelCell(mutator, row, col, item.getEmail());
                setExcelCell(mutator, row, col, item.getPhoneNumber());

                exporter.advanceRow();
              });
    }

    var outputStream = new ByteArrayOutputStream();
    mutator.writeTo(outputStream);
    var bytes = outputStream.toByteArray();
    var path = List.of("demographic_report", Long.toString(companyId));
    var fileName = company.getDisplayName() + "-Demographic Report.xlsx";

    return storageService.storeForImmediateDownload(bytes, fileName, CUSTOMER_FACING, path);
  }

  public StoredFileLink generateReportForDeductions(Long companyId) throws IOException {
    var exporter = new ReportExporter();
    var mutator = exporter.getMutator();
    var row = exporter.getRow();
    var col = exporter.getCol();

    exporter.createSheet("Deduction Report");

    var company = companyService.getCompany(companyId);
    var forDate = companyLocalTimeService.companyLocalNowDate(companyId);

    var phaseLookup = new PhaseLookup(Set.of(ACTIVE, UPCOMING), forDate, forDate);
    var lookup =
        new ContributionDeductionLookup()
            .withCompanyIds(Set.of(companyId))
            .withPhaseLookup(phaseLookup);
    var deductions = contributionDeductionLookupService.listAll(lookup, Pageable.unpaged());

    var payPeriodPref =
        payPeriodPreferenceService.getCompanyActiveOrUpcomingPreference(companyId, forDate);
    var payPeriodType = payPeriodPref.getPayPeriodType();
    var payPeriodsPerYear = payPeriodType.getPeriodsPerYear();
    // Multiplier to go from monthly amount to pay period amount
    var payPeriodsPerMonth = BigDecimal.valueOf(payPeriodsPerYear / 12D);

    var headers =
        List.of(
            "CompanyId",
            "PayrollId",
            "SSN",
            "FirstName",
            "MiddleName",
            "LastName",
            "Benefit",
            "DeductionCode",
            "TypeOfDeduction",
            "EmployeePerPayAmount",
            "EmployerPerPayAmount",
            "EmployeeMonthlyAmount",
            "EmployerMonthlyAmount",
            "DeductionPercentage",
            "DeductionStartDate",
            "ModifiedDate");
    exporter.writeHeaderCells(headers);

    for (var deduction : deductions) {
      var employee = detailedEmployeeLookupService.getEmployee(deduction.getEmployeeId());

      if (employee.isEnded()) {
        continue;
      }

      if (!employee.getEmploymentType().equals(EmploymentType.EMPLOYEE)) {
        continue;
      }

      var deductionAmountType = deduction.getAmountType();

      var amountEE = deduction.getAmountEE().getAmount();
      var amountER = deduction.getAmountER().getAmount();

      var applicationBasis =
          Optional.ofNullable(deduction.getFixedMethod())
              .map(ContributionDeductionFixedMethod::getApplicationBasis)
              .orElse(ContributionDeductionApplicationBasis.PER_PAYMENT);

      var amountEEPerPayPeriod =
          applicationBasis == ContributionDeductionApplicationBasis.PER_PAYMENT
              ? amountEE
              : amountEE.divide(payPeriodsPerMonth, HALF_UP);

      var amountERPerPayPeriod =
          applicationBasis == ContributionDeductionApplicationBasis.PER_PAYMENT
              ? amountER
              : amountER.divide(payPeriodsPerMonth, HALF_UP);

      var percentEE = deduction.getPercentEE();

      setExcelCell(mutator, row, col, employee.getCompanyId()); // CompanyId
      setExcelCell(mutator, row, col, employee.getWorkerId()); // PayrollId
      setExcelCell(mutator, row, col, employee.getUser().getTaxpayerIdentifier()); // SSN
      setExcelCell(mutator, row, col, employee.getFirstName()); // First name
      setExcelCell(mutator, row, col, employee.getMiddleName()); // Middle name
      setExcelCell(mutator, row, col, employee.getLastName()); // Last name
      setExcelCell(mutator, row, col, deduction.getType().name()); // Benefit
      setExcelCell(mutator, row, col, deduction.getType().name()); // Deduction code

      switch (deductionAmountType) {
        case PERCENT:
          setExcelCell(mutator, row, col, "Percentage"); // Type of deduction
          setExcelCell(mutator, row, col, BigDecimal.ZERO); // ee per pay amount
          setExcelCell(mutator, row, col, BigDecimal.ZERO); // er per pay amount
          setExcelCell(mutator, row, col, BigDecimal.ZERO); // ee monthly amount
          setExcelCell(mutator, row, col, BigDecimal.ZERO); // er monthly amount
          setExcelCell(mutator, row, col, percentEE); // Deduction %
          break;

        case FIXED:
          setExcelCell(mutator, row, col, "Per Pay"); // Type of deduction
          setExcelCell(mutator, row, col, amountEEPerPayPeriod); // ee per pay amount
          setExcelCell(mutator, row, col, amountERPerPayPeriod); // er per pay amount
          setExcelCell(
              mutator,
              row,
              col,
              applicationBasis == ContributionDeductionApplicationBasis.PER_PAYMENT
                  ? ""
                  : amountEE); // ee monthly
          setExcelCell(
              mutator,
              row,
              col,
              applicationBasis == ContributionDeductionApplicationBasis.PER_PAYMENT
                  ? ""
                  : amountER); // er monthly
          setExcelCell(mutator, row, col, BigDecimal.ZERO); // Deduction %
          break;
      }

      setExcelCell(mutator, row, col, deduction.getStartDate()); // Deduction start date
      setExcelCell(mutator, row, col, deduction.getUpdatedAt()); // Modified date

      exporter.advanceRow();
    }

    var outputStream = new ByteArrayOutputStream();
    mutator.writeTo(outputStream);
    var bytes = outputStream.toByteArray();
    var path = List.of("deduction_report", Long.toString(companyId));
    var fileName = company.getDisplayName() + "-Deduction Report.xlsx";

    return storageService.storeForImmediateDownload(bytes, fileName, CUSTOMER_FACING, path);
  }

  private void setExcelCell(
      ExcelMutator mutator, AtomicInteger row, AtomicInteger col, TemporalAccessor value) {
    setExcelCell(
        mutator, row, col, value != null ? DateTimeFormatter.ISO_LOCAL_DATE.format(value) : "");
  }

  private void setExcelCell(
      ExcelMutator mutator, AtomicInteger row, AtomicInteger col, Object value) {
    mutator.cell(row.get(), col.getAndIncrement()).set(value != null ? value.toString() : "");
  }
}
