package com.everee.api.integration.benefitsadmin.employeenavigator;

import static com.everee.api.company.ExternalBenefitsProvider.EMPLOYEE_NAVIGATOR;
import static com.everee.api.integration.benefitsadmin.employeenavigator.EmployeeNavigatorDeductionAmountTimeframe.PER_PAY;
import static java.math.BigDecimal.ZERO;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.EmployeeRequest;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.contributiondeduction.ContributionDeductionFixedMethod;
import com.everee.api.employee.contributiondeduction.ContributionDeductionLimitService;
import com.everee.api.employee.contributiondeduction.benefitsadmin.ManagedDeductionCreateOrUpdateParams;
import com.everee.api.employee.contributiondeduction.benefitsadmin.ManagedDeductionExpireOrDeleteLookup;
import com.everee.api.employee.contributiondeduction.benefitsadmin.ManagedDeductionSyncService;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.employee.onboarding.WorkerOnboardingService;
import com.everee.api.employee.onboarding.params.EmployeeParamsForInvite;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.integration.benefitsadmin.employeenavigator.exception.EmployeeNavigatorCannotCreateEmployeeException;
import com.everee.api.integration.benefitsadmin.employeenavigator.exception.EmployeeNavigatorEmployeeNotFoundException;
import com.everee.api.integration.benefitsadmin.employeenavigator.exception.EmployeeNavigatorMissingPositionException;
import com.everee.api.integration.benefitsadmin.employeenavigator.params.EmployeeNavigatorDeductionParams;
import com.everee.api.integration.benefitsadmin.employeenavigator.params.EmployeeNavigatorDemographicParams;
import com.everee.api.integration.benefitsadmin.employeenavigator.responses.EmployeeNavigatorDeductionSavedResponse;
import com.everee.api.integration.benefitsadmin.employeenavigator.responses.EmployeeNavigatorDemographicSavedResponse;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.user.DetailedUser;
import com.everee.api.user.TinVerificationStateService;
import com.everee.api.user.UpdateFieldsForTinVerificationParams;
import com.everee.api.user.UserService;
import com.everee.api.user.address.HomeAddress;
import com.everee.api.user.address.HomeAddressService;
import com.everee.api.util.DateUtil;
import com.everee.api.util.ObjectUtils;
import com.everee.api.worker.Worker;
import com.everee.api.worker.createparams.HomeAddressParamsForCreate;
import com.everee.api.worker.createparams.WorkerLegalWorkLocationParamsForCreate;
import com.everee.api.worker.updateparams.HomeAddressParamsForUpdate;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import javax.validation.Valid;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Service
@Component
@RequiredArgsConstructor
public class EmployeeNavigatorService {
  private final CompanyService companyService;
  private final ContributionDeductionLimitService limitService;
  private final DetailedEmployeeLookupService employeeLookupService;
  private final EmployeeService employeeService;
  private final HomeAddressService homeAddressService;
  private final ManagedDeductionSyncService managedDeductionSyncService;
  private final TinVerificationStateService tinVerificationStateService;
  private final UserService userService;
  private final WorkerOnboardingService workerOnboardingService;

  @Transactional
  public EmployeeNavigatorDemographicParams getDemographic(@NonNull String workerId) {
    var lookup =
        new EmployeeLookup()
            .withWorkerIds(Set.of(workerId))
            .withEmploymentTypes(Set.of(EmploymentType.EMPLOYEE))
            .withSkipApplyAuthorization(true);
    var employee = employeeLookupService.findOneOrThrow(lookup);
    return buildEmployeeNavigatorDemographicParams(employee);
  }

  @Transactional
  public EmployeeNavigatorDemographicSavedResponse createEmployee(
      @NonNull EmployeeNavigatorDemographicParams demographic) {
    var company = companyService.getCompany(demographic.getCompanyId());
    var params = validateAndBuildWorkerInvitationParams(demographic);
    var worker = createWorker(params, company);

    var homeAddressParams = buildAddressCreateParams(demographic);
    if (homeAddressParams != null) {
      var user = userService.getUser(worker.getUserId());
      createHomeAddress(homeAddressParams, company, user);
    }

    var workerId = worker.getWorkerId();
    return new EmployeeNavigatorDemographicSavedResponse(
        workerId,
        List.of(String.format("Successfully created employee with payroll ID: %s", workerId)));
  }

  @Transactional
  public EmployeeNavigatorDemographicSavedResponse updateEmployee(
      @NonNull String workerId, @NonNull EmployeeNavigatorDemographicParams params) {
    var companyId = params.getCompanyId();
    var lookup =
        new EmployeeLookup()
            .withCompanyIds(Set.of(companyId))
            .withWorkerIds(Set.of(workerId))
            .withEmploymentTypes(Set.of(EmploymentType.EMPLOYEE))
            .withSkipApplyAuthorization(true);

    var employee = employeeLookupService.findOneOrThrow(lookup);

    var newStartDate = params.getHireDate();
    var existingStartDate = employee.getStartDate();
    var existingEndDate = employee.getEndDate();
    var existingEmployeeTerminationExists = existingEndDate != null;
    var isRehire = existingEmployeeTerminationExists && newStartDate.isAfter(existingStartDate);

    if (isRehire) {
      newStartDate = DateUtil.maxDate(newStartDate, existingEndDate.plusDays(1));
      employee = createRehiredEmployee(employee, newStartDate);
    }

    updateUserFieldsFromDemographic(employee, params);

    return new EmployeeNavigatorDemographicSavedResponse(
        workerId,
        List.of(String.format("Successfully updated employee with payroll ID: %s", workerId)));
  }

  @Transactional
  public EmployeeNavigatorDeductionSavedResponse applyDeductionChange(
      @NonNull EmployeeNavigatorDeductionParams navigatorDeductionParams) {
    var companyId = navigatorDeductionParams.getCompanyId();
    var employeeLookup =
        new EmployeeLookup()
            .withCompanyIds(Set.of(companyId))
            .withWorkerIds(Set.of(navigatorDeductionParams.getPayrollId()))
            .withSkipApplyAuthorization(true);

    var employee =
        employeeLookupService
            .findOne(employeeLookup)
            .orElseThrow(EmployeeNavigatorEmployeeNotFoundException::new);

    var employeeId = employee.getId();

    if (isDeductionCancelledAfterEnrollment(navigatorDeductionParams)) {
      // The "enrollment end date" will only be populated if EN is aware of a specific enrollment
      // end date prior to the normal end of the plan year.
      var deductionEndDate =
          Optional.ofNullable(navigatorDeductionParams.getEnrollmentEndDate())
              .orElse(navigatorDeductionParams.getPlanYearEnd());

      var deductionLookup =
          new ManagedDeductionExpireOrDeleteLookup(
              EMPLOYEE_NAVIGATOR,
              navigatorDeductionParams.getDeductionCode(),
              navigatorDeductionParams.getPlanYearBegin(),
              navigatorDeductionParams.getPlanYearEnd(),
              deductionEndDate);

      managedDeductionSyncService.removeManagedDeduction(
          navigatorDeductionParams.getCompanyId(), employeeLookup, deductionLookup);
    } else {
      var deductionParams = buildDeductionParams(companyId, employeeId, navigatorDeductionParams);
      managedDeductionSyncService.addOrUpdateManagedDeduction(
          employee.getCompanyId(), employeeLookup, deductionParams);
    }

    return new EmployeeNavigatorDeductionSavedResponse(
        List.of(
            String.format(
                "Successfully saved deduction with type: %s",
                navigatorDeductionParams.getDeductionCode())));
  }

  private EmployeeNavigatorDemographicParams buildEmployeeNavigatorDemographicParams(
      @NonNull DetailedEmployee employee) {
    var company = companyService.getCompany(employee.getCompanyId());
    var homeAddress = getCurrentHomeAddress(employee, company.getNowDate());
    var position = getCurrentPosition(employee, company.getNowDate());

    var demographic = new EmployeeNavigatorDemographicParams();

    // Employee fields
    demographic.setCompanyId(employee.getCompanyId());
    demographic.setEmployeeIdentifier(employee.getWorkerId());
    demographic.setHireDate(employee.getStartDate());
    if (employee.getEndDate() != null) {
      demographic.setSeparationDate(employee.getEndDate());
    }

    // User fields
    demographic.setFirstName(employee.getUser().getFirstName());
    demographic.setMiddleName(employee.getUser().getMiddleName());
    demographic.setLastName(employee.getUser().getLastName());
    demographic.setEmail(employee.getUser().getEmail());
    demographic.setPhone(employee.getUser().getPhoneNumber());
    demographic.setTaxpayerIdentifier(employee.getUser().getTaxpayerIdentifier());
    demographic.setDateOfBirth(
        Optional.ofNullable(employee.getUser()).map(DetailedUser::getDateOfBirth).orElse(null));

    // Position fields
    demographic.setTitle(position.getTitle());
    demographic.setPayRateEffectiveDate(position.getStartDate());
    demographic.setHoursPerWeek(position.getExpectedWeeklyHours());
    demographic.setCompensationBasis(position.getPayType());
    demographic.setAnnualBaseSalary(position.getAnnualSalary());
    demographic.setAnnualBenefitSalary(position.getAnnualSalary());

    if (position.getPayType() == PayType.SALARY) {
      demographic.setBaseHourlyRate(Money.ZERO);
    } else {
      demographic.setBaseHourlyRate(position.getHourlyRate());
    }

    // Address fields
    demographic.setLine1(homeAddress.getLine1());
    demographic.setLine2(homeAddress.getLine2());
    demographic.setCity(homeAddress.getCity());
    demographic.setState(homeAddress.getState());
    demographic.setZip(homeAddress.getPostalCode());

    return demographic;
  }

  private DetailedEmployee createRehiredEmployee(
      @NonNull DetailedEmployee originalEmployee, @NonNull LocalDate startDate) {
    var request = new EmployeeRequest();
    var companyId = originalEmployee.getCompanyId();

    request.setUserId(originalEmployee.getUserId());
    request.setCompanyId(companyId);
    request.setStartDate(startDate);
    request.setEmploymentType(EmploymentType.EMPLOYEE);
    request.setPayRate(originalEmployee.getPayRate());
    request.setPayType(originalEmployee.getPayType());

    return employeeService.createEmployee(request);
  }

  private void updateUserFieldsFromDemographic(
      @NonNull DetailedEmployee employee, @NonNull EmployeeNavigatorDemographicParams params) {
    var user = userService.getUser(employee.getUserId());
    var company = companyService.getCompany(employee.getCompanyId());

    var addressParams = buildAddressUpdateParams(params, company.getNowDate());
    if (addressParams != null) {
      homeAddressService.scheduleHomeAddressChange(
          user, addressParams, company.getWorkweekConfig().getTimezone());
    }

    var tinVerificationParams =
        new UpdateFieldsForTinVerificationParams(
            params.getFirstName(),
            params.getMiddleName(),
            params.getLastName(),
            params.getTaxpayerIdentifier(),
            null);
    user.setTinVerificationState(
        tinVerificationStateService.getStateForUpdateFieldsForTinVerification(
            tinVerificationParams, user));

    user.setFirstName(params.getFirstName());
    user.setMiddleName(params.getMiddleName());
    user.setLastName(params.getLastName());
    user.setDateOfBirth(params.getDateOfBirth());
  }

  // EN handles the "cancel after enrollment" scenario by sending a new deduction record with an
  // enrollment end date one day prior to its enrollment start date.
  private boolean isDeductionCancelledAfterEnrollment(
      @NonNull EmployeeNavigatorDeductionParams deduction) {
    if (deduction.getEnrollmentEndDate() == null) {
      return false;
    }

    return deduction.getEnrollmentEndDate().isBefore(deduction.getEnrollmentStartDate());
  }

  // Validation boundary
  private Worker createWorker(
      @Valid @NonNull EmployeeParamsForInvite params, @NonNull DetailedCompany company) {
    return workerOnboardingService.inviteWorker(params, company);
  }

  // Validation boundary
  private void createHomeAddress(
      @Valid @NonNull HomeAddressParamsForCreate params,
      @NonNull DetailedCompany company,
      @NonNull DetailedUser user) {
    homeAddressService.createWorkerInitialHomeAddress(
        user, params, company.getWorkweekConfig().getTimezone());
  }

  private EmployeePosition getCurrentPosition(
      @NonNull DetailedEmployee employee, @NonNull LocalDate nowDate) {
    return Optional.ofNullable(
            employee
                .getPositions()
                .toPhasedSchedule(employee.getPhaseBoundedDate(nowDate), null)
                .getScheduledChangeOrCurrent())
        .orElseThrow(EmployeeNavigatorMissingPositionException::new);
  }

  private HomeAddress getCurrentHomeAddress(
      @NonNull DetailedEmployee employee, @NonNull LocalDate nowDate) {
    var referenceDate = employee.getPhaseBoundedDate(nowDate);
    var addressPhaseSchedule =
        employee.getUser().getHomeAddresses().toPhasedSchedule(referenceDate, null);
    return Optional.ofNullable(addressPhaseSchedule.getScheduledChangeOrCurrent())
        .orElseGet(HomeAddress::new);
  }

  private EmployeeParamsForInvite validateAndBuildWorkerInvitationParams(
      @NonNull EmployeeNavigatorDemographicParams demographic) {
    if (Stream.of(
            demographic.getFirstName(),
            demographic.getLastName(),
            demographic.getEmail(),
            demographic.getPhone(),
            demographic.getHireDate(),
            demographic.getCompensationBasis(),
            demographic.getHoursPerWeek())
        .anyMatch(StringUtils::isEmpty)) {
      throw new EmployeeNavigatorCannotCreateEmployeeException();
    }

    var params = new EmployeeParamsForInvite();
    params.setFirstName(demographic.getFirstName());
    params.setMiddleName(demographic.getMiddleName());
    params.setLastName(demographic.getLastName());
    params.setEmail(demographic.getEmail());
    params.setPhoneNumber(demographic.getPhone());
    params.setStartDate(demographic.getHireDate());
    params.setPayType(demographic.getCompensationBasis());
    params.setPayRate(
        demographic.getCompensationBasis().equals(PayType.HOURLY)
            ? demographic.getBaseHourlyRate()
            : demographic.getAnnualBaseSalary());
    params.setExpectedWeeklyHours(demographic.getHoursPerWeek());
    params.setLegalWorkAddress(
        new WorkerLegalWorkLocationParamsForCreate().setUseHomeAddress(true));

    return params;
  }

  @Nullable
  private HomeAddressParamsForCreate buildAddressCreateParams(
      @NonNull EmployeeNavigatorDemographicParams demographic) {
    if (!ObjectUtils.allPresent(
        demographic.getLine1(),
        demographic.getCity(),
        demographic.getState(),
        demographic.getZip())) {
      return null;
    }

    return new HomeAddressParamsForCreate()
        .setLine1(demographic.getLine1())
        .setLine2(demographic.getLine2())
        .setCity(demographic.getCity())
        .setState(demographic.getState())
        .setPostalCode(demographic.getZip());
  }

  @Nullable
  private HomeAddressParamsForUpdate buildAddressUpdateParams(
      @NonNull EmployeeNavigatorDemographicParams demographic, @NonNull LocalDate startDate) {
    if (!ObjectUtils.allPresent(
        demographic.getLine1(),
        demographic.getCity(),
        demographic.getState(),
        demographic.getZip())) {
      return null;
    }

    return new HomeAddressParamsForUpdate()
        .setEffectiveDate(startDate)
        .setLine1(demographic.getLine1())
        .setLine2(demographic.getLine2())
        .setCity(demographic.getCity())
        .setState(demographic.getState())
        .setPostalCode(demographic.getZip());
  }

  private ManagedDeductionCreateOrUpdateParams buildDeductionParams(
      @NonNull Long companyId,
      @NonNull Long employeeId,
      @NonNull EmployeeNavigatorDeductionParams params) {
    var deductionType = params.getDeductionCode();
    var enrollmentStartDate = params.getEnrollmentStartDate();

    switch (params.getAmountType()) {
      case FIXED:
        var fixedMethod = getFixedMethod(companyId);
        var deductionAmount = params.getEmployeeMonthlyAmount();
        var contributionAmount = params.getEmployerMonthlyAmount();

        if (params.getDeductionAmountTimeframe() == PER_PAY) {
          // While Navigator may specify a deduction as "per pay", we have our own approach for
          // determining whether we should store the "target amount" of a deduction as an amount on
          // a whole-month basis (e.g. a distributed deduction) or an amount on a per-payment basis
          // (e.g. a per-payroll-payment deduction).
          switch (fixedMethod.getApplicationBasis()) {
            case PER_MONTH:
              deductionAmount =
                  toPerMonthAmount(params.getEmployeePerPayAmount(), params.getDeductionsPerYear());
              contributionAmount =
                  toPerMonthAmount(params.getEmployerPerPayAmount(), params.getDeductionsPerYear());
              break;

            case PER_PAYMENT:
              // In this case, we take the literal per-pay value specified by Navigator, without any
              // conversion with respect to the EE's current pay cycle. This is "strict mode", where
              // we absolutely require that pay cycle settings match between Everee and Navigator.
              // This is the customer's and broker's responsibility to manage if changes need to
              // happen (probably en masse) to pay cycle settings.
              deductionAmount = params.getEmployeePerPayAmount();
              contributionAmount = params.getEmployerPerPayAmount();
              break;

            default:
              throw new IllegalStateException(
                  "Unknown ContributionDeductionApplicationBasis: "
                      + fixedMethod.getApplicationBasis());
          }
        }

        return ManagedDeductionCreateOrUpdateParams.fixed(
            EMPLOYEE_NAVIGATOR,
            deductionType,
            fixedMethod,
            limitService.getLimitType(companyId, employeeId, deductionType, enrollmentStartDate),
            true,
            deductionAmount,
            contributionAmount,
            params.getPlanYearBegin(),
            params.getPlanYearEnd(),
            enrollmentStartDate,
            params.getEnrollmentEndDate(),
            params.getDeductionStartDate(),
            params.getPlanYearEnd());

      case PERCENT:
        return ManagedDeductionCreateOrUpdateParams.percent(
            EMPLOYEE_NAVIGATOR,
            deductionType,
            limitService.getLimitType(companyId, employeeId, deductionType, enrollmentStartDate),
            params.getDeductionPercentage(),
            ZERO,
            params.getPlanYearBegin(),
            params.getPlanYearEnd(),
            enrollmentStartDate,
            params.getEnrollmentEndDate(),
            params.getDeductionStartDate(),
            params.getPlanYearEnd());

      default:
        throw new IllegalStateException(
            "Unknown EmployeeContributionDeductionAmountType: " + params.getAmountType());
    }
  }

  private Money toPerMonthAmount(Money perPayAmount, int deductionsPerYear) {
    return perPayAmount.times(deductionsPerYear).div(12);
  }

  private ContributionDeductionFixedMethod getFixedMethod(@NonNull Long companyId) {
    var company = companyService.getCompany(companyId);
    return Optional.ofNullable(company.getExternalBenefitsProviderFixedMethod())
        .orElse(ContributionDeductionFixedMethod.DISTRIBUTED);
  }
}
