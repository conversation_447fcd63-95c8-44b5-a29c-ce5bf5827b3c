package com.everee.api.integration.benefitsadmin.employeenavigator;

import java.util.Arrays;
import java.util.NoSuchElementException;
import lombok.Getter;

public enum EmployeeNavigatorDeductionAmountTimeframe {
  PER_PAY("Per Pay"),
  MONTHLY("Monthly");

  @Getter private final String employeeNavigatorValue;

  EmployeeNavigatorDeductionAmountTimeframe(String employeeNavigatorValue) {
    this.employeeNavigatorValue = employeeNavigatorValue;
  }

  public static EmployeeNavigatorDeductionAmountTimeframe fromEmployeeNavigatorValue(String value) {
    return Arrays.stream(values())
        .filter(timeframe -> timeframe.getEmployeeNavigatorValue().equals(value))
        .findFirst()
        .orElseThrow(
            () ->
                new NoSuchElementException(
                    String.format("Unknown deduction amount timeframe: %s", value)));
  }
}
