package com.everee.api.integration.galileo;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.integration.galileo.dto.*;
import com.everee.api.integration.galileo.dto.overview.GetAccountOverviewResponse;
import com.everee.api.integration.galileo.model.DebitCreditIndicator;
import com.everee.api.integration.galileo.service.product.GalileoProductType;
import com.everee.api.model.EmploymentType;
import com.everee.api.user.DetailedUser;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.opencensus.internal.DefaultVisibilityForTesting;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.NoConnectionReuseStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
@RequiredArgsConstructor
public class GalileoClient {

  public static final String TIMESTAMP_PATTERN = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_PATTERN = "yyyy-MM-dd";
  public static final DateTimeFormatter DATE_TIME_FORMATTER =
      DateTimeFormatter.ofPattern(TIMESTAMP_PATTERN);
  public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);
  public static final ZoneId MST_ZONE_ID = ZoneId.of("America/Phoenix");
  private final GalileoProperties properties;
  private RestTemplate restTemplate;

  private static final String PING_URI = "/ping";
  private static final String CREATE_ACCOUNT_URI = "/createAccount";
  private static final String UPDATE_ACCOUNT_URI = "/updateAccount";
  private static final String GET_ACCOUNT_BY_ID_URI = "/getAccountById";
  private static final String GET_CARD_URI = "/getCard";
  private static final String CREATE_PAYMENT_URI = "/createPayment";
  private static final String CREATE_ACCOUNT_TRANSFER_URI = "/createAccountTransfer";
  private static final String GET_ACCOUNT_OVERVIEW_URI = "/getAccountOverview";
  private static final String SET_USER_DEFINED_ACCOUNT_FIELD_URI = "/setUserDefinedAccountField";
  private static final String SET_ACCOUNT_FEATURE_URI = "/setAccountFeature";
  private static final String GET_BALANCE_URI = "/getBalance";
  private static final String GET_TRANSACTION_HISTORY_URI = "/getTransHistory";
  private static final String GET_FEE_HISTORY_URI = "/getFeeHistory";

  private static final String EIN_ACCOUNT_FIELD_ID = "EIN";
  private static final String GET_CARD_PIN_CHANGE_KEY_URI = "/getCardPinChangeKey";
  private static final String COMMIT_CARD_PIN_CHANGE_URI = "/commitCardPinChange";
  private static final String ADD_ACH_ACCOUNT_URI = "/addAchAccount";
  private static final String MODIFY_ACH_ACCOUNT_URI = "/modifyAchAccount";
  private static final String REMOVE_ACH_ACCOUNT_URI = "/removeAchAccount";
  private static final String GET_ACH_ACCOUNTS_URI = "/getAchAccounts";
  private static final String CREATE_ACH_TRANSACTION_URI = "/createAchTransaction";
  private static final String CANCEL_ACH_TRANSACTION_URI = "/cancelAchTransaction";
  private static final String GET_ACH_TRANSACTION_HISTORY_URI = "/getAchTransHistory";
  private static final String SWITCH_PRODUCT_URI = "/switchProduct";
  private static final String GET_ACCESS_TOKEN_URI = "/getAccessToken";
  private static final String ACTIVATE_CARD_URI = "/activateCard";
  private static final String RUN_CIP_URI = "/runCip";

  private static final String ASSESS_FEE_URI = "/assessFee";
  private static final String REVERSE_FEE_URI = "/reverseFee";
  private static final String CREATE_ADJUSTMENT_URI = "/createAdjustment";
  private static final String SSN_ID_TYPE = "2";
  private static final String ITIN_ID_TYPE = "15";
  private static final String ACCOUNT_TRANSFER_TYPE = "EVT";
  private static final String USA_COUNTRY_CODE = "840";

  public static final int MAX_ACH_DESCRIPTION_LENGTH = 30;
  public static final int MAX_ADJUSTMENT_DESCRIPTION_LENGTH = 40;

  private static final Map<Boolean, String> SHIPPING_ADDRESS_PERMANENT =
      Map.of(Boolean.FALSE, "0", Boolean.TRUE, "1");

  @PostConstruct
  private void postConstruct() {
    restTemplate = createRestTemplate();
  }

  public CreateAccountResponse createAccount(DetailedEmployee employee)
      throws JsonProcessingException {
    String url = properties.getApiUrl() + CREATE_ACCOUNT_URI;
    var requestEntity = createAccountRequest(employee);
    var response = restTemplate.postForObject(url, requestEntity, CreateAccountResponse.class);
    log.info("Galileo create account response: {}", response);
    return response;
  }

  public UpdateAccountResponse updateAccount(String accountId, DetailedUser user)
      throws JsonProcessingException {
    String url = properties.getApiUrl() + UPDATE_ACCOUNT_URI;
    var requestEntity = createUpdateAccountRequest(user, accountId);
    var response = restTemplate.postForObject(url, requestEntity, UpdateAccountResponse.class);
    log.info("Galileo update account response: {}", response);
    return response;
  }

  public GetAccountByIdResponse getAccountById(String id, int idType) {
    String url = properties.getApiUrl() + GET_ACCOUNT_BY_ID_URI;
    var requestEntity = createGetAccountByIdRequest(id, idType);
    var response = restTemplate.postForObject(url, requestEntity, GetAccountByIdResponse.class);
    log.info("Galileo get account by id response: {}", response);
    return response;
  }

  public GetAccountByIdResponse getAccountBySSN(String ssn) {
    return getAccountById(ssn, 2);
  }

  public GetCardResponse getCard(String pmtRefNo) {
    String url = properties.getApiUrl() + GET_CARD_URI;
    var requestEntity = createGetCardRequest(pmtRefNo);
    var response = restTemplate.postForObject(url, requestEntity, GetCardResponse.class);
    log.info("Galileo get card response: {}", response);
    return response;
  }

  public SetUserDefinedAccountFieldResponse setUserDefinedAccountField(
      String accountId, String fieldId, String fieldValue) {
    String url = properties.getApiUrl() + SET_USER_DEFINED_ACCOUNT_FIELD_URI;
    var requestEntity = createSetUserDefinedAccountFieldRequest(accountId, fieldId, fieldValue);
    var response =
        restTemplate.postForObject(url, requestEntity, SetUserDefinedAccountFieldResponse.class);
    log.info("Galileo update account response: {}", response);
    return response;
  }

  public SetUserDefinedAccountFieldResponse setUserEIN(String accountId, String einValue) {
    return setUserDefinedAccountField(accountId, EIN_ACCOUNT_FIELD_ID, einValue);
  }

  public BaseGalileoResponse ping() {
    String url = properties.getApiUrl() + PING_URI;
    var requestEntity = createBaseRequest();

    return restTemplate.postForObject(url, requestEntity, BaseGalileoResponse.class);
  }

  public CreatePaymentResponse createPayment(String accountId, String amount) {
    String url = properties.getApiUrl() + CREATE_PAYMENT_URI;

    var requestEntity = createCreatePaymentRequest(accountId, amount);
    var response = restTemplate.postForObject(url, requestEntity, CreatePaymentResponse.class);
    log.info("Galileo createPayment response: {}", response);

    return response;
  }

  public CreateAccountTransferResponse createAccountTransfer(String pmtRefNo, String amount) {
    var url = properties.getApiUrl() + CREATE_ACCOUNT_TRANSFER_URI;

    var request = createAccountTransferRequest(pmtRefNo, amount);
    var response = restTemplate.postForObject(url, request, CreateAccountTransferResponse.class);
    log.info("Galileo createAccountTransfer response: {}", response);

    return response;
  }

  public GetAccountOverviewResponse getAccountOverview(
      String pmtRefNo, ZonedDateTime startDate, ZonedDateTime endDate) {
    String url = properties.getApiUrl() + GET_ACCOUNT_OVERVIEW_URI;

    var requestEntity = createGetAccountOverviewRequest(pmtRefNo, startDate, endDate);
    var response = restTemplate.postForObject(url, requestEntity, GetAccountOverviewResponse.class);
    log.info("Galileo getAccountOverview response: {}", response);

    return response;
  }

  public SetAccountFeatureResponse setAccountFeature(
      String accountNo, int featureType, String featureValue) {
    String url = properties.getApiUrl() + SET_ACCOUNT_FEATURE_URI;

    var requestEntity = createSetAccountFeatureRequest(accountNo, featureType, featureValue);
    var response = restTemplate.postForObject(url, requestEntity, SetAccountFeatureResponse.class);
    log.info("Galileo setAccountFeatureResponse: {}", response);

    return response;
  }

  public SetAccountFeatureResponse enableESignOptIn(String accountNo) {
    return setAccountFeature(accountNo, 19, "Y");
  }

  public GetBalanceResponse getBalance(String accountNo) {
    String url = properties.getApiUrl() + GET_BALANCE_URI;

    var requestEntity = createGetBalanceRequest(accountNo);
    var response = restTemplate.postForObject(url, requestEntity, GetBalanceResponse.class);
    log.info("Galileo getBalanceResponse: {}", response);

    return response;
  }

  public GetTransactionHistoryResponse getTransactionHistory(
      String accountNo,
      ZonedDateTime startDate,
      ZonedDateTime endDate,
      int recordCnt,
      int page,
      int includeRelated) {
    String url = properties.getApiUrl() + GET_TRANSACTION_HISTORY_URI;

    var requestEntity =
        createGetTransactionHistoryRequest(
            accountNo, startDate, endDate, recordCnt, page, includeRelated);

    var response =
        restTemplate.postForObject(url, requestEntity, GetTransactionHistoryResponse.class);
    log.info("Galileo getTransactionHistoryResponse: {}", response);

    return response;
  }

  public GetFeeHistoryResponse getFeeHistory(
      String accountNo, LocalDate startDate, LocalDate endDate, int recordCnt, int page) {
    String url = properties.getApiUrl() + GET_FEE_HISTORY_URI;

    var requestEntity = createGetFeeHistoryRequest(accountNo, startDate, endDate, recordCnt, page);

    var response = restTemplate.postForObject(url, requestEntity, GetFeeHistoryResponse.class);
    log.info("Galileo getFeeHistoryResponse: {}", response);

    return response;
  }

  public GetCardPinChangeKeyResponse getCardPinChangeKey(String accountNo) {
    String url = properties.getApiUrl() + GET_CARD_PIN_CHANGE_KEY_URI;

    var requestEntity = createGetCardPinChangeKeyRequest(accountNo);
    var response =
        restTemplate.postForObject(url, requestEntity, GetCardPinChangeKeyResponse.class);
    log.info("Galileo getCardPinChangeKeyResponse: {}", response);

    return response;
  }

  public BaseGalileoResponse commitCardPinChange(String accountNo) {
    String url = properties.getApiUrl() + COMMIT_CARD_PIN_CHANGE_URI;

    var requestEntity = createCommitCardPinChangeRequest(accountNo);
    var response = restTemplate.postForObject(url, requestEntity, BaseGalileoResponse.class);
    log.info("Galileo commitCardPinChangeResponse: {}", response);

    return response;
  }

  public AddAchAccountResponse addAchAccount(
      String pmtRefNo,
      String accountName,
      String accountType,
      String routingNumber,
      String accountNumber) {
    var url = properties.getApiUrl() + ADD_ACH_ACCOUNT_URI;

    var request =
        createAddAchAccountRequest(
            pmtRefNo, accountName, accountType, routingNumber, accountNumber);
    var response = restTemplate.postForObject(url, request, AddAchAccountResponse.class);
    log.info("Galileo addAchAccount response: {}", response);

    return response;
  }

  public ModifyAchAccountResponse modifyAchAccount(
      String pmtRefNo,
      String achAccountId,
      String accountName,
      String accountType,
      String routingNumber,
      String accountNumber) {
    var url = properties.getApiUrl() + MODIFY_ACH_ACCOUNT_URI;

    var request =
        createModifyAchAccountRequest(
            pmtRefNo, achAccountId, accountName, accountType, routingNumber, accountNumber);
    var response = restTemplate.postForObject(url, request, ModifyAchAccountResponse.class);
    log.info("Galileo modifyAchAccount response: {}", response);

    return response;
  }

  public RemoveAchAccountResponse removeAchAccount(String pmtRefNo, String achAccountId) {
    var url = properties.getApiUrl() + REMOVE_ACH_ACCOUNT_URI;

    var request = createRemoveAchAccountRequest(pmtRefNo, achAccountId);
    var response = restTemplate.postForObject(url, request, RemoveAchAccountResponse.class);
    log.info("Galileo removeAchAccount response: {}", response);

    return response;
  }

  public GetAchAccountsResponse getAchAccounts(String pmtRefNo) {
    var url = properties.getApiUrl() + GET_ACH_ACCOUNTS_URI;

    var request = createGetAchAccountsRequest(pmtRefNo);
    var response = restTemplate.postForObject(url, request, GetAchAccountsResponse.class);
    log.info("Galileo getAchAccounts response: {}", response);

    return response;
  }

  public CreateAchTransactionResponse createAchTransaction(
      String pmtRefNo,
      String achAccountId,
      String amount,
      String description,
      String debitCreditIndicator,
      String sameDay) {
    var url = properties.getApiUrl() + CREATE_ACH_TRANSACTION_URI;

    if (description.length() > MAX_ACH_DESCRIPTION_LENGTH) {
      description = description.substring(0, MAX_ACH_DESCRIPTION_LENGTH);
      log.warn(
          "Galileo createAchTransaction: description field was too long (max {} chars). New description: {}",
          MAX_ACH_DESCRIPTION_LENGTH,
          description);
    }

    var request =
        createAchTransactionRequest(
            pmtRefNo, achAccountId, amount, description, debitCreditIndicator, sameDay);
    var response = restTemplate.postForObject(url, request, CreateAchTransactionResponse.class);
    log.info("Galileo createAchTransaction response: {}", response);

    return response;
  }

  public CancelAchTransactionResponse cancelAchTransaction(
      String pmtRefNo, String achTransactionId) {
    var url = properties.getApiUrl() + CANCEL_ACH_TRANSACTION_URI;

    var request = createCancelAchTransactionRequest(pmtRefNo, achTransactionId);
    var response = restTemplate.postForObject(url, request, CancelAchTransactionResponse.class);
    log.info("Galileo cancelAchTransaction response: {}", response);

    return response;
  }

  public GetAchTransactionHistoryResponse getAchTransactionHistory(
      String pmtRefNo, LocalDate startDate, LocalDate endDate, int recordCount, int page) {
    var url = properties.getApiUrl() + GET_ACH_TRANSACTION_HISTORY_URI;

    var request =
        createGetAchTransactionHistoryRequest(pmtRefNo, startDate, endDate, recordCount, page);
    var response = restTemplate.postForObject(url, request, GetAchTransactionHistoryResponse.class);
    log.info("Galileo getAchTransactionHistory response: {}", response);

    return response;
  }

  public SwitchProductResponse switchProduct(
      String pmtRefNo,
      String newProductId,
      boolean doReissue,
      boolean newPan,
      boolean newExpiryDate,
      boolean emboss) {
    var url = properties.getApiUrl() + SWITCH_PRODUCT_URI;

    var request =
        createSwitchProductRequest(
            pmtRefNo, newProductId, doReissue, newPan, newExpiryDate, emboss);
    var response = restTemplate.postForObject(url, request, SwitchProductResponse.class);
    log.info("Galileo switchProduct response: {}", response);

    return response;
  }

  public GetAccessTokenResponse getAccessToken(String pmtRefNo, int type) {
    var url = properties.getApiUrl() + GET_ACCESS_TOKEN_URI;

    var request = createGetAccessTokenRequest(pmtRefNo, type);
    var response = restTemplate.postForObject(url, request, GetAccessTokenResponse.class);
    log.info("Galileo getAccessToken response: {}", response);

    return response;
  }

  public ActivateCardResponse activateCard(
      String pmtRefNo, String cardExpiryDate, String cardSecurityCode) {
    var url = properties.getApiUrl() + ACTIVATE_CARD_URI;

    var request = createActivateCardRequest(pmtRefNo, cardExpiryDate, cardSecurityCode);
    var response = restTemplate.postForObject(url, request, ActivateCardResponse.class);
    log.info("Galileo activateCard response: {}", response);

    return response;
  }

  public RunCipResponse runCip(String pmtRefNo) {
    var url = properties.getApiUrl() + RUN_CIP_URI;

    var request = createRunCipRequest(pmtRefNo);
    var response = restTemplate.postForObject(url, request, RunCipResponse.class);
    log.info("Galileo runCip response: {}", response);

    return response;
  }

  public AssessFeeResponse assessFee(String paymentReferenceNumber, String amount, String code) {
    var url = properties.getApiUrl() + ASSESS_FEE_URI;

    var request = createAssessFeeRequest(paymentReferenceNumber, amount, code);
    var response = restTemplate.postForObject(url, request, AssessFeeResponse.class);
    log.info("Galileo assessFee response: {}", response);

    return response;
  }

  public ReverseFeeResponse reverseFee(String paymentReferenceNumber, String feeId) {
    var url = properties.getApiUrl() + REVERSE_FEE_URI;

    var request = createReverseFeeRequest(paymentReferenceNumber, feeId);
    var response = restTemplate.postForObject(url, request, ReverseFeeResponse.class);
    log.info("Galileo reverseFee response: {}", response);

    return response;
  }

  public CreateAdjustmentResponse createAdjustment(
      String paymentReferenceNumber,
      String amount,
      String type,
      DebitCreditIndicator debitCreditIndicator,
      String description) {
    var url = properties.getApiUrl() + CREATE_ADJUSTMENT_URI;

    if (description.length() > MAX_ADJUSTMENT_DESCRIPTION_LENGTH) {
      description = description.substring(0, MAX_ADJUSTMENT_DESCRIPTION_LENGTH);
      log.warn(
          "Galileo createAdjustment: description field was too long (max {} chars). New description: {}",
          MAX_ADJUSTMENT_DESCRIPTION_LENGTH,
          description);
    }

    var request =
        createAdjustmentRequest(
            paymentReferenceNumber, amount, type, debitCreditIndicator, description);
    var response = restTemplate.postForObject(url, request, CreateAdjustmentResponse.class);
    log.info("Galileo createAdjustment response: {}", response);

    return response;
  }

  public SetAccountFeatureResponse setCustomArtCode(String accountId, String customArtCode) {
    return setAccountFeature(accountId, 16, customArtCode);
  }

  private HttpEntity<MultiValueMap<String, String>> createBaseRequest() {
    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    headers.add("response-content-type", "json");

    var requestBody = new LinkedMultiValueMap<String, String>();
    requestBody.add("apiLogin", properties.getApiLogin());
    requestBody.add("apiTransKey", properties.getApiTransKey());
    requestBody.add("providerId", properties.getProviderId());
    requestBody.add("transactionId", createTransactionId());

    return new HttpEntity<>(requestBody, headers);
  }

  private HttpEntity<MultiValueMap<String, String>> createBaseAdjustmentRequest() {
    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    headers.add("response-content-type", "json");

    var requestBody = new LinkedMultiValueMap<String, String>();
    requestBody.add("apiLogin", properties.getApiLogin());
    requestBody.add("apiTransKey", properties.getApiTransKey());
    requestBody.add("providerId", properties.getProviderId());
    requestBody.add("transactionId", createNumericTransactionId().toString());

    return new HttpEntity<>(requestBody, headers);
  }

  private HttpEntity<MultiValueMap<String, String>> createAccountRequest(
      DetailedEmployee employee) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    var user = employee.getUser();
    request.getBody().add("id", user.getTaxpayerIdentifier());
    request.getBody().add("idType", getIdType(employee));
    request.getBody().add("prodId", GalileoProductType.VIRTUAL.getProductId());

    request.getBody().add("email", user.getEmail());
    request.getBody().add("firstName", sanitizeName(user.getFirstName()));
    request.getBody().add("middleName", sanitizeName(user.getMiddleName()));
    request.getBody().add("lastName", sanitizeName(user.getLastName()));
    request.getBody().add("primaryPhone", user.getPhoneNumber());
    if (user.getDateOfBirth() != null) {
      request.getBody().add("dateOfBirth", toDateString(user.getDateOfBirth()));
    }

    user.findActiveHomeAddress(LocalDate.now())
        .ifPresent(
            homeAddress -> {
              request.getBody().add("address1", homeAddress.getLine1());
              request.getBody().add("address2", homeAddress.getLine2());
              request.getBody().add("city", homeAddress.getCity());
              request.getBody().add("state", homeAddress.getState().toString());
              request.getBody().add("postalCode", homeAddress.getPostalCode());
            });

    if (user.getShippingAddress() != null) {
      request.getBody().add("shipToAddress1", user.getShippingAddress().getLine1());
      request.getBody().add("shipToAddress2", user.getShippingAddress().getLine2());
      request.getBody().add("shipToCity", user.getShippingAddress().getCity());
      request.getBody().add("shipToState", user.getShippingAddress().getState().toString());
      request.getBody().add("shipToPostalCode", user.getShippingAddress().getPostalCode());
      request.getBody().add("shipToCountyCode", USA_COUNTRY_CODE);
      request.getBody().add("shipToAddressPermanent", SHIPPING_ADDRESS_PERMANENT.get(Boolean.TRUE));
    }

    request.getBody().add("cipStatus", "2");

    return request;
  }

  private String getIdType(DetailedEmployee employee) {
    // https://docs.galileo-ft.com/pro/reference/api-reference-customer-id-types
    var id = employee.getUser().getTaxpayerIdentifier();
    if (id != null && id.length() == 9 && id.matches("\\d+")) {
      if (id.startsWith("9") && employee.getEmploymentType() == EmploymentType.CONTRACTOR) {
        return ITIN_ID_TYPE;
      } else if (!id.startsWith("000")
          && !id.startsWith("666")
          && !id.startsWith("00", 3)
          && !id.endsWith("0000")) {
        return SSN_ID_TYPE;
      }
    }
    throw new IllegalArgumentException("Invalid taxpayer identifier: " + id);
  }

  private HttpEntity<MultiValueMap<String, String>> createUpdateAccountRequest(
      DetailedUser user, String accountId) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountId);

    request.getBody().add("email", user.getEmail());
    request.getBody().add("firstName", sanitizeName(user.getFirstName()));
    request.getBody().add("middleName", sanitizeName(user.getMiddleName()));
    request.getBody().add("lastName", sanitizeName(user.getLastName()));
    request.getBody().add("primaryPhone", user.getPhoneNumber());

    if (user.getDateOfBirth() != null) {
      request.getBody().add("dateOfBirth", toDateString(user.getDateOfBirth()));
    }

    user.findActiveHomeAddress(LocalDate.now())
        .ifPresent(
            homeAddress -> {
              request.getBody().add("address1", homeAddress.getLine1());
              request.getBody().add("address2", homeAddress.getLine2());
              request.getBody().add("city", homeAddress.getCity());
              request.getBody().add("state", homeAddress.getState().toString());
              request.getBody().add("postalCode", homeAddress.getPostalCode());
            });

    if (user.getShippingAddress() != null) {
      request.getBody().add("shipToAddress1", user.getShippingAddress().getLine1());
      request.getBody().add("shipToAddress2", user.getShippingAddress().getLine2());
      request.getBody().add("shipToCity", user.getShippingAddress().getCity());
      request.getBody().add("shipToState", user.getShippingAddress().getState().toString());
      request.getBody().add("shipToPostalCode", user.getShippingAddress().getPostalCode());
      request.getBody().add("shipToCountyCode", USA_COUNTRY_CODE);
      request.getBody().add("shipToAddressPermanent", SHIPPING_ADDRESS_PERMANENT.get(Boolean.TRUE));
    }

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetAccountByIdRequest(
      String id, int idType) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("id", id);
    request.getBody().add("idType", String.valueOf(idType));

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetCardRequest(String pmtRefNo) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createSetUserDefinedAccountFieldRequest(
      String accountId, String fieldId, String fieldValue) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountId);
    request.getBody().add("fieldId", fieldId);
    request.getBody().add("fieldValue", fieldValue);

    return request;
  }

  /* Galileo only allows letters, spaces and hyphens in names */
  private String sanitizeName(String name) {
    return name == null ? null : name.replaceAll("[^A-Za-z-' ]", "");
  }

  private HttpEntity<MultiValueMap<String, String>> createCreatePaymentRequest(
      String accountId, String amount) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountId);
    request.getBody().add("amount", amount);
    request.getBody().add("type", "RL");

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createAccountTransferRequest(
      String pmtRefNo, String amount) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", properties.getMasterFundingAccountId());
    request.getBody().add("transferToAccountNo", pmtRefNo);
    request.getBody().add("amount", amount);
    request.getBody().add("type", ACCOUNT_TRANSFER_TYPE);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetAccountOverviewRequest(
      String pmtRefNo, ZonedDateTime startDate, ZonedDateTime endDate) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);

    request.getBody().add("startDate", toMstTimestampString(startDate));
    request.getBody().add("endDate", toMstTimestampString(endDate));

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createSetAccountFeatureRequest(
      String accountNo, int featureType, String featureValue) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("featureType", String.valueOf(featureType));
    request.getBody().add("featureValue", featureValue);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetCardPinChangeKeyRequest(
      String accountNo) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createCommitCardPinChangeRequest(
      String accountNo) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetBalanceRequest(String accountNo) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetTransactionHistoryRequest(
      String accountNo,
      ZonedDateTime startDate,
      ZonedDateTime endDate,
      int recordCnt,
      int page,
      int includeRelated) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("startDate", toMstTimestampString(startDate));
    request.getBody().add("endDate", toMstTimestampString(endDate));
    request.getBody().add("recordCnt", String.valueOf(recordCnt));
    request.getBody().add("page", String.valueOf(page));
    request.getBody().add("includeRelated", String.valueOf(includeRelated));

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetFeeHistoryRequest(
      String accountNo, LocalDate startDate, LocalDate endDate, int recordCnt, int page) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("startDate", toDateString(startDate));
    request.getBody().add("endDate", toDateString(endDate));
    request.getBody().add("recordCnt", String.valueOf(recordCnt));
    request.getBody().add("page", String.valueOf(page));

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createAddAchAccountRequest(
      String pmtRefNo,
      String accountName,
      String accountType,
      String routingNumber,
      String accountNumber) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);
    request.getBody().add("name", accountName);
    request.getBody().add("type", accountType);
    request.getBody().add("achAccountNo", accountNumber);
    request.getBody().add("achRoutingNo", routingNumber);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createModifyAchAccountRequest(
      String pmtRefNo,
      String achAccountId,
      String accountName,
      String accountType,
      String routingNumber,
      String accountNumber) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);
    request.getBody().add("achAccountId", achAccountId);
    request.getBody().add("name", accountName);
    request.getBody().add("type", accountType);
    request.getBody().add("achAccountNo", accountNumber);
    request.getBody().add("achRoutingNo", routingNumber);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createRemoveAchAccountRequest(
      String pmtRefNo, String achAccountId) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);
    request.getBody().add("achAccountId", achAccountId);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetAchAccountsRequest(String pmtRefNo) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createAchTransactionRequest(
      String pmtRefNo,
      String achAccountId,
      String amount,
      String description,
      String debitCreditIndicator,
      String sameDay) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);
    request.getBody().add("achAccountId", achAccountId);
    request.getBody().add("amount", amount);
    request.getBody().add("description", description);
    request.getBody().add("debitCreditIndicator", debitCreditIndicator);
    request.getBody().add("sameDay", sameDay);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createCancelAchTransactionRequest(
      String pmtRefNo, String achTransactionId) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", pmtRefNo);
    request.getBody().add("achTransactionId", achTransactionId);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createGetAchTransactionHistoryRequest(
      String accountNo, LocalDate startDate, LocalDate endDate, int recordCount, int page) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("startDate", toDateString(startDate));
    request.getBody().add("endDate", toDateString(endDate));
    request.getBody().add("recordCnt", String.valueOf(recordCount));
    request.getBody().add("page", String.valueOf(page));

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createSwitchProductRequest(
      String accountNo,
      String newProductId,
      boolean doReissue,
      boolean newPan,
      boolean newExpiryDate,
      boolean emboss) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("prodId", newProductId);
    request.getBody().add("doReissue", booleanToStringParam(doReissue));
    request.getBody().add("newPan", booleanToStringParam(newPan));
    request.getBody().add("newExpiryDate", booleanToStringParam(newExpiryDate));
    request.getBody().add("emboss", booleanToStringParam(emboss));

    return request;
  }

  private String booleanToStringParam(boolean value) {
    return value ? "Y" : "N";
  }

  private HttpEntity<MultiValueMap<String, String>> createGetAccessTokenRequest(
      String accountNo, int type) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("type", Integer.toString(type));

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createActivateCardRequest(
      String accountNo, String cardExpiryDate, String cardSecurityCode) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("cardExpiryDate", cardExpiryDate);
    request.getBody().add("cardSecurityCode", cardSecurityCode);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createRunCipRequest(String accountNo) {
    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createAssessFeeRequest(
      String accountNo, String amount, String code) {

    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("type", code);
    request.getBody().add("amount", amount);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createReverseFeeRequest(
      String accountNo, String feeId) {

    var request = createBaseRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("feeId", feeId);

    return request;
  }

  private HttpEntity<MultiValueMap<String, String>> createAdjustmentRequest(
      String accountNo,
      String amount,
      String type,
      DebitCreditIndicator debitCreditIndicator,
      String description) {

    var request = createBaseAdjustmentRequest();
    Assert.notNull(request.getBody(), "Request body cannot be null");

    request.getBody().add("accountNo", accountNo);
    request.getBody().add("amount", amount);
    request.getBody().add("type", type);
    request.getBody().add("debitCreditIndicator", debitCreditIndicator.getValue());
    request.getBody().add("description", description);

    return request;
  }

  private String toMstTimestampString(ZonedDateTime startDate) {
    return ZonedDateTime.ofInstant(startDate.toInstant(), MST_ZONE_ID)
        .toLocalDateTime()
        .format(DATE_TIME_FORMATTER);
  }

  private String toDateString(LocalDate date) {
    return date.format(DATE_FORMATTER);
  }

  private String createTransactionId() {
    return UUID.randomUUID().toString();
  }

  private Long createNumericTransactionId() {
    return ThreadLocalRandom.current().nextLong(Long.MAX_VALUE - 1);
  }

  private RestTemplate createRestTemplate() {
    final Registry<ConnectionSocketFactory> schemeRegistry =
        RegistryBuilder.<ConnectionSocketFactory>create()
            .register("http", PlainConnectionSocketFactory.getSocketFactory())
            .register("https", SSLConnectionSocketFactory.getSocketFactory())
            .build();

    final PoolingHttpClientConnectionManager connManager =
        new PoolingHttpClientConnectionManager(schemeRegistry);
    final CloseableHttpClient closeableHttpClient =
        HttpClients.custom()
            .setConnectionReuseStrategy(NoConnectionReuseStrategy.INSTANCE)
            .setConnectionManager(connManager)
            .build();

    return new RestTemplateBuilder()
        .requestFactory(() -> new HttpComponentsClientHttpRequestFactory(closeableHttpClient))
        .build();
  }

  @DefaultVisibilityForTesting
  void setRestTemplate(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
  }
}
