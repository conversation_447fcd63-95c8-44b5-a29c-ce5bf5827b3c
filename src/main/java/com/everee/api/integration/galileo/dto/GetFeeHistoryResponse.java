package com.everee.api.integration.galileo.dto;

import com.everee.api.integration.galileo.dto.deserializer.MoneyDeserializer;
import com.everee.api.integration.galileo.dto.deserializer.MountainStandardTimestampDeserializer;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class GetFeeHistoryResponse extends BaseGalileoResponse {

  @JsonAlias("response_data")
  private ResponseData responseData;

  @Data
  @EqualsAndHashCode()
  @JsonIgnoreProperties(ignoreUnknown = true)
  @Accessors(chain = true)
  public static class ResponseData {

    @JsonAlias("fee_count")
    private int feeCount;

    @JsonAlias("start_date")
    @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
    private ZonedDateTime startDate;

    @JsonAlias("end_date")
    @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
    private ZonedDateTime endDate;

    @JsonAlias("number_of_pages")
    private int numberOfPages;

    private int page;

    @JsonAlias("total_record_count")
    private int totalRecordCount;

    private List<Fee> fees;

    @Data
    @EqualsAndHashCode()
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Accessors(chain = true)
    public static class Fee {
      @JsonAlias("pmt_ref_no")
      private String pmtRefNo;

      @JsonAlias("fee_id")
      private String feeId;

      @JsonAlias("fee_date")
      @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
      private ZonedDateTime feeDate;

      @JsonAlias("amt")
      @JsonDeserialize(using = MoneyDeserializer.class)
      private Money amount;

      private String status;

      @JsonAlias("status_description")
      private String statusDescription;

      private String type;

      @JsonAlias("type_description")
      private String typeDescription;

      @JsonAlias("fee_event_id")
      private String feeEventId;
    }
  }
}
