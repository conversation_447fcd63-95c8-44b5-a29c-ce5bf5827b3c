package com.everee.api.integration.galileo.dto.overview;

import com.everee.api.integration.galileo.dto.deserializer.MoneyDeserializer;
import com.everee.api.integration.galileo.dto.deserializer.MountainStandardTimestampDeserializer;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.ZonedDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode()
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class Authorization {

  // Description provided by Merchant
  private String details;

  // Formatted description
  @JsonAlias("details_formatted")
  private String detailsFormatted;

  @JsonDeserialize(using = MoneyDeserializer.class)
  private Money amount;

  @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
  private ZonedDateTime timestamp;

  // TODO normalize into enum
  // https://docs.galileo-ft.com/pro/reference/api-reference-transaction-types
  // Transaction type
  private String type;

  @JsonAlias("network_code")
  private String networkCode;

  @JsonAlias("auth_id")
  private String authorizationCode;
}
