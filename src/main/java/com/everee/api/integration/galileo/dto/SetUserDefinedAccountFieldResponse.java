package com.everee.api.integration.galileo.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SetUserDefinedAccountFieldResponse extends BaseGalileoResponse {
  public static final String STATUS_VALUE_UPDATED = "518-01";
}
