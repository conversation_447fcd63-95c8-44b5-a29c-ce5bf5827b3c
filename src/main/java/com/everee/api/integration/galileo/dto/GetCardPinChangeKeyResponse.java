package com.everee.api.integration.galileo.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class GetCardPinChangeKeyResponse extends BaseGalileoResponse {

  @JsonAlias("response_data")
  private ResponseData responseData;

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ResponseData {
    String token;
  }
}
