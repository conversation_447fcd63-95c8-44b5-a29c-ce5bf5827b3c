package com.everee.api.integration.galileo.dto.deserializer;

import com.everee.api.integration.galileo.GalileoClient;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

public class MountainStandardTimestampDeserializer extends StdDeserializer<ZonedDateTime> {

  public MountainStandardTimestampDeserializer() {
    super(ZonedDateTime.class);
  }

  @Override
  public ZonedDateTime deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException, JsonProcessingException {
    String stringTimestamp = jsonParser.getValueAsString();

    var localDateTime = LocalDateTime.parse(stringTimestamp, GalileoClient.DATE_TIME_FORMATTER);

    return ZonedDateTime.of(localDateTime, GalileoClient.MST_ZONE_ID);
  }
}
