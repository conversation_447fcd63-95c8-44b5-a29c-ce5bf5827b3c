package com.everee.api.integration.galileo.service.product;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum GalileoProductType {
  VIRTUAL("VIRTUAL", "2609"),
  DIGITAL_FIRST("DIGITAL_FIRST", "1968"),
  DIGITAL_FIRST_CO_BRANDED("DIGITAL_FIRST_CO_BRANDED", "3080"),
  DIGITAL_FIRST_FULLY_BRANDED("DIGITAL_FIRST_FULLY_BRANDED", "3081");

  private final String name;
  private final String productId;

  private static final Map<String, GalileoProductType> productTypeMap = new HashMap<>();

  static {
    Arrays.stream(GalileoProductType.values())
        .forEach(
            type -> {
              productTypeMap.put(type.getProductId(), type);
            });
  }

  public static Optional<GalileoProductType> getByProductId(String productId) {
    return Optional.ofNullable(productTypeMap.get(productId));
  }
}
