package com.everee.api.integration.galileo.dto;

import com.everee.api.integration.galileo.dto.deserializer.MoneyDeserializer;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class CreateAdjustmentResponse extends BaseGalileoResponse {

  @JsonAlias("response_data")
  private ResponseData responseData;

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ResponseData {

    @JsonAlias("old_balance")
    @JsonDeserialize(using = MoneyDeserializer.class)
    private Money oldBalance;

    @JsonAlias("new_balanace")
    @JsonDeserialize(using = MoneyDeserializer.class)
    private Money newBalance;

    @JsonAlias("adjustment_trans_id")
    private Long adjustmentTransId;

    // transactionId is a positive 64-bit integer that is used as the argument
    // to the Reverse Adjustment endpoint
    // see https://docs.galileo-ft.com/pro/reference/post_createadjustment
    // and https://docs.galileo-ft.com/pro/reference/post_reverseadjustment
    @JsonAlias("transaction_id")
    private String transactionId;
  }
}
