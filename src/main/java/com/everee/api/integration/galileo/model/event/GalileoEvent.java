package com.everee.api.integration.galileo.model.event;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({
  @JsonSubTypes.Type(value = GalileoAuthEvent.class, name = "auth"),
  @JsonSubTypes.Type(value = GalileoSettlementEvent.class, name = "setl")
})
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public abstract class GalileoEvent {
  private String msgEventId; // TODO looks like number in documentation
  private String msgId; // TODO replace with enum
  private String pmtRefNo; // TODO confirm if this is a number
}
