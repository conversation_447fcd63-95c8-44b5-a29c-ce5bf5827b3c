package com.everee.api.integration.galileo.model;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum GalileoTransactionCodeType {
  // Card Maintenance Fees
  FE0201("Activation Fee"),
  FE0031("Additional Card Fee"),
  FE0203("Monthly Fee"),
  FE0017("Dormancy Fee"),
  FE0205("Cancellation Fee"),
  FE0208("Send Cash Fee"),

  // Domestic Transactions
  FE0012("Over-the-Counter Withdrawal Fee"),
  FE0029("Signature Transaction Fee"),
  FE0025("Signature Transaction Decline Fee"),
  FE2487("PIN Transaction Fee"),
  FE0023("ATM Decline Fee"),
  FE0013("ATM Withdrawal Fee - out-of-network"),
  FE0021("ATM Balance Inquiry Fee"),
  FE0018("PIN Transaction Decline Fee"),

  // International Transactions
  FE0220("Signature Transaction Fee (Int'l)"),
  FE0026("Signature Transaction Decline Fee (Int'l)"),
  FE2448("PIN Transaction Fee (Int'l)"),
  FE2449("PIN Transaction Decline Fee (Int'l)"),
  FE0014("ATM Withdrawal Fee (Int'l)"),
  FE0024("ATM Decline Fee (Int'l)"),
  FE2306("Over-the-Counter Withdrawal Fee (Int'l)"),
  FE0022("ATM Balance Inquiry Fee (Int'l)"),
  FE0028("ATM Balance Inquiry Decline Fee (Int'l)"),

  // Service Fees
  FE0400("Automated Voice Response Fee"),
  FE0401("Automated Voice Response Fee"),
  FE0020("Paper Statement (Mail)"),
  FE0218("Card Replacement Fee (Standard Shipping)"),
  FE0204("Card Replacement Fee (Expedited)"),
  FE0301("Bill Pay Fee (Electronic)"),
  FE0302("Bill Pay Fee (Paper Check)"),
  FE0303("Bill Pay Fee (Check Cancellation)"),

  // MISC
  FE0599("Outbound ACH Transaction Fee (Transfer to bank)");

  private final String description;

  public static String getTransactionDescription(@NonNull String transactionCodeType) {
    try {
      return GalileoTransactionCodeType.valueOf(transactionCodeType).getDescription();
    } catch (IllegalArgumentException e) {
      return null;
    }
  }
}
