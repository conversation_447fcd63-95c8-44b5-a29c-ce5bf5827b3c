package com.everee.api.integration.galileo.dto.overview;

import com.everee.api.integration.galileo.dto.deserializer.MoneyDeserializer;
import com.everee.api.integration.galileo.dto.deserializer.MountainStandardTimestampDeserializer;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.ZonedDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode()
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class Hold {

  @JsonAlias("create_dt")
  @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
  private ZonedDateTime createDate;

  @JsonAlias("expiry_dt")
  @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
  private ZonedDateTime expirationDate;

  @JsonAlias("change_ts")
  @JsonDeserialize(using = MountainStandardTimestampDeserializer.class)
  private ZonedDateTime changeTimestamp;

  @JsonAlias("hold_type")
  private String holdType;

  @JsonAlias("dscr")
  private String description;

  @JsonDeserialize(using = MoneyDeserializer.class)
  private Money amount;
}
