package com.everee.api.integration.galileo.exception;

import com.everee.api.integration.galileo.dto.BaseGalileoResponse;

public class InvalidGalileoResponseException extends RuntimeException {

  public InvalidGalileoResponseException() {
    super("Invalid response received from Galileo call");
  }

  public InvalidGalileoResponseException(BaseGalileoResponse response) {
    super(
        String.format(
            "Invalid response received from Galileo call (statusCode: %s, status: %s, errors: %s)",
            response.getStatusCode(), response.getStatus(), response.getErrors()));
  }

  public InvalidGalileoResponseException(String message) {
    super(message);
  }
}
