package com.everee.api.integration.accounting.journalentry;

import static com.everee.api.integration.SupportedIntegration.QUICKBOOKS;
import static com.everee.api.integration.accounting.ExpenseType.*;
import static com.everee.api.integration.accounting.journalentry.PostingType.CREDIT;
import static com.everee.api.integration.accounting.journalentry.PostingType.DEBIT;

import com.everee.api.featureflag.FeatureFlagService;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.integration.ConfiguredIntegration;
import com.everee.api.integration.ConfiguredIntegrationLookup;
import com.everee.api.integration.ConfiguredIntegrationLookupService;
import com.everee.api.integration.IntegrationHealth;
import com.everee.api.integration.SupportedIntegration;
import com.everee.api.integration.SyncMethod;
import com.everee.api.integration.accounting.ExpenseType;
import com.everee.api.integration.accounting.journalentry.transaction.RecordableTranactionService;
import com.everee.api.integration.accounting.journalentry.transaction.RecordableTransaction;
import com.everee.api.integration.accounting.journalentry.transaction.TransactionAllocationKey;
import com.everee.api.integration.accounting.journalentry.transaction.TransactionBenefitTotalAllocationKey;
import com.everee.api.integration.accounting.journalentry.transaction.TransactionEarningTotalAllocationKey;
import com.everee.api.integration.accounting.quickbooksonline.QBOIntegrationValidator;
import com.everee.api.integration.accounting.quickbooksonline.QBOJournalEntryService;
import com.everee.api.integration.accounting.quickbooksonline.config.QBOConfigLookup;
import com.everee.api.integration.accounting.quickbooksonline.config.QBOConfigLookupService;
import com.everee.api.integration.accounting.quickbooksonline.exception.QBOIntegrationNotValidForAutomaticSendException;
import com.everee.api.integration.exception.IntegrationException;
import com.everee.api.integration.exception.IntegrationFailedToGenerateException;
import com.everee.api.integration.exception.IntegrationNotGeneratingException;
import com.everee.api.integration.exception.InvalidIntegrationConfigurationException;
import com.everee.api.money.Money;
import com.everee.api.taxauthority.EvereeAccountingJournalEntryService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class JournalEntryGenerator {
  private final ConfiguredIntegrationLookupService configuredIntegrationLookupService;
  private final JournalEntryRepository journalEntryRepository;
  private final JournalEntryLineRepository journalEntryLineRepository;
  private final JournalEntryLookupService journalEntryLookupService;
  private final QBOConfigLookupService qboConfigLookupService;
  private final QBOIntegrationValidator qboIntegrationValidator;
  private final QBOJournalEntryService qboJournalEntryService;
  private final RecordableTranactionService recordableTranactionService;
  private final EvereeAccountingJournalEntryService evereeAccountingJournalEntryService;
  private final JournalEntryGeneratorEmailService journalEntryGeneratorEmailService;
  private final FeatureFlagService featureFlagService;

  private static final Set<SupportedIntegration> SUPPORTED_GL_INTEGRATIONS = Set.of(QUICKBOOKS);

  @Value("${app.everee-accounting-sync-method}")
  private SyncMethod evereeAccountingSyncMethod;

  @Transactional
  public List<JournalEntry> createJournalEntries(JournalEntryParamsForCreate params) {
    return createJournalEntriesAndSend(params, false);
  }

  @Transactional
  public List<JournalEntry> createJournalEntriesAndSend(JournalEntryParamsForCreate params) {
    return createJournalEntriesAndSend(params, true);
  }

  /**
   * Send occurs if sync method is Automatic (either on integration or system property.)
   *
   * @param params
   * @param performSend
   * @return
   */
  private List<JournalEntry> createJournalEntriesAndSend(
      JournalEntryParamsForCreate params, boolean performSend) {
    return listGLIntegrations(params).stream()
        .map(
            integration -> {
              validateIntegration(params, integration);
              var journalEntry = createJournalEntryFromParams(params, integration);
              if (journalEntry != null
                  && performSend
                  && journalEntry.getStatus() != JournalEntryStatus.PENDING_GROUPED) {
                journalEntry = sendJournalEntry(params, integration, journalEntry);
              }

              return journalEntry;
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  @Transactional
  public List<JournalEntry> createGroupedJournalEntriesAndSend(JournalEntryParamsForCreate params) {
    var journalEntries = new ArrayList<JournalEntry>();
    listGLIntegrations(params)
        .forEach(
            integration -> {
              createGroupedJournalEntriesFromParams(params, integration)
                  .forEach(
                      entry -> {
                        try {
                          params.setSourceCompanyId(entry.getCompanyId());
                          validateIntegration(params, integration);

                          pushJournalEntryToIntegration(integration, entry);
                          journalEntries.add(entry);

                        } catch (IllegalStateException | IntegrationException e) {
                          log.error("Error generating journal entry", e);
                        }
                      });
            });
    return journalEntries;
  }

  @Transactional(Transactional.TxType.REQUIRES_NEW)
  public void triggerSafeJournalEntryCreation(JournalEntryParamsForCreate params) {
    listGLIntegrations(params)
        .forEach(
            integration -> {
              try {
                validateIntegration(params, integration);
              } catch (IntegrationException ignored) {
              }

              try {
                var journalEntry = createJournalEntryFromParams(params, integration);
                if (journalEntry != null) {
                  sendJournalEntry(params, integration, journalEntry);
                }
              } catch (IntegrationFailedToGenerateException e) {
                log.error("Error generating journal entry", e);
                journalEntryGeneratorEmailService.sendFor(params, e);
              } catch (IntegrationNotGeneratingException e) {
                // Not a critical error (usually a JE is not needed.)
                log.info("Integration not generating", e);
              }
            });
  }

  private JournalEntry sendJournalEntry(
      JournalEntryParamsForCreate params,
      ConfiguredIntegration integration,
      JournalEntry journalEntry) {
    if (getIntegrationSyncMethod(params, integration) == SyncMethod.AUTOMATIC) {
      try {
        validateJournalEntryForAutomaticSend(params, integration);
        return pushJournalEntryToIntegration(integration, journalEntry);
      } catch (QBOIntegrationNotValidForAutomaticSendException ignored) {
        journalEntry.setStatus(JournalEntryStatus.BLOCKED);
      }
    }

    return journalEntry;
  }

  public JournalEntry createJournalEntryFromParams(
      JournalEntryParamsForCreate params, ConfiguredIntegration integration) {
    if (params.isEvereeAccountingTracking()) {
      try {
        return evereeAccountingJournalEntryService
            .createJournalEntryFromParams(params, integration)
            .orElseThrow(
                () -> new IntegrationNotGeneratingException("No accounting journal entry needed"));
      } catch (IntegrationFailedToGenerateException | IntegrationNotGeneratingException e) {
        throw e;
      } catch (Exception e) {
        throw new IntegrationFailedToGenerateException(e.getMessage(), e);
      }
    }

    var transaction = getTransaction(params);
    if (transaction == null) {
      return null;
    }

    var journalEntry = new JournalEntry();
    journalEntry.setCompanyId(params.getCompanyId());
    journalEntry.setTransactionDate(params.getTransactionDate());
    journalEntry.setIntegration(integration.getIntegration());
    journalEntry.setStatus(JournalEntryStatus.PENDING_SEND);
    journalEntry.setAchFileId(params.getAchFileId());
    journalEntry.setCompanyDistributionId(params.getCompanyDistributionId());
    journalEntry.setCompanyFundingId(params.getCompanyFundingId());
    journalEntry.setConfiguredIntegrationId(integration.getId());
    journalEntry = journalEntryRepository.save(journalEntry);

    var lineParams = getLines(journalEntry, params, transaction);
    subtractChildAmountsFromParents(transaction.getAllocationKeys(), lineParams);
    var lines = lineParams.stream().map(this::createJournalEntryLine).collect(Collectors.toList());

    if (log.isDebugEnabled()) {
      lines.forEach(
          line -> {
            var message =
                String.format(
                    "JE LINE: expenseType %s - postingType %s - amount %s - approvalGroupId %s - workerRoleId %s - workLocationId %s",
                    line.getExpenseType(),
                    line.getPostingType(),
                    line.getAmount().getAmount(),
                    line.getApprovalGroupId(),
                    line.getWorkerRoleId(),
                    line.getWorkLocationId());
            System.out.println(message);
          });
    }

    return journalEntry;
  }

  private List<JournalEntry> createGroupedJournalEntriesFromParams(
      JournalEntryParamsForCreate params, ConfiguredIntegration integration) {
    var groupedEntries = new ArrayList<JournalEntry>();
    var now = LocalDateTime.now();
    var startAt = params.getTransactionDate().atStartOfDay();
    var endAt = params.getTransactionDate().plusDays(1).atStartOfDay().minusSeconds(1L);

    var entries = journalEntryRepository.findByPendingGroupedAndCreatedAtBetween(startAt, endAt);

    // ungroup single entry
    if (entries.size() == 1) {
      var ungroupedEntry =
          journalEntryRepository.save(entries.get(0).setStatus(JournalEntryStatus.PENDING_SEND));
      groupedEntries.add(ungroupedEntry);

    } else if (entries.size() > 1) {
      // group journal entries under a parent journal entry per company and return parent entries
      Long companyId = null;
      JournalEntry parentEntry = null;
      for (int i = 0; i < entries.size(); i++) {
        var childEntry = entries.get(i);

        // create parent entry for each company
        if (!childEntry.getCompanyId().equals(companyId)) {
          companyId = childEntry.getCompanyId();

          // ungroup single entry if only one per company
          var nextEntry = i + 1 < entries.size() ? entries.get(i + 1) : null;
          if (nextEntry == null || !nextEntry.getCompanyId().equals(companyId)) {
            var ungroupedEntry =
                journalEntryRepository.save(childEntry.setStatus(JournalEntryStatus.PENDING_SEND));
            groupedEntries.add(ungroupedEntry);
            continue;
          }

          // create parent entry
          parentEntry =
              journalEntryRepository.save(
                  (JournalEntry)
                      new JournalEntry()
                          .setCompanyId(childEntry.getCompanyId())
                          .setTransactionDate(now.toLocalDate())
                          .setStatus(JournalEntryStatus.PENDING_SEND)
                          .setIntegration(integration.getIntegration())
                          .setConfiguredIntegrationId(integration.getId())
                          .setEvereeAccountingRecord(Boolean.TRUE)
                          .setCreatedAt(now));
          groupedEntries.add(parentEntry);
        }

        // update child entry
        if (parentEntry != null && parentEntry.getCompanyId().equals(childEntry.getCompanyId())) {
          journalEntryRepository.save(
              (JournalEntry)
                  childEntry
                      .setStatus(JournalEntryStatus.GROUPED)
                      .setParentJournalEntryId(parentEntry.getId())
                      .setUpdatedAt(now));
        }
      }
    }
    return groupedEntries;
  }

  private RecordableTransaction getTransaction(JournalEntryParamsForCreate params) {
    RecordableTransaction transaction = null;
    if (params.getCompanyDistributionId() != null) {
      transaction =
          recordableTranactionService.buildRecordableTransactionForCompanyDistribution(
              params.getCompanyDistributionId());
    } else {
      var achDetailsLookup = params.buildAchDetailsLookup();
      transaction = recordableTranactionService.buildRecordableTransactionForAch(achDetailsLookup);
    }

    if (transaction == null) {
      return null;
    }
    if (params.getPywPaymentAmount().isNotZero()
        && transaction.getFirstTimePaymentAmounts().isNotZero()) {
      params.setPywPaymentAmount(transaction.getFirstTimePaymentAmounts());
    }
    params.setPywPaymentAmount(
        params.getPywPaymentAmount().minus(transaction.getRemainderPaymentAmounts()));
    return transaction;
  }

  private List<JournalEntryLineParamsForCreate> getLines(
      @NonNull JournalEntry journalEntry,
      @NonNull JournalEntryParamsForCreate params,
      @NonNull RecordableTransaction transaction) {
    var lines = new ArrayList<JournalEntryLineParamsForCreate>();
    var fundingsSettlementZero = params.getPywPaymentAmount().gt(params.getPostFundingAmount());

    if (!params.isPostFundingTransaction()) {
      lines.add(
          makeLineParams(
              OUTSTANDING_FUNDING_SETTLEMENT_EXPENSE,
              fundingsSettlementZero ? Money.ZERO : params.getPywTransactionAmount(),
              params.isPostFundingTransaction() ? CREDIT : DEBIT,
              TransactionAllocationKey.unallocated(),
              journalEntry));
    }

    lines.add(
        makeLineParams(
            OUTSTANDING_FUNDING_SETTLEMENT_LIABILITY,
            params.getPywTransactionAmount(),
            params.isPostFundingTransaction() ? DEBIT : CREDIT,
            TransactionAllocationKey.unallocated(),
            journalEntry));

    if (!params.anyFundingOccurred() && params.getPywPaymentAmount().isZero()) {
      return lines;
    }

    if (!params.isPostFundingTransaction()) {
      lines.add(
          makeLineParams(
              EXTERNAL_PAYMENTS_EXPENSE,
              params.getExternalPaymentsAmount(),
              CREDIT,
              TransactionAllocationKey.unallocated(),
              journalEntry));

      lines.add(
          makeLineParams(
              APPLIED_TAX_CREDITS,
              transaction.getTotalTaxCredits(),
              CREDIT,
              TransactionAllocationKey.unallocated(),
              journalEntry));
    }

    lines.add(
        makeLineParams(
            BANK_DEFAULT_FUNDING,
            params.getTotalFundingAmount(),
            CREDIT,
            TransactionAllocationKey.unallocated(),
            journalEntry));

    if (!params.isPostFundingTransaction()) {
      transaction.getAllocationKeys().stream()
          .map(key -> getLinesForAllocationKey(key, transaction, journalEntry))
          .flatMap(Collection::stream)
          .forEach(lines::add);
    }

    return lines;
  }

  private List<JournalEntryLineParamsForCreate> getLinesForAllocationKey(
      @NonNull TransactionAllocationKey allocationKey,
      @NonNull RecordableTransaction transaction,
      @NonNull JournalEntry journalEntry) {
    var totalEarnings =
        transaction.getAllocatedEarnings().entrySet().stream()
            .filter(e -> allocationKey.equalsKey(e.getKey()))
            .map(Entry::getValue)
            .reduce(Money.ZERO, Money::plus);

    var totalERTax =
        transaction.getAllocatedERTaxes().entrySet().stream()
            .filter(e -> allocationKey.equalsKey(e.getKey()))
            .map(Entry::getValue)
            .reduce(Money.ZERO, Money::plus);

    var totalERContributionExpense =
        transaction.getAllocatedBenefitExpenses().entrySet().stream()
            .filter(e -> allocationKey.equalsKey(e.getKey()))
            .map(Entry::getValue)
            .reduce(Money.ZERO, Money::plus);

    var totalERBenefitLiability =
        transaction.getAllocatedBenefitLiabilities().entrySet().stream()
            .filter(e -> allocationKey.equalsKey(e.getKey()))
            .map(Entry::getValue)
            .reduce(Money.ZERO, Money::plus);

    var lines = new ArrayList<JournalEntryLineParamsForCreate>();

    // Add parent lines first. Their amounts may be reduced later if child lines are also added.
    lines.add(
        makeLineParams(
            GENERAL_EARNINGS_EXPENSE, totalEarnings, DEBIT, allocationKey, journalEntry));
    lines.add(
        makeLineParams(
            GENERAL_PAYROLL_TAX_EXPENSE, totalERTax, DEBIT, allocationKey, journalEntry));
    lines.add(
        makeLineParams(
            GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
            totalERContributionExpense,
            DEBIT,
            allocationKey,
            journalEntry));
    lines.add(
        makeLineParams(
            GENERAL_BENEFITS_LIABILITY,
            totalERBenefitLiability,
            CREDIT,
            allocationKey,
            journalEntry));

    // Add "child" earning expense lines.
    transaction.getAllocatedEarnings().entrySet().stream()
        .filter(e -> allocationKey.equalsKey(e.getKey()))
        .filter(e -> e.getValue().isPlus())
        .map(e -> getLineForAllocatedEarning(e.getKey(), e.getValue(), journalEntry))
        .filter(Objects::nonNull)
        .forEach(lines::add);

    // Add "child" benefit expense lines.
    transaction.getAllocatedBenefitExpenses().entrySet().stream()
        .filter(e -> allocationKey.equalsKey(e.getKey()))
        .map(e -> getLineForAllocatedBenefitExpense(e.getKey(), e.getValue(), journalEntry))
        .filter(Objects::nonNull)
        .forEach(lines::add);

    // Add "child" benefit liability lines.
    transaction.getAllocatedBenefitLiabilities().entrySet().stream()
        .filter(e -> allocationKey.equalsKey(e.getKey()))
        .map(e -> getLineForAllocatedBenefitLiability(e.getKey(), e.getValue(), journalEntry))
        .filter(Objects::nonNull)
        .forEach(lines::add);

    return lines;
  }

  @Nullable
  private JournalEntryLineParamsForCreate getLineForAllocatedEarning(
      @NonNull TransactionEarningTotalAllocationKey key,
      @NonNull Money amount,
      @NonNull JournalEntry journalEntry) {
    ExpenseType expenseType;

    switch (key.getType()) {
      case REGULAR_SALARY:
        expenseType = SALARY_EXPENSE;
        break;

      case REGULAR_HOURLY:
        expenseType = HOURLY_EXPENSE;
        break;

      case BONUS:
        expenseType = BONUS_EXPENSE;
        break;

      case COMMISSION:
        expenseType = COMMISSION_EXPENSE;
        break;

      case CONTRACTOR:
        expenseType = CONTRACTOR_EXPENSE;
        break;

      case TIPS:
      case TIP_CREDIT:
      case PREVIOUSLY_PAID_TIPS:
        expenseType = TIPS_EXPENSE;
        break;

      case REIMBURSEMENT:
        expenseType = REIMBURSEMENT_EXPENSE;
        break;

      default:
        return null;
    }

    return makeLineParams(
        expenseType, amount, DEBIT, TransactionAllocationKey.from(key), journalEntry);
  }

  @Nullable
  private JournalEntryLineParamsForCreate getLineForAllocatedBenefitExpense(
      @NonNull TransactionBenefitTotalAllocationKey key,
      @NonNull Money amount,
      @NonNull JournalEntry journalEntry) {
    ExpenseType expenseType;

    switch (key.getType()) {
      case ERISA_401K:
        expenseType = ERISA_401K_CONTRIBUTION_EXPENSE;
        break;

      case ROTH_401K:
        expenseType = ROTH_401K_CONTRIBUTION_EXPENSE;
        break;

      case HSA:
        expenseType = HSA_CONTRIBUTION_EXPENSE;
        break;

      case FSA:
        expenseType = FSA_CONTRIBUTION_EXPENSE;
        break;

      case MEDICAL_INSURANCE:
        expenseType = MEDICAL_INSURANCE_CONTRIBUTION_EXPENSE;
        break;

      case MEDICAL_INSURANCE_SECONDARY:
        expenseType = MEDICAL_INSURANCE_SECONDARY_CONTRIBUTION_EXPENSE;
        break;

      case DENTAL_INSURANCE:
        expenseType = DENTAL_INSURANCE_CONTRIBUTION_EXPENSE;
        break;

      case VISION_INSURANCE:
        expenseType = VISION_INSURANCE_CONTRIBUTION_EXPENSE;
        break;

      case GROUP_LIFE_INSURANCE:
        expenseType = GROUP_LIFE_INSURANCE_CONTRIBUTION_EXPENSE;
        break;

      case VOLUNTARY_LIFE_INSURANCE:
        expenseType = VOLUNTARY_LIFE_INSURANCE_CONTRIBUTION_EXPENSE;
        break;

      default:
        return null;
    }

    var allocationKey = TransactionAllocationKey.from(key);
    return makeLineParams(expenseType, amount, DEBIT, allocationKey, journalEntry);
  }

  @Nullable
  private JournalEntryLineParamsForCreate getLineForAllocatedBenefitLiability(
      @NonNull TransactionBenefitTotalAllocationKey key,
      @NonNull Money amount,
      @NonNull JournalEntry journalEntry) {
    ExpenseType liabilityType;

    switch (key.getType()) {
      case ERISA_401K:
        liabilityType = ERISA_401K_LIABILITY;
        break;

      case ROTH_401K:
        liabilityType = ROTH_401K_LIABILITY;
        break;

      case HSA:
        liabilityType = HSA_LIABILITY;
        break;

      case FSA:
        liabilityType = FSA_LIABILITY;
        break;

      case GARNISHMENT:
      case GARNISHMENT_CHILD_SUPPORT:
      case GARNISHMENT_BANKRUPTCY_ORDER:
      case GARNISHMENT_FEDERAL_TAX_LEVY:
      case GARNISHMENT_FEDERAL_STUDENT_LOAN_ORDER:
      case GARNISHMENT_STATE_TAX_LEVY:
      case GARNISHMENT_LOCAL_TAX_LEVY:
      case GARNISHMENT_CREDITOR_GARNISHMENT:
      case GARNISHMENT_OTHER_GARNISHMENT_OR_WITHHOLDING_ORDER:

        liabilityType = GARNISHMENT_LIABILITY;
        break;

      case MEDICAL_INSURANCE:
        liabilityType = MEDICAL_INSURANCE_LIABILITY;
        break;

      case MEDICAL_INSURANCE_SECONDARY:
        liabilityType = MEDICAL_INSURANCE_SECONDARY_LIABILITY;
        break;

      case DENTAL_INSURANCE:
        liabilityType = DENTAL_INSURANCE_LIABILITY;
        break;

      case VISION_INSURANCE:
        liabilityType = VISION_INSURANCE_LIABILITY;
        break;

      case GROUP_LIFE_INSURANCE:
        liabilityType = GROUP_LIFE_INSURANCE_LIABILITY;
        break;

      case VOLUNTARY_LIFE_INSURANCE:
        liabilityType = VOLUNTARY_LIFE_INSURANCE_LIABILITY;
        break;

      default:
        return null;
    }

    var allocationKey = TransactionAllocationKey.from(key);
    return makeLineParams(liabilityType, amount, CREDIT, allocationKey, journalEntry);
  }

  private JournalEntryLineParamsForCreate makeLineParams(
      @NonNull ExpenseType expenseType,
      @NonNull Money amount,
      @NonNull PostingType postingType,
      @NonNull TransactionAllocationKey allocationKey,
      @NonNull JournalEntry journalEntry) {
    var params = new JournalEntryLineParamsForCreate();

    params.setExpenseType(expenseType);
    params.setAmount(amount);
    params.setPostingType(postingType);
    params.setCompanyId(journalEntry.getCompanyId());
    params.setJournalEntryId(journalEntry.getId());
    params.setApprovalGroupId(allocationKey.getApprovalGroupId());
    params.setWorkerRoleId(allocationKey.getWorkerRoleId());
    params.setWorkLocationId(allocationKey.getWorkLocationId());
    params.setDescription(
        params
            .getExpenseType()
            .getLocalizedTitle()
            .getLocalizedValue(MessageSourceHolder.getMessageSource(), Locale.ENGLISH));

    return params;
  }

  private void subtractChildAmountsFromParents(
      @NonNull Collection<TransactionAllocationKey> allocationKeys,
      @NonNull Collection<JournalEntryLineParamsForCreate> allLines) {
    allocationKeys.forEach(
        key -> {
          var allocatedLines =
              allLines.stream()
                  .filter(line -> line.getAllocationKey().equalsKey(key))
                  .collect(Collectors.toList());

          allocatedLines.forEach(
              params -> {
                var expenseType = params.getExpenseType();
                var parentType = expenseType.getParentType();
                if (parentType != null) {
                  var parentLineParams =
                      allocatedLines.stream()
                          .filter(p -> p.getExpenseType() == parentType)
                          .findFirst()
                          .orElseThrow(InvalidIntegrationConfigurationException::new);

                  var parentAmount = parentLineParams.getAmount();
                  parentLineParams.setAmount(parentAmount.minus(params.getAmount()));
                }
              });
        });
  }

  private List<ConfiguredIntegration> listGLIntegrations(JournalEntryParamsForCreate params) {
    var lookup =
        new ConfiguredIntegrationLookup()
            .setCompanyIds(Set.of(params.getCompanyId()))
            .setIntegrations(Set.of(SupportedIntegration.values()))
            .setHealths(Set.of(IntegrationHealth.CONNECTED));

    return configuredIntegrationLookupService
        .listAll(lookup, Pageable.unpaged())
        .filter(integration -> SUPPORTED_GL_INTEGRATIONS.contains(integration.getIntegration()))
        .toList();
  }

  private JournalEntryLine createJournalEntryLine(JournalEntryLineParamsForCreate params) {
    var line = new JournalEntryLine();

    line.setCompanyId(params.getCompanyId());
    line.setJournalEntryId(params.getJournalEntryId());
    line.setExpenseType(params.getExpenseType());
    line.setPostingType(params.getPostingType());
    line.setAmount(params.getAmount());
    line.setDescription(params.getDescription());
    line.setApprovalGroupId(params.getApprovalGroupId());
    line.setWorkerRoleId(params.getWorkerRoleId());
    line.setWorkLocationId(params.getWorkLocationId());

    return journalEntryLineRepository.save(line);
  }

  private void validateIntegration(
      JournalEntryParamsForCreate params, ConfiguredIntegration integration) {
    switch (integration.getIntegration()) {
      case QUICKBOOKS:
        qboIntegrationValidator.validateIntegrationConfiguration(params, integration);
        break;

      default:
        throw new IllegalStateException(
            String.format(
                "Journal entry generation is unsupported for integration type %s",
                integration.getIntegration()));
    }
  }

  private void validateJournalEntryForAutomaticSend(
      JournalEntryParamsForCreate params, ConfiguredIntegration configuredIntegration) {
    var lookup =
        new JournalEntryLookup()
            .withCompanyIds(Set.of(configuredIntegration.getCompanyId()))
            .withConfiguredIntegrationIds(Set.of(configuredIntegration.getId()));

    if (!params.isEvereeAccountingTracking()
        && journalEntryLookupService.listUnsent(lookup, Pageable.unpaged()).getSize() > 1) {
      throw new QBOIntegrationNotValidForAutomaticSendException();
    }
  }

  private SyncMethod getIntegrationSyncMethod(
      JournalEntryParamsForCreate params, ConfiguredIntegration integration) {
    var companyId = integration.getCompanyId();
    var integrationType = integration.getIntegration();

    switch (integrationType) {
      case QUICKBOOKS:
        if (params.isEvereeAccountingTracking()) {
          return Optional.ofNullable(evereeAccountingSyncMethod).orElse(SyncMethod.MANUAL);
        }
        var lookup = new QBOConfigLookup().setCompanyIds(Set.of(companyId));
        return qboConfigLookupService.findOneOrThrow(lookup).getSyncMethod();

      default:
        throw new IllegalStateException("Unsupported integration type: " + integrationType);
    }
  }

  private JournalEntry pushJournalEntryToIntegration(
      ConfiguredIntegration integration, JournalEntry journalEntry) {
    switch (integration.getIntegration()) {
      case QUICKBOOKS:
        var lookup =
            new JournalEntryLookup()
                .withCompanyIds(Set.of(journalEntry.getCompanyId()))
                .withIds(Set.of(journalEntry.getId()));
        return qboJournalEntryService.sendJournalEntry(lookup);

      default:
        throw new IllegalStateException(
            "Unsupported integration type: " + integration.getIntegration());
    }
  }
}
