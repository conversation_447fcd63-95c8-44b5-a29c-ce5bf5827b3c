package com.everee.api.integration.accounting.journalentry;

import com.everee.api.ach.AchInvoice;
import com.everee.api.ach.details.AchDetailsLookup;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentDeposit;
import com.everee.api.payment.distribution.PaymentDistribution;
import java.time.LocalDate;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Value;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;

@Data
@Accessors(chain = true)
public class JournalEntryParamsForCreate {
  private Long companyId;
  private Long sourceCompanyId;
  private Long achFileId;
  private LocalDate transactionDate;
  private Money preFundingAmount = Money.ZERO;
  private Money optimisticFundingAmount = Money.ZERO;
  private Money postFundingAmount = Money.ZERO;
  private Money pywPaymentAmount = Money.ZERO;
  private Money externalPaymentsAmount = Money.ZERO;
  private Set<Long> fundingAchFileRecordIds;
  private boolean evereeAccountingTracking;
  private AchInvoice achInvoice;
  private Set<ReturnDepositInfo> returnDeposits;
  private Long companyDistributionId;
  private Long companyFundingId;

  public Money getTotalFundingAmount() {
    return postFundingAmount.plus(optimisticFundingAmount).plus(preFundingAmount);
  }

  public Money getPywTransactionAmount() {
    return isPostFundingTransaction()
        ? postFundingAmount.minus(pywPaymentAmount)
        : pywPaymentAmount;
  }

  public boolean isPreFundingTransaction() {
    return preFundingAmount.compareTo(Money.ZERO) > 0;
  }

  public boolean isOptimisticFundingTransaction() {
    return optimisticFundingAmount.compareTo(Money.ZERO) > 0;
  }

  public boolean isPostFundingTransaction() {
    return postFundingAmount.compareTo(Money.ZERO) > 0;
  }

  public boolean anyFundingOccurred() {
    return isOptimisticFundingTransaction()
        || isPostFundingTransaction()
        || isPreFundingTransaction();
  }

  public AchDetailsLookup buildAchDetailsLookup() {
    if (isOptimisticFundingTransaction()
        || isPostFundingTransaction()
        || isPreFundingTransaction()) {
      return AchDetailsLookup.builder()
          .fundingAchFileRecordIds(fundingAchFileRecordIds)
          .regularFundingOccurred(isOptimisticFundingTransaction())
          .pywFundingOccurred(isPostFundingTransaction())
          .preFundingOccurred(isPreFundingTransaction())
          .excludeRemainders(Boolean.TRUE)
          .build();
    }

    return AchDetailsLookup.builder()
        .achFileIds(Set.of(achFileId))
        .excludeRemainders(Boolean.TRUE)
        .build();
  }

  public JournalEntryParamsForCreate setPaymentDeposits(Set<PaymentDeposit> deposits) {
    if (CollectionUtils.isNotEmpty(deposits)) {
      this.returnDeposits =
          deposits.stream().map(ReturnDepositInfo::new).collect(Collectors.toSet());
    }
    return this;
  }

  public JournalEntryParamsForCreate setPaymentDistributions(
      Set<PaymentDistribution> distributions) {
    if (CollectionUtils.isNotEmpty(distributions)) {
      this.returnDeposits =
          distributions.stream().map(ReturnDepositInfo::new).collect(Collectors.toSet());
    }
    return this;
  }

  @Value
  public static class ReturnDepositInfo {
    private Money amount;
    private Long paymentId;

    public ReturnDepositInfo(PaymentDistribution distribution) {
      this.paymentId = distribution.getPaymentId();
      this.amount = distribution.getAmounts().getAmount();
    }

    public ReturnDepositInfo(PaymentDeposit deposit) {
      this.paymentId = deposit.getPaymentId();
      this.amount = deposit.getAmounts().getAmount();
    }
  }
}
