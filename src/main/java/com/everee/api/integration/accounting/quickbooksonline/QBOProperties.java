package com.everee.api.integration.accounting.quickbooksonline;

import java.net.URI;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "app.quickbooks")
public class QBOProperties {
  @NotBlank private String clientId;
  @NotBlank private String clientSecret;
  @NotNull private URI redirectUri;
  @NotBlank private String redirectUriCallback;
  @NotBlank private String accountingApiHost;
  @NotBlank private String environment;
}
