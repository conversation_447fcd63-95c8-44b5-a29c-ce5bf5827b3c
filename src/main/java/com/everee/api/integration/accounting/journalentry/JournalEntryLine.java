package com.everee.api.integration.accounting.journalentry;

import com.everee.api.integration.accounting.ExpenseType;
import com.everee.api.model.BaseModel;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Optional;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Entity
@RequiredArgsConstructor
@Accessors(chain = true)
public class JournalEntryLine extends BaseModel {
  @NotNull private Long companyId;
  @NotNull private Long journalEntryId;
  private String externalIdentifier;
  @NotNull private Money amount;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PostingType postingType;

  @Enumerated(EnumType.STRING)
  private ExpenseType expenseType;

  private String description;

  @Column(name = "classname")
  private String evereeGLClassName;

  @Column(name = "classidentifier")
  private String evereeGLClassIdentifier;

  private Long approvalGroupId;
  private Long workerRoleId;
  private Long workLocationId;

  @Formula("(select a.name from approvalgroup a where a.id = approvalgroupid)")
  @Setter(AccessLevel.NONE)
  private String approvalGroupName;

  @Formula("(select wl.name from worklocation wl where wl.id = worklocationid)")
  @Setter(AccessLevel.NONE)
  private String workLocationName;

  @Embedded
  private JournalEntryLineEvereeGLAccountMapping evereeGLAccountMapping =
      new JournalEntryLineEvereeGLAccountMapping();

  @Embedded
  private JournalEntryLineEvereeGLEntityMapping evereeGLEntityMapping =
      new JournalEntryLineEvereeGLEntityMapping();

  @JsonIgnore
  public String getEvereeGLAccountEntityIdentifier() {
    return Optional.ofNullable(evereeGLAccountMapping)
        .map(JournalEntryLineEvereeGLAccountMapping::getEvereeGLAccountIdentifier)
        .orElse(null);
  }

  @JsonIgnore
  public String getEvereeGLAccountEntityName() {
    return Optional.ofNullable(evereeGLAccountMapping)
        .map(JournalEntryLineEvereeGLAccountMapping::getEvereeGLAccountName)
        .orElse(null);
  }
}
