package com.everee.api.integration.accounting.journalentry;

import com.everee.api.integration.SupportedIntegration;
import com.everee.api.lookup.Lookup;
import java.time.LocalDate;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class JournalEntryLookup implements Lookup {
  private Set<Long> ids;
  private Set<Long> companyIds;
  private Set<String> externalIdentifiers;
  private Set<LocalDate> transactionDates;
  private Set<JournalEntryStatus> statuses;
  private Set<Long> configuredIntegrationIds;
  private Set<SupportedIntegration> supportedIntegrations;
  private Set<Long> achFileIds;
  private Boolean evereeAccountingRecords;
}
