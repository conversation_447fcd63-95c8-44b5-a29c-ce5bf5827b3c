package com.everee.api.integration.accounting.journalentry.transaction;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TransactionAllocationKey implements IAllocationKey {
  Long approvalGroupId;
  Long workerRoleId;
  Long workLocationId;

  public static TransactionAllocationKey from(IAllocationKey key) {
    return TransactionAllocationKey.builder()
        .approvalGroupId(key.getApprovalGroupId())
        .workLocationId(key.getWorkLocationId())
        .workerRoleId(key.getWorkerRoleId())
        .build();
  }

  public static TransactionAllocationKey unallocated() {
    return TransactionAllocationKey.builder()
        .approvalGroupId(null)
        .workLocationId(null)
        .workerRoleId(null)
        .build();
  }
}
