package com.everee.api.integration.accounting.quickbooksonline;

import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.data.Vendor;
import lombok.Data;

@Data
public class QBOVendor {
  private String id;
  private String fullyQualifiedName;
  private String displayName;

  static <E extends IEntity> QBOVendor fromQboEntity(E entity) {
    var qboVendor = (Vendor) entity;

    var vendor = new QBOVendor();
    vendor.setId(qboVendor.getId());
    vendor.setDisplayName(qboVendor.getDisplayName());
    vendor.setFullyQualifiedName(qboVendor.getFullyQualifiedName());

    return vendor;
  }
}
