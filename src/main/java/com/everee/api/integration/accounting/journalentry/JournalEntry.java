package com.everee.api.integration.accounting.journalentry;

import com.everee.api.integration.ConfiguredIntegration;
import com.everee.api.integration.SupportedIntegration;
import com.everee.api.model.BaseModelV2;
import java.time.LocalDate;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Entity
@RequiredArgsConstructor
@Accessors(chain = true)
@JsonTypeName("JournalEntry")
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class JournalEntry extends BaseModelV2<JournalEntry> {
  private Long parentJournalEntryId;
  @NotNull private Long companyId;
  @NotNull private LocalDate transactionDate;
  private String externalIdentifier;
  private LocalDateTime sentAt;
  private Long achFileId;
  private Long companyDistributionId;
  private Long companyFundingId;
  @NotNull private Long configuredIntegrationId;
  private String name;
  private boolean evereeAccountingRecord = false;

  @NotNull
  @Enumerated(EnumType.STRING)
  private JournalEntryStatus status;

  @NotNull
  @Enumerated(EnumType.STRING)
  private SupportedIntegration integration;

  @ManyToOne
  @JoinColumn(name = "configuredIntegrationId", insertable = false, updatable = false)
  private ConfiguredIntegration configuredIntegration;

  public boolean canBeRegenerated() {
    return this.status != JournalEntryStatus.GENERATING && this.status != JournalEntryStatus.SENT;
  }
}
