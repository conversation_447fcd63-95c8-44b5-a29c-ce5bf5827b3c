package com.everee.api.integration.accounting.journalentry.transaction;

import static com.everee.api.payment.total.PaymentTotalVersion.V1;

import com.everee.api.ach.AchFileRecord;
import com.everee.api.ach.AchFileRecordRepository;
import com.everee.api.ach.details.AchDetailsLookup;
import com.everee.api.distribution.CompanyDistributionRepository;
import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransaction;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionLookup;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionLookupService;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentRepository;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.total.PaymentTotalService;
import com.everee.api.payment.total.v1.PaymentTotal;
import com.everee.api.taxaccumulator.TaxTypeAccumulation;
import com.everee.api.taxaccumulator.TaxTypeAccumulationPaymentType;
import com.everee.api.taxaccumulator.lookup.TaxTypeAccumulationLookup;
import com.everee.api.taxaccumulator.lookup.TaxTypeAccumulationLookupService;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@RequiredArgsConstructor
@Service
public class RecordableTranactionService {
  private final DetailedEmployeeRepository employeeRepository;
  private final PaymentTotalService paymentTotalService;
  private final TaxTypeAccumulationLookupService taxTypeAccumulationLookupService;
  private final EmployeeAccountTransactionLookupService employeeAccountTransactionLookupService;
  private final AchFileRecordRepository achFileRecordRepository;
  private final PaymentRepository paymentRepository;
  private final CompanyDistributionRepository companyDistributionRepository;

  public RecordableTransaction buildRecordableTransactionForAch(AchDetailsLookup achDetailsLookup) {
    var paymentLookup = buildPaymentLookup(achDetailsLookup);
    var taxTypeAccumulationLookup = buildTaxTypeAccumulationLookup(achDetailsLookup);
    var employeeAccountTransactionLookup = buildEmployeeAccountTransactionLookup(achDetailsLookup);

    var paymentTotal = (PaymentTotal) paymentTotalService.getTotals(paymentLookup, V1);

    var employeeAccountTransactions =
        employeeAccountTransactionLookupService
            .listAll(employeeAccountTransactionLookup, Pageable.unpaged())
            .toList();

    var totalTaxCredits =
        taxTypeAccumulationLookup
            .map(lookup -> taxTypeAccumulationLookupService.listAll(lookup, Pageable.unpaged()))
            .orElse(Page.empty()).stream()
            .map(TaxTypeAccumulation::getAppliedCredit)
            .reduce(Money.ZERO, Money::plus);

    var remainderPaymentAmounts =
        getRemainderPaymentAmounts(paymentLookup, taxTypeAccumulationLookup);

    return toRecordableTransaction(paymentTotal, employeeAccountTransactions, totalTaxCredits)
        .setRemainderPaymentAmounts(remainderPaymentAmounts);
  }

  public RecordableTransaction buildRecordableTransactionForCompanyDistribution(
      Long companyDistributionId) {
    var paymentIds =
        companyDistributionRepository.getFirstTimePaymentIdsInCompanyDistribution(
            companyDistributionId);

    if (paymentIds.isEmpty()) return null;

    var paymentLookup = new PaymentLookup().setIds(paymentIds);
    var paymentTotal = (PaymentTotal) paymentTotalService.getTotals(paymentLookup, V1);
    var firstTimePaymentAmounts = getFirstTimePaymentAmounts(companyDistributionId);

    return toRecordableTransaction(paymentTotal, List.of(), Money.ZERO)
        .setFirstTimePaymentAmounts(firstTimePaymentAmounts);
  }

  private RecordableTransaction toRecordableTransaction(
      @NonNull PaymentTotal paymentTotal,
      @NonNull Collection<EmployeeAccountTransaction> employeeAccountTransactions,
      @NonNull Money totalTaxCredits) {
    var recordableTransaction = new RecordableTransaction().setTotalTaxCredits(totalTaxCredits);

    // Retrieve "default" (i.e. based on the current HR configuration) worker expense allocation
    // keys. These are used in place of specific allocation fields on contributions and taxes,
    // which aren't (yet?) supported like they are for earnings, and also as a fallback value for
    // any earnings that lack a more specific setting.
    var employeeIds = paymentTotal.getWorkerIds();

    var workerAllocationKeys = new HashMap<Long, TransactionAllocationKey>();
    try (var keys = employeeRepository.getWorkerDefaultExpenseAllocationKeys(employeeIds)) {
      keys.forEach(
          item -> {
            var key = item.getEmployeeId();
            var value =
                TransactionAllocationKey.builder()
                    .approvalGroupId(item.getApprovalGroupId())
                    .workLocationId(item.getWorkLocationId())
                    .workerRoleId(item.getWorkerRoleId())
                    .build();
            workerAllocationKeys.put(key, value);
          });
    }

    // Transfer pre-allocated earnings into AchDetails (falling back to the current HR configuration
    // when more explicit allocation fields are missing), and keep track of each unique key.
    paymentTotal
        .getAllocatedEarningsByEmployee()
        .forEach(
            (employeeId, allocatedEarnings) -> {
              var workerKey = workerAllocationKeys.get(employeeId);

              allocatedEarnings.forEach(
                  (key, earningTotal) -> {
                    var amount = earningTotal.getAmount();
                    var earningKey =
                        TransactionEarningTotalAllocationKey.builder()
                            .approvalGroupId(
                                coalesce(key.getApprovalGroupId(), workerKey.getApprovalGroupId()))
                            .workLocationId(
                                coalesce(key.getWorkLocationId(), workerKey.getWorkLocationId()))
                            .workerRoleId(
                                coalesce(key.getWorkerRoleId(), workerKey.getWorkerRoleId()))
                            .type(key.getType())
                            .build();

                    recordableTransaction
                        .getAllocationKeys()
                        .add(TransactionAllocationKey.from(earningKey));

                    var allocatedEarningAmount =
                        recordableTransaction
                            .getAllocatedEarnings()
                            .getOrDefault(earningKey, Money.ZERO)
                            .plus(amount);
                    recordableTransaction
                        .getAllocatedEarnings()
                        .put(earningKey, allocatedEarningAmount);
                  });
            });

    // Allocate ER contributions according to default worker allocation keys, and keep track of
    // each unique key.
    paymentTotal
        .getBenefitsByEmployee()
        .forEach(
            (employeeId, benefitsByType) -> {
              var workerKey = workerAllocationKeys.get(employeeId);

              benefitsByType.forEach(
                  (benefitTotal) -> {
                    var type = benefitTotal.getBenefitTypeWithName().getType();
                    var benefitKey =
                        TransactionBenefitTotalAllocationKey.builder()
                            .approvalGroupId(workerKey.getApprovalGroupId())
                            .workLocationId(workerKey.getWorkLocationId())
                            .workerRoleId(workerKey.getWorkerRoleId())
                            .type(type)
                            .build();

                    recordableTransaction
                        .getAllocationKeys()
                        .add(TransactionAllocationKey.from(benefitKey));

                    var expenseAmount =
                        recordableTransaction
                            .getAllocatedBenefitExpenses()
                            .getOrDefault(benefitKey, Money.ZERO)
                            .plus(benefitTotal.getAmountER());
                    recordableTransaction
                        .getAllocatedBenefitExpenses()
                        .put(benefitKey, expenseAmount);

                    if (benefitTotal.isFundAndRemit()) {
                      return;
                    }

                    var preemptiveFundingAmount =
                        employeeAccountTransactions.stream()
                            .filter(t -> t.getEmployeeContributionDeductionType().equals(type))
                            .map(EmployeeAccountTransaction::getFundingAmount)
                            .reduce(Money.ZERO, Money::plus);

                    var liabilityAmount =
                        recordableTransaction
                            .getAllocatedBenefitLiabilities()
                            .getOrDefault(benefitKey, Money.ZERO)
                            .plus(benefitTotal.getAmountER())
                            .plus(benefitTotal.getAmountEE())
                            .plus(preemptiveFundingAmount);
                    recordableTransaction
                        .getAllocatedBenefitLiabilities()
                        .put(benefitKey, liabilityAmount);
                  });
            });

    // Allocate ER taxes according to default worker allocation keys, and keep track of each
    // unique key.
    paymentTotal
        .getEmployerTaxesByEmployee()
        .forEach(
            (employeeId, employerTaxes) -> {
              var workerKey = workerAllocationKeys.get(employeeId);
              recordableTransaction.getAllocationKeys().add(workerKey);

              employerTaxes.forEach(
                  (taxTotal) -> {
                    var taxAmount =
                        recordableTransaction
                            .getAllocatedERTaxes()
                            .getOrDefault(workerKey, Money.ZERO)
                            .plus(taxTotal.getAmount());

                    recordableTransaction.getAllocatedERTaxes().put(workerKey, taxAmount);
                  });
            });

    return recordableTransaction;
  }

  private Optional<TaxTypeAccumulationLookup> buildTaxTypeAccumulationLookup(
      AchDetailsLookup achDetailsLookup) {
    var lookup = new TaxTypeAccumulationLookup();
    if (!CollectionUtils.isEmpty(achDetailsLookup.getFundingAchFileRecordIds())) {
      var achFileIdsToFundingTypes =
          paymentRepository.getAchFileIdsToFundingTypeForFundingAchFileRecordIds(
              achDetailsLookup.getFundingAchFileRecordIds());

      lookup.applyAchFileIdsToFundingTypes(achFileIdsToFundingTypes);

      // Not sure this is possible.  If there are achfileids in the lookup that aren't
      // also being funded, make sure they get in the lookup map.
      if (!CollectionUtils.isEmpty(achDetailsLookup.getAchFileIds())
          && !lookup
              .getAchFileIdsToPaymentTypes()
              .keySet()
              .containsAll(achDetailsLookup.getAchFileIds())) {
        achDetailsLookup.getAchFileIds().stream()
            .filter(achFileId -> !lookup.getAchFileIdsToPaymentTypes().containsKey(achFileId))
            .forEach(
                achFileId ->
                    lookup
                        .getAchFileIdsToPaymentTypes()
                        .put(achFileId, Set.of(TaxTypeAccumulationPaymentType.values())));
      }

      if (CollectionUtils.isEmpty(lookup.getAchFileIdsToPaymentTypes())
          && CollectionUtils.isEmpty(lookup.getFundingAchFileIdsToPaymentTypes())) {
        return Optional.empty();
      }

    } else if (!CollectionUtils.isEmpty(achDetailsLookup.getAchFileIds())) {
      lookup.setAchFileIds(achDetailsLookup.getAchFileIds());
    } else {
      return Optional.empty();
    }

    return Optional.of(lookup);
  }

  private EmployeeAccountTransactionLookup buildEmployeeAccountTransactionLookup(
      AchDetailsLookup achDetailsLookup) {
    var achFileIds =
        Optional.ofNullable(achDetailsLookup.getAchFileIds())
            .map(HashSet::new)
            .orElse(new HashSet<>());

    if (!CollectionUtils.isEmpty(achDetailsLookup.getFundingAchFileRecordIds())) {
      achFileIds.addAll(
          achFileRecordRepository.findAllById(achDetailsLookup.getFundingAchFileRecordIds())
              .stream()
              .map(AchFileRecord::getAchFileId)
              .collect(Collectors.toSet()));
    }

    return new EmployeeAccountTransactionLookup()
        .setPaymentTransaction(false)
        .setFundingAchFileIds(achFileIds);
  }

  private PaymentLookup buildPaymentLookup(AchDetailsLookup achDetailsLookup) {
    return new PaymentLookup()
        .withAchFileIds(achDetailsLookup.getAchFileIds())
        .withFundingAchFileRecordIds(achDetailsLookup.getFundingAchFileRecordIds());
  }

  private Money getFirstTimePaymentAmounts(Long companyDistributionId) {
    var cd = companyDistributionRepository.getOne(companyDistributionId);
    if (cd.getType() != PaymentDistributionType.PTPC
        && cd.getType() != PaymentDistributionType.PAY_CARD) {
      return Money.ZERO;
    }

    // PTPC and PAY_CARD transactions only send through the worker distribution amount.  If there
    // are
    // taxes/ECDs, record those now.  Also, worker initiated PTPC distributions can cause payments
    // to be sent through the JE generator multiple times.  Record the entire transaction the 1st
    // time through, and subsequent passes should not generate a JE, otherwise we have to figure out
    // how to split a payment across multiple JE's
    return Money.valueOf(
        companyDistributionRepository.getFirstTimePaymentDistributionTotal(companyDistributionId));
  }

  private Money getRemainderPaymentAmounts(
      PaymentLookup paymentLookup, Optional<TaxTypeAccumulationLookup> taxTypeAccumulationLookup) {
    var achFileIds = new HashSet();
    if (paymentLookup.getAchFileIds() != null) {
      achFileIds.addAll(paymentLookup.getAchFileIds());
    }
    taxTypeAccumulationLookup.ifPresent(
        l -> {
          if (l.getAchFileIds() != null) {
            achFileIds.addAll(l.getAchFileIds());
          }
          if (l.getAchFileIdsToPaymentTypes() != null) {
            achFileIds.addAll(l.getAchFileIdsToPaymentTypes().keySet());
          }
        });
    if (!achFileIds.isEmpty()) {
      return Money.valueOf(
          companyDistributionRepository.getDistributionAmountWithRemainderInAch(achFileIds));
    }

    return Money.ZERO;
  }

  @Nullable
  private Long coalesce(Long... items) {
    return Stream.of(items).filter(Objects::nonNull).findFirst().orElse(null);
  }
}
