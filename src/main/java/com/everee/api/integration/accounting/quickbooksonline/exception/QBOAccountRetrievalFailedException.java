package com.everee.api.integration.accounting.quickbooksonline.exception;

import com.everee.api.integration.exception.LocalizedIntegrationException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class QBOAccountRetrievalFailedException extends LocalizedIntegrationException {
  public QBOAccountRetrievalFailedException(Throwable throwable) {
    super(
        "integration.accounting.quickbooksonline.exception.QBOAccountRetrievalFailedException.message",
        throwable);
  }
}
