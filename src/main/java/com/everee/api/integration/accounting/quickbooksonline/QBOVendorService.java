package com.everee.api.integration.accounting.quickbooksonline;

import com.everee.api.integration.accounting.quickbooksonline.exception.QBOAccountRetrievalFailedException;
import com.everee.api.integration.accounting.quickbooksonline.exception.QBOAddFailedException;
import com.intuit.ipp.data.Vendor;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QBOVendorService {
  private final QBOService qboService;

  public Page<QBOVendor> listVendors(Long companyId, Pageable pageable, String nameLike) {
    var vendors =
        qboService.withDataService(companyId, ds -> fetchQboVendors(ds, pageable, nameLike))
            .getEntities().stream()
            .map(QBOVendor::fromQboEntity)
            .collect(Collectors.toList());

    var pageNum = pageable.isUnpaged() ? 0 : pageable.getPageNumber();
    var pageSize = pageable.isUnpaged() ? 1000 : pageable.getPageSize();
    var vendorsSize = (pageNum * pageSize) + vendors.size() + (vendors.size() < pageSize ? 0 : 1);
    return new PageImpl<>(vendors, pageable, vendorsSize);
  }

  public QBOVendor addVendor(Long companyId, Vendor vendor) {
    return qboService.withDataService(
        companyId,
        (dataService -> {
          try {
            return QBOVendor.fromQboEntity(dataService.add(vendor));
          } catch (FMSException e) {
            throw new QBOAddFailedException(e);
          }
        }));
  }

  private QueryResult fetchQboVendors(DataService dataService, Pageable pageable, String nameLike) {
    try {
      var page = pageable.isUnpaged() ? 0 : pageable.getPageNumber();
      var pagesize = pageable.isUnpaged() ? 1000 : pageable.getPageSize();
      var startPosition = page * pagesize + 1;
      return dataService.executeQuery(
          "select * from Vendor"
              + Optional.ofNullable(nameLike)
                  .map(nl -> " WHERE DisplayName LIKE '%" + nl + "%'")
                  .orElse("")
              + " STARTPOSITION "
              + startPosition
              + " MAXRESULTS "
              + pagesize);
    } catch (FMSException ex) {
      throw new QBOAccountRetrievalFailedException(ex);
    }
  }

  public QBOVendor getVendor(Long companyId, String vendorId) {
    return qboService.withDataService(
        companyId,
        (dataService -> {
          try {
            var vendor = new Vendor();
            vendor.setId(vendorId);
            return QBOVendor.fromQboEntity(dataService.findById(vendor));
          } catch (FMSException e) {
            throw new QBOAddFailedException(e);
          }
        }));
  }
}
