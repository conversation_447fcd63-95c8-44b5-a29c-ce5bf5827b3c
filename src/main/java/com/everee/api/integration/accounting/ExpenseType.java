package com.everee.api.integration.accounting;

import com.everee.api.i18n.LocalizedString;
import java.util.EnumSet;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ExpenseType {
  // REQUIRED CORE MAPPINGS

  GENERAL_EARNINGS_EXPENSE(
      TransactionClassification.EXPENSE,
      null,
      "integration.accounting.ExpenseType.GENERAL_EARNINGS_EXPENSE.title"),
  GENERAL_PAYROLL_TAX_EXPENSE(
      TransactionClassification.EXPENSE,
      null,
      "integration.accounting.ExpenseType.GENERAL_PAYROLL_TAX_EXPENSE.title"),
  GENERAL_BENEFITS_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      null,
      "integration.accounting.ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE.title"),
  GENERAL_BENEFITS_LIABILITY(
      TransactionClassification.LIABILITY,
      null,
      "integration.accounting.ExpenseType.GENERAL_BENEFITS_LIABILITY.title"),
  OUTSTANDING_FUNDING_SETTLEMENT_EXPENSE(
      TransactionClassification.EXPENSE,
      null,
      "integration.accounting.ExpenseType.OUTSTANDING_FUNDING_SETTLEMENT_EXPENSE.title"),
  OUTSTANDING_FUNDING_SETTLEMENT_LIABILITY(
      TransactionClassification.LIABILITY,
      null,
      "integration.accounting.ExpenseType.OUTSTANDING_FUNDING_SETTLEMENT_LIABILITY.title"),
  APPLIED_TAX_CREDITS(
      TransactionClassification.ASSET,
      null,
      "integration.accounting.ExpenseType.APPLIED_TAX_CREDITS.title"),
  EXTERNAL_PAYMENTS_EXPENSE(
      TransactionClassification.ASSET,
      null,
      "integration.accounting.ExpenseType.EXTERNAL_PAYMENTS_EXPENSE.title"),
  BANK_DEFAULT_FUNDING(
      TransactionClassification.ASSET,
      null,
      "integration.accounting.ExpenseType.BANK_DEFAULT_FUNDING.title"),

  // EARNINGS

  SALARY_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.SALARY_EXPENSE.title"),
  HOURLY_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.HOURLY_EXPENSE.title"),
  BONUS_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.BONUS_EXPENSE.title"),
  COMMISSION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.COMMISSION_EXPENSE.title"),
  CONTRACTOR_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.CONTRACTOR_EXPENSE.title"),
  TIPS_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.TIPS_EXPENSE.title"),
  REIMBURSEMENT_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.REIMBURSEMENT_EXPENSE.title"),

  // TAXES

  EMPLOYEE_TAX_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.EMPLOYEE_TAX_EXPENSE.title"),

  // BENEFITS

  //  FUNDED_CONTRIBUTION_EXPENSE(
  //    TransactionClassification.EXPENSE,
  //    ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
  //    "integration.accounting.ExpenseType.FUNDED_CONTRIBUTION_EXPENSE.title"),

  ERISA_401K_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.ERISA_401K_DEDUCTION_EXPENSE.title"),
  ERISA_401K_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.ERISA_401K_CONTRIBUTION_EXPENSE.title"),
  ERISA_401K_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.ERISA_401K_LIABILITY.title"),

  ROTH_401K_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.ROTH_401K_DEDUCTION_EXPENSE.title"),
  ROTH_401K_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.ROTH_401K_CONTRIBUTION_EXPENSE.title"),
  ROTH_401K_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.ROTH_401K_LIABILITY.title"),

  HSA_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.HSA_DEDUCTION_EXPENSE.title"),
  HSA_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.HSA_CONTRIBUTION_EXPENSE.title"),
  HSA_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.HSA_LIABILITY.title"),

  FSA_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.FSA_DEDUCTION_EXPENSE.title"),
  FSA_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.FSA_CONTRIBUTION_EXPENSE.title"),
  FSA_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.FSA_LIABILITY.title"),

  GARNISHMENT_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.GARNISHMENT_DEDUCTION_EXPENSE.title"),
  GARNISHMENT_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.GARNISHMENT_LIABILITY.title"),

  GARNISHMENT_CHILD_SUPPORT_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_CHILD_SUPPORT_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_BANKRUPTCY_ORDER_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_BANKRUPTCY_ORDER_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_FEDERAL_TAX_LEVY_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_FEDERAL_TAX_LEVY_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_FEDERAL_STUDENT_LOAN_ORDER_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_FEDERAL_STUDENT_LOAN_ORDER_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_STATE_TAX_LEVY_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_STATE_TAX_LEVY_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_LOCAL_TAX_LEVY_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_LOCAL_TAX_LEVY_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_CREDITOR_GARNISHMENT_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_CREDITOR_GARNISHMENT_DEDUCTION_EXPENSE.title"),

  GARNISHMENT_OTHER_GARNISHMENT_OR_WITHHOLDING_ORDER_DEDUCTION_EXPENSE(
    TransactionClassification.EXPENSE,
    ExpenseType.GENERAL_EARNINGS_EXPENSE,
    "integration.accounting.ExpenseType.GARNISHMENT_OTHER_GARNISHMENT_OR_WITHHOLDING_ORDER_DEDUCTION_EXPENSE.title"),

  MEDICAL_INSURANCE_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.MEDICAL_INSURANCE_DEDUCTION_EXPENSE.title"),
  MEDICAL_INSURANCE_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.MEDICAL_INSURANCE_CONTRIBUTION_EXPENSE.title"),
  MEDICAL_INSURANCE_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.MEDICAL_INSURANCE_LIABILITY.title"),

  MEDICAL_INSURANCE_SECONDARY_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.MEDICAL_INSURANCE_SECONDARY_DEDUCTION_EXPENSE.title"),
  MEDICAL_INSURANCE_SECONDARY_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.MEDICAL_INSURANCE_SECONDARY_CONTRIBUTION_EXPENSE.title"),
  MEDICAL_INSURANCE_SECONDARY_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.MEDICAL_INSURANCE_SECONDARY_LIABILITY.title"),

  DENTAL_INSURANCE_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.DENTAL_INSURANCE_DEDUCTION_EXPENSE.title"),
  DENTAL_INSURANCE_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.DENTAL_INSURANCE_CONTRIBUTION_EXPENSE.title"),
  DENTAL_INSURANCE_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.DENTAL_INSURANCE_LIABILITY.title"),

  VISION_INSURANCE_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.VISION_INSURANCE_DEDUCTION_EXPENSE.title"),
  VISION_INSURANCE_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.VISION_INSURANCE_CONTRIBUTION_EXPENSE.title"),
  VISION_INSURANCE_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.VISION_INSURANCE_LIABILITY.title"),

  GROUP_LIFE_INSURANCE_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.GROUP_LIFE_INSURANCE_DEDUCTION_EXPENSE.title"),
  GROUP_LIFE_INSURANCE_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.GROUP_LIFE_INSURANCE_CONTRIBUTION_EXPENSE.title"),
  GROUP_LIFE_INSURANCE_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.GROUP_LIFE_INSURANCE_LIABILITY.title"),

  VOLUNTARY_LIFE_INSURANCE_DEDUCTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_EARNINGS_EXPENSE,
      "integration.accounting.ExpenseType.VOLUNTARY_LIFE_INSURANCE_DEDUCTION_EXPENSE.title"),
  VOLUNTARY_LIFE_INSURANCE_CONTRIBUTION_EXPENSE(
      TransactionClassification.EXPENSE,
      ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
      "integration.accounting.ExpenseType.VOLUNTARY_LIFE_INSURANCE_CONTRIBUTION_EXPENSE.title"),
  VOLUNTARY_LIFE_INSURANCE_LIABILITY(
      TransactionClassification.LIABILITY,
      ExpenseType.GENERAL_BENEFITS_LIABILITY,
      "integration.accounting.ExpenseType.VOLUNTARY_LIFE_INSURANCE_LIABILITY.title");

  private final TransactionClassification classification;
  private final ExpenseType parentType;
  private final String titleCode;

  public LocalizedString getLocalizedTitle() {
    return LocalizedString.of(titleCode);
  }

  public static EnumSet<ExpenseType> PARENT_MAPPINGS =
      EnumSet.of(
          ExpenseType.GENERAL_EARNINGS_EXPENSE,
          ExpenseType.GENERAL_BENEFITS_CONTRIBUTION_EXPENSE,
          ExpenseType.GENERAL_BENEFITS_LIABILITY,
          ExpenseType.GENERAL_PAYROLL_TAX_EXPENSE,
          ExpenseType.APPLIED_TAX_CREDITS,
          ExpenseType.EXTERNAL_PAYMENTS_EXPENSE,
          ExpenseType.BANK_DEFAULT_FUNDING,
          ExpenseType.OUTSTANDING_FUNDING_SETTLEMENT_EXPENSE,
          ExpenseType.OUTSTANDING_FUNDING_SETTLEMENT_LIABILITY);
}
