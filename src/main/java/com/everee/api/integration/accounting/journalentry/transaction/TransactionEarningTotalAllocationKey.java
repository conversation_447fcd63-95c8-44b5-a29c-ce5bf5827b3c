package com.everee.api.integration.accounting.journalentry.transaction;

import com.everee.api.earnings.EarningType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TransactionEarningTotalAllocationKey implements IAllocationKey {
  EarningType type;
  Long approvalGroupId;
  Long workerRoleId;
  Long workLocationId;
}
