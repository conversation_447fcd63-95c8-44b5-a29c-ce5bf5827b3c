package com.everee.api.integration.accounting;

import com.everee.api.integration.SupportedIntegration;
import com.everee.api.lookup.Lookup;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class ExpenseTypeAccountMappingLookup implements Lookup {
  private Set<Long> ids;
  private Set<String> entityIdentifiers;
  private Set<Long> companyIds;
  private Set<ExpenseType> expenseTypes;
  private Set<SupportedIntegration> integrations;
  private Set<Long> configuredIntegrationIds;
  private boolean deleted;
}
