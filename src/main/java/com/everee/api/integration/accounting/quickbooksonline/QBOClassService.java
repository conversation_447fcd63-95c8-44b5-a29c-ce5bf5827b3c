package com.everee.api.integration.accounting.quickbooksonline;

import com.everee.api.integration.accounting.quickbooksonline.exception.QBOAccountRetrievalFailedException;
import com.everee.api.integration.accounting.quickbooksonline.exception.QBOAddFailedException;
import com.intuit.ipp.data.Class;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QBOClassService {
  private final QBOService qboService;

  public Page<QBOClass> listClasses(Long companyId) {
    var classes =
        qboService.withDataService(companyId, this::fetchQboClasses).getEntities().stream()
            .map(QBOClass::fromQboEntity)
            .collect(Collectors.toList());

    return new PageImpl<>(classes);
  }

  private QueryResult fetchQboClasses(DataService dataService) {
    try {
      return dataService.executeQuery("select * from Class STARTPOSITION 1 MAXRESULTS 1000");
    } catch (FMSException ex) {
      throw new QBOAccountRetrievalFailedException(ex);
    }
  }

  public QBOClass create(String name, Long companyId) {
    var qboClazz = new Class();
    qboClazz.setName(name);

    return QBOClass.fromQboEntity(
        qboService.withDataService(
            companyId,
            (dataService -> {
              try {
                return dataService.add(qboClazz);
              } catch (FMSException e) {
                throw new QBOAddFailedException(e);
              }
            })));
  }
}
