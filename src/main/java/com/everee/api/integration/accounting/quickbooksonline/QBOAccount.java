package com.everee.api.integration.accounting.quickbooksonline;

import com.everee.api.money.Money;
import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.data.Account;
import lombok.Data;

@Data
public class QBOAccount {
  String id;
  String name;
  String fullyQualifiedName;
  Money currentBalance;
  Money currentBalanceWithSubAccounts;

  static <E extends IEntity> QBOAccount fromQboEntity(E entity) {
    var qboAccount = (Account) entity;

    var account = new QBOAccount();
    account.setId(qboAccount.getId());
    account.setName(qboAccount.getName());
    account.setFullyQualifiedName(qboAccount.getFullyQualifiedName());
    account.setCurrentBalance(Money.valueOf(qboAccount.getCurrentBalance()));
    account.setCurrentBalanceWithSubAccounts(
        Money.valueOf(qboAccount.getCurrentBalanceWithSubAccounts()));

    return account;
  }
}
