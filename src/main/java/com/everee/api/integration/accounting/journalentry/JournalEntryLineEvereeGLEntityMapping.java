package com.everee.api.integration.accounting.journalentry;

import com.everee.api.taxauthority.qbo.CompanyQboCustomerMapping;
import com.everee.api.taxauthority.qbo.TaxAuthorityQboVendorMapping;
import javax.persistence.Embeddable;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import lombok.Data;
import lombok.experimental.Accessors;

@Embeddable
@Data
@Accessors(chain = true)
public class JournalEntryLineEvereeGLEntityMapping {

  private Long companyCustomerMappingId;
  private Long taxAuthorityVendorMappingId;

  @OneToOne
  @JoinColumn(name = "companyCustomerMappingId", insertable = false, updatable = false)
  private CompanyQboCustomerMapping companyCustomerMapping;

  public JournalEntryLineEvereeGLEntityMapping setCompanyCustomerMapping(
      CompanyQboCustomerMapping companyCustomerMapping) {
    this.companyCustomerMappingId = companyCustomerMapping.getId();
    this.companyCustomerMapping = companyCustomerMapping;
    return this;
  }

  @OneToOne
  @JoinColumn(name = "taxAuthorityVendorMappingId", insertable = false, updatable = false)
  private TaxAuthorityQboVendorMapping taxAuthorityVendorMapping;

  public JournalEntryLineEvereeGLEntityMapping setTaxAuthorityVendorMapping(
      TaxAuthorityQboVendorMapping taxAuthorityVendorMapping) {
    this.taxAuthorityVendorMappingId = taxAuthorityVendorMapping.getId();
    this.taxAuthorityVendorMapping = taxAuthorityVendorMapping;
    return this;
  }

  public String getEntityIdentifier() {
    if (taxAuthorityVendorMapping != null) {
      return taxAuthorityVendorMapping.getVendorIdentifier();
    } else if (companyCustomerMapping != null) {
      return companyCustomerMapping.getCustomerIdentifier();
    } else {
      return null;
    }
  }

  public String getEntityName() {
    if (taxAuthorityVendorMapping != null) {
      return taxAuthorityVendorMapping.getVendorName();
    } else if (companyCustomerMapping != null) {
      return companyCustomerMapping.getCustomerName();
    } else {
      return null;
    }
  }

  public EntityType getEntityType() {
    if (taxAuthorityVendorMapping != null) {
      return taxAuthorityVendorMapping.getEntityType();
    } else if (companyCustomerMapping != null) {
      return companyCustomerMapping.getEntityType();
    } else {
      return null;
    }
  }
}
