package com.everee.api.integration.salesforce;

import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Slf4j
public abstract class SalesforceService<T extends SFObject> {

  protected SalesforceProperties properties;
  private RestTemplate restTemplate;
  private AuthResponse authResponse;

  public SalesforceService(SalesforceProperties properties, RestTemplate restTemplate) {
    this.properties = properties;
    this.restTemplate = restTemplate;
  }

  public abstract String getQueryById(String id);

  public abstract String getObjectPath(String id);

  public abstract ParameterizedTypeReference<QueryResponse<T>> getParameterizedTypeReference();

  public QueryResponse<T> findById(String id) {
    authenticate();
    var url =
        authResponse.getInstanceUrl()
            + properties.getRestBasePath()
            + properties.getRestVersionPath()
            + properties.getRestQueryPath()
            + "?q="
            + getQueryById(id);

    var request = new HttpEntity<>(Map.of(), getHttpHeaders());
    var responseEntity =
        restTemplate.exchange(url, HttpMethod.GET, request, getParameterizedTypeReference());
    return responseEntity.getBody();
  }

  public List<T> findByQuery(String query) {
    authenticate();
    var url =
        authResponse.getInstanceUrl()
            + properties.getRestBasePath()
            + properties.getRestVersionPath()
            + properties.getRestQueryPath()
            + "?q="
            + query;

    var request = new HttpEntity<>(Map.of(), getHttpHeaders());
    var responseEntity =
        restTemplate.exchange(url, HttpMethod.GET, request, getParameterizedTypeReference());
    var body = responseEntity.getBody();
    return body.getRecords();
  }

  public T getById(String id) {
    return findById(id).getRecords().stream().findFirst().orElseThrow();
  }

  public void update(T obj) {
    authenticate();
    var url =
        authResponse.getInstanceUrl()
            + properties.getRestBasePath()
            + properties.getRestVersionPath()
            + getObjectPath(obj.getId());

    var request = new HttpEntity<>(obj.clone().setId(null), getHttpHeaders());
    var response =
        restTemplate.exchange(url, HttpMethod.PATCH, request, (Class<Object>) null, Map.of());
    log.info("SF Response" + response);
  }

  protected HttpHeaders getHttpHeaders() {
    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("Authorization", "Bearer " + authResponse.getAccessToken());
    return headers;
  }

  public AuthResponse authenticate() {
    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

    var params = new LinkedMultiValueMap<String, String>();
    params.add("client_secret", properties.getClientSecret());
    params.add("client_id", properties.getClientId());
    params.add("grant_type", properties.getGrantType());

    var request = new HttpEntity<MultiValueMap<String, String>>(params, headers);
    var response = restTemplate.postForEntity(properties.getAuthUrl(), request, AuthResponse.class);
    this.authResponse = response.getBody();
    return authResponse;
  }
}
