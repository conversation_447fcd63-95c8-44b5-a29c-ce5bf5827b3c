package com.everee.api.integration.salesforce;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class Opportunity extends SFObject {

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("Price_PEPM__c")
  private BigDecimal pricePEPM;

  @JsonProperty("Price_PCPM__c")
  private BigDecimal pricePCPM;

  @JsonProperty("Price_Unit_Based__c")
  private BigDecimal priceUnitBased;

  @JsonProperty("Credit_Supported_Fee_Interest__c")
  private BigDecimal creditSupportedFeeInterest;

  ///

  @JsonProperty("Num_Employees_Paid_MTD__c")
  private Integer numEmployeesPaidMTD;

  @JsonProperty("Num_Contractors_Paid_MTD__c")
  private Integer numContractorsPaidMTD;

  @JsonProperty("Num_Payments_MTD__c")
  private Integer numPaymentsMTD;

  @JsonProperty("Total_Credit_Payments_MTD__c")
  private BigDecimal totalCreditPaymentsMTD;

  ///

  @JsonProperty("Num_Employees_Paid_Last_Month__c")
  private Integer numEmployeesPaidLastMonth;

  @JsonProperty("Num_Contractors_Paid_Last_Month__c")
  private Integer numContractorsPaidLastMonth;

  @JsonProperty("Num_Payments_Last_Month__c")
  private Integer numPaymentsLastMonth;

  @JsonProperty("Total_Credit_Payments_Last_Month__c")
  private BigDecimal totalCreditPaymentsLastMonth;

  ///

  @JsonProperty("Num_Employees_Paid_R30__c")
  private Integer numEmployeesPaidR30;

  @JsonProperty("Num_Contractors_Paid_R30__c")
  private Integer numContractorsPaidR30;

  @JsonProperty("Num_Payments_R30__c")
  private Integer numPaymentsR30;

  @JsonProperty("Total_Credit_Payments_R30__c")
  private BigDecimal totalCreditPaymentsR30;

  public void zero() {
    pricePEPM = BigDecimal.ZERO;
    pricePCPM = BigDecimal.ZERO;
    priceUnitBased = BigDecimal.ZERO;
    creditSupportedFeeInterest = BigDecimal.ZERO;

    numEmployeesPaidMTD = Integer.valueOf(0);
    numContractorsPaidMTD = Integer.valueOf(0);
    numPaymentsMTD = Integer.valueOf(0);
    totalCreditPaymentsMTD = BigDecimal.ZERO;

    numEmployeesPaidLastMonth = Integer.valueOf(0);
    numContractorsPaidLastMonth = Integer.valueOf(0);
    numPaymentsLastMonth = Integer.valueOf(0);
    totalCreditPaymentsLastMonth = BigDecimal.ZERO;

    numEmployeesPaidR30 = Integer.valueOf(0);
    numContractorsPaidR30 = Integer.valueOf(0);
    numPaymentsR30 = Integer.valueOf(0);
    totalCreditPaymentsR30 = BigDecimal.ZERO;
  }
}
