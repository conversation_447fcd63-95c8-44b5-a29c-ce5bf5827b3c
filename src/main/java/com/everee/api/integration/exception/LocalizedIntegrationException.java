package com.everee.api.integration.exception;

import static com.everee.api.i18n.MessageSourceHolder.getMessageSource;

import org.springframework.context.support.MessageSourceAccessor;

public class LocalizedIntegrationException extends IntegrationException {
  public LocalizedIntegrationException(String localizationCode, Object... args) {
    super(new MessageSourceAccessor(getMessageSource()).getMessage(localizationCode, args));
  }

  public LocalizedIntegrationException(String localizationCode, Throwable cause, Object... args) {
    super(new MessageSourceAccessor(getMessageSource()).getMessage(localizationCode, args), cause);
  }
}
