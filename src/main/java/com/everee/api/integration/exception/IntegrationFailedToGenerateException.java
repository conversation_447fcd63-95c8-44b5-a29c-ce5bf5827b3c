package com.everee.api.integration.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class IntegrationFailedToGenerateException extends IntegrationException {

  public IntegrationFailedToGenerateException(String message) {
    super(message);
  }

  public IntegrationFailedToGenerateException(String message, Throwable t) {
    super(message, t);
  }
}
