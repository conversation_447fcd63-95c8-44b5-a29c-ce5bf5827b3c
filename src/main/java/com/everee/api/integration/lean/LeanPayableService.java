package com.everee.api.integration.lean;

import com.everee.api.earnings.EarningType;
import com.everee.api.integration.lean.io.LeanGigParams;
import com.everee.api.integration.lean.io.LeanSpecialPaymentParams;
import com.everee.api.integration.lean.io.LeanSpecialPaymentResponse;
import com.everee.api.money.Money;
import com.everee.api.money.MoneyDTO;
import com.everee.api.payable.PayablePaymentRequestService;
import com.everee.api.payable.PayableService;
import com.everee.api.payable.lookup.PayableLookup;
import com.everee.api.payable.lookup.PayableLookupService;
import com.everee.api.payable.model.CreatePayableDTO;
import com.everee.api.payable.model.CreatePayablePaymentRequest;
import com.everee.api.payable.model.PayableModel;
import com.everee.api.util.BooleanApiParam;
import com.everee.api.util.SetUtils;
import java.time.Instant;
import java.util.Currency;
import java.util.Set;
import javax.annotation.Nullable;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class LeanPayableService {
  private final PayableService payableService;
  private final PayableLookupService payableLookupService;
  private final PayablePaymentRequestService payablePaymentRequestService;

  private static final String ID_PREFIX_SPECIAL_PAYMENT = "lean_special_payment_";
  private static final String ID_PREFIX_GIG = "lean_gig_";

  @Transactional
  public void processSpecialPaymentPayable(
      @NonNull Long companyId, @NonNull LeanSpecialPaymentParams params) {
    var payableParams = new CreatePayableDTO();
    payableParams.setEarningTimestamp(Instant.now());
    payableParams.setEarningType(EarningType.CONTRACTOR);
    payableParams.setPayableModel(PayableModel.PRE_CALCULATED);
    payableParams.setVerified(true);
    payableParams.setExternalWorkerId(params.getPartnerUserId());
    payableParams.setLabel(params.getDescription());
    payableParams.setType(mapType(params.getType()));
    payableParams.setExternalId(mapExternalId(params));
    payableParams.setEarningAmount(mapAmount(params.getAmount()));

    payableService.create(payableParams, companyId);

    var createRequest = new CreatePayablePaymentRequest();
    createRequest.setExternalWorkerIds(Set.of(payableParams.getExternalWorkerId()));

    payablePaymentRequestService.createPayablePaymentRequest(companyId, createRequest);
  }

  @Transactional
  public void processGigPayable(@NonNull Long companyId, @NonNull LeanGigParams params) {
    var payableParams = new CreatePayableDTO();
    payableParams.setEarningTimestamp(Instant.now());
    payableParams.setEarningType(EarningType.CONTRACTOR);
    payableParams.setPayableModel(PayableModel.PRE_CALCULATED);
    payableParams.setVerified(true);
    payableParams.setExternalWorkerId(params.getPartnerUserId());
    payableParams.setLabel(params.getDescription());
    payableParams.setType(mapType(params.getType()));
    payableParams.setExternalId(mapExternalId(params));
    payableParams.setEarningAmount(mapAmount(params.getTotalAmount()));

    payableService.create(payableParams, companyId);

    var createRequest = new CreatePayablePaymentRequest();
    createRequest.setExternalWorkerIds(Set.of(payableParams.getExternalWorkerId()));

    payablePaymentRequestService.createPayablePaymentRequest(companyId, createRequest);
  }

  @Transactional(readOnly = true)
  public Page<LeanSpecialPaymentResponse> listSpecialPaymentPayables(
      @Nullable String partnerUserId, @Nullable String status, @NonNull Pageable pageable) {
    var paidStatus = mapStatus(status);

    var lookup =
        new PayableLookup()
            .setExternalWorkerIds(SetUtils.toSetOrNull(partnerUserId))
            .setPaid(paidStatus);

    return payableLookupService
        .listAll(lookup, pageable)
        .map(
            payable -> {
              var specialPayment = new LeanSpecialPaymentResponse();
              specialPayment.setSpecialPaymentId(
                  payable.getExternalId().replace(ID_PREFIX_SPECIAL_PAYMENT, ""));
              specialPayment.setType(payable.getExternalType());
              specialPayment.setDescription(payable.getLabel());
              specialPayment.setAmount(payable.getEarningAmount().getAmount().toPlainString());
              specialPayment.setPartnerUserId(payable.getExternalWorkerId());
              return specialPayment;
            });
  }

  private BooleanApiParam mapStatus(@Nullable String status) {
    if (status == null) return BooleanApiParam.UNSPECIFIED;

    switch (status) {
      case "PENDING":
        return BooleanApiParam.from(false);

      case "SUCCESS":
        return BooleanApiParam.from(true);

      default:
        return BooleanApiParam.UNSPECIFIED;
    }
  }

  private String mapType(String type) {
    return type;
  }

  private String mapExternalId(LeanSpecialPaymentParams params) {
    return String.format("%s%s", ID_PREFIX_SPECIAL_PAYMENT, params.getSpecialPaymentId());
  }

  private String mapExternalId(LeanGigParams params) {
    return String.format("%s%s", ID_PREFIX_GIG, params.getGigId());
  }

  private MoneyDTO mapAmount(Money amount) {
    return new MoneyDTO().setAmount(amount.getAmount()).setCurrency(Currency.getInstance("USD"));
  }
}
