package com.everee.api.integration.tabapay.model;

import com.everee.api.model.BaseModelV2;
import com.everee.api.util.CardUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.util.StringUtils;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class TabapayAccount extends BaseModelV2<TabapayAccount> {
  @NotNull @Column private Long userId;

  @NotNull
  @Enumerated(EnumType.STRING)
  private TabapayAccountType accountType;

  @Column private String accountId;

  @Column private String cardLast4;

  @Column private String cardExpirationDate;

  @Column private Boolean cardPushEnabled;

  @Column private String cardNetwork;

  @Column private String cardType;

  @Column private String cardPushAvailability;

  @Column private Boolean cardRegulated;

  @Column private String cardCurrency;

  @Column private String cardCountry;

  @Column private boolean depositsBlocked;

  @JsonIgnore
  public boolean isSameDayDepositAvailable() {
    return accountType == TabapayAccountType.CARD && StringUtils.hasText(accountId);
  }

  public TabapayAccount setCardLast4(String cardLast4) {
    this.cardLast4 = CardUtil.shortenToLast4(cardLast4);
    return this;
  }

  public String getCardLast4() {
    return CardUtil.shortenToLast4(this.cardLast4);
  }
}
