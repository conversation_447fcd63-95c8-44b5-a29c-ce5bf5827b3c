package com.everee.api.integration.tabapay.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RetrieveTransactionResponse extends BaseTabapayResponse {
  @JsonProperty("transactionID")
  private String transactionId;

  @JsonProperty("referenceID")
  private String referenceId;

  private String network;

  @JsonProperty("networkRC")
  private String networkRc;

  @JsonProperty("networkId")
  private String networkId;

  private String status;

  private String approvalCode;

  private String amount;

  private String last4;

  private FeeInfo fees;
}
