package com.everee.api.integration.tabapay.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class TransactionCreateResponse extends BaseTabapayResponse {
  @JsonProperty("transactionID")
  private String transactionId;

  private String network;

  @JsonProperty("networkRC")
  private String networkRc;

  @JsonProperty("networkId")
  private String networkId;

  private String status;

  private String approvalCode;

  private List<String> errors;

  private FeeInfo fees;

  private CardInfo card;
}
