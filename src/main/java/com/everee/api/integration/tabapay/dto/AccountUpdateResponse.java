package com.everee.api.integration.tabapay.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountUpdateResponse extends BaseTabapayResponse {

  @JsonAlias("duplicateAccountIDs")
  private List<String> dupplicateAccountIds;
}
