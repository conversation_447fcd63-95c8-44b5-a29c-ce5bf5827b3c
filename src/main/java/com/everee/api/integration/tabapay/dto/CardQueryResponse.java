package com.everee.api.integration.tabapay.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardQueryResponse extends BaseTabapayResponse {
  private CardAttributes card;
  private AVSInfo AVS;
  private FeeAttributes fees;

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CardAttributes {
    private CardTypeAttributes pull;
    private CardTypeAttributes push;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CardTypeAttributes {
    private boolean enabled;
    private String network;
    private String type;
    private String availability;
    private boolean regulated;
    private String currency;
    private String country;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class AVSInfo {
    private String networkRC;
    private String authorizeID;
    private String resultText;
    private String codeAVS;
    private String codeSecurityCode;
    private String EC;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class FeeAttributes {
    private FeeInfo pull;
    private FeeInfo push;
  }
}
