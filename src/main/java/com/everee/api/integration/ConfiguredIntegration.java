package com.everee.api.integration;

import com.everee.api.model.BaseModel;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ConfiguredIntegration extends BaseModel {
  @NotNull private Long companyId;

  @NotNull
  @Enumerated(EnumType.STRING)
  private SupportedIntegration integration;

  @NotNull
  @Enumerated(EnumType.STRING)
  private IntegrationHealth health;

  private LocalDateTime disabledAt;
}
