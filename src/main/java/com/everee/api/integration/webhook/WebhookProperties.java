package com.everee.api.integration.webhook;

import java.net.URI;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "app.webhook")
public class WebhookProperties {
  @NotNull private boolean deliveryEnabled;
  private URI overrideTargetUrl;
  private String overrideSigningSecretKey;
}
