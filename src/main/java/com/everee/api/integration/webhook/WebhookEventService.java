package com.everee.api.integration.webhook;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;

import com.everee.api.company.CompanyService;
import com.everee.api.event.ResourceEvent;
import com.everee.api.integration.webhook.entity.WebhookEvent;
import com.everee.api.integration.webhook.entity.WebhookEventRepository;
import com.everee.api.integration.webhook.entity.WebhookEventStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.ZonedDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class WebhookEventService {
  private final CompanyService companyService;
  private final ObjectMapper objectMapper;
  private final WebhookEventDeliveryService webhookEventDeliveryService;
  private final WebhookEventRepository webhookEventRepository;

  /**
   * Reschedules events for another delivery attempt once their latest delivery-processing window
   * has expired. This is a "sweep" operation that should be called on a regular basis to ensure no
   * events are stuck in a non-terminal state.
   *
   * @param reschedulingTimestamp the maximum time of the latest delivery attempt
   */
  @Transactional
  public void rescheduleDesynchronizedEvents(ZonedDateTime reschedulingTimestamp) {
    webhookEventRepository.rescheduleDesynchronizedEvents(
        WebhookEventStatus.DELIVERY_ATTEMPT_IN_PROGRESS,
        WebhookEventStatus.DELIVERY_SCHEDULED,
        reschedulingTimestamp);
  }

  @Transactional
  public <T extends ResourceEvent & WebhookEventSerializable> void scheduleEventForDelivery(T event)
      throws JsonProcessingException {
    var company = companyService.getCompany(event.getCompanyId());

    var webhookEvent = new WebhookEvent();
    webhookEvent.setId(event.getId());
    webhookEvent.setCompanyId(event.getCompanyId());
    webhookEvent.setEventType(event.getEventType());
    webhookEvent.setResourceId(event.getResourceId());
    webhookEvent.setResourceName(event.getResourceName());
    webhookEvent.setActionName(event.getActionName());
    webhookEvent.setTimestamp(ZonedDateTime.ofInstant(event.getInstant(), UTC_ZONE_ID));

    // Events are marked dead upon creation when webhook delivery is disabled. This prevents a
    // massive buffer of events from hammering the delivery system when webhooks are enabled for
    // an existing customer account.
    var initialStatus =
        company.getWebhookSettings().isDeliveryEnabled()
            ? WebhookEventStatus.DELIVERY_SCHEDULED
            : WebhookEventStatus.DEAD;
    webhookEvent.setStatus(initialStatus);

    var nextDeliveryAttemptAt =
        webhookEventDeliveryService.getNextAttemptTimestamp(0, webhookEvent.getTimestamp());
    webhookEvent.setNextDeliveryAttemptAt(nextDeliveryAttemptAt);

    var serializedData = serializeEvent(webhookEvent, event.getWebhookEventPayload());
    webhookEvent.setData(serializedData);

    webhookEventRepository.save(webhookEvent);
  }

  private byte[] serializeEvent(WebhookEvent event, WebhookEventPayload payload)
      throws JsonProcessingException {
    var envelope = buildEnvelope(event, payload);
    return objectMapper.writeValueAsBytes(envelope);
  }

  private WebhookEventEnvelope buildEnvelope(WebhookEvent event, WebhookEventPayload subject) {
    var payload = new WebhookEventEnvelope();
    payload.setId(event.getId());
    payload.setCompanyId(event.getCompanyId());
    payload.setVersion("1");
    payload.setType(event.getEventType());
    payload.setTimestamp(event.getTimestamp().toInstant().getEpochSecond());
    payload.setData(new WebhookEventPayloadData(subject));
    return payload;
  }
}
