package com.everee.api.caffiene;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
public class CaffeineCacheConfig {

    @Value("${app.caffeine-qbo-cache-duration-in-minutes}")
    private Long duration;

    @Value("${app.max-qbo-caffeine-cache-size}")
    private Long maxCacheSize;

    @Bean
    public CaffeineCache QBOCache() {
        return new CaffeineCache(CacheNameKeyHelper.QBO,
                Caffeine.newBuilder()
                        .expireAfterWrite(duration, TimeUnit.MINUTES)
                        .maximumSize(maxCacheSize)
                        .build());
    }
}
