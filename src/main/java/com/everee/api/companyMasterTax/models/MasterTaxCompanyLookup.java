package com.everee.api.companyMasterTax.models;

import com.everee.api.config.RequestParams;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Data
@Accessors(chain = true)
@RequestParams
public class MasterTaxCompanyLookup {

    @ApiParam(name = "q")
    private String q;

    @ApiParam(name = "demo-company")
    private Boolean demoCompany;

    @ApiParam(name = "start-date")
    private  LocalDate startDate;

    @ApiParam(name = "end-date")
    private  LocalDate endDate;

}
