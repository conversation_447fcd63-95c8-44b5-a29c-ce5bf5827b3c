package com.everee.api.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.PropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CustomJacksonFilterProvider extends SimpleFilterProvider {

  private final ObjectMapper objectMapper;

  @Autowired(required = false)
  public void register(PropertyFilter[] propertyFilters) {
    Arrays.stream(propertyFilters)
        .forEach(
            propertyFilter -> addFilter(propertyFilter.getClass().getSimpleName(), propertyFilter));
    objectMapper.setFilterProvider(this);
  }
}
