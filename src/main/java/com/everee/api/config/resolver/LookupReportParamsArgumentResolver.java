package com.everee.api.config.resolver;

import com.everee.api.config.LookupReportRequestParams;
import com.everee.api.lookup.Lookup;
import com.everee.api.report.generator.ReportType;
import java.lang.reflect.InvocationTargetException;
import java.time.format.DateTimeParseException;
import javax.validation.Validator;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

public class LookupReportParamsArgumentResolver extends RequestParamsArgumentResolver {

  public LookupReportParamsArgumentResolver(Validator validator) {
    super(validator);
  }

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return parameter.getParameterType().getAnnotation(LookupReportRequestParams.class) != null;
  }

  @Override
  public Object resolveArgument(
      MethodParameter parameter,
      ModelAndViewContainer mavContainer,
      NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory)
      throws IllegalArgumentException, DateTimeParseException, NoSuchMethodException,
          IllegalAccessException, InvocationTargetException, InstantiationException {

    var returnType = parameter.getParameterType();
    if (returnType.isInterface() && returnType.isAssignableFrom(Lookup.class)) {
      var reportType = ReportType.valueOf(webRequest.getParameterValues("type")[0]);
      returnType = reportType.getLookupClass();
    }
    return super.resolveArgument(returnType, parameter, mavContainer, webRequest, binderFactory);
  }
}
