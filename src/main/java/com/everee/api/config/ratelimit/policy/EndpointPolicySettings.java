package com.everee.api.config.ratelimit.policy;

import com.everee.api.util.ObjectUtils;
import java.time.Duration;
import lombok.Getter;
import org.springframework.util.StringUtils;

public class EndpointPolicySettings {
  @Getter String path;
  @Getter Integer bucketCapacity;
  Integer windowSeconds;

  public EndpointPolicySettings(String value) {
    if (StringUtils.isEmpty(value)) return;

    var parts = value.split(":");

    if (parts.length != 3) return;

    this.path = parts[0];

    try {
      this.bucketCapacity = Integer.parseInt(parts[1]);
    } catch (Exception ignored) {
    }

    try {
      this.windowSeconds = Integer.parseInt(parts[2]);
    } catch (Exception ignored) {
    }
  }

  public Duration getWindow() {
    return Duration.ofSeconds(windowSeconds);
  }

  public boolean isValid() {
    return ObjectUtils.allPresent(path, bucketCapacity, windowSeconds);
  }
}
