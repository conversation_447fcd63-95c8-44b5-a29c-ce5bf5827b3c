package com.everee.api.employeePosition;

import com.everee.api.employee.CoreEmployee;
import com.everee.api.employee.EmploymentStatus;
import com.everee.api.model.BaseModelV2;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.phase.Phased;
import com.everee.api.workerscomp.WorkersCompClass;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Optional;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Accessors(chain = true)
@Entity
@ToString
@EqualsAndHashCode(callSuper = true)
public class EmployeePosition extends BaseModelV2 implements Phased {
  private static final String FULL_TIME_HOURS_THRESHOLD_FORMULA =
      "(select c.fulltimeweeklyhoursthreshold from company c where c.id = companyid)";

  public static long WORKING_HOURS_PER_YEAR = 2080;
  public static int SALARY_WEEKLY_HOURS = 40;

  static long WEEKS_PER_YEAR = 52;

  @Setter(AccessLevel.NONE)
  @JsonIgnore
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "employeeId", insertable = false, updatable = false)
  private CoreEmployee employee;

  private Long employeeId;

  private String title;

  @NotNull private Money payRate;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "wagetype")
  private PayType payType;

  @NotNull private Boolean eligibleForOvertime;

  @NotNull private Integer expectedWeeklyHours = 0;

  @NotNull private Long companyId;

  @Setter(AccessLevel.NONE)
  @Formula(FULL_TIME_HOURS_THRESHOLD_FORMULA)
  @JsonIgnore
  private Integer fullTimeWeeklyHoursThreshold;

  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @ManyToOne
  @JoinColumn(name = "workersCompClassId", insertable = false, updatable = false)
  private WorkersCompClass workersCompClass;

  private Long workersCompClassId;

  @NotNull private LocalDate startDate;

  private LocalDate endDate;

  private Long workLocationId;

  private Long workerRoleId;

  // TODO: we can remove this when we support overtime for salary workers
  public Boolean getEligibleForOvertime() {
    return payType == PayType.HOURLY && eligibleForOvertime;
  }

  @Deprecated
  public PayType getWageType() {
    return payType;
  }

  @SuppressWarnings("unused")
  public EmploymentStatus getEmploymentStatus() {
    if (fullTimeWeeklyHoursThreshold == null) {
      return null;
    }

    if (expectedWeeklyHours.compareTo(fullTimeWeeklyHoursThreshold) >= 0) {
      return EmploymentStatus.FULL_TIME;
    }

    return EmploymentStatus.PART_TIME;
  }

  public Money getHourlyRate() {
    switch (payType) {
      case HOURLY:
        return payRate;

      case SALARY:
        var hoursPerYear = new BigDecimal(WORKING_HOURS_PER_YEAR);
        return Optional.of(payRate)
            .map(Money::getAmount)
            .map(amount -> amount.divide(hoursPerYear, 2, RoundingMode.HALF_EVEN))
            .map(Money::valueOf)
            .orElse(Money.ZERO);
      default:
        return Money.ZERO;
    }
  }

  public Money getAnnualSalary() {
    switch (payType) {
      case SALARY:
        return payRate;
      case HOURLY:
        return payRate.times(expectedWeeklyHours * WEEKS_PER_YEAR);
      default:
        return Money.ZERO;
    }
  }
}
