package com.everee.api.tax.filing.form;

import com.everee.api.annuals.FilingType;
import com.everee.api.annuals.GeneratedFilingStorageService;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.model.TransactionHandler;
import com.everee.api.query.update.UpdateProperty;
import com.everee.api.tax.filing.file.*;
import com.everee.api.tax.filing.form.lookup.CompanyTaxFormFilingInstanceLookup;
import com.everee.api.tax.filing.form.lookup.CompanyTaxFormFilingInstanceLookupService;
import com.everee.api.tax.filing.generate.CompanyTaxFilingInstanceGenerator;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionArchivedEvent;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionDeactivatedEvent;
import com.everee.api.tax.jurisdictions.lookup.CompanyTaxJurisdictionLookup;
import com.everee.api.tax.jurisdictions.lookup.CompanyTaxJurisdictionLookupService;
import com.everee.api.taxaccumulator.TaxFilingType;
import com.everee.api.util.BooleanApiParam;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyTaxFormFilingInstanceService {

  private final CompanyTaxFormFilingInstanceRepository companyTaxFormFilingInstanceRepository;
  private final CompanyTaxFormFilingInstanceFileRepository
      companyTaxFormFilingInstanceFileRepository;
  private final CompanyTaxFormFilingInstanceLookupService companyTaxFormFilingInstanceLookupService;
  private final CompanyTaxFilingInstanceGenerator companyTaxFilingInstanceGenerator;
  private final CompanyTaxJurisdictionLookupService companyTaxJurisdictionLookupService;
  private final CompanyTaxFormFilingInstanceFileService companyTaxFormFilingInstanceFileService;
  private final TaxFilingReportFileDAO taxFilingReportFileDAO;
  private final TransactionHandler transactionHandler;

  @Transactional
  public void delete(Long instanceId, Long userId) {
    var instance = get(instanceId);
    if (instance.getHasUserUploadedDocuments()) {
      instance.setDeletedAt(LocalDateTime.now());
      instance.setDeletedBy(userId);
      companyTaxFormFilingInstanceRepository.save(instance);
    } else {
      companyTaxFormFilingInstanceRepository.delete(instance);
    }
  }

  @Transactional
  public CompanyTaxFormFilingInstance update(
      Long instanceId, CompanyTaxFormFilingInstance request, Long userId) {
    var instance = get(instanceId);
    instance.setNotes(request.getNotes());

    boolean filingStatusChanged = !Objects.equals(instance.getFiledAt(), request.getFiledAt());
    if (filingStatusChanged) {
      instance.setFiledAt(request.getFiledAt());
      instance.setFiledBy(request.getFiledAt() == null ? null : userId);
    }
    instance.setPaymentOpsAmount(request.getPaymentOpsAmount());

    instance = companyTaxFormFilingInstanceRepository.save(instance);
    if (filingStatusChanged) {
      var documentMetadata = getDocumentMetadata(instance);
      var visible = instance.getFiledAt() != null;
      transactionHandler.executeInNewTransaction(
          () -> setDocumentVisibility(instanceId, documentMetadata, visible));
    }
    return instance;
  }

  @Transactional
  public CompanyTaxFormFilingInstance setDocumentVisibility(Long instanceId, boolean visible) {
    var instance = get(instanceId);
    setDocumentVisibility(instanceId, getDocumentMetadata(instance), visible);
    return instance;
  }

  @Transactional
  public CompanyTaxFormFilingInstance setNotTemporary(Long instanceId) {
    return companyTaxFormFilingInstanceRepository.save(get(instanceId).setIsTemporary(false));
  }

  private CompanyTaxFormFilingInstance get(Long id) {
    return companyTaxFormFilingInstanceRepository
        .findById(id)
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    "Company Tax Form Filing Instance not found with id " + id));
  }

  public List<TaxFilingReportFileDTO> getW2ReportTaxFormFilingInstanceFiles(Long companyId) {
    return taxFilingReportFileDAO.findAllW2ReportFilesForCompany(companyId);
  }

  public List<TaxFilingReportFileDTO> get1099ReportTaxFormFilingInstanceFiles(Long companyId) {
    return taxFilingReportFileDAO.findAll1099ReportFilesForCompany(companyId);
  }

  @EventListener
  @Transactional
  public void handleEvent(TaxFilingFormSavedEvent event) {
    log.debug("handleEvent(event: {})", event);

    try {
      var definition = event.getTaxFilingFormDefinition();
      var jurisdictionIds = definition.getJurisdictionIds();

      if (jurisdictionIds.isEmpty()) {
        return;
      }

      var hasEndDate = definition.getEndDate() != null;
      var contextDate = hasEndDate ? definition.getEndDate() : definition.getStartDate();

      var instancesToDelete =
          companyTaxFormFilingInstanceLookupService.listAll(
              new CompanyTaxFormFilingInstanceLookup()
                  .setTaxJurisdictionIds(new HashSet<>(jurisdictionIds))
                  .setFilingFrequencies(
                      Optional.ofNullable(definition.getForFilingFrequency())
                          .map(ff -> Set.of(ff))
                          .orElse(null))
                  .setMinFilingPeriodStartDate(contextDate)
                  .setIncludeFiled(BooleanApiParam.EXCLUDE_ALL)
                  .setHasUserUploadedDocuments(BooleanApiParam.EXCLUDE_ALL),
              Pageable.unpaged());

      companyTaxFormFilingInstanceRepository.deleteAll(instancesToDelete);

      var toRun = instancesToDelete.stream().collect(Collectors.groupingBy(this::getKey));

      // TODO: consider throwing a job on to do this rather than run it inline.
      // The problem with the job is the definition has to be saved before the jobs can start
      // running
      toRun.forEach(
          (key, instances) -> {
            var oldInstance = instances.iterator().next();
            var companyTaxJurisdictions =
                Optional.ofNullable(oldInstance.getCompanyTaxJurisdiction())
                    .map(ctj -> Set.of(ctj))
                    .orElseGet(
                        () ->
                            companyTaxJurisdictionLookupService
                                .listAll(
                                    new CompanyTaxJurisdictionLookup()
                                        .setTaxJurisdictionIds(new HashSet(jurisdictionIds))
                                        .setCompanyIds(Set.of(oldInstance.getCompanyId())),
                                    Pageable.unpaged())
                                .toSet());

            // Can happen when removing jurisdictions
            if (companyTaxJurisdictions.isEmpty()) {
              return;
            }

            // When setting an end date, send all the company tax jurisdictions through to get data
            // created
            // When creating/updating, just need to send 1 instance through as it will look up the
            // form definition and calculate things appropriately
            companyTaxJurisdictions =
                hasEndDate
                    ? companyTaxJurisdictions
                    : Set.of(companyTaxJurisdictions.iterator().next());

            companyTaxJurisdictions.forEach(
                companyTaxJurisdiction -> {
                  var newInstance =
                      companyTaxFilingInstanceGenerator.findOrCreateInSameTransaction(
                          companyTaxJurisdiction.getCompanyId(),
                          null,
                          companyTaxJurisdiction,
                          oldInstance.getFilingFrequency(),
                          oldInstance.getFilingPeriodStartDate(),
                          Boolean.TRUE.equals(oldInstance.getIsTemporary()));

                  companyTaxFilingInstanceGenerator.generateFilesInSameTransaction(
                      newInstance, null);
                });
          });
      var instancesByCompanyToDelete =
          instancesToDelete.getContent().stream()
              .collect(Collectors.groupingBy(CompanyTaxFormFilingInstance::getCompanyId));

      instancesByCompanyToDelete.forEach(
          (companyId, companyInstancesToDelete) ->
              companyTaxFormFilingInstanceFileService.deleteFilesForInstances(
                  companyId,
                  companyInstancesToDelete.stream()
                      .map(CompanyTaxFormFilingInstance::getId)
                      .collect(Collectors.toSet())));
    } catch (Exception e) {
      log.error("Exception in handleEvent(event: {})", event, e);
      throw e;
    }
  }

  @EventListener
  @Transactional
  public void handleEvent(CompanyTaxJurisdictionDeactivatedEvent event) {
    log.debug("handleEvent(event: {})", event);

    try {
      if (event == null
          || event.getCompanyId() == null
          || event.getCompanyTaxJurisdiction() == null
          || event.getCompanyTaxJurisdiction().getTaxJurisdictionId() == null) {
        throw new IllegalArgumentException("Event must have company and tax jurisdiction data.");
      }

      cleanupCompanyTaxFormFilingInstances(
          event.getCompanyId(), event.getCompanyTaxJurisdiction().getTaxJurisdictionId());
    } catch (Exception e) {
      log.error("Exception in handleEvent(event: {})", event, e);
      throw e;
    }
  }

  @EventListener
  @Transactional
  public void handleEvent(CompanyTaxJurisdictionArchivedEvent event) {
    log.debug("handleEvent(event: {})", event);

    try {
      if (event == null
          || event.getCompanyId() == null
          || event.getCompanyTaxJurisdiction() == null
          || event.getCompanyTaxJurisdiction().getTaxJurisdictionId() == null) {
        throw new IllegalArgumentException("Event must have company and tax jurisdiction data.");
      }

      cleanupCompanyTaxFormFilingInstances(
          event.getCompanyId(), event.getCompanyTaxJurisdiction().getTaxJurisdictionId());
    } catch (Exception e) {
      log.error("Exception in handleEvent(event: {})", event, e);
      throw e;
    }
  }

  private void cleanupCompanyTaxFormFilingInstances(Long companyId, String taxJurisdictionId) {
    var instancesToDelete =
        companyTaxFormFilingInstanceLookupService.listAll(
            new CompanyTaxFormFilingInstanceLookup()
                .setCompanyIds(Set.of(companyId))
                .setTaxJurisdictionIds(Set.of(taxJurisdictionId))
                .setIncludeFiled(BooleanApiParam.EXCLUDE_ALL)
                .setHasUserUploadedDocuments(BooleanApiParam.EXCLUDE_ALL),
            Pageable.unpaged());
    companyTaxFormFilingInstanceRepository.deleteAll(instancesToDelete);
  }

  public void cleanupCompanyTaxFormFilingInstancesBeforeDate(Long companyId, LocalDate date) {
    var instancesToCheck =
        companyTaxFormFilingInstanceLookupService.listAll(
            new CompanyTaxFormFilingInstanceLookup()
                .setCompanyIds(Set.of(companyId))
                .setIncludeFiled(BooleanApiParam.EXCLUDE_ALL),
            Pageable.unpaged());

    var instancesToDelete =
        instancesToCheck.stream()
            .filter(
                instance ->
                    instance
                        .getFilingFrequency()
                        .getDateRange(instance.getFilingPeriodStartDate())
                        .getEndDate()
                        .isBefore(date))
            .collect(Collectors.toList());

    companyTaxFormFilingInstanceRepository.deleteAll(instancesToDelete);
  }

  private String getKey(CompanyTaxFormFilingInstance instance) {
    return instance.getCompanyId()
        + "_"
        + instance.getFilingFrequency()
        + "_"
        + instance.getFilingPeriodStartDate().toString();
  }

  public void publishAndSetDocumentVisibility(
      List<Long> instanceIds, Optional<String> employeeDocMetadata, boolean publish) {
    if (CollectionUtils.isEmpty(instanceIds)) {
      return;
    }

    var publishDate = publish ? LocalDateTime.now() : null;
    companyTaxFormFilingInstanceLookupService.update(
        new CompanyTaxFormFilingInstanceLookup().setIds(new HashSet(instanceIds)),
        List.of(new UpdateProperty(CompanyTaxFormFilingInstance_.FILED_AT, publishDate)));
    companyTaxFormFilingInstanceFileRepository.publishCompanyDocuments(instanceIds, publishDate);

    employeeDocMetadata.ifPresent(
        documentMetadata ->
            companyTaxFormFilingInstanceFileRepository.publishEmployeeDocuments(
                instanceIds, documentMetadata, publishDate));
  }

  public void setDocumentVisibility(
      Long instanceId, Optional<String> employeeDocMetadata, boolean visible) {
    var publishDate = visible ? LocalDateTime.now() : null;
    companyTaxFormFilingInstanceFileRepository.publishCompanyDocuments(
        List.of(instanceId), publishDate);

    employeeDocMetadata.ifPresent(
        documentMetadata ->
            companyTaxFormFilingInstanceFileRepository.publishEmployeeDocuments(
                List.of(instanceId), documentMetadata, publishDate));
  }

  private Optional<String> getDocumentMetadata(CompanyTaxFormFilingInstance instance) {
    var isW2 =
        Optional.ofNullable(instance.getTaxFilingFormDefinition())
            .map(TaxFilingFormDefinition::getSystemTaxFilingFormType)
            .filter(taxFilingType -> taxFilingType == TaxFilingType.W2)
            .isPresent();

    if (isW2 && instance.getStateOverride() == null) {
      return Optional.of(
          GeneratedFilingStorageService.getDocumentMetadata(
              Year.of(instance.getFilingPeriodStartDate().getYear()), FilingType.W2));
    }

    return Optional.empty();
  }
}
