package com.everee.api.tax.filing.bulk;

import com.everee.api.config.PresignedInternalFileLocationSerializer;
import com.everee.api.tax.filing.bulk.file.BulkTaxFormFilingInstanceFile;
import com.everee.api.model.BaseModel;
import com.everee.api.tax.filing.form.CompanyTaxFormFilingInstance;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Formula;
import org.hibernate.annotations.Type;

/**
 * A convenience entity for working with a group of {@link CompanyTaxFormFilingInstance} entities. A
 * bulk tax filing instance is associated with one or more {@link CompanyTaxFormFilingInstance}s
 * based on a set of user-provided criteria.
 */
@Data
@Slf4j
@Entity
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class BulkTaxFormFilingInstance extends BaseModel<BulkTaxFormFilingInstance> {

  private static final String TOTAL_COUNT_FORMULA =
      "(select count(*) from bulkTaxFilingCompanySubmissionStatus c where c.bulktaxformfilinginstanceid = {alias}.id)";
  private static final String FILED_COUNT_FORMULA =
      "(select count(*) from bulkTaxFilingCompanySubmissionStatus b where b.bulktaxformfilinginstanceid = {alias}.id and b.status='SUCCESS')";
  private static final String UNFILED_COUNT_FORMULA =
      "(select count(*) from bulkTaxFilingCompanySubmissionStatus b where b.bulktaxformfilinginstanceid = {alias}.id and b.status='PENDING')";

  @Deprecated(
      forRemoval =
          true) // will be removed after the UI has been updated to get the name from the "files"
  // rather than from here
  @Formula(
      "(select f.filename from bulktaxformfilinginstancefile f where f.bulktaxformfilinginstanceid = {alias}.id limit 1)")
  private String fileName;

  @Deprecated(
      forRemoval =
          true) // will be removed after the UI has been updated to get the url from the "files"
  // rather than from here
  @JsonSerialize(using = PresignedInternalFileLocationSerializer.class)
  @Formula(
      "(select f.filelocation from bulktaxformfilinginstancefile f where f.bulktaxformfilinginstanceid = {alias}.id limit 1)")
  private String fileLocation;

  @NotNull
  @Enumerated(EnumType.STRING)
  private BulkTaxFilingGenerationStatus generationStatus;

  @Type(type = "json")
  @Column(columnDefinition = "json")
  private BulkTaxFilingGenerationParams generationParams;

  @NotNull private Long taxFilingFormDefinitionId;

  @Setter(AccessLevel.NONE)
  @Formula(TOTAL_COUNT_FORMULA)
  private long totalCount;

  @Setter(AccessLevel.NONE)
  @Formula(FILED_COUNT_FORMULA)
  private long filedCount;

  @Setter(AccessLevel.NONE)
  @Formula(UNFILED_COUNT_FORMULA)
  private long unfiledCount;

  @EqualsAndHashCode.Exclude
  @OneToMany(
      fetch = FetchType.EAGER,
      mappedBy = "bulkTaxFormFilingInstanceId",
      orphanRemoval = true,
      cascade = CascadeType.ALL)
  @OrderBy("createdAt")
  private List<BulkTaxFormFilingInstanceFile> files = new ArrayList<>();

  public BulkTaxFormFilingInstance(BulkTaxFilingGenerationParams generationParams) {
    this.generationParams = generationParams;
    this.generationStatus = BulkTaxFilingGenerationStatus.GENERATING;
  }
}
