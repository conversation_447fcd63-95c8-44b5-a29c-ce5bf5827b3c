package com.everee.api.tax.filing.contractor.fire.firefile;

import com.everee.api.excel.ExcelWriter;
import com.everee.api.money.Money;
import com.everee.api.tax.filing.contractor.fire.firerecord.AFireRecordRepository;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Optional;
import javax.annotation.WillClose;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class FireFileReportService {
  private static final List<String> FIRE_FILE_REPORT_HEADERS =
      List.of(
          "Payer's TIN",
          "Payer's name",
          "Payer's name 2",
          "Payer's address",
          "Payer's city",
          "Payer's state",
          "Payer's postal code",
          "Payer's country",
          "Payer's phone",
          "Recipient's TIN",
          "Recipient's name",
          "Recipient's address",
          "Recipient's city",
          "Recipient's state",
          "Recipients postal code",
          "Account number",
          "Year",
          "Corrected",
          "Nonemployee compensation (Box 1)",
          "Direct sales > $5000 (Box 2)",
          "Federal income tax withheld (Box 4)",
          "State tax withheld 1 (Box 5)",
          "State tax withheld 2 (Box 5)",
          "State Payer's no. 1 (Box 6)",
          "State Payer's no. 2 (Box 6)",
          "State income 1 (Box 7)",
          "State income 2 (Box 7)");

  private final AFireRecordRepository fireRecordRepository;

  @Transactional
  public void generateReport(@NonNull Long fireFileId, @WillClose OutputStream outputStream)
      throws IOException {
    try (var excelWriter = new ExcelWriter(outputStream)) {
      excelWriter.sheet("1099 records");
      FIRE_FILE_REPORT_HEADERS.forEach(excelWriter::writeCell);

      fireRecordRepository
          .streamFireFileReportData(fireFileId)
          .forEach(
              item -> {
                excelWriter.advanceRow();

                // Payer info
                excelWriter.writeCell(item.getIssuerTaxpayerIdentifier());
                excelWriter.writeCell(item.getFirstIssuerName());
                excelWriter.writeCell(item.getSecondIssuerName());
                excelWriter.writeCell(item.getIssuerShippingAddress());
                excelWriter.writeCell(item.getIssuerCity());
                excelWriter.writeCell(item.getIssuerState());
                excelWriter.writeCell(item.getIssuerZip());
                excelWriter.writeCell("United States of America");
                excelWriter.writeCell(item.getIssuerPhone());

                // Payee info
                excelWriter.writeCell(item.getPayeeTin());
                excelWriter.writeCell(item.getFirstPayeeName());
                excelWriter.writeCell(item.getPayeeMailingAddress());
                excelWriter.writeCell(item.getPayeeCity());
                excelWriter.writeCell(item.getPayeeState());
                excelWriter.writeCell(item.getPayeeZip());
                excelWriter.writeCell(item.getIssuerAccountNumberOfPayee());
                excelWriter.writeCell(item.getPaymentYear());
                excelWriter.writeCell(item.getCorrection());
                excelWriter.writeCell(convertToMoney(item.getPaymentAmount1()));

                // we don't have data for the rest of the columns yet
              });

      excelWriter.writeToStream();
    }
  }

  private static Money convertToMoney(Long value) {
    return Optional.ofNullable(value)
        .map(Long::doubleValue)
        .map(v -> v / 100)
        .map(Money::valueOf)
        .orElse(null);
  }
}
