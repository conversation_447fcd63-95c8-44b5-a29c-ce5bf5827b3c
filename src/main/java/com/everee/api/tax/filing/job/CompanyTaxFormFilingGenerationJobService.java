package com.everee.api.tax.filing.job;

import com.everee.api.tax.FilingFrequency;
import com.everee.api.tax.filing.job.lookup.CompanyTaxFormFilingGenerationJobLookup;
import com.everee.api.tax.filing.job.lookup.CompanyTaxFormFilingGenerationJobLookupService;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class CompanyTaxFormFilingGenerationJobService {

  private final CompanyTaxFormFilingGenerationJobDbService dbService;
  private final CompanyTaxFormFilingGenerationJobProducer producer;
  private final CompanyTaxFormFilingGenerationJobLookupService lookupService;

  public List<CompanyTaxFormFilingGenerationJob> createJobsIfNoneExist(
      LocalDate contextDate,
      FilingFrequency filingFrequency,
      Long createdByUserId,
      boolean temporary) {
    var filingFrequencyRange = filingFrequency.getDateRange(contextDate);
    var newJobsCount =
        lookupService.count(
            new CompanyTaxFormFilingGenerationJobLookup()
                .setFilingPeriodStartDate(filingFrequencyRange.getStartDate())
                .setFilingFrequencies(Set.of(filingFrequency))
                .setStatuses(Set.of(TaxFilingGenerationJobStatus.NEW)));

    if (newJobsCount > 0) return List.of();

    return createJobs(
        filingFrequencyRange.getEndDate().plusDays(1),
        Set.of(filingFrequency),
        createdByUserId,
        temporary);
  }

  public List<CompanyTaxFormFilingGenerationJob> createJobs(
      LocalDate contextDate,
      Set<FilingFrequency> filingFrequencies,
      Long createdByUserId,
      boolean temporary) {
    return createJobs(null, contextDate, filingFrequencies, createdByUserId, temporary);
  }

  public List<CompanyTaxFormFilingGenerationJob> createJobs(
      Set<Long> companyIds,
      LocalDate contextDate,
      Set<FilingFrequency> filingFrequencies,
      Long createdByUserId,
      boolean temporary) {
    var jobs =
        dbService.createJobs(
            companyIds, contextDate, filingFrequencies, createdByUserId, temporary);

    jobs.forEach(job -> producer.sendJobMessage(job.getId()));

    log.info("Created {} CompanyTaxFormFilingGenerationJobs", jobs.size());
    return jobs;
  }
}
