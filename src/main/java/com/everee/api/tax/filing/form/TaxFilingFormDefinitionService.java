package com.everee.api.tax.filing.form;

import static com.everee.api.storage.StorageAccess.EVEREE_INTERNAL;

import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.filing.form.lookup.TaxFilingFormDefinitionLookup;
import com.everee.api.tax.filing.form.lookup.TaxFilingFormDefinitionLookupService;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
@Service
@Slf4j
public class TaxFilingFormDefinitionService {
  public static final String TAX_FILINGS_PATH = "tax-filings";
  private final List<String> PATH_ELEMENTS = List.of(TAX_FILINGS_PATH, "form-definitions");

  private final TaxFilingFormDefinitionRepository repository;
  private final StorageService storageService;
  private final TaxFilingFormDefinitionLookupService lookupService;
  private final ApplicationEventPublisher eventPublisher;

  public TaxFilingFormDefinition getOne(Long id) {
    TaxFilingFormDefinition definition =
        lookupService.findOneOrThrow(new TaxFilingFormDefinitionLookup().setIds(Set.of(id)));
    addRawFileDownloadUrl(definition);
    return definition;
  }

  private void addRawFileDownloadUrl(TaxFilingFormDefinition definition) {
    var rawFileLocation = definition.getRawFileLocation();
    if (!StringUtils.isEmpty(rawFileLocation)) {
      definition.setRawFileDownloadUrl(
          storageService.getStoredFileLink(EVEREE_INTERNAL, rawFileLocation).getUrl().toString());
    }
  }

  @Transactional
  public void delete(Long id) {
    log.debug("delete(id: {})", id);
    var entity =
        repository
            .findById(id)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "Tax Filing Form Definition not found with id " + id));
    deleteFile(entity.getRawFileLocation());
    repository.delete(entity);
  }

  @Transactional
  public TaxFilingFormDefinition save(TaxFilingFormDefinition request) {
    log.debug("save(request: {})", request);
    request.setRawFileLocation(saveFile(request));
    request.setRawFileBytes(null);
    request = repository.save(request);

    eventPublisher.publishEvent(new TaxFilingFormSavedEvent(request));

    return request;
  }

  @Transactional
  public TaxFilingFormDefinition update(Long id, TaxFilingFormDefinition request) {
    log.debug("update(id: {}, request: {})", id, request);

    var entity = repository.findById(id).orElseThrow(ResourceNotFoundException::new);
    entity.setTaxFormName(request.getTaxFormName());
    entity.setDescription(request.getDescription());

    if (request.getRawFileBytes() == null && request.getRawFileLocation() == null) {
      // delete file from s3 if both rawFileBytes and rawFileLocation are null
      deleteFile(entity.getRawFileLocation());
      entity.setRawFileLocation(null);
    } else if (!StringUtils.isEmpty(request.getRawFileBytes())) {
      // save new file and delete old one
      var originalRawFileLocation = entity.getRawFileLocation();
      entity.setRawFileLocation(saveFile(request));
      deleteFile(originalRawFileLocation);
      request.setRawFileBytes(null);
    }

    entity.setFileUrl(request.getFileUrl());
    entity.setStartDate(request.getStartDate());
    entity.setEndDate(request.getEndDate());
    entity.setForFilingFrequency(request.getForFilingFrequency());
    entity.setSystemTaxFilingFormType(request.getSystemTaxFilingFormType());
    entity.setJurisdictionIds(request.getJurisdictionIds());
    entity.setNeedsDataByPaydate(request.getNeedsDataByPaydate());
    entity.setUpdatedAt(LocalDateTime.now());
    entity = repository.save(entity);

    eventPublisher.publishEvent(new TaxFilingFormSavedEvent(entity));

    return entity;
  }

  @Transactional
  public void deleteFile(String rawFileLocation) {
    if (!StringUtils.isEmpty(rawFileLocation)) {
      storageService.deleteFile(EVEREE_INTERNAL, rawFileLocation);
    }
  }

  @Transactional
  public String saveFile(TaxFilingFormDefinition entity) {
    if (entity != null && !StringUtils.isEmpty(entity.getRawFileBytes())) {
      return storageService.storeFile(
          Base64.getDecoder().decode(entity.getRawFileBytes()),
          entity.getTaxFormName() + "-" + UUID.randomUUID(),
          EVEREE_INTERNAL,
          PATH_ELEMENTS);
    }
    return null;
  }
}
