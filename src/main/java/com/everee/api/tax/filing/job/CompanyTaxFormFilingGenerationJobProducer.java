package com.everee.api.tax.filing.job;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyTaxFormFilingGenerationJobProducer {

  @Value("${app.taxes.calc-filing-instances-queue-name}")
  private String queueName;

  private final JmsTemplate jmsTemplate;

  public void sendJobMessage(Long jobId) {
    jmsTemplate.convertAndSend(queueName, jobId);
  }
}
