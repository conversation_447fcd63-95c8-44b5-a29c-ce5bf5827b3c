package com.everee.api.tax.filing.unemployment;

import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.brandCellStyle;
import static com.itextpdf.layout.property.UnitValue.createPercentArray;

import com.everee.api.pdf.brandcomponents.SpaceBetweenParagraph;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Div;
import com.itextpdf.layout.element.LineSeparator;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
class UnemploymentTaxFilingFooterEventHandler implements IEventHandler {
  private final UnemploymentTaxFilingContentData data;

  @Override
  public void handleEvent(Event event) {
    var docEvent = (PdfDocumentEvent) event;
    var pdfDoc = docEvent.getDocument();
    var page = docEvent.getPage();
    if (pdfDoc.getPageNumber(page) > 1) return;

    var aboveCanvas = new PdfCanvas(page.newContentStreamAfter(), page.getResources(), pdfDoc);
    var x = page.getPageSize().getX() + 36;
    var y = page.getPageSize().getY() + 24;
    var width = page.getPageSize().getWidth() - 72;
    var height = 108;
    var area = new Rectangle(x, y, width, height);
    var canvas = new Canvas(aboveCanvas, pdfDoc, area);
    canvas.add(new UnemploymentTaxFilingFooter(data));
  }

  private static class UnemploymentTaxFilingFooter extends Div {
    UnemploymentTaxFilingFooter(@NonNull UnemploymentTaxFilingContentData data) {
      setKeepTogether(true);
      add(new LineSeparator(new SolidLine()).setMarginTop(20));
      add(new TotalsSummary(data));
    }
  }

  private static class TotalsSummary extends Table {
    TotalsSummary(@NonNull UnemploymentTaxFilingContentData data) {
      super(createPercentArray(new float[] {6, 4}), true);
      setFixedLayout();
      addCell(new Cell().addStyle(brandCellStyle()));
      addCell(
          new Cell()
              .setTextAlignment(TextAlignment.LEFT)
              .addStyle(brandCellStyle())
              .add(
                  new SpaceBetweenParagraph(
                      "Grand total wages (All pages)", data.getTotalWages().toDisplayString()))
              .add(
                  new SpaceBetweenParagraph(
                      "Wages in excess", data.getExcessWages().toDisplayString()))
              .add(
                  new SpaceBetweenParagraph(
                      "Subject wages", data.getSubjectWages().toDisplayString()))
              .add(
                  new SpaceBetweenParagraph(
                      "Contribution due", data.getTaxAmount().toDisplayString()))
              .add(
                  new SpaceBetweenParagraph(
                      "Total payment due", data.getTaxAmount().toDisplayString())));
    }
  }
}
