package com.everee.api.tax.jurisdictions.summary;

import static com.everee.api.money.Money.valueOf;
import static com.everee.api.quarterlies.forms.state.StateSuperSetOfFieldsQuarterlyFor2019Generator.getHoursWorkedForEmployeesSql;
import static com.everee.api.quarterlies.forms.state.StateSuperSetOfFieldsQuarterlyFor2019Generator.getWeeksWorkedInPeriodSql;

import com.everee.api.taxauthority.jurisdiction.TaxJurisdiction;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaxJurisdictionSummaryDAO {
  private final EntityManager entityManager;

  private static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

  public List<TaxJurisdictionSummaryDetail> getSummaries(
      TaxJurisdictionSummaryCriteria criteria,
      TaxJurisidictionSummaryGroupingConfig groupingConfig) {

    var sql = buildQuery(criteria, groupingConfig);
    log.debug(sql);

    return (List)
        entityManager.createNativeQuery(sql).getResultList().stream()
            .map(
                obj -> {
                  Object[] row = (Object[]) obj;
                  var colIdx = new AtomicInteger();
                  var summaryRow = new TaxJurisdictionSummaryDetail();
                  summaryRow.setCompanyDisplayName((String) row[colIdx.getAndIncrement()]);
                  summaryRow.setCompanyLegalName((String) row[colIdx.getAndIncrement()]);
                  summaryRow.setWorkerCount(
                      ((BigInteger) row[colIdx.getAndIncrement()]).longValue());
                  summaryRow.setGrossWages(valueOf((BigDecimal) row[colIdx.getAndIncrement()]));
                  var jdata =
                      criteria.getTaxJurisdictions().stream()
                          .map(
                              taxJurisdiction -> {
                                var totalWages =
                                    valueOf((BigDecimal) row[colIdx.getAndIncrement()]);
                                var subjectWages =
                                    valueOf((BigDecimal) row[colIdx.getAndIncrement()]);
                                var taxAmount = valueOf((BigDecimal) row[colIdx.getAndIncrement()]);

                                return new TaxJurisdictionSummaryData()
                                    .setJurisdiction(taxJurisdiction)
                                    .setTaxAmount(taxAmount)
                                    .setSubjectWages(subjectWages)
                                    .setTotalWages(totalWages);
                              })
                          .collect(Collectors.toList());
                  summaryRow.setJurisdictionSummaryData(jdata);

                  if (groupingConfig.isByWorker()) {
                    // If none of the jurisdictions have wages/taxes for the period, don't put them
                    // in
                    // the report.  (don't include and jurisdictions added by this generator such as
                    // suta/sit.)
                    if (jdata.isEmpty()
                        || jdata.stream()
                            .filter(
                                jurisdictionSummaryData ->
                                    notAddedInternally(jurisdictionSummaryData, criteria))
                            .noneMatch(TaxJurisdictionSummaryData::hasWagesOrTaxes)) {
                      return null;
                    }

                    summaryRow.setUserId(((BigInteger) row[colIdx.getAndIncrement()]).longValue());
                    summaryRow.setLastName((String) row[colIdx.getAndIncrement()]);
                    summaryRow.setMiddleName((String) row[colIdx.getAndIncrement()]);
                    summaryRow.setFirstName((String) row[colIdx.getAndIncrement()]);
                    summaryRow.setDateOfBirth(
                        Optional.ofNullable((Date) row[colIdx.getAndIncrement()])
                            .map(Date::toLocalDate)
                            .orElse(null));
                    summaryRow.setTaxpayerIdentifier((String) row[colIdx.getAndIncrement()]);
                    summaryRow.setHoursWorked(
                        ((BigDecimal) row[colIdx.getAndIncrement()]).doubleValue());
                    summaryRow.setWeeksWorked(
                        ((BigInteger) row[colIdx.getAndIncrement()]).intValue());
                  }

                  if (groupingConfig.isByMonth()) {
                    summaryRow.setMonth(
                        Month.of(((BigDecimal) row[colIdx.getAndIncrement()]).intValue()));
                  }

                  if (criteria.getDisplayCorporateOfficer()){
                      summaryRow.setCorporateOfficer((Boolean) row[colIdx.getAndIncrement()]);
                      summaryRow.setExempt((Boolean) row[colIdx.getAndIncrement()]);
                  }

                  if (groupingConfig.isByPaydate()) {
                    summaryRow.setPayDate(((Date) row[colIdx.getAndIncrement()]).toLocalDate());
                  }

                  return summaryRow;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
  }

  private String buildQuery(
      TaxJurisdictionSummaryCriteria criteria,
      TaxJurisidictionSummaryGroupingConfig groupingConfig) {
    var jurisidictionIdToTableAlias = getJurisdictionIdsToTableAliasMap(criteria);

    StringBuilder sql =
        new StringBuilder()
            .append("SELECT ")
            .append("\n  c.displayname as companyname")
            .append(",\n  c.legalentityname as companylegalentityname")
            .append(",\n  COUNT(DISTINCT p.employeeid) as workerCount")
            .append(",\n  SUM(p.grossearnings) as grosswages");

    jurisidictionIdToTableAlias.forEach(
        (jurisdictionId, alias) -> {
          sql.append(",\n  SUM(coalesce(")
              .append(alias)
              .append(".uncappedsubjectwages, 0.00)) AS ")
              .append(getTotalTaxableWagesColumnAlias(alias));
          sql.append(",\n  SUM(coalesce(")
              .append(alias)
              .append(".subjectwages, 0.00)) AS ")
              .append(getTaxableWagesColumnAlias(alias));
          sql.append(",\n  SUM(coalesce(")
              .append(alias)
              .append(".amount, 0.00)) AS ")
              .append(getTaxAmountColumnAlias(alias));
        });

    if (groupingConfig.isByWorker()) {
      sql.append(",\n u.id AS userId");
      sql.append(",\n u.lastname AS lastname");
      sql.append(",\n u.middlename AS middlename");
      sql.append(",\n u.firstname AS firstname");
      sql.append(",\n u.dateofbirth AS dateofbirth");
      sql.append(
          ",\n coalesce(u.taxpayeridentifier, u.unverifiedtaxpayeridentifier) AS taxpayeridentifier");
      sql.append(",\n ROUND(MAX(COALESCE(hoursPaid.hours, 0.0))::::numeric, 2) as hoursWorked");
      sql.append(",\n MAX(COALESCE(weeksWorkedInPeriod.weeksworked, 0)) as weeksWorked");
    }

    if (groupingConfig.isByMonth()) {
      sql.append(",\n extract(month from p.paydate)::::numeric AS month");
    }

    if (criteria.getDisplayCorporateOfficer()){
      sql.append(",\n COALESCE(e.corporateofficer, false) as corporateOfficer");
      sql.append(",\n  COALESCE(")
         .append("\n    (SELECT ")
         .append("\n    etjx.isexempt")
         .append("\n    FROM employeetaxjurisdiction etjx")
         .append("\n    WHERE etjx.taxjurisdictionid = '").append(criteria.getCompanyTaxJurisdiction().getTaxJurisdictionId()).append("'")
         .append("\n    AND etjx.startDate <= ")
         .append(toSqlDateString(criteria.getDateRange().getEndDate()))
         .append("\n    AND ( etjx.endDate >= ")
         .append(toSqlDateString(criteria.getDateRange().getStartDate()))
         .append("\n    OR etjx.endDate IS NULL) ")
         .append("\n    AND etjx.isresident = TRUE ")
         .append("\n    AND etjx.employeeid = e.id")
         .append("\n    ORDER BY etjx.updatedat DESC")
         .append("\n    LIMIT 1), false) as exempt");
    }

    if (groupingConfig.isByPaydate()) {
      sql.append(",\n p.paydate AS payDate");
    }

    sql.append("\n FROM").append("\n    payment p").append("\n JOIN company c ON c.id=p.companyid");

    jurisidictionIdToTableAlias.forEach(
        (jurisdictionId, alias) -> {
          sql.append("\n  LEFT JOIN")
              .append("\n    paymenttax ")
              .append(alias)
              .append("\n      ON ")
              .append(alias)
              .append(".paymentid=p.id AND ")
              .append(alias)
              .append(".jurisdictiontaxid='")
              .append(jurisdictionId)
              .append("'");
        });

    if (groupingConfig.isByWorker()) {
      sql.append("\n  JOIN employee e ON e.id=p.employeeid")
          .append("\n  JOIN appuser u ON u.id=e.userid");
      addHoursAndWeeksWorkedJoin(sql, criteria, groupingConfig.isByMonth());
    }

    sql.append("\n WHERE")
        .append("\n    p.companyId=")
        .append(criteria.getCompanyId())
        .append("\n    AND p.status in ('PAID', 'IMPORTED')")
        .append("\n    AND p.paydate BETWEEN ")
        .append(toSqlDateString(criteria.getDateRange().getStartDate()))
        .append(" AND ")
        .append(toSqlDateString(criteria.getDateRange().getEndDate()))
        .append("\n GROUP BY")
        .append("\n     p.companyid, c.displayname, c.legalentityname");

    if (groupingConfig.isByWorker()) {
      sql.append(",\n  u.id")
          .append(",\n  u.lastname")
          .append(",\n  u.firstname")
          .append(",\n  coalesce(u.taxpayeridentifier, u.unverifiedtaxpayeridentifier)");
    }

    if (criteria.getDisplayCorporateOfficer()){
      sql.append(",\n e.corporateofficer");
      sql.append(",\n exempt");
    }

    if (groupingConfig.isByMonth()) {
      sql.append(",\n extract(month from p.paydate)");
    }

    if (groupingConfig.isByPaydate()) {
      sql.append(",\n p.paydate");
    }

    sql.append("\n ORDER BY").append("\n      c.displayname");

    if (groupingConfig.isByWorker()) {
      sql.append(",\n  u.lastname")
          .append(",\n  u.firstname")
          .append(",\n  coalesce(u.taxpayeridentifier, u.unverifiedtaxpayeridentifier)");
    }

    if (groupingConfig.isByMonth()) {
      sql.append(",\n extract(month from p.paydate)");
    }

    if (groupingConfig.isByPaydate()) {
      sql.append(",\n p.paydate");
    }

    return sql.toString();
  }

  private void addHoursAndWeeksWorkedJoin(
      StringBuilder sql, TaxJurisdictionSummaryCriteria criteria, boolean byMonth) {
    var states =
        criteria.getTaxJurisdictions().stream()
            .map(TaxJurisdiction::getState)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    sql.append("\n  LEFT JOIN (\n ");
    sql.append(
        replaceCriteria(
            getHoursWorkedForEmployeesSql("e.id as employeeid", states, byMonth), criteria));
    sql.append("\n ) hoursPaid ON hoursPaid.employeeid=e.id");
    if (byMonth) {
      sql.append("\n and hoursPaid.month = extract(month from p.paydate)");
    }
    sql.append("\n  LEFT JOIN (\n ");
    sql.append(
        replaceCriteria(
            getWeeksWorkedInPeriodSql("e.id as employeeid", states, byMonth), criteria));
    sql.append("\n ) weeksWorkedInPeriod on weeksWorkedInPeriod.employeeid=e.id");
    if (byMonth) {
      sql.append("\n and weeksWorkedInPeriod.month = extract(month from p.paydate)");
    }
  }

  private String replaceCriteria(String sql, TaxJurisdictionSummaryCriteria criteria) {
    return sql.replaceAll(":startDate", toSqlDateString(criteria.getDateRange().getStartDate()))
        .replaceAll(":endDate", toSqlDateString(criteria.getDateRange().getEndDate()))
        .replaceAll(":companyId", criteria.getCompanyId().toString());
  }

  private Map<String, String> getJurisdictionIdsToTableAliasMap(
      TaxJurisdictionSummaryCriteria criteria) {
    return criteria.getTaxJurisdictions().stream()
        .map(TaxJurisdiction::getId)
        .collect(
            Collectors.toMap(
                s -> s,
                this::tableAliasForJurisdicition,
                (existing, replacement) -> existing,
                LinkedHashMap::new));
  }

  private String tableAliasForJurisdicition(String jurisdictionId) {
    return ("J" + jurisdictionId.replaceAll("-", "").replaceAll("_", "")).toLowerCase();
  }

  private String toSqlDateString(LocalDate date) {
    return "'" + date.format(YYYY_MM_DD) + "'";
  }

  private String getTaxableWagesColumnAlias(String tableAlias) {
    return tableAlias + "TaxableWages";
  }

  private String getTotalTaxableWagesColumnAlias(String tableAlias) {
    return tableAlias + "TotalTaxableWages";
  }

  private String getTaxAmountColumnAlias(String tableAlias) {
    return tableAlias + "TaxAmount";
  }

  private boolean notAddedInternally(
      TaxJurisdictionSummaryData jurisdictionSummaryData, TaxJurisdictionSummaryCriteria criteria) {
    return CollectionUtils.isEmpty(criteria.getInternalTaxJurisdictionsAdded())
        || criteria.getInternalTaxJurisdictionsAdded().stream()
            .map(TaxJurisdiction::getId)
            .noneMatch(
                tjId -> Objects.equals(tjId, jurisdictionSummaryData.getJurisdiction().getId()));
  }
}
