package com.everee.api.tax.jurisdictions;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.util.stream.Collectors.toSet;

import com.everee.api.company.CompanyService;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.model.EmploymentType;
import com.everee.api.notification.email.EmailRecipient;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.PaymentTax;
import com.everee.api.payment.event.PaymentUpdatedEvent;
import com.everee.api.tax.FilingFrequency;
import com.everee.api.tax.TaxType;
import com.everee.api.tax.UniqueTaxID;
import com.everee.api.tax.jurisdictions.lookup.CompanyTaxJurisdictionLookup;
import com.everee.api.tax.jurisdictions.lookup.CompanyTaxJurisdictionLookupService;
import com.everee.api.tax.preprocessing.jurisdictions.EmployeeTaxJurisdiction;
import com.everee.api.tax.preprocessing.jurisdictions.EmployeeTaxJurisdictionUpdatedEvent;
import com.everee.api.tax.state.State;
import com.everee.api.taxaccumulator.TaxFilingType;
import com.everee.api.taxauthority.jurisdiction.TaxJurisdiction;
import com.everee.api.taxauthority.jurisdiction.TaxJurisdictionRepository;
import com.everee.api.user.UserService;
import com.everee.api.util.BooleanApiParam;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
@Service
@Slf4j
public class CompanyTaxJurisdictionService {

  private final CompanyService companyService;
  private final CompanyTaxJurisdictionRepository companyTaxJurisdictionRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final CompanyTaxJurisdictionLookupService lookupService;
  private final NotificationService notificationService;
  private final TaxJurisdictionRepository taxJurisdictionRepository;

  @Value("${app.notification.tax.email}")
  private String taxTeamEmailProperty;

  private List<String> getTaxTeamEmails() {
    if (!StringUtils.isEmpty(taxTeamEmailProperty)) {
      var emails = taxTeamEmailProperty.split(",");
      if (emails.length > 0) {
        return List.of(emails);
      }
    }
    return Collections.emptyList();
  }

  @EventListener
  @Transactional
  public void handleEvent(EmployeeTaxJurisdictionUpdatedEvent event) {
    log.debug("handleEvent(event: {})", event);

    try {
      if (event == null || event.getEmployeeTaxJurisdictions() == null) {
        throw new IllegalArgumentException("Event must have company and tax jurisdiction data.");
      }
      if (event.getEmployee().getEmploymentType() == EmploymentType.CONTRACTOR) {
        return;
      }
      addCompanyTaxJurisdictions(
          event.getCompanyId(),
          event.getEmployeeTaxJurisdictions().stream()
              .map(EmployeeTaxJurisdiction::getTaxJurisdictionId)
              .filter(id -> !StringUtils.isEmpty(id))
              .collect(toSet()));
    } catch (Exception e) {
      log.error("Exception in handleEvent(event: {})", event, e);
      throw e;
    }
  }

  @EventListener
  @Transactional
  public void handleEvent(PaymentUpdatedEvent event) {
    log.debug("handleEvent(event: {})", event);

    try {
      if (event == null || event.getCompanyId() == null || event.getPayment() == null) {
        throw new IllegalArgumentException("Event must have company and tax jurisdiction data.");
      }

      if (event.getPayment().getStatus() != PaymentStatus.APPROVED) {
        return;
      }

      var taxJurisdictionIdsWithAmounts =
          Optional.ofNullable(event.getPayment().getTaxes()).orElse(Set.of()).stream()
              .filter(
                  paymentTax ->
                      !paymentTax.getAmounts().getAmount().isZero()
                          || !paymentTax.getSubjectWageAmounts().getAmount().isZero())
              .map(PaymentTax::getJurisdictionTaxId)
              .filter(id -> !StringUtils.isEmpty(id))
              .collect(toSet());

      if (CollectionUtils.isEmpty(taxJurisdictionIdsWithAmounts)) {
        return;
      }
      addCompanyTaxJurisdictions(event.getCompanyId(), taxJurisdictionIdsWithAmounts);
    } catch (Exception e) {
      log.error("Exception in handleEvent(event: {})", event, e);
      throw e;
    }
  }

  @Transactional
  public void addCompanyTaxJurisdictions(Long companyId, Set<String> taxJurisdictionIds) {
    if (companyId == null) {
      throw new IllegalArgumentException("Company ID is required.");
    }
    if (CollectionUtils.isEmpty(taxJurisdictionIds)) {
      return;
    }

    // update existing company tax jurisdictions that were archived or deactivated
    var existingCompanyTaxJurisdictions =
        lookupService.listAll(
            new CompanyTaxJurisdictionLookup()
                .setCompanyIds(Set.of(companyId))
                .setTaxJurisdictionIds(taxJurisdictionIds)
                .setArchived(BooleanApiParam.UNSPECIFIED)
                .setSkipTenantCheck(true),
            Pageable.unpaged());
    var updatingCompanyTaxJurisdictions =
        existingCompanyTaxJurisdictions.stream()
            .filter(e -> TRUE.equals(e.getArchived()) || FALSE.equals(e.getActive()))
            .map(e -> e.setArchived(FALSE).setActive(TRUE).setUpdatedAt(LocalDateTime.now()))
            .collect(toSet());
    List<CompanyTaxJurisdiction> updatedJurisdictions = null;
    if (!CollectionUtils.isEmpty(updatingCompanyTaxJurisdictions)) {
      updatedJurisdictions =
          companyTaxJurisdictionRepository.saveAll(updatingCompanyTaxJurisdictions);
    }

    // build new set of updated tax jurisdiction IDs for clean insert
    var newTaxJurisdictionIds = new HashSet<>(taxJurisdictionIds);
    newTaxJurisdictionIds.removeAll(
        existingCompanyTaxJurisdictions.stream()
            .map(CompanyTaxJurisdiction::getTaxJurisdictionId)
            .collect(toSet()));

    // insert new company tax jurisdictions
    var addedJurisdictions = (List) List.of();
    if (!CollectionUtils.isEmpty(newTaxJurisdictionIds)) {
      var taxJurisdictions = taxJurisdictionRepository.findAllById(newTaxJurisdictionIds);
      if (!CollectionUtils.isEmpty(taxJurisdictions)) {
        addedJurisdictions =
            companyTaxJurisdictionRepository.saveAll(
                taxJurisdictions.stream()
                    .map(
                        j ->
                            new CompanyTaxJurisdiction()
                                .setCompanyId(companyId)
                                .setTaxJurisdictionId(j.getId())
                                .setTaxType(j.getTaxType())
                                .setState(j.getState())
                                .setAccountNumber(is940or941Filing(j) ? "-" : null)
                                .setFilingFrequencies(getDefaultFilingFrequency(j))
                                .setCreatedbyuserid(
                                  UserService.findAuthenticatedUserId()
                                    .orElse(null))
                                .setAuto(true))
                    .collect(toSet()));
      }
    }

    notifyOfAddedJurisdictions(companyId, addedJurisdictions, updatedJurisdictions);
  }

  public CompanyTaxJurisdiction create(CompanyTaxJurisdiction companyTaxJurisdiction) {
    //KIWI-73 auto = false since "create" function called by the controller only, which mean it is added by the admin
    companyTaxJurisdiction.setAuto(false);

    return createOrUpdate(
        true,
        companyTaxJurisdiction.getCompanyId() + "." + companyTaxJurisdiction.getTaxJurisdictionId(),
        companyTaxJurisdiction,
        false);
  }

  public CompanyTaxJurisdiction update(
      String companyTaxJurisdictionId, CompanyTaxJurisdiction companyTaxJurisdiction) {
    return createOrUpdate(false, companyTaxJurisdictionId, companyTaxJurisdiction, null);
  }

  @Transactional
  public CompanyTaxJurisdiction createOrUpdate(
      boolean allowCreate,
      String companyTaxJurisdictionId,
      CompanyTaxJurisdiction companyTaxJurisdiction,
      Boolean newArchivedValue) {
    var updatedCtj =
        companyTaxJurisdictionRepository
            .findById(companyTaxJurisdictionId)
            .map(
                record -> {
                  record.setAccountNumber(companyTaxJurisdiction.getAccountNumber());
                  if (companyTaxJurisdiction.getActive() != null
                      && !companyTaxJurisdiction.getActive().equals(record.getActive())
                      && FALSE.equals(companyTaxJurisdiction.getActive())) {
                    eventPublisher.publishEvent(new CompanyTaxJurisdictionDeactivatedEvent(record));
                  }
                  record.setActive(companyTaxJurisdiction.getActive());
                  record.setFilingFrequencies(
                      Optional.ofNullable(companyTaxJurisdiction.getFilingFrequencies())
                          .orElseGet(
                              () ->
                                  companyTaxJurisdiction.getFilingFrequency() == null
                                      ? Set.of()
                                      : Set.of(companyTaxJurisdiction.getFilingFrequency())));
                  record.setPaymentFrequency(companyTaxJurisdiction.getPaymentFrequency());
                  record.setNotes(companyTaxJurisdiction.getNotes());
                  record.setUsername(companyTaxJurisdiction.getUsername());
                  record.setPassword(companyTaxJurisdiction.getPassword());
                  record.setSubjectEmployeeCount(companyTaxJurisdiction.getSubjectEmployeeCount());
                  record.setTaxCalculationHandling(
                      companyTaxJurisdiction.getTaxCalculationHandling());
                  if (newArchivedValue != null) {
                    if (!record.getArchived().equals(newArchivedValue)
                        && TRUE.equals(newArchivedValue)) {
                      eventPublisher.publishEvent(new CompanyTaxJurisdictionArchivedEvent(record));
                    }
                    record.setArchived(newArchivedValue);
                  }
                  if (companyTaxJurisdiction.getStatus() != null) {
                    record.setStatus(companyTaxJurisdiction.getStatus());
                  }
                  return companyTaxJurisdictionRepository.save(record);
                });
    if (updatedCtj.isPresent()) {
      return updatedCtj.get();
    }

    if (!allowCreate) {
      throw new ResourceNotFoundException(
          "CompanyTaxJurisdiction not found with id = " + companyTaxJurisdictionId);
    }

    if (companyTaxJurisdiction.getTaxType() == null) {
      var taxJurisdictionId = new UniqueTaxID(companyTaxJurisdiction.getTaxJurisdictionId());
      companyTaxJurisdiction.setState(taxJurisdictionId.getState());
      companyTaxJurisdiction.setTaxType(taxJurisdictionId.getTaxType());
    }

    companyTaxJurisdiction.setCreatedbyuserid(
      UserService.findAuthenticatedUserId()
        .orElse(null));

    return companyTaxJurisdictionRepository.save(companyTaxJurisdiction);
  }

  @Transactional
  public void delete(String companyTaxJurisdictionId) {
    var optional = companyTaxJurisdictionRepository.findById(companyTaxJurisdictionId);
    if (optional.isPresent()) {
      var archived = companyTaxJurisdictionRepository.save(optional.get().setArchived(TRUE));
      eventPublisher.publishEvent(new CompanyTaxJurisdictionArchivedEvent(archived));
    }
  }

  public Optional<CompanyTaxJurisdiction> getStateSitRecord(Long companyId, State state) {
    return lookupService.findOne(
        new CompanyTaxJurisdictionLookup()
            .setCompanyIds(Set.of(companyId))
            .setStates(Set.of(state))
            .setTaxTypes(Set.of(TaxType.SIT)));
  }

  public Optional<CompanyTaxJurisdiction> getStateSutaRecord(Long companyId, State state) {
    return lookupService.findOne(
        new CompanyTaxJurisdictionLookup()
            .setCompanyIds(Set.of(companyId))
            .setStates(Set.of(state))
            .setTaxTypes(Set.of(TaxType.ER_SUTA)));
  }

  private void notifyOfAddedJurisdictions(
      Long companyId,
      List<CompanyTaxJurisdiction> addedTaxJurisdictions,
      List<CompanyTaxJurisdiction> updatedTaxJurisdictions) {
    var non940or941AddedJurisdictions =
        addedTaxJurisdictions.stream()
            .filter(
                companyTaxJurisdiction ->
                    !is940or941Filing(
                        new TaxJurisdiction(companyTaxJurisdiction.getTaxJurisdictionId())))
            .collect(Collectors.toList());

    if (!CollectionUtils.isEmpty(non940or941AddedJurisdictions)
        || !CollectionUtils.isEmpty(updatedTaxJurisdictions)) {
      var company = companyService.getCompany(companyId);
      getTaxTeamEmails()
          .forEach(
              email -> {
                notificationService.enqueueEmailNotification(
                    new CompanyTaxJurisdictionEmailNotification(
                        company, non940or941AddedJurisdictions, updatedTaxJurisdictions),
                    new EmailRecipient("", email));
              });
    }
  }

  private boolean is940or941Filing(TaxJurisdiction j) {
    return TaxFilingType.FED_940.getTaxTypes().contains(j.getTaxType())
        || TaxFilingType.FED_941.getTaxTypes().contains(j.getTaxType());
  }

  private Set<FilingFrequency> getDefaultFilingFrequency(TaxJurisdiction j) {
    if (TaxType.FIT == j.getTaxType()) {
      return Set.of(FilingFrequency.QUARTERLY, FilingFrequency.ANNUAL);
    } else if (TaxFilingType.FED_940.getTaxTypes().contains(j.getTaxType())) {
      return Set.of(FilingFrequency.ANNUAL);
    } else if (TaxFilingType.FED_941.getTaxTypes().contains(j.getTaxType())) {
      return Set.of(FilingFrequency.QUARTERLY);
    }

    return Set.of();
  }
}
