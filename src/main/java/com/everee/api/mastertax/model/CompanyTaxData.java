package com.everee.api.mastertax.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyTaxData {
    private String formatId;
    private String companyGroupName;
    private String payrollCode;
    private String taxCode;
    private String effectiveDate;
    private String companyTaxStatus;
    private String companyTaxServiceLevel;
    private String einType;
    private String ein;
    private String taxRate;
    private String taxRate2;
    private String paymentFrequency;
    private String paymentMethod;
    private String eftPassword;
    private String countyCode;
    private String referenceEin;
    private String markAllReturnsAsFinal;
    private String finalReturnEffectiveDate;
}
