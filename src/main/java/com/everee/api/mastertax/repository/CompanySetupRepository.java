package com.everee.api.mastertax.repository;

import com.everee.api.mastertax.model.CompanySetupData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CompanySetupRepository {
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public List<CompanySetupData> findByCompanyIdsAndDemo(List<Long> companyIds, boolean demoCompany) {
        String sql = "SELECT format_id, company_group_name, payroll_code, payroll_description, company_name,\treporting_payroll_code, name_control, company_start_date, quarterly_wage_reporting_flag, worksite_reporting_flag,\n" +
                "    yearend_employee_filing_flag, wage_attachment_flag, cash_service_level, kind_of_employer, naics_code,effective_date, fein_type, fein, reserved, Nine44_filer, company_status, service_level,\n" +
                "    company_dba_name, in_care_of, address_line_1 as addressLine1, address_line_2 as addressLine2, city, state, zip_code, country_code, psd_code, first_name, middle_initial, last_name, phone_area_code, phone_number, phone_extension,\n" +
                "    fax_area_code, fax_number, email_address, bank_account_name, transit_routing_number, bank_account_number, bank_account_type, draft_days, demo_company" +
                " FROM company_setup_view WHERE payroll_code IN (:companyIds) AND demo_company = :demoCompany";
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("companyIds", companyIds)
                .addValue("demoCompany", demoCompany);
        return namedParameterJdbcTemplate.query(sql, parameters, new BeanPropertyRowMapper<>(CompanySetupData.class));
    }
}
