package com.everee.api.settlement;

import com.everee.api.company.CompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/settlement-periods")
@RequiredArgsConstructor
public class SettlementPeriodController {

  private final SettlementPeriodRepository repository;

  @GetMapping
  public Page<SettlementPeriod> listSettlementPeriods(Pageable pageable) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    return repository.findAllByCompanyId(companyId, pageable);
  }
}
