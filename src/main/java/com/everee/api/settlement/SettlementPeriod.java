package com.everee.api.settlement;

import static com.everee.api.util.ObjectUtils.allPresent;

import com.everee.api.model.DateRange;
import com.everee.api.phase.Phased;
import java.time.LocalDate;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.springframework.context.i18n.LocaleContextHolder;

@Data
@Entity
@Immutable
public class SettlementPeriod implements Phased {

  @Id private Long id;

  private Long companyId;

  private LocalDate startDate;

  private LocalDate endDate;

  @SuppressWarnings("unused")
  public String getLocalizedDateRange() {
    if (allPresent(startDate, endDate)) {
      return DateRange.of(startDate, endDate)
          .getLocalizedDescription(LocaleContextHolder.getLocale());
    } else {
      return null;
    }
  }
}
