package com.everee.api.historicalpayments;

import com.everee.api.company.CompanyService;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.historicalpayments.processors.HistoricalPaymentsPayRunsProcess;
import com.everee.api.historicalpayments.processors.context.PayRunContext;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@AllArgsConstructor
public class HistoricalPaymentsService {

    private final HistoricalPaymentsPayRunsProcess historicalPaymentsPayRunsProcess;
    private final CompanyService companyService;

    @Deprecated
    @Transactional
    public void executePayRun(Long companyId) {
        var company = companyService.getCompany(companyId);

        if (company == null) {
            throw new InvalidRequestException("Company with ID " + companyId + " not found");
        }

        var context = new PayRunContext()
                .setCompanyId(companyId);

        historicalPaymentsPayRunsProcess.build().execute(context);
    }
}
