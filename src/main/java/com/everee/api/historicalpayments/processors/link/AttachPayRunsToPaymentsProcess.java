package com.everee.api.historicalpayments.processors.link;

import com.everee.api.historicalpayments.models.HistoricalPaymentDto;
import com.everee.api.historicalpayments.processors.context.PayRunContext;
import com.everee.api.payperiod.PayPeriodRepository;
import com.everee.api.payrun.models.PayRun;
import com.everee.api.payrun.models.PayRunType;
import com.everee.api.pipeline.Process;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class AttachPayRunsToPaymentsProcess implements Process<PayRunContext> {

  private final PayPeriodRepository payPeriodRepository;

    @Override
    public void process(PayRunContext context) {
        Map<PayRun, List<Long>> payRunToPaymentsMap = new HashMap<>();

        context.getGroupedPayments().forEach((payPeriodId, dateGroups) -> {
          var period = payPeriodRepository.getOne(payPeriodId);
            dateGroups.forEach((payDate, payments) -> {

                // Create pay run for each group of payments
                PayRun payRun = new PayRun();
                payRun.setCompanyId(context.getCompanyId());
                payRun.setPayPeriodId(payPeriodId);
                payRun.setPayDate(payDate);
                payRun.setType(PayRunType.ON_CYCLE);
                payRun.setDescription(period.getPayPeriodType().getDisplayName());
                payRun.setCreatedByUserId(UserService.getAuthenticatedUserId());
                payRun.setUpdatedByUserId(UserService.getAuthenticatedUserId());
                payRunToPaymentsMap.put(payRun, payments.stream().map(HistoricalPaymentDto::getId).collect(Collectors.toList()));
            });
        });
        context.setPayRunToPaymentsMap(payRunToPaymentsMap);
    }
}
