package com.everee.api.historicalpayments.processors;

import com.everee.api.historicalpayments.processors.context.PayRunContext;
import com.everee.api.historicalpayments.processors.create.CreatePayrunsForGroupedPaymentsProcess;
import com.everee.api.historicalpayments.processors.fetch.GetPaymentsProcess;
import com.everee.api.historicalpayments.processors.group.GroupPaymentsByPaydateProcess;
import com.everee.api.historicalpayments.processors.link.AttachPayRunsToGrossEarningsProcess;
import com.everee.api.historicalpayments.processors.link.AttachPayRunsToPaymentsProcess;
import com.everee.api.pipeline.Pipeline;
import com.everee.api.pipeline.SequentialPipeline;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HistoricalPaymentsPayRunsProcess {
    private final GetPaymentsProcess getPaymentsProcess;
    private final GroupPaymentsByPaydateProcess groupPaymentsByPaydateProcess;
    private final CreatePayrunsForGroupedPaymentsProcess createPayrunsForGroupedPaymentsProcess;
    private final AttachPayRunsToGrossEarningsProcess attachPayRunsToGrossEarningsProcess;
    private final AttachPayRunsToPaymentsProcess attachPayRunsToPaymentsProcess;

    public Pipeline<PayRunContext> build() {
        SequentialPipeline<PayRunContext> pipeline = new SequentialPipeline<>();

        pipeline
                .addProcess(getPaymentsProcess)
                .addProcess(groupPaymentsByPaydateProcess)
                .addProcess(attachPayRunsToPaymentsProcess)
                .addProcess(createPayrunsForGroupedPaymentsProcess)
                .addProcess(attachPayRunsToGrossEarningsProcess);
        return pipeline;
    }
}
