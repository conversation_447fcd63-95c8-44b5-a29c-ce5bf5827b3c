package com.everee.api.project;

import static com.everee.api.model.BaseModel_.ID;
import static com.everee.api.project.Project_.ARCHIVE_DATE;
import static com.everee.api.project.Project_.COMPANY_ID;
import static com.everee.api.project.Project_.NAME;
import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.query.Query;
import java.util.Optional;
import javax.persistence.EntityManager;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProjectLookupService implements LookupService<Project, ProjectLookup, Query<Project>> {
  @Getter private final EntityManager entityManager;

  @Override
  public void configureQuery(ProjectLookup lookup, Query<Project> query) {
    query
        .where(property(ID).in(lookup.getIds()))
        .where(property(COMPANY_ID).in(lookup.getCompanyIds()))
        .where(property(NAME).like(lookup.getName()));

    var status = Optional.ofNullable(lookup.getProjectStatus());
    if (status.isPresent() && status.get().equals(ProjectStatus.ARCHIVED)) {
      query.where(property(ARCHIVE_DATE).isNotNull());
    }
    if (status.isPresent() && status.get().equals(ProjectStatus.ACTIVE)) {
      query.where(property(ARCHIVE_DATE).isNull());
    }
  }

  @Override
  public Query<Project> createQuery() {
    return new Query<>(entityManager, Project.class);
  }
}
