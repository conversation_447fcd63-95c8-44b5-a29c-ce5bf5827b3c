package com.everee.api.project.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ProjectTaskWithDuplicateNameException extends LocalizedRuntimeException {
  public ProjectTaskWithDuplicateNameException() {
    super("project.ProjectTaskWithDuplicateNameException.message");
  }
}
