package com.everee.api.project;

import static com.everee.api.project.ProjectTrackingReportTotal.getProjectedCost;
import static com.everee.api.project.ProjectTrackingReportTotal.projectTimeTotalComparator;
import static com.everee.api.util.SetUtils.toSetOrNull;

import com.everee.api.approvalgroup.ApprovalGroup;
import com.everee.api.bankingday.Weekdays;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.DetailedEmployee_;
import com.everee.api.employee.Employee;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.employeePosition.EmployeePositionLookup;
import com.everee.api.employeePosition.EmployeePositionLookupService;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.report.ReportExporter;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocation;
import java.io.ByteArrayOutputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectTrackingReportExportService {
  private static final String HEADER_DATE = "Date";
  private static final String HEADER_EMPLOYEE = "Employee";
  private static final String HEADER_WORKER_TYPE = "Worker Type";
  private static final String HEADER_APPROVAL_GROUP = "Approval Group";
  private static final String HEADER_TEAM = "Team";
  private static final String HEADER_LOCATION = "Location";
  private static final String HEADER_PROJECT = "Project";
  private static final String HEADER_TASK = "Task";
  private static final String HEADER_PROJECT_TIME = "Project Time";
  private static final String HEADER_RATE = "Rate per hour";
  private static final String HEADER_ESTIMATED_COST = "Estimated Labor Cost";
  private static final String HEADER_TIME_PAID = "Time Paid";
  private static final String HEADER_GAP_TIME = "Gap Time";
  private static final String HEADER_PERCENT_TIME_ON_PROJECTS = "Percentage Of Time On Projects";

  private final MessageSource messageSource;
  private final StorageService storageService;
  private final PartnerRepository partnerRepository;
  private final ProjectTimeService projectTimeService;
  private final ProjectTimeLookupService projectTimeLookupService;
  private final DetailedEmployeeLookupService employeeLookupService;
  private final EmployeePositionLookupService employeePositionLookupService;

  public StoredFileLink generateReport(
      DetailedCompany company, ProjectTimeLookup projectTimeLookup) {
    var exporter = new ReportExporter();
    var projectTimes =
        projectTimeLookupService.listAll(
            projectTimeLookup,
            PageRequest.of(
                0,
                Integer.MAX_VALUE,
                Sort.by(
                    ProjectTime_.WORK_LOCATION_START_TIME,
                    ProjectTime_.EMPLOYEE + "." + DetailedEmployee_.LAST_NAME,
                    ProjectTime_.EMPLOYEE + "." + DetailedEmployee_.FIRST_NAME)));

    buildByDateProjectTaskSheet(exporter, projectTimes);
    buildByProjectTaskSheet(exporter, projectTimes);
    buildUtilizationReportSheet(exporter, projectTimes, projectTimeLookup, company);
    buildDetailsSheet(exporter, projectTimes);
    var partner = partnerRepository.findByCompanyId(company.getId()).orElseThrow();

    return exporter.exportReport(
        storageService, new ByteArrayOutputStream(), company, partner, "Project-Tracking");
  }

  private void buildDetailsSheet(ReportExporter exporter, Page<ProjectTime> projectTimes) {
    exporter.createSheet("Details");
    exporter.writeHeaderCells(
        List.of(
            HEADER_DATE,
            HEADER_EMPLOYEE,
            HEADER_WORKER_TYPE,
            HEADER_APPROVAL_GROUP,
            HEADER_TEAM,
            HEADER_LOCATION,
            HEADER_PROJECT,
            HEADER_TASK,
            HEADER_PROJECT_TIME,
            HEADER_RATE,
            HEADER_ESTIMATED_COST));

    if (projectTimes.isEmpty()) {
      return;
    }

    projectTimes.forEach(
        projectTime -> {
          var employee = projectTime.getEmployee();
          var project = projectTime.getProject();
          var task = Optional.ofNullable(projectTime.getTask()).orElse(new Task());
          var startDate = projectTime.getWorkLocationStartTime().toLocalDate();

          exporter.writeCell(startDate);
          exporter.writeCell(employee.getDisplayFullName());
          exporter.writeCell(
              EmploymentType.CONTRACTOR.equals(employee.getEmploymentType())
                  ? "Contractor"
                  : Objects.toString(projectTime.getPayType(), null));
          exporter.writeCell(
              Optional.ofNullable(employee.getApprovalGroup())
                  .map(ApprovalGroup::getName)
                  .orElse("-"));
          exporter.writeCell(
              Optional.ofNullable(employee.getApprovalGroup())
                  .map(ApprovalGroup::getName)
                  .orElse("-"));
          exporter.writeCell(getLocationName(employee, startDate));

          exporter.writeCell(project.getName());
          exporter.writeCell(task.getName());
          exporter.writeCell(projectTime.getDuration());
          exporter.writeCell(projectTime.getHourlyRate());
          exporter.writeCell(getProjectedCost(projectTime));
          exporter.advanceRow();
        });

    var totalDuration =
        projectTimes.map(ProjectTime::getDuration).stream().reduce(Duration.ZERO, Duration::plus);

    if (exporter.getRow().get() > 1) {
      exporter.advanceCol(7);
      exporter.writeCell(totalDuration);
      exporter.advanceCol();
      exporter.writeSumCell();
    }
  }

  private void buildByProjectTaskSheet(ReportExporter exporter, Page<ProjectTime> projectTimes) {
    exporter.createSheet("By Project & Task");
    exporter.writeHeaderCells(List.of(HEADER_PROJECT, HEADER_TASK, HEADER_PROJECT_TIME));
    if (projectTimes.isEmpty()) {
      return;
    }
    List<ProjectTrackingReportTotal> projectTimeTotals = new ArrayList();
    projectTimes.forEach(
        projectTime -> {
          var total =
              findTotalOrCreate(
                  projectTimeTotals, null, projectTime.getProject(), projectTime.getTask());
          total.add(projectTime);
        });

    projectTimeTotals.sort(projectTimeTotalComparator);
    for (ProjectTrackingReportTotal projectTimeTotal : projectTimeTotals) {
      var project = projectTimeTotal.getProject();
      var task = Optional.ofNullable(projectTimeTotal.getTask()).orElse(new Task());

      exporter.writeCell(project.getName());
      exporter.writeCell(task.getName());
      exporter.writeCell(projectTimeTotal.getDuration());
      exporter.advanceRow();
    }

    if (exporter.getRow().get() > 1) {
      exporter.advanceCol(2);
      exporter.writeSumCell();
    }
  }

  private void buildByDateProjectTaskSheet(
      ReportExporter exporter, Page<ProjectTime> projectTimes) {
    exporter.createSheet("Project Summary by Day");
    exporter.writeHeaderCells(
        List.of(HEADER_DATE, HEADER_PROJECT, HEADER_TASK, HEADER_PROJECT_TIME));

    if (projectTimes.isEmpty()) {
      return;
    }
    List<ProjectTrackingReportTotal> projectTimeTotals = new ArrayList();
    projectTimes.forEach(
        projectTime -> {
          var total =
              findTotalOrCreate(
                  projectTimeTotals,
                  projectTime.getWorkLocationStartTime().toLocalDate(),
                  projectTime.getProject(),
                  projectTime.getTask());
          total.add(projectTime);
        });

    projectTimeTotals.sort(projectTimeTotalComparator);
    projectTimeTotals.forEach(
        projectTimeTotal -> {
          var project = projectTimeTotal.getProject();
          var task = Optional.ofNullable(projectTimeTotal.getTask()).orElse(new Task());

          exporter.writeCell(projectTimeTotal.getProjectDate());
          exporter.writeCell(project.getName());
          exporter.writeCell(task.getName());
          exporter.writeCell(projectTimeTotal.getDuration());
          exporter.advanceRow();
        });
    if (exporter.getRow().get() > 1) {
      exporter.advanceCol(3);
      exporter.writeSumCell();
    }
  }

  private void buildUtilizationReportSheet(
      ReportExporter exporter,
      Page<ProjectTime> projectTimes,
      ProjectTimeLookup projectTimeLookup,
      DetailedCompany company) {
    exporter.createSheet("Utilization Report");
    exporter.writeHeaderCells(
        List.of(
            HEADER_EMPLOYEE,
            HEADER_WORKER_TYPE,
            HEADER_APPROVAL_GROUP,
            HEADER_TEAM,
            HEADER_LOCATION,
            HEADER_PROJECT_TIME,
            HEADER_TIME_PAID,
            HEADER_GAP_TIME,
            HEADER_PERCENT_TIME_ON_PROJECTS));

    if (projectTimes.isEmpty()) {
      return;
    }

    var startDate = projectTimeLookup.getProjectDateRange().getStartTime().toLocalDate();
    var endDate = projectTimeLookup.getProjectDateRange().getEndTime().toLocalDate();

    var employees = new ArrayList<>(getEmployees(company, startDate, endDate));
    employees.sort(Comparator.comparing(DetailedEmployee::getDisplayFullName));

    var employeeIds = employees.stream().map(Employee::getId).collect(Collectors.toSet());
    var shiftSecondsByEmployeeId =
        projectTimeService.getShiftSecondsForDates(company, employeeIds, startDate, endDate);
    var employeePositionsByEmployeeId =
        employeePositionLookupService
            .listEmployeePositionRecords(
                Pageable.unpaged(),
                new PhaseLookup(Phase.ACTIVE, startDate, endDate),
                null,
                company.getId(),
                employeeIds)
            .stream()
            .collect(Collectors.groupingBy(EmployeePosition::getEmployeeId));

    // holidays?
    var salaryDurationInRange =
        Duration.ofHours(
            startDate
                .datesUntil(endDate.plusDays(1))
                .filter(Weekdays::isWeekday)
                .mapToLong(d -> 8L)
                .sum());

    List<ProjectTrackingReportTotal> projectTimeTotals = new ArrayList();
    projectTimes.forEach(
        projectTime -> {
          var total = findTotalOrCreate(projectTimeTotals, projectTime.getEmployee());
          total.add(projectTime);
        });

    var mutator = exporter.getMutator();
    var row = exporter.getRow();
    var col = exporter.getCol();

    employees.forEach(
        employee -> {
          var projectTimeTotal =
              projectTimeTotals.stream()
                  .filter(
                      projectTrackingReportTotal ->
                          projectTrackingReportTotal.getEmployee().equals(employee))
                  .findFirst()
                  .orElse(new ProjectTrackingReportTotal());
          var employeePosition =
              Optional.ofNullable(employeePositionsByEmployeeId.get(employee.getId()))
                  .orElse(new ArrayList<>()).stream()
                  .findFirst()
                  .orElse(new EmployeePosition());

          var projectTime = projectTimeTotal.getDuration();
          var workedDuration = Duration.ofSeconds(shiftSecondsByEmployeeId.get(employee.getId()));

          if (PayType.SALARY.equals(employeePosition.getPayType())) {
            workedDuration = salaryDurationInRange;
          }

          // Don't include employees that didn't work
          if (workedDuration.isNegative() || workedDuration.isZero()) {
            return;
          }

          exporter.writeCell(employee.getDisplayFullName());
          exporter.writeCell(Objects.toString(employeePosition.getPayType(), null));
          exporter.writeCell(
              Optional.ofNullable(employee.getApprovalGroup())
                  .map(ApprovalGroup::getName)
                  .orElse("-"));
          exporter.writeCell(
              Optional.ofNullable(employee.getApprovalGroup())
                  .map(ApprovalGroup::getName)
                  .orElse("-"));
          exporter.writeCell(getLocationName(employee, endDate));

          var projectTimeCell = exporter.getColLetter() + (exporter.getRow().get() + 1);
          exporter.writeCell(projectTime);
          var workedDurationCell = exporter.getColLetter() + (exporter.getRow().get() + 1);
          exporter.writeCell(workedDuration);

          // Let excel do the math
          // writeCell(workedDuration.minus(projectTime));
          mutator
              .cell(row.get(), col.getAndIncrement())
              .setCellFormula(workedDurationCell + "-" + projectTimeCell);

          // excel is a bit funny with durations and zero values in there, so just write a 0 if
          // there's no project time
          if (projectTime.isZero()) {
            mutator.cell(row.get(), col.getAndIncrement()).setCellTypePercentage().set(0d);
          } else {
            mutator
                .cell(row.get(), col.getAndIncrement())
                .setCellTypePercentage()
                .setCellFormula(String.format("%s/%s", projectTimeCell, workedDurationCell));
          }
          exporter.advanceRow();
        });
    if (row.get() > 1) {
      exporter.advanceCol(4);
      exporter.writeSumCell();
      exporter.writeSumCell();
      exporter.writeSumCell();
    }
  }

  private List<DetailedEmployee> getEmployees(
      DetailedCompany company, LocalDate startDate, LocalDate endDate) {
    var phaseLookup = new PhaseLookup(Set.of(Phase.ACTIVE), startDate, endDate);
    var employmentTypes = new HashSet<EmploymentType>();
    var payTypes = new HashSet<PayType>();

    if (company.getProjectTrackingConfiguration().isContractorsAllowed()) {
      employmentTypes.add(EmploymentType.CONTRACTOR);
    }

    if (company.getProjectTrackingConfiguration().isSalaryEmployeesAllowed()) {
      employmentTypes.add(EmploymentType.EMPLOYEE);
      payTypes.add(PayType.SALARY);
    }

    if (company.getProjectTrackingConfiguration().isHourlyEmployeesAllowed()) {
      employmentTypes.add(EmploymentType.EMPLOYEE);
      payTypes.add(PayType.HOURLY);
    }

    var employeeLookup =
        new EmployeeLookup()
            .withEmploymentTypes(toSetOrNull(employmentTypes))
            .withPhaseLookup(phaseLookup);
    if (!payTypes.isEmpty()) {
      employeeLookup.setPositionLookup(
          new EmployeePositionLookup().withPayTypes(payTypes).withPhaseLookup(phaseLookup));
    }

    return employeeLookupService.listAll(employeeLookup, Pageable.unpaged()).getContent();
  }

  private ProjectTrackingReportTotal findTotalOrCreate(
      List<ProjectTrackingReportTotal> projectTimeTotals,
      LocalDate projectDate,
      Project project,
      Task task) {
    return projectTimeTotals.stream()
        .filter(
            projectTimeTotal ->
                Objects.equals(projectTimeTotal.getProjectDate(), projectDate)
                    && Objects.equals(projectTimeTotal.getProject(), project)
                    && Objects.equals(projectTimeTotal.getTask(), task))
        .findFirst()
        .orElseGet(
            () -> {
              var total =
                  new ProjectTrackingReportTotal()
                      .setProjectDate(projectDate)
                      .setProject(project)
                      .setTask(task);
              projectTimeTotals.add(total);
              return total;
            });
  }

  private ProjectTrackingReportTotal findTotalOrCreate(
      List<ProjectTrackingReportTotal> projectTimeTotals, DetailedEmployee employee) {
    return projectTimeTotals.stream()
        .filter(projectTimeTotal -> Objects.equals(projectTimeTotal.getEmployee(), employee))
        .findFirst()
        .orElseGet(
            () -> {
              var total = new ProjectTrackingReportTotal().setEmployee(employee);
              projectTimeTotals.add(total);
              return total;
            });
  }

  private String getLocationName(DetailedEmployee employee, LocalDate activeDate) {
    return employee
        .getLegalWorkLocations()
        .findActive(activeDate)
        .map(WorkerLegalWorkLocation::getName)
        .map(name -> name.getLocalizedValue(messageSource, LocaleContextHolder.getLocale()))
        .orElse("-");
  }
}
