package com.everee.api.project;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class ProjectTimeQueriesService {
  private final EntityManager entityManager;

  public List<ProjectTimeDaySummary> getProjecTimeSummaries(
      Long companyId, LocalDate startDate, LocalDate endDate, Collection<Long> employeeIds) {
    String sqlString =
        "SELECT "
            + "   CAST(worklocationstarttime as date) as dt,"
            + "   sum(durationseconds) as durationseconds,"
            + "   count(distinct projectid) as projectsWorked,"
            + "   count(distinct concat(cast(projectid as text), '_', cast(taskid as text))) as projectTasksWorked,"
            + "   count(*) as projectTimeEntries"
            + " FROM ProjectTime p"
            + " WHERE p.companyId = :companyId"
            + "   AND p.worklocationstarttime >= :startTime"
            + "   AND p.worklocationstarttime < :endTime";
    if (!CollectionUtils.isEmpty(employeeIds)) {
      sqlString += " AND p.employeeid in (" + StringUtils.join(employeeIds, ", ") + ")";
    }
    sqlString += " GROUP BY 1";
    return (List)
        entityManager.createNativeQuery(sqlString).setParameter("companyId", companyId)
            .setParameter("startTime", startDate.atStartOfDay())
            .setParameter("endTime", endDate.plusDays(1).atStartOfDay()).getResultList().stream()
            .map(
                result -> {
                  Object[] resultArray = (Object[]) result;
                  LocalDate localDate = ((Date) resultArray[0]).toLocalDate();
                  Long durationSeconds = ((BigDecimal) resultArray[1]).longValue();
                  Integer projectsWorkedCount = ((BigInteger) resultArray[2]).intValue();
                  Integer projectTasksWorkedCount = ((BigInteger) resultArray[3]).intValue();
                  Integer projectTimeEntriesCount = ((BigInteger) resultArray[4]).intValue();

                  return new ProjectTimeDaySummary()
                      .setDate(localDate)
                      .setDuration(Duration.ofSeconds(durationSeconds))
                      .setProjectsWorkedCount(projectsWorkedCount)
                      .setProjectTasksWorkedCount(projectTasksWorkedCount)
                      .setProjectTimeEntriesCount(projectTimeEntriesCount);
                })
            .collect(Collectors.toList());
  }
}
