package com.everee.api.company.autopayroll;

import static com.everee.api.company.autopayroll.CompanyAutoPayrollSettingsMapper.MAPPER;

import com.everee.api.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/api-private/v1/companies/{companyId}/auto-payroll-settings")
@RequiredArgsConstructor
@RestController
public class PrivCompanyAutoPayrollSettingsController {

  private final CompanyAutoPayrollSettingsService companyAutoPayrollSettingsService;

  @GetMapping
  public CompanyAutoPayrollSettingsDto getCompanyAutoPayrollSettings(@PathVariable Long companyId) {
    return companyAutoPayrollSettingsService
        .getCompanyAutoPayrollSettings(companyId)
        .map(MAPPER::toDto)
        .orElseThrow(ResourceNotFoundException::new);
  }
}
