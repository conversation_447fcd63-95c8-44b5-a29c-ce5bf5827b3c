package com.everee.api.company.autopayroll;

import static com.everee.api.company.autopayroll.CompanyAutoPayrollSettingsMapper.MAPPER;

import com.everee.api.company.CompanyService;
import com.everee.api.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/api/v2/company/auto-payroll-settings")
@RequiredArgsConstructor
@RestController
public class CompanyAutoPayrollSettingsController {

  private final CompanyAutoPayrollSettingsService companyAutoPayrollSettingsService;

  @GetMapping
  public CompanyAutoPayrollSettingsDto getCompanyAutoPayrollSettings() {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    return companyAutoPayrollSettingsService
        .getCompanyAutoPayrollSettings(companyId)
        .map(MAPPER::toDto)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @PutMapping
  public CompanyAutoPayrollSettingsDto updateCompanyAutoPayrollSettings(
      @RequestBody CompanyAutoPayrollSettingsDto companyAutoPayrollSettingsDto) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    var updatedSettings =
        companyAutoPayrollSettingsService.updateCompanyAutoPayrollSettings(
            MAPPER.toEntity(companyAutoPayrollSettingsDto).setCompanyId(companyId));
    if (updatedSettings == null) {
      throw new ResourceNotFoundException();
    }
    return MAPPER.toDto(updatedSettings);
  }
}
