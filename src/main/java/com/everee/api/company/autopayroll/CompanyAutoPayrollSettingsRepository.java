package com.everee.api.company.autopayroll;

import java.util.Optional;
import java.util.stream.Stream;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CompanyAutoPayrollSettingsRepository
    extends JpaRepository<CompanyAutoPayrollSettings, Long> {

  Optional<CompanyAutoPayrollSettings> findByCompanyId(Long companyId);

  Stream<CompanyAutoPayrollSettings> streamAllByEnabled(boolean enabled);
}
