package com.everee.api.company;

import com.everee.api.money.Money;
import java.util.Optional;
import lombok.Value;

@Value
public class CompanyPtpcLimitsDTO {
  Money perTxnLimitAmount;
  Money companyPtpcBalance;
  Money companyPtpcLimitAmount;

  public Money getCompanyPtpcAvailableAmount() {
    return Optional.ofNullable(companyPtpcLimitAmount)
        .map(a -> a.minus(companyPtpcBalance))
        .orElse(Money.ZERO);
  }
}
