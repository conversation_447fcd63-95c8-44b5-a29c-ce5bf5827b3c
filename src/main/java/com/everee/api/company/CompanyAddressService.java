package com.everee.api.company;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.phase.PhaseQuery;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyAddressService {
  private final EntityManager entityManager;
  private final CompanyAddressRepository addressRepository;
  private final CompanyService companyService;

  public CompanyAddress getCompanyAddress(@NotNull Long addressId) {
    return addressRepository
        .findById(addressId)
        .orElseThrow(() -> new ResourceNotFoundException("Address not found"));
  }

  public Page<CompanyAddress> listCompanyAddresses(
      @NotNull Pageable pageable,
      @NotNull PhaseLookup phaseLookup,
      List<Long> ids,
      Long companyId) {
    if (ids != null) {
      return addressRepository.findAllByIdIn(ids, pageable);
    }

    return new PhaseQuery<>(entityManager, CompanyAddress.class)
        .byPhase(phaseLookup)
        .eqIfPresent("companyId", companyId)
        .findAll(pageable);
  }

  public Optional<CompanyAddress> findCompanyAddress(@NotNull Long companyId, Phase phase) {
    return new PhaseQuery<>(entityManager, CompanyAddress.class)
        .byPhase(new PhaseLookup(List.of(phase), LocalDate.now()))
        .eq("companyId", companyId)
        .findOne();
  }

  public CompanyAddress getActiveCompanyAddress(@NotNull Long companyId) {
    return new PhaseQuery<>(entityManager, CompanyAddress.class)
        .byPhase(new PhaseLookup(List.of(Phase.ACTIVE), LocalDate.now()))
        .eq("companyId", companyId)
        .findOne()
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Transactional
  public CompanyAddress createCompanyAddress(@Valid CompanyAddress newAddress) {
    var company = companyService.getCompany(newAddress.getCompanyId());
    var createError = newAddress.isValidForCreate();
    if (createError != null) {
      throw new InvalidRequestException(createError);
    }

    findCompanyAddress(company.getId(), Phase.UPCOMING).ifPresent(addressRepository::delete);

    findCompanyAddress(company.getId(), Phase.ACTIVE)
        .ifPresent(
            currentAddress -> {
              if (currentAddress.getStartDate().isBefore(newAddress.getStartDate())) {
                currentAddress.setEndDate(newAddress.getStartDate().minusDays(1));
              } else {
                throw new InvalidRequestException(
                    "Unable to backdate address to before active address");
              }
            });

    newAddress.setCompanyId(company.getId());
    newAddress.setEndDate(null);

    return addressRepository.save(newAddress);
  }

  public CompanyAddress updateCompanyAddress(
      @NotNull Long addresssId, @Valid CompanyAddress updatedAddress) {
    var address = getCompanyAddress(addresssId);

    var currentPhase = address.getCurrentPhase();
    if (currentPhase != Phase.UPCOMING) {
      throw new InvalidRequestException("Cannot change an active or expired address");
    }

    address.setLine1(updatedAddress.getLine1());
    address.setLine2(updatedAddress.getLine2());
    address.setCity(updatedAddress.getCity());
    address.setState(updatedAddress.getState());
    address.setPostalCode(updatedAddress.getPostalCode());

    return addressRepository.save(address);
  }

  public void deleteCompanyAddress(@NotNull Long addresssId) {
    var address = getCompanyAddress(addresssId);

    var deleteError = address.isValidForDelete();
    if (deleteError != null) {
      throw new InvalidRequestException(deleteError);
    }

    findCompanyAddress(address.getCompanyId(), Phase.ACTIVE)
        .ifPresent(
            (activeAddress) -> {
              activeAddress.setEndDate(null);
              addressRepository.save(activeAddress);
            });

    addressRepository.delete(address);
  }
}
