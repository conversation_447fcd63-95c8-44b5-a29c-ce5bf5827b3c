package com.everee.api.company.event;

import com.everee.api.company.CompanyAchConfiguration;
import com.everee.api.company.DetailedCompany;
import com.everee.api.event.ResourceEvent;
import lombok.Getter;
import lombok.NonNull;

public class CompanyAchConfigurationChangedEvent extends ResourceEvent {

  @Getter private final CompanyAchConfiguration oldConfig;

  @Getter private final CompanyAchConfiguration newConfig;

  public CompanyAchConfigurationChangedEvent(
      @NonNull DetailedCompany company,
      @NonNull CompanyAchConfiguration oldConfig,
      @NonNull CompanyAchConfiguration newConfig) {
    super(company.getId(), company);
    this.oldConfig = oldConfig;
    this.newConfig = newConfig;
  }

  public DetailedCompany getCompany() {
    return (DetailedCompany) getSource();
  }

  @Override
  public String getResourceId() {
    return getCompany().getId().toString();
  }

  @Override
  public String getResourceName() {
    return "company";
  }

  @Override
  public String getActionName() {
    return "ach-config-changed";
  }
}
