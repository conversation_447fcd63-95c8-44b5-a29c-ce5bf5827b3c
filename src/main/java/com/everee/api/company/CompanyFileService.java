package com.everee.api.company;

import com.everee.api.company.document.*;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.file.*;
import com.everee.api.form.*;
import com.everee.api.form.lookup.FormRepository;
import com.everee.api.storage.StorageService;
import com.everee.api.user.UserService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class CompanyFileService extends FileService<CompanyDocument> {
  private final CompanyDocumentRepository companyDocumentRepository;
  private final CompanyDocumentSigningRequestInfoRepository
      companyDocumentSigningRequestInfoRepository;
  private final FormRepository formRepository;
  private final FolderService folderService;

  public CompanyFileService(
      FolderRepository folderRepository,
      StorageService storageService,
      CompanyDocumentRepository companyDocumentRepository,
      CompanyDocumentSigningRequestInfoRepository companyDocumentSigningRequestInfoRepository,
      FormRepository formRepository,
      FolderService folderService,
      ApplicationEventPublisher applicationEventPublisher,
      DocumentEventService documentEventService) {
    super(folderRepository, storageService, applicationEventPublisher, documentEventService);
    this.companyDocumentRepository = companyDocumentRepository;
    this.companyDocumentSigningRequestInfoRepository = companyDocumentSigningRequestInfoRepository;
    this.formRepository = formRepository;
    this.folderService = folderService;
  }

  @Override
  public CompanyDocument newDocumentInstance(Long companyId, Long employeeId) {
    var document = new CompanyDocument();
    document.setCompanyId(companyId);
    document.setPublishedToCompanyAt(LocalDateTime.now());

    return document;
  }

  @Override
  public Page<CompanyDocument> find(
      Long companyId, Long employeeId, Long folderId, Pageable pageable) {
    return companyDocumentRepository
        .findByCompanyIdAndFolderIdAndDeletedAtIsNullAndPublishedToCompanyAtIsNotNull(
            companyId, folderId, pageable);
  }

  @Override
  public CompanyDocument findById(Long companyId, Long id) {
    return companyDocumentRepository
        .findByCompanyIdAndId(companyId, id)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public File<CompanyDocument> getDocumentFile(Long companyId, Long employeeId, Long id) {
    var doc = companyDocumentRepository.getOne(id);
    Assert.isTrue(Objects.equals(companyId, doc.getCompanyId()), "Invalid document");

    return addDocumentUrl(doc);
  }

  @Override
  public void delete(CompanyDocument document) {
    companyDocumentRepository.delete(document);
  }

  @Override
  public CompanyDocument save(CompanyDocument document) {
    return companyDocumentRepository.save(document);
  }

  @Override
  public List<CompanyDocument> saveAll(List<CompanyDocument> documents) {
    return companyDocumentRepository.saveAll(documents);
  }

  @Override
  public List<CompanyDocument> findByDeletedAtBefore(LocalDate deletedAtMaxExclusive) {
    return companyDocumentRepository.findByDeletedAtBefore(deletedAtMaxExclusive);
  }

  public List<File<CompanyDocument>> findByCompanyIdAndFileNameStartsWith(
      Long companyId, String filenameStart) {
    return companyDocumentRepository
        .findByCompanyIdAndFileNameStartsWithAndDeletedAtIsNull(companyId, filenameStart).stream()
        .map(this::addDocumentUrl)
        .collect(Collectors.toList());
  }

  public Optional<Folder> getSignedDocumentsFolder(Long companyId) {
    return folderService.getSystemFolder(companyId, SystemFolderType.SIGNED_DOCUMENTS, null);
  }

  public Folder getOrCreateSignedDocumentsFolder(Long companyId) {
    return folderService.getOrCreateSystemFolder(
        companyId, SystemFolderType.SIGNED_DOCUMENTS, null, false);
  }

  public Folder getOrCreateArchivedDocumentsFolder(Long companyId) {
    return folderService.getOrCreateSystemFolder(
        companyId, SystemFolderType.ARCHIVED_DOCUMENTS, null, false);
  }

  public Optional<Folder> getArchivedDocumentsFolder(Long companyId) {
    return folderService.getSystemFolder(companyId, SystemFolderType.ARCHIVED_DOCUMENTS, null);
  }

  public Folder getOrCreateTaxDocumentsFolder(Long companyId) {
    return folderService.getOrCreateSystemFolder(
        companyId, SystemFolderType.TAX_DOCUMENTS, null, true);
  }

  public Folder getOrCreateGarnishmentOrdersFolder(Long companyId) {
    return folderService.getOrCreateSystemFolder(
        companyId, SystemFolderType.GARNSISHMENT_ORDERS, null, true);
  }

  public Folder getOrCreateCompanyFolder(Long companyId, Long parentFolderId, String name) {
    var folders =
        folderRepository.findFirstByCompanyIdAndParentFolderIdAndNameAndEmployeeIdIsNull(
            companyId, parentFolderId, name);

    return folders.stream()
        .findFirst()
        .orElseGet(
            () ->
                folderRepository.save(
                    new Folder()
                        .setCompanyId(companyId)
                        .setParentFolderId(parentFolderId)
                        .setName(name)));
  }

  @Transactional
  public File<CompanyDocument> requestSignature(
      Long companyId, String fileId, CompanyDocumentSignatureRequest signatureRequest) {
    var file = getFile(companyId, null, fileId);
    if (!file.getIsDocument() || !(file.getDocument() instanceof CompanyDocument)) {
      throw new ResourceNotFoundException();
    }
    var companyDocument = (CompanyDocument) file.getDocument();
    companyDocument.setFolderId(getOrCreateSignedDocumentsFolder(companyId).getId());

    ensureRequestSignatureFormExists(companyId, companyDocument);

    CompanyDocumentSigningRequestInfo info = null;

    if (signatureRequest.getOnboardingSettings() != null) {
      info =
          buildOnboardingInfo(
              signatureRequest.getOnboardingSettings(), companyDocument.getSigningInfo());
      companyDocument
          .getSigningInfo()
          .setOnboardingSignableByContractor(
              signatureRequest.getOnboardingSettings().isSignedByContractors())
          .setOnboardingSignableByHourly(
              signatureRequest.getOnboardingSettings().isSignedByHourly())
          .setOnboardingSignableBySalary(
              signatureRequest.getOnboardingSettings().isSignedBySalary());
    }

    var isSendNow =
        signatureRequest.isSendToContractors()
            || signatureRequest.isSendToSalary()
            || signatureRequest.isSendToHourly();

    if (isSendNow) {
      sendSignatureRequestEmails(companyId, companyDocument, signatureRequest, info);
    } else if (info != null) {
      info.setCompanyDocumentId(companyDocument.getId());
      companyDocumentSigningRequestInfoRepository.save(info);
    }

    return new File(companyDocumentRepository.save(companyDocument));
  }

  private CompanyDocumentSigningRequestInfo buildOnboardingInfo(
      CompanyDocumentOnboardingSignatureRequest onboardingSettings,
      CompanyDocumentSigningInfo signingInfo) {
    var sendToHourlyChanged =
        signingInfo.isOnboardingSignableByHourly() != onboardingSettings.isSignedByHourly();
    var sendToSalaryChanged =
        signingInfo.isOnboardingSignableBySalary() != onboardingSettings.isSignedBySalary();
    var sendToContractorsChanged =
        signingInfo.isOnboardingSignableByContractor()
            != onboardingSettings.isSignedByContractors();

    if (sendToContractorsChanged || sendToHourlyChanged || sendToSalaryChanged) {
      var info =
          new CompanyDocumentSigningRequestInfo()
              .setMessage("")
              .setCreatedByUserId(UserService.getAuthenticatedUserId());
      if (sendToHourlyChanged)
        info.setNewOnboardingHourlyValue(onboardingSettings.isSignedByHourly());
      if (sendToSalaryChanged)
        info.setNewOnboardingSalaryValue(onboardingSettings.isSignedBySalary());
      if (sendToContractorsChanged)
        info.setNewOnboardingContractorValue(onboardingSettings.isSignedByContractors());
      return info;
    }

    return null;
  }

  private void sendSignatureRequestEmails(
      Long companyId,
      CompanyDocument companyDocument,
      CompanyDocumentSignatureRequest signatureRequest,
      CompanyDocumentSigningRequestInfo info) {
    var companyDocumentSigningInfo =
        Optional.ofNullable(info)
            .orElse(new CompanyDocumentSigningRequestInfo())
            .setCompanyDocumentId(companyDocument.getId())
            .setMessage(signatureRequest.getMessage())
            .setSendToContractors(signatureRequest.isSendToContractors())
            .setSendToHourly(signatureRequest.isSendToHourly())
            .setSendToSalary(signatureRequest.isSendToSalary())
            .setCreatedByUserId(UserService.getAuthenticatedUserId());

    companyDocument.getSigningInfo().getSignatureRequests().add(companyDocumentSigningInfo);

    eventPublisher.publishEvent(
        new CompanyDocumentSignatureRequestEvent(
            companyId,
            companyDocumentSigningInfo,
            companyDocument.getSigningInfo().getSignatureFormId(),
            companyDocument.getFileName()));

    companyDocumentSigningRequestInfoRepository.save(companyDocumentSigningInfo);
  }

  private void ensureRequestSignatureFormExists(Long companyId, CompanyDocument companyDocument) {
    if (companyDocument.getSigningInfo().getSignatureFormId() != null) {
      return;
    }

    var form =
        new Form()
            .setName(companyDocument.getFileName())
            .setCompanyId(companyId)
            .setFormType(FormType.POLICY)
            .setSignatureType(FormSignatureType.EMPLOYEE)
            .setFormRenderer(FormRenderer.COMPANY_DOCUMENT_PREVIEW)
            .setStatus(FormStatus.LIVE);
    formRepository.save(form);

    var formQuestion =
        new FormQuestion()
            .setForm(form)
            .setQuestionNum(0)
            .setQuestionType(FormQuestionType.COMPANY_DOCUMENT_PREVIEW)
            .setCompanyDocumentId(companyDocument.getId())
            .setQuestion("");
    form.getQuestions().add(formQuestion);

    formRepository.save(form);
    companyDocument.getSigningInfo().setSignatureFormId(form.getId());
  }

  @Transactional
  public void archiveDocument(String fileId, Long companyId, Long userId) {
    var documentFile = getDocumentFile(companyId, null, File.parseLongId(fileId));
    archiveDocument(
        documentFile.getDocument(), userId, getOrCreateArchivedDocumentsFolder(companyId));
  }

  @Transactional
  public void unarchiveDocument(String fileId, Long companyId) {
    var documentFile = getDocumentFile(companyId, null, File.parseLongId(fileId));
    Folder folder = null;
    if (documentFile.getDocument().getSigningInfo().getSignatureFormId() != null) {
      folder = getOrCreateSignedDocumentsFolder(companyId);
    }
    unarchiveDocument(documentFile.getDocument(), folder);
  }
}
