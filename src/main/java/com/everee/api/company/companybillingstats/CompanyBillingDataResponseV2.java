package com.everee.api.company.companybillingstats;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface CompanyBillingDataResponseV2 {
  Long getId();

  String getDisplayName();

  String getLegalEntityName();

  String getPartnerLegalName();

  LocalDate getStartDate();

  String getCreditLevel();

  BigDecimal getCreditPercentageRate();

  BigDecimal getUnitPrice();

  BigDecimal getInterestRate();

  BigDecimal getPerEmployeePriceMonthly();

  BigDecimal getPerContractorPriceMonthly();

  BigDecimal getCommittedPaymentDollars();

  BigDecimal getAllocatedCreditDollars();

  String getBankName();

  String getRoutingNumber();

  String getAccountNumber();

  Long getEmployeeCount();

  Long getContractorCount();

  Long getContractorPaymentCount();

  Long getEmployeePaymentCount();

  BigDecimal getContractorPaymentAmount();

  BigDecimal getEmployeePaymentAmount();

  BigDecimal getFundedAmount();

  BigDecimal getCreditPaymentAmount();

  Long getWorkerInitiatedInstantDepositCount();

  BigDecimal getWorkerInitiatedInstantDepositAmount();

  Long getCompanyInitiatedInstantDepositCount();

  BigDecimal getCompanyInitiatedInstantDepositAmount();

  Long getPayCardDepositCount();

  BigDecimal getPayCardDepositAmount();
}
