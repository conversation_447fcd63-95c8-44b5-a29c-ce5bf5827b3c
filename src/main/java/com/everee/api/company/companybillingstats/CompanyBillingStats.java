package com.everee.api.company.companybillingstats;

import com.everee.api.model.DateRange;
import java.time.LocalDate;
import java.util.Locale;
import lombok.Data;

@Data
public class CompanyBillingStats {
  private final Long id;
  private final String displayName;
  private final String legalEntityName;
  private final Long employeeCount;
  private final Long contractorCount;
  private final LocalDate startDate;
  private final LocalDate endDate;

  public String getDateRangeAsString() {
    return DateRange.of(getStartDate(), getEndDate()).getLocalizedDescription(Locale.ENGLISH);
  }

  public static CompanyBillingStats of(
      CompanyBillingDataResponse response, LocalDate startDate, LocalDate endDate) {
    return new CompanyBillingStats(
        response.getId(),
        response.getDisplayName(),
        response.getLegalEntityName(),
        response.getEmployeeCount(),
        response.getContractorCount(),
        startDate,
        endDate);
  }
}
