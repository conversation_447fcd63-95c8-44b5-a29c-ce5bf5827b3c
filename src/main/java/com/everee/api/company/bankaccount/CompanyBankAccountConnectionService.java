package com.everee.api.company.bankaccount;

import com.everee.api.company.Company;
import java.util.List;
import java.util.Optional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CompanyBankAccountConnectionService {
  private final CompanyBankAccountRepository companyBankAccountRepository;
  private final List<BankAccountConnectionStatusProvider> connectionStatusProviders;

  /**
   * Finds the active company bank account, validates the status (if possible) and returns the
   * status found.
   *
   * @param company to validate funding bank account for
   * @return the connection status found by the provider; the existing status if a provider is
   *     missing
   */
  @Transactional
  public CompanyBankAccountConnectionStatus validateAndUpdateBankAccountConnectionStatus(
      @NonNull Company company) {
    return companyBankAccountRepository
        .findActiveForCompany(company.getId())
        .map(this::updateConnectionStatus)
        .orElse(CompanyBankAccountConnectionStatus.OK);
  }

  private CompanyBankAccountConnectionStatus updateConnectionStatus(
      @NonNull CompanyBankAccount companyBankAccount) {
    var statusProvider = findStatusProvider(companyBankAccount);
    if (statusProvider.isEmpty()) return companyBankAccount.getConnectionStatus();

    var status = statusProvider.get().getConnectionStatus(companyBankAccount);
    companyBankAccount.setConnectionStatus(status);

    return status;
  }

  private Optional<BankAccountConnectionStatusProvider> findStatusProvider(
      @NonNull CompanyBankAccount bankAccount) {
    return connectionStatusProviders.stream()
        .filter(p -> p.canRequestConnectionStatus(bankAccount))
        .findFirst();
  }
}
