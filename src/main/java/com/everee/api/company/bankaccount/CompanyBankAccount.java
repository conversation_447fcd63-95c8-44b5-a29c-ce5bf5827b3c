package com.everee.api.company.bankaccount;

import com.everee.api.bankaccount.BankAccount;
import com.everee.api.bankaccount.BankAccountType;
import java.time.ZonedDateTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;

@Accessors(chain = true)
@Data
@Entity
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyBankAccount extends BankAccount<CompanyBankAccount> {

  @NotNull private Long companyId;

  @NotNull
  @Enumerated(EnumType.STRING)
  private BankAccountType accountType;

  private Long plaidItemId;
  private String plaidAccountId;

  private String finicityCustomerId;
  private String finicityAccountId;
  private Long finicityInstitutionLoginId;
  private String finicityAccountOwners;

  @Enumerated(EnumType.STRING)
  private CompanyBankAccountConnectionStatus connectionStatus =
      CompanyBankAccountConnectionStatus.OK;

  private boolean locked;
  private String authorizationAgreementSignature;
  private Long authorizationAgreementSignedByUserId;
  private ZonedDateTime authorizationAgreementSignedAt;

  public boolean isAuthorizationAgreementSigned() {
    return ObjectUtils.allNotNull(
        authorizationAgreementSignedAt,
        authorizationAgreementSignature,
        authorizationAgreementSignedByUserId);
  }
}
