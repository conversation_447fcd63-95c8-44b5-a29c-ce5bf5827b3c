package com.everee.api.company;

import static com.everee.api.company.CompanyBillingConfiguration_.SF_ACCOUNT_ID;
import static com.everee.api.company.DetailedCompany_.*;
import static com.everee.api.model.BaseModelV2_.ID;
import static com.everee.api.query.where.Like.safePercentBuffered;
import static com.everee.api.query.where.Like.safeToLowerCase;
import static com.everee.api.query.where.Where.by;
import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.TenantlessLookupService;
import com.everee.api.phase.PhaseQuery;
import java.util.function.BiFunction;
import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DetailedCompanyLookupService
    implements TenantlessLookupService<
        DetailedCompany, DetailedCompanyLookup, PhaseQuery<DetailedCompany>> {

  private final EntityManager entityManager;

  @Override
  public void configureQuery(DetailedCompanyLookup lookup, PhaseQuery<DetailedCompany> query) {
    if (lookup.getPhaseLookup() != null) {
      query.byPhase(lookup.getPhaseLookup());
    }

    query
        .where(property(ID).in(lookup.getIds()))
        .where(by(searchFilter(lookup.getSearchQuery())))
        .where(by(sfAccountIdMatcher(lookup.getSfAccountId())))
        .where(property(PARTNER_ID).in(lookup.getPartnerIds()));
  }

  private BiFunction<CriteriaBuilder, Root<?>, Predicate> sfAccountIdMatcher(String sfAccountId) {
    return (builder, root) -> {
      if (StringUtils.isBlank(sfAccountId)) return builder.conjunction();

      Join<DetailedCompany, CompanyBillingConfiguration> companyCompanyBillingConfigurationJoin =
          root.join(COMPANY_BILLING_CONFIGURATION);
      var sfAccountIdPath = companyCompanyBillingConfigurationJoin.get(SF_ACCOUNT_ID);

      return builder.equal(sfAccountIdPath, sfAccountId);
    };
  }

  private BiFunction<CriteriaBuilder, Root<?>, Predicate> searchFilter(String filter) {
    return (builder, root) -> {
      if (StringUtils.isBlank(filter)) return builder.conjunction();

      var searchFilterVal = safeToLowerCase(filter.strip());
      var searchLikeQuery = safePercentBuffered(searchFilterVal);

      Path<Long> idPath = root.get(ID);
      Path<String> displayNamePath = root.get(DISPLAY_NAME);
      Path<String> legalEntityNamePath = root.get(LEGAL_ENTITY_NAME);

      return builder.or(
          builder.like(builder.lower(displayNamePath), searchLikeQuery),
          builder.like(builder.lower(legalEntityNamePath), searchLikeQuery),
          builder.like(builder.lower(idPath.as(String.class)), searchLikeQuery)
      );
    };
  }

  @Override
  public PhaseQuery<DetailedCompany> createQuery() {
    return new PhaseQuery<>(entityManager, DetailedCompany.class);
  }
}
