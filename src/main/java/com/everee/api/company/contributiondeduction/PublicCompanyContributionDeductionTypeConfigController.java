package com.everee.api.company.contributiondeduction;

import com.everee.api.auth.annotation.PeopleApproverAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@PeopleApproverAccess
@RestController()
@RequestMapping("/api/v2/company-contribution-deduction-type-config")
@RequiredArgsConstructor
@Transactional
public class PublicCompanyContributionDeductionTypeConfigController {

  private final CompanyContributionDeductionTypeConfigService
      companyContributionDeductionTypeConfigService;

  @GetMapping("/{type}/is-fund-and-remit")
  public Boolean isFundAndRemitForEcdType(@PathVariable EmployeeContributionDeductionType type) {
    return companyContributionDeductionTypeConfigService
        .getConfig(type, CompanyService.getAuthenticatedCompanyId())
        .isFundAndRemit();
  }
}
