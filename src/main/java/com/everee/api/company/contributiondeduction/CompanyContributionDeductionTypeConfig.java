package com.everee.api.company.contributiondeduction;

import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.model.BaseModel;
import com.sun.istack.NotNull;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Entity
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyContributionDeductionTypeConfig extends BaseModel {
  @NotNull private Long companyId;

  @Enumerated(EnumType.STRING)
  @NotNull
  private EmployeeContributionDeductionType employeeContributionDeductionType;

  @NotNull private boolean fundAndRemit = false;
  @NotNull private boolean prefundFullAmount = false;

  @Enumerated(EnumType.STRING)
  @NotNull
  private CompanyContributionDeductionFundingSourceType fundingSourceType;
}
