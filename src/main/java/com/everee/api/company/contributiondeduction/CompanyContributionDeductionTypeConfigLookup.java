package com.everee.api.company.contributiondeduction;

import com.everee.api.config.RequestParams;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.lookup.Lookup;
import com.everee.api.util.SetUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiParam;
import java.util.Set;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@RequestParams
public class CompanyContributionDeductionTypeConfigLookup implements Lookup {
  @JsonProperty("company-id")
  @ApiParam(name = "company-id", example = "1")
  private Long companyId;

  @JsonIgnore
  public Set<Long> getCompanyIds() {
    return SetUtils.toSetOrNull(companyId);
  }

  @JsonProperty("id")
  @ApiParam(name = "id", allowMultiple = true)
  private Set<Long> ids;

  @JsonProperty("type")
  @ApiParam(name = "type", allowMultiple = true)
  private Set<EmployeeContributionDeductionType> types;
}
