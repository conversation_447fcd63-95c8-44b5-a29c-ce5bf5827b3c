package com.everee.api.company.document;

import com.everee.api.auth.annotation.EvereeAdminAccess;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api-private/v1/company-files")
@EvereeAdminAccess
@RequiredArgsConstructor
public class CompanyDocumentSignaturePrivateController {
  private final CompanyDocumentSignatureRequestService companyDocumentSignatureRequestService;

  @PostMapping("/send-company-document-signature-summaries")
  public void sendCompanyDocumentSignatureSummaries(
      @RequestParam(name = "signature-after", required = false) LocalDateTime signatureAfter) {
    companyDocumentSignatureRequestService.sendCompanySignatureRequestNotifications(
        Optional.ofNullable(signatureAfter).orElse(LocalDateTime.now().minusDays(1)));
  }
}
