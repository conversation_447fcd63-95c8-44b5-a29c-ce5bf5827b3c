package com.everee.api.company;

import java.util.List;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CoreCompanyRepository extends JpaRepository<CoreCompany, Long> {
  Page<CoreCompany> findAllByIdIn(Set<Long> ids, Pageable pageable);

  @Query("select c from CoreCompany c where lower(c.displayName) like %?1% ")
  Page<CoreCompany> findAllLikeName(String pattern, Pageable pageable);

  @Query("select c.distributionFlowType <> 'DISABLED' from CoreCompany c where id=?1 ")
  boolean isNewDistributionFlowEnabled(Long companyId);

  @Query("select c.id from CoreCompany c where c.demoCompany = true")
  List<Long> listDemoCompaniesIds();
}
