package com.everee.api.company;

/**
 *
 *
 * <ul>
 *   <li>F = Federal govt. (Federal government entity or instrumentality)
 *   <li>S = State/local non-501c. (State or local government or instrumentality (this includes
 *       cities, townships, counties, special-purpose districts or other publicly-owned entities
 *       with governmental authority))
 *   <li>T = 501c non-govt. (Non-governmental tax-exempt Section 501(c) organization (types of
 *       501(c) non-governmental organizations include private foundations, public charities, social
 *       and recreation clubs and veterans’ organizations))
 *   <li>Y = State/local 501c. (State or local government or instrumentality where the employer
 *       received a determination letter from the IRS indication that they are also a tax-exempt
 *       organization under Section 501(c)(3))
 *   <li>N = None Apply
 * </ul>
 */
public enum CompanyTaxKindOfEmployer {
  F,
  S,
  T,
  Y,
  N;
}
