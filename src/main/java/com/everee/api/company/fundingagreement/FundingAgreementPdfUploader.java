package com.everee.api.company.fundingagreement;

import com.everee.api.google.GoogleProperties;
import com.everee.api.pdf.FilePdf;
import com.google.api.client.http.ByteArrayContent;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import java.io.IOException;
import java.util.List;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class FundingAgreementPdfUploader {

  private final Drive drive;

  private final GoogleProperties googleProperties;

  public File upload(@NonNull FilePdf filePdf) throws IOException {
    var fileContent = new ByteArrayContent("application/pdf", filePdf.getData());

    var fileMetadata =
        new File()
            .setName(filePdf.getFilename().replace(".pdf", ""))
            .setParents(List.of(googleProperties.getFundingAgreementsDriveId()));

    fileMetadata =
        drive
            .files()
            .create(fileMetadata, fileContent)
            .setFields("id,name,mimeType,webViewLink")
            .setSupportsAllDrives(true)
            .execute();

    log.info("Uploaded funding agreement PDF to {}", fileMetadata.getWebViewLink());

    return fileMetadata;
  }
}
