package com.everee.api.company.fundingagreement;

import com.everee.api.company.Company;
import com.everee.api.company.bankaccount.CompanyBankAccount;
import com.everee.api.pdf.brandcomponents.BrandLabeledField;
import com.itextpdf.layout.element.Div;
import lombok.NonNull;

class FundingAccountDetailsSection extends Div {
  FundingAccountDetailsSection(@NonNull CompanyBankAccount bankAccount, @NonNull Company company) {
    add(new BrandLabeledField("Legal business name", company.getLegalEntityName()));

    add(new BrandLabeledField("Financial institution", bankAccount.getBankName()));

    add(new BrandLabeledField("Routing number", bankAccount.getRoutingNumber()));

    add(new BrandLabeledField("Business checking account number", bankAccount.getAccountNumber()));
  }
}
