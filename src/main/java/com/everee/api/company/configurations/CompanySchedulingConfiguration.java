package com.everee.api.company.configurations;

import com.everee.api.company.DetailedCompany;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import javax.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

@Data
@Entity
public class CompanySchedulingConfiguration {

  @Id
  @Column(insertable = false, updatable = false)
  private Long companyId;

  @CreationTimestamp private LocalDateTime createdAt;

  @Version private LocalDateTime updatedAt;

  private boolean hourlyEmployeesAllowed;
  private boolean salaryEmployeesAllowed;
  private boolean contractorsAllowed;
  private boolean openShiftsEnabled;
  private boolean shiftCoverageEnabled;
  private boolean publishReminderEnabled;
  private boolean testModeEnabled;
  private boolean schedulingEnabled;

  @Enumerated(EnumType.STRING)
  private DayOfWeek publishReminderDay = DayOfWeek.THURSDAY;

  private LocalTime publishReminderTime = LocalTime.of(17, 0, 0);

  @JsonIgnore
  @OneToOne
  @JoinColumn(name = "companyId")
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  private DetailedCompany company;

  public Long getId() {
    return companyId;
  }

  public boolean isHourlyEmployeesAllowed() {
    return isSchedulingEnabled() && hourlyEmployeesAllowed;
  }

  public boolean isSalaryEmployeesAllowed() {
    return isSchedulingEnabled() && salaryEmployeesAllowed;
  }

  public boolean isContractorsAllowed() {
    return isSchedulingEnabled() && contractorsAllowed;
  }

  public boolean isOpenShiftsEnabled() {
    return isSchedulingEnabled() && openShiftsEnabled;
  }

  public boolean isShiftCoverageEnabled() {
    return isSchedulingEnabled() && shiftCoverageEnabled;
  }

  public boolean isPublishReminderEnabled() {
    return isSchedulingEnabled() && publishReminderEnabled;
  }

  public boolean isTestModeEnabled() {
    return isSchedulingEnabled() && testModeEnabled;
  }

  public void setCompany(@NonNull DetailedCompany company) {
    this.companyId = company.getId();
    this.company = company;
  }
}
