package com.everee.api.company.configurations;

import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

@Value
@Builder
public class CompanyProjectTrackingConfigDTO {
  @NonNull Long companyId;
  @NonNull Boolean projectTrackingEnabled;
  @NonNull Boolean hourlyEmployeesAllowed;
  @NonNull Boolean salaryEmployeesAllowed;
  @NonNull Boolean contractorsAllowed;

  public static CompanyProjectTrackingConfigDTO fromConfig(
      @NonNull CompanyProjectTrackingConfiguration projectTrackingConfig,
      @NonNull CompanyTimeTrackingConfiguration timeTrackingConfig) {
    var timeClockEnabled =
        timeTrackingConfig.getTimeTrackingMode().equals(TimeTrackingMode.TIME_CLOCK);

    return CompanyProjectTrackingConfigDTO.builder()
        .companyId(projectTrackingConfig.getCompanyId())
        .projectTrackingEnabled(projectTrackingConfig.isProjectTrackingEnabled())
        .hourlyEmployeesAllowed(
            projectTrackingConfig.isHourlyEmployeesAllowed() && timeClockEnabled)
        .salaryEmployeesAllowed(projectTrackingConfig.isSalaryEmployeesAllowed())
        .contractorsAllowed(projectTrackingConfig.isContractorsAllowed())
        .build();
  }
}
