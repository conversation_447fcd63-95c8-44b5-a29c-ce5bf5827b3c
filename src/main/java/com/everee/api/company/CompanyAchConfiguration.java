package com.everee.api.company;

import com.everee.api.ach.AchBankAccount;
import com.everee.api.ach.AchConfiguration;
import com.everee.api.funding.CompanyFundingTransferType;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentFundingType;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Embeddable
@Accessors(chain = true)
public class CompanyAchConfiguration {

  public static final Money WORKER_ACH_FEE = Money.valueOf("1.50");

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentFundingType traditionalFundingType = PaymentFundingType.PRE_FUNDED;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentFundingType adHocFundingType = PaymentFundingType.PRE_FUNDED;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentFundingType pywFundingType = PaymentFundingType.PRE_FUNDED;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentFundingType podFundingType = PaymentFundingType.PRE_FUNDED;

  @NotNull private Long achConfigurationId = 1L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "achConfigurationId", insertable = false, updatable = false)
  private AchConfiguration achConfiguration;

  public void setAchConfiguration(AchConfiguration achConfiguration) {
    this.achConfiguration = achConfiguration;
    this.achConfigurationId = achConfiguration.getId();
  }

  @NotNull private Long preFundedPaymentAccountId = 1L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "preFundedPaymentAccountId", insertable = false, updatable = false)
  private AchBankAccount preFundedPaymentAccount;

  public void setPreFundedPaymentAccount(AchBankAccount achBankAccount) {
    this.preFundedPaymentAccount = achBankAccount;
    this.preFundedPaymentAccountId = achBankAccount.getId();
  }

  @NotNull private Long preFundedFundingAccountId = 1L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "preFundedFundingAccountId", insertable = false, updatable = false)
  private AchBankAccount preFundedFundingAccount;

  public void setPreFundedFundingAccount(AchBankAccount achBankAccount) {
    this.preFundedFundingAccount = achBankAccount;
    this.preFundedFundingAccountId = achBankAccount.getId();
  }

  @NotNull private Long postFundedPaymentAccountId = 2L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "postFundedPaymentAccountId", insertable = false, updatable = false)
  private AchBankAccount postFundedPaymentAccount;

  public void setPostFundedPaymentAccount(AchBankAccount achBankAccount) {
    this.postFundedPaymentAccount = achBankAccount;
    this.postFundedPaymentAccountId = achBankAccount.getId();
  }

  @NotNull private Long postFundedFundingAccountId = 4L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "postFundedFundingAccountId", insertable = false, updatable = false)
  private AchBankAccount postFundedFundingAccount;

  public void setPostFundedFundingAccount(AchBankAccount achBankAccount) {
    this.postFundedFundingAccount = achBankAccount;
    this.postFundedFundingAccountId = achBankAccount.getId();
  }

  @NotNull private Long taxLockboxAccountId = 5L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "taxLockboxAccountId", insertable = false, updatable = false)
  private AchBankAccount taxLockboxAccount;

  public void setTaxLockboxAccount(AchBankAccount achBankAccount) {
    this.taxLockboxAccount = achBankAccount;
    this.taxLockboxAccountId = achBankAccount.getId();
  }

  @NotNull private Long contributionDeductionLockboxAccountId = 6L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "contributionDeductionLockboxAccountId", insertable = false, updatable = false)
  private AchBankAccount contributionDeductionLockboxAccount;

  public void setContributionDeductionLockboxAccount(AchBankAccount achBankAccount) {
    this.contributionDeductionLockboxAccount = achBankAccount;
    this.contributionDeductionLockboxAccountId = achBankAccount.getId();
  }

  @NotNull private Long feeLockboxAccountId = 16L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "feeLockboxAccountId", insertable = false, updatable = false)
  private AchBankAccount feeLockboxAccount;

  public void setFeeLockboxAccount(AchBankAccount achBankAccount) {
    this.feeLockboxAccount = achBankAccount;
    this.feeLockboxAccountId = achBankAccount.getId();
  }

  @NotNull private Long overAchLimitPaymentAccountId = 2L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "overAchLimitPaymentAccountId", insertable = false, updatable = false)
  private AchBankAccount overAchLimitPaymentAccount;

  public void setOverAchLimitPaymentAccount(AchBankAccount achBankAccount) {
    this.overAchLimitPaymentAccount = achBankAccount;
    this.overAchLimitPaymentAccountId = achBankAccount.getId();
  }

  @NotNull private Long overAchLimitFundingAccountId = 4L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "overAchLimitFundingAccountId", insertable = false, updatable = false)
  private AchBankAccount overAchLimitFundingAccount;

  public void setOverAchLimitFundingAccount(AchBankAccount achBankAccount) {
    this.overAchLimitFundingAccount = achBankAccount;
    this.overAchLimitFundingAccountId = achBankAccount.getId();
  }

  @NotNull private Long returnPaymentLockboxAccountId = 12L;

  @NotNull
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "returnPaymentLockboxAccountId", insertable = false, updatable = false)
  private AchBankAccount returnPaymentLockboxAccount;

  public void setReturnPaymentLockboxAccount(AchBankAccount achBankAccount) {
    this.returnPaymentLockboxAccount = achBankAccount;
    this.returnPaymentLockboxAccountId = achBankAccount.getId();
  }

  private boolean disableACHFundingGeneration = false;

  public boolean isPostFundingEnabled() {
    return traditionalFundingType == PaymentFundingType.POST_FUNDED
        || adHocFundingType == PaymentFundingType.POST_FUNDED
        || pywFundingType == PaymentFundingType.POST_FUNDED
        || podFundingType == PaymentFundingType.POST_FUNDED;
  }

  @Enumerated(EnumType.STRING)
  private CompanyFundingTransferType fundingTransferType = CompanyFundingTransferType.ACH;

  private boolean workerPaidAchFee = false;

  private Double achLimit;

  public Set<PaymentFundingType> getFundingTypes() {
    return new HashSet<>(
        List.of(traditionalFundingType, adHocFundingType, pywFundingType, podFundingType));
  }
}
