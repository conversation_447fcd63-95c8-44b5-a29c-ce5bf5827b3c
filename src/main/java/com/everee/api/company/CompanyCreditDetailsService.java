package com.everee.api.company;

import static com.everee.api.storage.StorageAccess.EVEREE_INTERNAL;

import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import java.io.IOException;
import java.util.List;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@RequiredArgsConstructor
@Transactional
public class CompanyCreditDetailsService {
  private final CompanyCreditDetailsRepository companyCreditDetailsRepository;
  private final DetailedCompanyRepository detailedCompanyRepository;
  private final StorageService storageService;

  public StoredFileLink getDocumentLink(long companyId, String key) throws IOException {
    var company = detailedCompanyRepository.getOne(companyId);
    var creditDetails = company.getCreditDetails();
    var document = creditDetails.getDocuments().get(key);
    if (document == null) {
      return storageService.getStoredFileLink(EVEREE_INTERNAL, document.getLocation());
    } else {
      throw new ResourceNotFoundException();
    }
  }

  public StoredFileLink uploadDocument(long companyId, String key, MultipartFile file)
      throws IOException {
    var company = detailedCompanyRepository.getOne(companyId);
    var creditDetails = company.getCreditDetails();
    if (creditDetails == null) {
      creditDetails = new CompanyCreditDetails();
      creditDetails.setCompany(company);
      companyCreditDetailsRepository.save(creditDetails);
      company.setCreditDetails(creditDetails);
    }
    var path = List.of("kyc", Long.toString(companyId));
    var location =
        storageService.storeFile(
            file.getBytes(), file.getOriginalFilename(), EVEREE_INTERNAL, path);
    var link = storageService.getStoredFileLink(EVEREE_INTERNAL, location);
    var document = creditDetails.getDocuments().get(key);
    if (document == null) {
      document = new CompanyCreditDetailsDocument();
      document.setCompanyId(companyId);
      document.setCompanyCreditDetailsId(creditDetails.getId());
      document.setKey(key);
    }
    document.setLocation(location);
    document.setLink(link);
    creditDetails.getDocuments().put(key, document);
    return link;
  }
}
