package com.everee.api.company.UnemploymentRates;

import com.everee.api.auth.annotation.EvereeAdminAccess;
import com.everee.api.tax.state.State;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/** <AUTHOR> */
@RestController()
@RequestMapping("/api/v1")
@EvereeAdminAccess
@RequiredArgsConstructor
public class CompanyUnemploymentRatesController {
  private final CompanyUnemploymentRatesService companyUnemploymentRatesService;

  /**
   * Find unemployment rates for the given company, tax year and state
   *
   * @param companyId
   * @param taxYear
   * @param residentState
   * @return FederalWithholdingAllowance
   */
  @GetMapping("/company/{companyId}/unemployment-rates/{taxYear}/{residentState}")
  public CompanyUnemploymentRates getFederalWithholdingAllowanceForTaxYearAndResidentState(
    @PathVariable Long companyId,
    @PathVariable Integer taxYear,
    @PathVariable State residentState,
    @RequestParam(value = "for-date") LocalDate forDate) {
    return companyUnemploymentRatesService.getUnemploymentRates(companyId, taxYear, residentState, forDate);
  }
}
