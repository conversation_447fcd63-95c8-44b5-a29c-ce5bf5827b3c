package com.everee.api.company.UnemploymentRates;

import com.everee.api.company.DetailedCompanyRepository;
import com.everee.api.tax.state.State;
import com.everee.api.unemploymentsettings.FederalUnemploymentOverride;
import com.everee.api.unemploymentsettings.FederalUnemploymentService;
import com.everee.api.unemploymentsettings.RateType;
import com.everee.api.unemploymentsettings.StateUnemploymentService;

import java.time.LocalDate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyUnemploymentRatesService {
  private final DetailedCompanyRepository detailedCompanyRepository;
  private final FederalUnemploymentService federalUnemploymentService;
  private final StateUnemploymentService stateUnemploymentService;

  public CompanyUnemploymentRates getUnemploymentRates(Long companyId, Integer year, State state){
    //TODO: fix, to get the exact period match when we refactor the state wage base
    // , for now we will extract the forDate from year as 1st of January
    return getUnemploymentRates(companyId, year, state, LocalDate.of(year, 1, 1));
  }

  public CompanyUnemploymentRates getUnemploymentRates(Long companyId, Integer year, State state, LocalDate forDate) {
    var company = detailedCompanyRepository.getOne(companyId);
    var fedOverrideOptional = federalUnemploymentService.findOverride(year, companyId);
    var fedDefault =
        federalUnemploymentService
            .findDefault(year)
            .orElseThrow(() -> new MissingFederalUnemploymentDefaultException(companyId, year));
    var stateWageBase =
        stateUnemploymentService
            .findWageBase(year, state)
            .orElseThrow(
                () -> new MissingStateUnemploymentWageBaseException(companyId, year, state));

    var stateRate = stateUnemploymentService.findRate(forDate, state, companyId, RateType.SUTA)
            .orElseThrow(
              () -> new MissingStateUnemploymentRateException(companyId, forDate, state));

    var futaRate =
        fedOverrideOptional
            .map(FederalUnemploymentOverride::getFutaRate)
            .orElse(fedDefault.getFutaRate());

    var suiWageBase =
        Optional.ofNullable(stateWageBase.getAltSuiWageBase())
            .filter(r -> company.isUseAltSuiWageBase())
            .orElse(stateWageBase.getSuiWageBase());

    Double eafRate = 0.0;
    if (state == State.WA){
        eafRate = stateUnemploymentService.findRate(forDate, state, companyId, RateType.EAF)
                        .orElseThrow(() -> new MissingStateEAFRateException(companyId, forDate, state)).getSuiRate();
    }

    return new CompanyUnemploymentRates()
        .setCompanyId(companyId)
        .setTaxYear(year)
        .setResidentState(state)
        .setFutaWageBase(fedDefault.getFutaWageBase())
        .setFutaRate(futaRate)
        .setSuiWageBase(Optional.ofNullable(stateRate.getSuiWageBaseOverride())
          .orElse(suiWageBase))
        .setSuiRate(stateRate.getSuiRate())
        .setEafRate(eafRate);
  }
}
