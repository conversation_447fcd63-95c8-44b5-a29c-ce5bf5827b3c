package com.everee.api.auditing;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.Instant;

@Data
@NoArgsConstructor
@Entity
@Table(name = "AuditLog", schema = "auditing")
public class AuditLog {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private String recordType;

  @Column(nullable = false)
  private String reference;

  private Long referenceId;

  @Column(nullable = false)
  private Instant auditDateUtc;

  @Column(nullable = false)
  private String actionLog;

  @Column(nullable = false)
  private String moduleName;

  @Column(nullable = false)
  private String moduleShortName;

  @Column(nullable = false)
  private String description;

  @Column(nullable = false)
  private Long companyId;

  @Column(nullable = false)
  private String companyName;

  @Column(nullable = false)
  private Long userId;

  @Column(nullable = false)
  private String userName;
}
