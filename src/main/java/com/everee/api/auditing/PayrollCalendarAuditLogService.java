package com.everee.api.auditing;

import com.everee.api.company.CompanyService;
import com.everee.api.payrollcalendar.PayrollCalendarAuditLogConstants;
import com.everee.api.user.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
@RequiredArgsConstructor
public class PayrollCalendarAuditLogService {

  private final AuditLogRepository auditLogRepository;

  public void logAudit(String actionLog, String description, String reference) {
    AuditLog auditLog = new AuditLog();
    auditLog.setActionLog(actionLog);
    auditLog.setModuleName(PayrollCalendarAuditLogConstants.MODULE_NAME);
    auditLog.setModuleShortName(PayrollCalendarAuditLogConstants.MODULE_SHORT_NAME);
    auditLog.setRecordType(PayrollCalendarAuditLogConstants.RECORD_TYPE);
    auditLog.setDescription(description);
    auditLog.setReference(reference);
    auditLog.setReferenceId(CompanyService.getAuthenticatedCompanyId());
    auditLog.setAuditDateUtc(Instant.now());
    auditLog.setCompanyId(CompanyService.getAuthenticatedCompanyId());
    auditLog.setCompanyName(CompanyService.getAuthenticatedCompanyName());
    auditLog.setUserId(UserService.getAuthenticatedUserId());
    auditLog.setUserName(UserService.getAuthenticatedUser().getDisplayFullName());

    auditLogRepository.save(auditLog);
  }
}
