package com.everee.api.hoursofoperation;

import com.everee.api.model.BaseModel;
import java.time.DayOfWeek;
import java.time.LocalTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@Entity
@ToString
@EqualsAndHashCode(callSuper = true)
public class HoursOfOperation extends BaseModel {
  @NotNull private Long companyId;

  @Enumerated(EnumType.STRING)
  private DayOfWeek openDayOfWeek;

  @Enumerated(EnumType.STRING)
  private DayOfWeek closeDayOfWeek;

  private LocalTime openTime;

  private LocalTime closeTime;
}
