package com.everee.api.query.where;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.validation.constraints.NotNull;
import lombok.Getter;

public class In extends ValueBasedWhereClause<Object> {
  @Getter private Collection<?> value;

  In(String propertyPath, Collection<?> value) {
    super(propertyPath, value);
    this.value = value;
  }

  @Override
  public @NotNull <T> Predicate buildPredicate(CriteriaBuilder builder, Root<T> root) {
    return getExpression(root).in(value);
  }

  @Override
  public boolean isEnabled() {
    return isEnabled(value);
  }

  private static boolean isEnabled(Collection<?> value) {
    return value != null && !value.isEmpty();
  }

  private List<?> getValues() {
    return getValues(value);
  }

  protected static List<?> getValues(Collection<?> value) {
    if (isEnabled(value)) {
      var partitioned = value.stream().collect(Collectors.partitioningBy(Enum.class::isInstance));
      var enumValues =
          partitioned.get(true).stream()
              .map(Enum.class::cast)
              .map(Enum::name)
              .collect(Collectors.toList());
      var values = new ArrayList();
      values.addAll(enumValues);
      values.addAll(partitioned.get(false));
      return values;
    }
    return List.of();
  }

  @Override
  public void setParameterNative(Query query) {
    if (isEnabled()) {
      query.setParameter(getNativeBindingName(), getValues());
    }
  }

  @Override
  public String renderNative() {
    return (isEnabled())
        ? " AND " + propertyPath + " IN :" + getNativeBindingName()
        : super.renderNative();
  }

  @Override
  public String toString() {
    if (isEnabled()) {
      var partitioned =
          getValues().stream().collect(Collectors.partitioningBy(String.class::isInstance));
      var stringValues =
          partitioned.get(true).stream()
              .map(String.class::cast)
              .map(s -> "'" + s + "'")
              .collect(Collectors.toList());
      var values = new ArrayList();
      values.addAll(stringValues);
      values.addAll(
          partitioned.get(false).stream().map(Object::toString).collect(Collectors.toList()));

      return propertyPath + " IN (" + values.stream().collect(Collectors.joining(",")) + ")";
    }
    return "";
  }
}
