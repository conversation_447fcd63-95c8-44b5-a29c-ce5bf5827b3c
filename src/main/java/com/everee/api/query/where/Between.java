package com.everee.api.query.where;

import com.everee.api.query.where.Between.BetweenRange;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.validation.constraints.NotNull;
import lombok.Value;

public class Between<Y extends Comparable<? super Y>>
    extends ValueBasedWhereClause<BetweenRange<Y>> {
  private BetweenRange<Y> value;

  Between(String propertyPath, BetweenRange<Y> range) {
    super(propertyPath, range);
    this.value = range;
  }

  @Override
  public @NotNull <T> Predicate buildPredicate(CriteriaBuilder builder, Root<T> root) {
    var path = getExpression(root);
    return builder.and(
        builder.greaterThanOrEqualTo(path, value.getMinInclusive()),
        builder.lessThan(path, value.getMaxExclusive()));
  }

  @Value
  static class BetweenRange<Y> {
    Y minInclusive;
    Y maxExclusive;
  }
}
