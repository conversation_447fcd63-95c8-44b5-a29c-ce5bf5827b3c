package com.everee.api.notification.spi;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Data
@Component
@Validated
@ConfigurationProperties(prefix = "app.notification")
public class NotificationProperties {

  @Getter private static NotificationProperties instance;

  @PostConstruct
  public void init() {
    instance = this;
  }

  @NotBlank private String requestsQueueName;
  @NotBlank private String resultsQueueName;

  private String smsOverrideDeliveryNumber;
  private String emailOverrideDeliveryAddress;
  private boolean smsDryRunEnabled;
  private boolean pushDryRunEnabled;
  private boolean emailDryRunEnabled;

  @NotBlank private String legacyEmailTemplateId;
  @NotBlank private String transactionalInstallAppsEmailTemplateId;
  @NotBlank private String transactionalNoActionEmailTemplateId;
  @NotBlank private String transactionalSingleActionEmailTemplateId;
  @NotBlank private String allowedEmailTypes;
}
