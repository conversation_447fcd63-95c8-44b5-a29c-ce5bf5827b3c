package com.everee.api.notification.spi;

import com.everee.api.notification.email.EmailNotification;
import com.everee.api.notification.email.EmailNotificationRequest;
import com.everee.api.notification.email.EmailRecipient;
import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.push.PushNotification;
import com.everee.api.notification.push.PushNotificationRequest;
import com.everee.api.notification.sms.SmsNotification;
import com.everee.api.notification.sms.SmsNotificationRequest;
import com.everee.api.notification.user.UserNotification;
import com.everee.api.notification.user.UserNotificationRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class NotificationService {

  public static final String USER_NOTIFICATION_REQUEST = "UserNotificationRequest";
  private final NotificationRequestService requestService;

  public void enqueueUserNotification(UserNotification notification, Long userId) {
    var request = new UserNotificationRequest();
    BeanUtils.copyProperties(notification, request);
    request.setEmailType(getEmailType(notification));
    request.setUserId(userId);
    requestService.enqueue(request);
  }

  public void enqueueSmsNotification(SmsNotification notification, String phoneNumber) {
    var request = new SmsNotificationRequest();
    BeanUtils.copyProperties(notification, request);
    request.setPhoneNumber(phoneNumber);
    requestService.enqueue(request);
  }

  public void enqueueEmailNotification(EmailNotification notification, EmailRecipient recipient) {
    var request = new EmailNotificationRequest();
    request.setName(recipient.getFullName());
    request.setEmail(recipient.getEmailAddress());
    request.setEmailSubject(notification.getEmailSubject());
    request.setEmailBody(notification.getEmailBody());
    request.setEmailTemplateId(notification.getEmailTemplateId());
    request.setEmailTemplateVariables(notification.getEmailTemplateVariables());
    request.setDeliveryCritical(notification.isDeliveryCritical());
    request.setClickTrackingEnabled(notification.isClickTrackingEnabled());

    if (!USER_NOTIFICATION_REQUEST.equals(notification.getClass().getSimpleName())) {
        request.setEmailType(getEmailType(notification));
    }
    requestService.enqueue(request, null);
  }

  public void enqueuePushNotification(PushNotification notification, String pushTokenId) {
    var request = new PushNotificationRequest();
    BeanUtils.copyProperties(notification, request);
    request.setPushTokenId(pushTokenId);
    requestService.enqueue(request);
  }

  public EmailType getEmailType(EmailNotification notification) {
    return notification.getEmailType();
  }
}
