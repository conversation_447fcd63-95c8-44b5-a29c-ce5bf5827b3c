package com.everee.api.notification.user;

import com.everee.api.i18n.LocalizedString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
public class UserNotificationSettingsOption {

  @NonNull
  @ApiModelProperty(required = true)
  private final UserNotificationMechanism mechanism;

  @ApiModelProperty(required = true)
  private boolean selected;

  @ApiModelProperty(required = true)
  private boolean editable;

  @NonNull
  @ApiModelProperty(required = true)
  private final LocalizedString localizedTitle;

  @NonNull
  @ApiModelProperty(required = true)
  private final LocalizedString localizedDescription;
}
