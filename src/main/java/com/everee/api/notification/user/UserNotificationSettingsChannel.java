package com.everee.api.notification.user;

import com.everee.api.i18n.LocalizedString;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.Singular;

@Data
@Builder
public class UserNotificationSettingsChannel {

  @NonNull
  @ApiModelProperty(required = true)
  private final UserNotificationChannel channel;

  @NonNull
  @ApiModelProperty(required = true)
  private final LocalizedString localizedTitle;

  @NonNull
  @ApiModelProperty(required = true)
  private final LocalizedString localizedDescription;

  private final LocalizedString localizedValidationHint;

  @NonNull
  @Singular
  @ApiModelProperty(required = true)
  private final List<UserNotificationSettingsOption> options;
}
