package com.everee.api.notification.user;

import com.everee.api.user.UserService;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserNotificationSettingsController {

  private final UserNotificationSettingsService service;

  @GetMapping("/api/v2/user-notification-settings")
  public UserNotificationSettings getUserNotificationSettings() {
    var userId = UserService.getAuthenticatedUserId();
    return service.getUserNotificationSettings(userId);
  }

  @PutMapping("/api/v2/user-notification-settings")
  public UserNotificationSetting updateUserNotificationSetting(
      @RequestBody @Valid UserNotificationSettingUpdate update) {
    update.setUserId(UserService.getAuthenticatedUserId());
    return service.updateUserNotificationSetting(update);
  }
}
