package com.everee.api.notification.user;

import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.spi.NotificationRequest;
import java.util.Map;
import lombok.Data;

@Data
public final class UserNotificationRequest implements UserNotification, NotificationRequest {

  private Long companyId;

  private Long userId;

  private String title;

  private String message;

  private String smsBody;

  private String pushTitle;

  private String pushMessage;

  private String pushThreadId;

  private String pushChannelId;

  private String pushCollapseKey;

  private Priority pushPriority;

  private Map<String, String> pushPayload;

  private String emailBody;

  private String emailSubject;

  private String emailTemplateId;

  private Map<String, Object> emailTemplateVariables;

  private UserNotificationChannel channel;

  private boolean deliveryCritical;
  private EmailType emailType;
}
