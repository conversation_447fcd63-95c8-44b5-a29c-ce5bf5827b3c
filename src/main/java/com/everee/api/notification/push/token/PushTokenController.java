package com.everee.api.notification.push.token;

import com.everee.api.model.DeletedResponse;
import com.everee.api.user.UserService;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Transactional
@RequiredArgsConstructor
class PushTokenController {

  private final PushTokenRepository repository;

  @PutMapping("/api/v2/push-tokens")
  public PushToken registerPushToken(@RequestBody @Valid PushTokenRegisterRequest req) {
    var deleted = repository.deleteAllByAppInstanceId(req.getAppInstanceId());
    deleted += repository.deleteAllByPlatformAndValue(req.getPlatform(), req.getValue());
    log.info("Deleted {} push token(s) for new registration", deleted);
    var token = new PushToken();
    token.setPlatform(req.getPlatform());
    token.setValue(req.getValue());
    token.setAppId(req.getAppId());
    token.setLocale(LocaleContextHolder.getLocale());
    token.setUserId(UserService.getAuthenticatedUserId());
    token.setDeviceOS(req.getDeviceOS());
    token.setDeviceName(req.getDeviceName());
    token.setAppInstanceId(req.getAppInstanceId());
    return repository.save(token);
  }

  @DeleteMapping("/api/v2/push-tokens")
  public DeletedResponse deletePushTokens(@RequestParam(name = "app-instance-id") String aid) {
    var deleted = repository.deleteAllByAppInstanceId(aid);
    log.info("Deleted {} push token(s)", deleted);
    return DeletedResponse.of(deleted);
  }

  @Deprecated(forRemoval = true)
  @PutMapping("/api/v1/push-tokens")
  public PushToken registerPushToken(@RequestBody @Valid PushTokenRegisterRequestV1 req) {
    var deleted = repository.deleteAllByPlatformAndValue(req.getPlatform(), req.getValue());
    log.info("Deleted {} push token(s) for new registration", deleted);
    var token = new PushToken();
    token.setPlatform(req.getPlatform());
    token.setValue(req.getValue());
    token.setUserId(UserService.getAuthenticatedUserId());
    token.setLocale(LocaleContextHolder.getLocale());
    return repository.save(token);
  }

  @Deprecated(forRemoval = true)
  @DeleteMapping("/api/v1/push-tokens")
  public DeletedResponse deletePushToken(@RequestBody @Valid PushTokenDeleteRequestV1 req) {
    var deleted = repository.deleteAllByPlatformAndValue(req.getPlatform(), req.getValue());
    log.info("Deleted {} push token(s)", deleted);
    return DeletedResponse.of(deleted);
  }
}
