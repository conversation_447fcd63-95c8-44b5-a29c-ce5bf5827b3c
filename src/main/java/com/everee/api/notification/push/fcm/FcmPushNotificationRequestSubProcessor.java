package com.everee.api.notification.push.fcm;

import com.everee.api.notification.push.PushNotification.Priority;
import com.everee.api.notification.push.PushNotificationRequest;
import com.everee.api.notification.push.PushNotificationRequestSubProcessor;
import com.everee.api.notification.push.token.PushToken;
import com.everee.api.notification.push.token.PushTokenRepository;
import com.google.firebase.messaging.*;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class FcmPushNotificationRequestSubProcessor implements PushNotificationRequestSubProcessor {

  private final FirebaseMessaging firebaseMessaging;
  private final PushTokenRepository tokenRepository;

  @Override
  public void process(PushNotificationRequest request, PushToken token) throws Exception {
    try {
      var message = getMessage(request, token);
      var messageId = firebaseMessaging.send(message, request.isDryRun());
      log.info("Successfully sent FCM message id {} to user {}", messageId, token.getUserId());
    } catch (FirebaseMessagingException e) {
      handleFirebaseException(e, token);
    }
  }

  private void handleFirebaseException(FirebaseMessagingException e, PushToken token)
      throws Exception {
    if (Objects.equals(e.getErrorCode(), "registration-token-not-registered")) {
      log.info(
          "Deleting expired FCM token {} for user {} ({})",
          token.getValue(),
          token.getUserId(),
          e.getErrorCode(),
          e);
      tokenRepository.delete(token);
    } else {
      throw e;
    }
  }

  private static Message getMessage(PushNotificationRequest request, PushToken token) {
    return Message.builder()
        .setNotification(getNotification(request))
        .setToken(token.getValue())
        .putAllData(getData(request))
        .setApnsConfig(getApnsConfig(request))
        .setAndroidConfig(getAndroidConfig(request))
        .build();
  }

  private static Map<String, String> getData(PushNotificationRequest request) {
    return Optional.ofNullable(request.getPushPayload()).orElse(Collections.emptyMap());
  }

  private static ApnsConfig getApnsConfig(PushNotificationRequest request) {
    return ApnsConfig.builder().setAps(getApsConfig(request)).build();
  }

  private static Aps getApsConfig(PushNotificationRequest request) {
    return Aps.builder()
        .setThreadId(request.getPushThreadId())
        .setCategory(request.getPushChannelId())
        .build();
  }

  private static AndroidConfig getAndroidConfig(PushNotificationRequest request) {
    return AndroidConfig.builder()
        .setPriority(getAndroidPriority(request))
        .setCollapseKey(request.getPushCollapseKey())
        .setNotification(getAndroidNotification(request))
        .build();
  }

  private static AndroidConfig.Priority getAndroidPriority(PushNotificationRequest request) {
    if (request.getPushPriority() == Priority.HIGH) {
      return AndroidConfig.Priority.HIGH;
    } else {
      return AndroidConfig.Priority.NORMAL;
    }
  }

  private static AndroidNotification getAndroidNotification(PushNotificationRequest request) {
    return AndroidNotification.builder().setChannelId(request.getPushChannelId()).build();
  }

  private static Notification getNotification(PushNotificationRequest request) {
    return Notification.builder()
        .setTitle(request.getPushTitle())
        .setBody(request.getPushMessage())
        .build();
  }
}
