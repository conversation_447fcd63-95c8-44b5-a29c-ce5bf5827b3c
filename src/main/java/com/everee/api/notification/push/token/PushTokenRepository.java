package com.everee.api.notification.push.token;

import static org.hibernate.jpa.QueryHints.HINT_FETCH_SIZE;

import java.util.stream.Stream;
import javax.persistence.LockModeType;
import javax.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

@Repository
public interface PushTokenRepository extends JpaRepository<PushToken, String> {

  @QueryHints(value = @QueryHint(name = HINT_FETCH_SIZE, value = "50"))
  Stream<PushToken> streamAllByUserId(Long userId);

  @Lock(LockModeType.PESSIMISTIC_WRITE)
  long deleteAllByAppInstanceId(String instanceId);

  @Lock(LockModeType.PESSIMISTIC_WRITE)
  long deleteAllByPlatformAndValue(PushToken.Platform platform, String value);
}
