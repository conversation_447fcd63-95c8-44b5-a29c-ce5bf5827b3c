package com.everee.api.notification.email;

import java.util.Map;

public interface EmailNotification {
  /** Required for all templates */
  String getEmailSubject();

  /**
   * Content for plain text emails (ie: getEmailTemplateId() is null)
   *
   * @return body for plain text email
   */
  default String getEmailBody() {
    return null;
  }

  /**
   * Reference for sendgrid templates; leave null for plain text emails
   *
   * @return ID for sendgrid template
   */
  String getEmailTemplateId();

  /**
   * Content for sendgrid templates (ie: getEmailTemplateId() is not null)
   *
   * @return key/value map to satisfy sendgrid template
   */
  default Map<String, Object> getEmailTemplateVariables() {
    return null;
  }

  /** Disables users ability to unsubscribe to this particular email */
  default boolean isDeliveryCritical() {
    return false;
  }

  /**
   * Controls whether click-tracking will be applied to links in the email. This bounces all linked
   * requests through Sendgrid so they can be counted.
   */
  default boolean isClickTrackingEnabled() {
    return true;
  }


  /**
   * Email type
   *
   * @return email type
   */
  default EmailType getEmailType() { return EmailType.Unknown; }
}
