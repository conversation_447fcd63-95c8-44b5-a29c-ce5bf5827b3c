package com.everee.api.notification.email;

public enum EmailType {
  SEND_BEGIN_ONBOARDING,
  WORKER_ONBOARDING_COMPLETE,
  WORKER_CLAIMED_BUT_ONBOARDINGIN_COMPLETE,
  TIN_VERIFICATION_FAILURE,
  PTPC_BALANCE_WARNING_LIMIT_EXCEEDED,
  PTPC_BALANCE_LIMIT_REACHED,
  PTPC_EXPIRED_CARD_WORKER,
  COMPANY_TAX_JURISDICTION,
  JOURNAL_ENTRY_GENERATOR,
  PAYMENT_<PERSON>PC_FAILED_WORKER,
  COMPANY_<PERSON><PERSON>_ENABLED,
  COMPANY_DOCUMENT_SIGNATURE_FROM_WORKER,
  COMPANY_DOCUMENT_SIGNATURES,
  PAYMENT_DEPOSIT_FAILED_WORKER,
  PAYMENT_DEPOSIT_FAILED,
  SET_USER_PASSWORD,
  PTPC_CARD_NOT_SETUP_WORKER,
  USER_SCHEDULING_ADMIN_NEED_TO_PUBLISH_SHIFTS,
  USER_PAY_ON_DEMAND,
  USER_SET_PASSWORD_SUCCESS,
  USER_FUNDING_ACCOUNT_DISCONNECTED,
  USER_PAY_YAY,
  USER_UNAPPROVED_PAYMENTS_REMINDER,
  USER_TIMEOFF_REQUEST_ADMIN,
  USER_UNVERIFIED_SHIFTS_REMINDER,
  USER_SCHEDULING_EMPLOYEE_UPCOMING_SHIFT,
  USER_SCHEDULING_EMPLOYEE_SHIFT_UPDATED,
  USER_SCHEDULING_EMPLOYEE_SHIFTS_PUBLISHED,
  USER_SCHEDULING_EMPLOYEE_OPEN_SHIFTS_PUBLISHED,
  USER_SCHEDULING_EMPLOYEE_REQUEST_CLAIM_HAS_MANAGER_RESPONSE,
  USER_SCHEDULING_ADMIN_NEED_TO_REVIEW_REQUESTS,
  USER_SCHEDULING_ADMIN_UNAVAILABLE_TIME_ADDED,
  USER_SCHEDULING_EMPLOYEE_REQUEST_HAS_MANAGER_RESPONSE,
  USER_SCHEDULING_EMPLOYEE_REQUEST_CREATED,
  EMAILED_REPORT_NOTIFICATION,
  Unknown
}
