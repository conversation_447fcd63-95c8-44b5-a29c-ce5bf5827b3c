package com.everee.api.lookup;

import static com.everee.api.query.where.Where.property;
import static com.everee.api.util.BooleanApiParam.UNSPECIFIED;

import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.query.Query;
import com.everee.api.query.update.UpdateProperty;
import com.everee.api.query.where.Where;
import com.everee.api.query.where.WhereClause;
import com.everee.api.util.BooleanApiParam;
import com.everee.api.util.CollectionFilterParam;
import com.everee.api.util.ObjectUtils;
import java.time.LocalDate;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.LockModeType;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * This lookup service is intended for entities that are not linked to a tenant scoped context.
 * Prefer use of {@link LookupService} for entities related to tenant records.
 */
public interface TenantlessLookupService<T, L, Q extends Query<T>> {
  void configureQuery(L lookup, Q query);

  Q createQuery();

  default Stream<T> streamAll(L lookup) {
    return getConfiguredQuery(lookup).streamAll();
  }

  default Stream<T> streamAll(L lookup, Pageable pageable) {
    return getConfiguredQuery(lookup).streamAll(pageable);
  }

  default Stream<T> streamAll(L lookup, Pageable pageable, Sort sort) {
    return getConfiguredQuery(lookup).sortBy(sort).streamAll(pageable);
  }

  default Stream<T> streamAll(L lookup, Pageable pageable, LockModeType lockModeType) {
    return getConfiguredQuery(lookup).withLock(lockModeType).streamAll(pageable);
  }

  default Page<T> listAll(L lookup, Pageable pageable) {
    return getConfiguredQuery(lookup).findAll(pageable);
  }

  default Page<T> listAllUnsafe(L lookup, Pageable pageable) {
    return getConfiguredQueryUnsafe(lookup).findAll(pageable);
  }

  default Page<T> listAll(L lookup, Pageable pageable, Sort sort) {
    return getConfiguredQuery(lookup).sortBy(sort).findAll(pageable);
  }

  default List<T> findAll(L lookup) {
    return getConfiguredQuery(lookup).findAll();
  }

  default List<T> findAllUnsafe(L lookup) {
    return getConfiguredQueryUnsafe(lookup).findAll();
  }

  default Optional<T> findOne(@NonNull L lookup) {
    return getConfiguredQuery(lookup).findOne();
  }

  default Optional<T> findOneUnsafe(@NonNull L lookup) {
      return getConfiguredQueryUnsafe(lookup).findOne();
  }

  default T findOneOrThrow(@NonNull L lookup) {
    return findOne(lookup).orElseThrow(ResourceNotFoundException::new);
  }

  default T findOneOrThrowUnsafe(@NonNull L lookup) {
      return findOneUnsafe(lookup).orElseThrow(ResourceNotFoundException::new);
  }

  default int count(@NonNull L lookup) {
    return getConfiguredQuery(lookup).count();
  }

  default boolean exists(@NonNull L lookup) {
    return count(lookup) > 0;
  }

  default Q getConfiguredQuery(L lookup) {
    var query = createQuery();
    configureQuery(lookup, query);
    return query;
  }

  default Q getConfiguredQueryUnsafe(L lookup) {
      var query = createQuery();
      configureQuery(lookup, query);
      return query;
  }

  default BiFunction<CriteriaBuilder, Root<?>, Predicate> booleanApiParam(
      BooleanApiParam paramValue, String propertyName) {
    if (paramValue == null || paramValue == UNSPECIFIED || propertyName == null) {
      return null;
    }

    return (builder, root) -> {
      Path<String> propertyPath = root.get(propertyName);

      switch (paramValue) {
        case INCLUDE_ONLY:
          return builder.equal(propertyPath, true);
        case EXCLUDE_ALL:
          return builder.equal(propertyPath, false);
      }

      throw new IllegalArgumentException(
          String.format("Unrecognized BooleanApiParam %s", paramValue.toString()));
    };
  }

  default BiFunction<CriteriaBuilder, Root<?>, Predicate> presenceAsBooleanApiParam(
      BooleanApiParam paramValue, String propertyName) {
    if (paramValue == null || paramValue == UNSPECIFIED || propertyName == null) {
      return null;
    }

    return (builder, root) -> {
      Path<String> propertyPath = root.get(propertyName);

      switch (paramValue) {
        case INCLUDE_ONLY:
          return builder.isNotNull(propertyPath);
        case EXCLUDE_ALL:
          return builder.isNull(propertyPath);
      }

      throw new IllegalArgumentException(
          String.format("Unrecognized BooleanApiParam %s", paramValue.toString()));
    };
  }

  default BiFunction<CriteriaBuilder, Root<?>, Predicate> dateRangeLookup(
      DateRangeLookup lookup, String propertyName) {
    if (lookup == null || propertyName == null) {
      return null;
    }

    var minReferenceDate = Optional.ofNullable(lookup.getMinReferenceDate());
    var maxReferenceDate = Optional.ofNullable(lookup.getMaxReferenceDate());

    return (builder, root) -> {
      Path<LocalDate> propertyPath = root.get(propertyName);

      var minDatePredicate =
          minReferenceDate
              .map(date -> builder.greaterThanOrEqualTo(propertyPath, date))
              .orElse(builder.conjunction());
      var maxDatePredicate =
          maxReferenceDate
              .map(date -> builder.lessThanOrEqualTo(propertyPath, date))
              .orElse(builder.conjunction());

      return builder.and(minDatePredicate, maxDatePredicate);
    };
  }

  default <Y extends Enum<Y>> WhereClause enumCollectionFilter(
      CollectionFilterParam<Y> paramValue, Class<Y> enumClass, String propertyName) {
    if (paramValue == null || propertyName == null) {
      return Where.by(null);
    }

    var allNames = EnumSet.allOf(enumClass).stream().map(Y::name).collect(Collectors.toSet());
    var scopesEmptyPredicate = property(propertyName).containsNone(allNames);
    var scopesPresentPredicate = property(propertyName).containsAny(allNames);

    var containsPredicates = new ArrayList<WhereClause>();
    Optional.ofNullable(paramValue.getHasAll())
        .map(Set::stream)
        .map(s -> s.map(Y::name))
        .map(s -> s.collect(Collectors.toSet()))
        .filter(ObjectUtils::notEmpty)
        .map(value -> property(propertyName).containsAll(value))
        .ifPresent(containsPredicates::add);
    Optional.ofNullable(paramValue.getHasAny())
        .map(Set::stream)
        .map(s -> s.map(Y::name))
        .map(s -> s.collect(Collectors.toSet()))
        .filter(ObjectUtils::notEmpty)
        .map(value -> property(propertyName).containsAny(value))
        .ifPresent(containsPredicates::add);
    Optional.ofNullable(paramValue.getHasNone())
        .map(Set::stream)
        .map(s -> s.map(Y::name))
        .map(s -> s.collect(Collectors.toSet()))
        .filter(ObjectUtils::notEmpty)
        .map(value -> property(propertyName).containsNone(value))
        .ifPresent(containsPredicates::add);
    var andedContainsPredicates = containsPredicates.stream().reduce(Where::and);

    switch (paramValue.getHasScopes()) {
      case INCLUDE_ONLY:
        return Where.and(scopesPresentPredicate, andedContainsPredicates.orElse(null));
      case EXCLUDE_ALL:
        return scopesEmptyPredicate;
      case UNSPECIFIED:
        return andedContainsPredicates
            .map(p -> Where.or(scopesEmptyPredicate, p))
            .orElse(Where.or(null, null));
      default:
        throw new IllegalArgumentException(
            String.format("Unrecognized boolean param value: %s", paramValue.getHasScopes()));
    }
  }

  default int update(L lookup, List<UpdateProperty> updates) {
    return getConfiguredQuery(lookup).set((List) updates).update();
  }
}
