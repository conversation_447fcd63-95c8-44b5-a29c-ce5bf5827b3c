package com.everee.api.lookup;

import com.everee.api.model.DateRange;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDate;
import java.util.Optional;

public interface DateRangeLookup<T> extends Lookup {
  LocalDate getMinReferenceDate();

  LocalDate getMaxReferenceDate();

  @JsonIgnore
  default Optional<DateRange> getDateRange() {
    if (getMinReferenceDate() == null || getMaxReferenceDate() == null) return Optional.empty();
    return Optional.of(DateRange.of(getMinReferenceDate(), getMaxReferenceDate()));
  }
}
