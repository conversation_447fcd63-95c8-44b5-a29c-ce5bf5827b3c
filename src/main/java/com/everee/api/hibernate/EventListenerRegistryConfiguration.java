package com.everee.api.hibernate;

import javax.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
class EventListenerRegistryConfiguration {

  private final EntityManagerFactory entityManagerFactory;

  @Bean
  public EventListenerRegistry eventListenerRegistry() {
    return entityManagerFactory
        .unwrap(SessionFactoryImpl.class)
        .getServiceRegistry()
        .getService(EventListenerRegistry.class);
  }
}
