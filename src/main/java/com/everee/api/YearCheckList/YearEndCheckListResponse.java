package com.everee.api.YearCheckList;
import lombok.Data;
import java.util.List;
@Data
public class YearEndCheckListResponse {

    private List<YearEndCheckList> items;
    private int pageSize;
    private int pageNumber;
    private int totalPages;
    private long totalItems;
    private List<SortOrder> sortOrders;

    @Data
    static class SortOrder {
        private final String property;
        private final String direction;
    }
}
