package com.everee.api.user.picture;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/v2/users/{userId}/picture")
@RequiredArgsConstructor
public class UserPictureController {
  private final UserPictureService userPictureService;

  @PostMapping
  @PreAuthorize("#currentUser.isSelf(#userId)")
  public UserPicture uploadUserPicture(
      @PathVariable Long userId, @RequestParam("file") MultipartFile multipartFile) {
    return userPictureService.uploadUserPicture(userId, multipartFile);
  }

  @DeleteMapping
  @PreAuthorize("#currentUser.canAccessUser(#userId)")
  public void deleteUserPicture(@PathVariable Long userId) {
    userPictureService.deleteUserPicture(userId);
  }
}
