package com.everee.api.user.picture;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserPictureRepository extends JpaRepository<UserPicture, String> {
  public UserPicture findByUserId(Long userId);

  @Modifying
  @Query("delete from UserPicture up where up.userId = ?1")
  void deleteByUserId(Long userId);
}
