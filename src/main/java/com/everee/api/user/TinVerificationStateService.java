package com.everee.api.user;

import static org.springframework.util.StringUtils.isEmpty;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.payeetype.PayeeType;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.payeetype.PayeeTypeUpdateParams;
import com.everee.api.worker.updateparams.WorkerPersonalInfoParamsForUpdate;
import com.everee.api.worker.updateparams.WorkerTaxpayerIdentifierParamsForUpdate;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class TinVerificationStateService {

  public TinVerificationState getStateForUpdateFieldsForTinVerification(
      UpdateFieldsForTinVerificationParams request, DetailedUser existing) {
    if (request == null
        || existing == null
        || existing.getPayeeType() == null
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.BUSINESS
            && isEmpty(request.getBusinessName()))
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.INDIVIDUAL
            && (isEmpty(request.getFirstName()) || isEmpty(request.getLastName())))
        || isEmpty(request.getTaxpayerIdentifier())) {
      return TinVerificationState.UNABLE_TO_VERIFY;
    }
    if ((existing.getPayeeType().getTypeName() == PayeeTypeName.BUSINESS
            && !request.getBusinessName().equals(existing.getPayeeType().getBusinessName()))
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.INDIVIDUAL
            && (!request.getFirstName().trim().equals(existing.getFirstName())
                || !request.getLastName().trim().equals(existing.getLastName())))
        || (existing.isClaimed()
            && !request.getTaxpayerIdentifier().equals(existing.getTaxpayerIdentifier()))
        || (!existing.isClaimed()
            && !request
                .getTaxpayerIdentifier()
                .equals(existing.getUnverifiedTaxpayerIdentifier()))) {
      return TinVerificationState.NEEDS_VERIFICATION;
    }
    return existing.getTinVerificationState();
  }

  public TinVerificationState getStateForUpdatedPayeeType(
      PayeeTypeUpdateParams request, PayeeType existing) {
    if (request == null
        || existing == null
        || (request.getTypeName() == PayeeTypeName.BUSINESS && isEmpty(request.getBusinessName()))
        || (request.getTypeName() == PayeeTypeName.INDIVIDUAL
            && (isEmpty(existing.getUser().getFirstName())
                || isEmpty(existing.getUser().getLastName())))
        || (isEmpty(existing.getUser().getTaxpayerIdentifier())
            && isEmpty(existing.getUser().getUnverifiedTaxpayerIdentifier()))) {
      return TinVerificationState.UNABLE_TO_VERIFY;
    }
    if (request.getTypeName() != existing.getTypeName()
        || (request.getTypeName() == PayeeTypeName.BUSINESS
            && !request.getBusinessName().equals(existing.getBusinessName()))) {
      return TinVerificationState.NEEDS_VERIFICATION;
    }
    return existing.getUser().getTinVerificationState();
  }

  public TinVerificationState getStateForNewUser(DetailedUser newUser) {
    if (newUser == null
        || isEmpty(newUser.getFirstName())
        || isEmpty(newUser.getLastName())
        || isEmpty(newUser.getTaxpayerIdentifier())) {
      return TinVerificationState.UNABLE_TO_VERIFY;
    }
    return TinVerificationState.NEEDS_VERIFICATION;
  }

  public TinVerificationState getStateForUpdatedUser(DetailedUser request, DetailedUser existing) {
    if (request == null
        || existing == null
        || existing.getPayeeType() == null
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.BUSINESS
            && isEmpty(existing.getPayeeType().getBusinessName()))
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.INDIVIDUAL
            && (isEmpty(request.getFirstName()) || isEmpty(request.getLastName())))
        || isEmpty(request.getTaxpayerIdentifier())) {
      return TinVerificationState.UNABLE_TO_VERIFY;
    }

    return TinVerificationState.NEEDS_VERIFICATION;
  }

  public TinVerificationState getStateForUpdatedWorker(
      WorkerPersonalInfoParamsForUpdate request, DetailedUser existing) {
    if (request == null
        || existing == null
        || existing.getPayeeType() == null
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.BUSINESS
            && isEmpty(existing.getPayeeType().getBusinessName()))
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.INDIVIDUAL
            && (isEmpty(request.getFirstName()) || isEmpty(request.getLastName())))
        || (isEmpty(existing.getTaxpayerIdentifier())
            && isEmpty(existing.getUnverifiedTaxpayerIdentifier()))) {
      return TinVerificationState.UNABLE_TO_VERIFY;
    }
    if (!request.getFirstName().trim().equals(existing.getFirstName())
        || !request.getLastName().trim().equals(existing.getLastName())) {
      return TinVerificationState.NEEDS_VERIFICATION;
    }
    return existing.getTinVerificationState();
  }

  public TinVerificationState getStateForUpdatedWorker(
      WorkerTaxpayerIdentifierParamsForUpdate request,
      DetailedEmployee employee,
      DetailedUser existing) {
    if (request == null
        || existing == null
        || existing.getPayeeType() == null
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.BUSINESS
            && isEmpty(existing.getPayeeType().getBusinessName()))
        || (existing.getPayeeType().getTypeName() == PayeeTypeName.INDIVIDUAL
            && (isEmpty(existing.getFirstName()) || isEmpty(existing.getLastName())))
        || isEmpty(request.getTaxpayerIdentifier())) {
      return TinVerificationState.UNABLE_TO_VERIFY;
    }
    if ((employee.getOnboarding().isClaimed()
            && !request.getTaxpayerIdentifier().equals(existing.getTaxpayerIdentifier()))
        || !request.getTaxpayerIdentifier().equals(existing.getTaxpayerIdentifier())) {
      return TinVerificationState.NEEDS_VERIFICATION;
    }
    return existing.getTinVerificationState();
  }

  public TinVerificationState getStateForNewlyClaimedUser(
      DetailedUser user, Optional<PayeeType> payeeTypeOpt) {
    if (user == null || payeeTypeOpt.isEmpty()) return TinVerificationState.UNABLE_TO_VERIFY;

    var payeeType = payeeTypeOpt.get();

    var hasTIN = StringUtils.isNotEmpty(user.getTaxpayerIdentifier());
    var hasFirstName = StringUtils.isNotEmpty(user.getFirstName());
    var hasLastName = StringUtils.isNotEmpty(user.getLastName());
    var isBusiness = payeeType.getTypeName() == PayeeTypeName.BUSINESS;
    var hasBusinessName = StringUtils.isNotEmpty(payeeType.getBusinessName());

    var markNeedsVerification =
        hasTIN && ((isBusiness && hasBusinessName) || (!isBusiness && hasFirstName && hasLastName));
    return markNeedsVerification
        ? TinVerificationState.NEEDS_VERIFICATION
        : TinVerificationState.UNABLE_TO_VERIFY;
  }
}
