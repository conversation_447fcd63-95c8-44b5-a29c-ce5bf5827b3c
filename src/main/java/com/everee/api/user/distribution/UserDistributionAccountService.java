package com.everee.api.user.distribution;

import com.everee.api.config.RequestLoggingUtils;
import com.everee.api.employee.CoreEmployeeRepository;
import com.everee.api.integration.galileo.repository.GalileoAccountRepository;
import com.everee.api.integration.tabapay.model.TabapayAccountType;
import com.everee.api.integration.tabapay.repository.TabapayAccountRepository;
import com.everee.api.user.bankaccount.UserBankAccountRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDistributionAccountService {
  private final UserBankAccountRepository userBankAccountRepository;
  private final CoreEmployeeRepository coreEmployeeRepository;
  private final TabapayAccountRepository tabapayAccountRepository;
  private final GalileoAccountRepository galileoAccountRepository;

  public List<UserDistributionAccount> listUserDistributionAccounts(Long userId) {
    var userDistributionAccounts = new ArrayList<UserDistributionAccount>();

    var stopWatch = new StopWatch("listUserDistributionAccounts");
    stopWatch.start(RequestLoggingUtils.elapsedMsKey("achDistributionAccounts_load"));
    userDistributionAccounts.addAll(getAchDistributionAccountsForUser(userId));
    stopWatch.stop();
    stopWatch.start(RequestLoggingUtils.elapsedMsKey("ptpcDistributionAccounts_load"));
    userDistributionAccounts.addAll(getPtpcDistributionAccountsForUser(userId));
    stopWatch.stop();
    stopWatch.start(RequestLoggingUtils.elapsedMsKey("paycardDistributionAccounts_load"));
    userDistributionAccounts.addAll(getPayCardDistributionAccountsForUser(userId));
    stopWatch.stop();
    RequestLoggingUtils.addExitMarkers(stopWatch);

    return userDistributionAccounts;
  }

  public boolean employeeHasInstantDepositAccount(Long employeeId, Long companyId) {
    var ee = coreEmployeeRepository.findByIdAndCompanyId(employeeId, companyId).orElseThrow();
    return !getPtpcDistributionAccountsForUser(ee.getUserId()).isEmpty();
  }

  public Optional<UserDistributionAccount> getInstantDepositAccount(Long userId) {
    var ptpcAccounts = getPtpcDistributionAccountsForUser(userId);
    if (ptpcAccounts.isEmpty()) {
      return Optional.empty();
    }

    return Optional.of(ptpcAccounts.get(0));
  }

  private List<UserDistributionAccount> getAchDistributionAccountsForUser(Long userId) {
    var bankAccounts = userBankAccountRepository.findByUserIdOrderByRuleOrder(userId);
    return bankAccounts.stream().map(UserDistributionAccount::new).collect(Collectors.toList());
  }

  private List<UserDistributionAccount> getPtpcDistributionAccountsForUser(Long userId) {
    var tabapayAccount = tabapayAccountRepository.findByUserId(userId);
    if (tabapayAccount != null
        && TabapayAccountType.CARD.equals(tabapayAccount.getAccountType())
        && StringUtils.isNotBlank(tabapayAccount.getAccountId())) {
      return List.of(new UserDistributionAccount(tabapayAccount));
    }

    return List.of();
  }

  private List<UserDistributionAccount> getPayCardDistributionAccountsForUser(Long userId) {
    var galileoAccount = galileoAccountRepository.findByUserId(userId);
    if (galileoAccount.isPresent() && StringUtils.isNotBlank(galileoAccount.get().getPmtRefNo())) {
      return List.of(new UserDistributionAccount(galileoAccount.get()));
    }

    return List.of();
  }
}
