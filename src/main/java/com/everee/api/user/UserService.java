package com.everee.api.user;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.trimToNull;
import static org.springframework.util.StringUtils.hasText;

import com.everee.api.auth.tenant.TenantSecurityContext;
import com.everee.api.auth.tenant.TenantSecurityContextHolder;
import com.everee.api.auth.util.AuthorizationCheckService;
import com.everee.api.company.CompanyService;
import com.everee.api.company.CoreCompany;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.employee.event.WorkerUpdatedContactInfoEvent;
import com.everee.api.employee.event.WorkerUpdatedPersonalInfoEvent;
import com.everee.api.employee.onboarding.WorkerOnboardingService;
import com.everee.api.employee.onboarding.entity.WorkerOnboarding;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.keycloak.event.UserCreatedEvent;
import com.everee.api.keycloak.event.UserDeletedEvent;
import com.everee.api.keycloak.event.UserUpdatedEvent;
import com.everee.api.keycloak.event.UserUpdatedPasswordEvent;
import com.everee.api.notification.user.UserNotificationSettingsRepository;
import com.everee.api.password.PasswordComplexityValidator;
import com.everee.api.password.SetPasswordTokenRepository;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.payeetype.PayeeTypeRepository;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.tax.verification.UserTinValidationStatusCode;
import com.everee.api.tax.verification.UserTinVerificationFileUserRepository;
import com.everee.api.user.address.HomeAddressRepository;
import com.everee.api.user.bankaccount.UserBankAccountRepository;
import com.everee.api.user.lookup.DetailedUserLookupService;
import com.everee.api.user.lookup.UserLookup;
import com.everee.api.user.params.UserContactInfoParams;
import com.everee.api.user.params.UserPersonalInfoParams;
import com.everee.api.util.ObjectUtils;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Lazy
@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {
  private final ApplicationEventPublisher eventPublisher;
  private final BCryptPasswordEncoder passwordEncoder;
  private final CompanyService companyService;
  private final CoreUserRepository coreUserRepository;
  private final DetailedEmployeeRepository detailedEmployeeRepository;
  private final DetailedUserRepository detailedUserRepository;
  private final DetailedUserLookupService detailedUserLookupService;
  private final PasswordComplexityValidator passwordComplexityValidator;
  private final WorkerOnboardingService workerOnboardingService;
  private final UserNotificationSettingsRepository userNotificationSettingsRepository;
  private final UserBankAccountRepository userBankAccountRepository;
  private final HomeAddressRepository homeAddressRepository;
  private final SetPasswordTokenRepository setPasswordTokenRepository;
  private final AuthorizationCheckService authorizationCheckService;
  private final TinVerificationStateService tinVerificationStateService;
  private final PayeeTypeRepository payeeTypeRepository;
  private final UserTinVerificationFileUserRepository tinVerificationFileUserRepository;

  @NonNull
  public static Optional<CoreUser> findAuthenticatedUser() {
    return TenantSecurityContextHolder.getContext().flatMap(TenantSecurityContext::getUser);
  }

  @NonNull
  public static Optional<Long> findAuthenticatedUserId() {
    return findAuthenticatedUser().map(CoreUser::getId);
  }

  @NonNull
  public static CoreUser getAuthenticatedUser() {
    return findAuthenticatedUser()
        .orElseThrow(() -> new AccessDeniedException("Authenticated user not present"));
  }

  @NonNull
  public static Long getAuthenticatedUserId() {
    return findAuthenticatedUserId()
        .orElseThrow(() -> new AccessDeniedException("Authenticated user not present"));
  }

  public Optional<CoreUser> findByUsernameOrEmail(String value) {
    return ofNullable(value)
        .filter(StringUtils::hasText)
        .map(String::toLowerCase)
        .flatMap(coreUserRepository::findByUsernameOrEmail);
  }

  public DetailedUser getUser(Long id) {
    return detailedUserRepository
        .findById(id)
        .orElseThrow(() -> new ResourceNotFoundException("User not found"));
  }

  /**
   * Creates a new unsorted list of {@link DetailedUser}.
   *
   * @param ids non-null list, must not be empty.
   */
  public List<DetailedUser> getUsers(List<Long> ids) {
    Page<DetailedUser> detailedUsers =
        detailedUserRepository.findAllByIdIn(ids, PageRequest.of(0, ids.size()));
    return detailedUsers.toList();
  }

  public DetailedUser getUser(CoreUser coreUser) {
    return getUser(coreUser.getId());
  }

  public CoreUser getCoreUser(Long id) {
    return coreUserRepository
        .findById(id)
        .orElseThrow(() -> new ResourceNotFoundException("CoreUser not found"));
  }

  public Page<DetailedUser> listUsers(
      @NotNull Pageable pageable, List<Long> ids, String nameEmailFilter) {
    return detailedUserLookupService.listAll(
        new UserLookup()
            .setIds(ofNullable(ids).map(HashSet::new).orElse(null))
            .setQ(nameEmailFilter),
        pageable);
  }

  @Transactional
  public DetailedUser createUser(DetailedUser newUser) {
    if (newUser.getId() != null) {
      throw new InvalidRequestException("User id already exists");
    }

    if (!hasText(newUser.getKioskPin())) {
      applyNewPinFromSSN(newUser.getTaxpayerIdentifier(), newUser);
    }

    var rawPassword = newUser.getPassword();
    if (hasText(newUser.getPassword())) {
      setUserPassword(newUser, newUser.getPassword());
    } else {
      newUser.setNoPassword();
    }

    newUser.setTinVerificationState(tinVerificationStateService.getStateForNewUser(newUser));

    eventPublisher.publishEvent(new UserCreatedEvent(newUser, rawPassword));
    return detailedUserRepository.saveAndRefresh(newUser);
  }

  @Transactional
  public DetailedUser updateUser(Long userId, DetailedUser params) {
    var user = getUser(userId);
    var employeeRecords = detailedEmployeeRepository.findByUserId(user.getId());

    if (user.getKioskPin() == null
        || !Objects.equals(user.getTaxpayerIdentifier(), params.getTaxpayerIdentifier())) {
      applyNewPinFromSSN(params.getTaxpayerIdentifier(), user);
    }

    if (hasText(params.getKioskPin())) {
      setUserKioskPin(user, params.getKioskPin());
    }

    user.setTinVerificationState(tinVerificationStateService.getStateForUpdatedUser(params, user));

    user.setUsername(params.getUsername());
    user.setFirstName(params.getFirstName());
    user.setMiddleName(params.getMiddleName());
    user.setLastName(params.getLastName());
    user.setDateOfBirth(params.getDateOfBirth());
    user.setSsaBsoId(params.getSsaBsoId());
    user.setInternalRole(params.getInternalRole());

    if (user.isClaimed()) {
      user.setEmail(params.getEmail());
      user.setPhoneNumber(params.getPhoneNumber());
      user.setTaxpayerIdentifier(params.getTaxpayerIdentifier());
      user.setUnverifiedTinType(params.getUnverifiedTinType());

      if (hasText(params.getPassword())) {
        setUserPassword(user, params.getPassword());
      }
    } else {
      user.setUnverifiedEmail(params.getEmail());
      if (user.isMigrating()) {
        user.setPhoneNumber(params.getPhoneNumber());
      } else {
        user.setUnverifiedPhoneNumber(params.getPhoneNumber());
      }
      user.setUnverifiedTaxpayerIdentifier(params.getTaxpayerIdentifier());
      user.setUnverifiedTinType(params.getUnverifiedTinType());

      // Setting a password through this code path will cause the user to be claimed if it isn't
      // already. This allows CS to bypass the notification-based invitation flow by manually
      // specifying a password.
      if (hasText(params.getPassword())) {

        claimUser(
            user,
            user.getUnverifiedEmail(),
            user.isMigrating() ? user.getPhoneNumber() : user.getUnverifiedPhoneNumber(),
            user.getUnverifiedTaxpayerIdentifier(),
            params.getPassword(),
            params.getUnverifiedTinType());

        detailedEmployeeRepository.findByUserId(user.getId()).stream()
            .map(DetailedEmployee::getOnboarding)
            .filter(Predicate.not(WorkerOnboarding::isClaimed))
            .forEach(workerOnboardingService::claimOnboarding);
      }
    }

    employeeRecords.forEach(
        employee -> eventPublisher.publishEvent(new WorkerUpdatedPersonalInfoEvent(employee)));
    employeeRecords.forEach(
        employee -> eventPublisher.publishEvent(new WorkerUpdatedContactInfoEvent(employee)));

    eventPublisher.publishEvent(new UserUpdatedEvent(user, params.getPassword()));
    return detailedUserRepository.saveAndRefresh(user);
  }

  public void deleteUsers(List<Long> userIds, boolean isHardDeleted) {
    deleteUsersInternal(userIds, isHardDeleted);
  }

  @Transactional
  public void deleteUsersInternal(List<Long> userIds, boolean isHardDeleted) {
    // Validate if userIds is null or empty
    if (Objects.isNull(userIds) || userIds.isEmpty()) {
      throw new InvalidRequestException("User ids can't be null or empty");
    }
    // Get detailed users
    List<DetailedUser> detailedUsers = getUsers(userIds);
    // List to store users that linked with employees
    List<Long> notDeleteEligible = new ArrayList<>();
    for (var detailedUser : detailedUsers) {
      try {
        deleteUser(detailedUser, isHardDeleted);
      } catch (InvalidRequestException ex) {
        log.error("Not allowed to delete user.", ex);
        notDeleteEligible.add(detailedUser.getId());
      } catch (Exception ex) {
        throw new InvalidRequestException("Failed to delete user.", ex);
      }
    }
    // If the list is not empty we can't delete these users
    if (!notDeleteEligible.isEmpty()) {
      throw new InvalidRequestException(
          String.format("User has associated employee records. Users Ids: %s", notDeleteEligible));
    }
  }

  @Transactional
  public void deleteUser(DetailedUser user, boolean isHardDeleted) {
    var employees = detailedEmployeeRepository.findByUserId(user.getId());
    if (employees.isEmpty()) {
      if (isHardDeleted) {
        processHardDeleteUser(user);
      } else {
        processSoftDeleteUser(user);
      }
    } else {
      throw new InvalidRequestException("User has associated employee records");
    }
  }

  private void processHardDeleteUser(DetailedUser user) {
    // delete usernotificationsettings
    var notificationSettings = userNotificationSettingsRepository.findByUserId(user.getId());
    userNotificationSettingsRepository.deleteAll(notificationSettings);

    var bankAccounts = userBankAccountRepository.findByUserId(user.getId());
    userBankAccountRepository.deleteAll(bankAccounts);

    var addresses = homeAddressRepository.findByUserId(user.getId());
    homeAddressRepository.deleteAll(addresses);

    var setPasswordToken = setPasswordTokenRepository.findByUserId(user.getId());
    setPasswordToken.ifPresent(setPasswordTokenRepository::delete);

    // delete user
    detailedUserRepository.delete(user);

    eventPublisher.publishEvent(new UserDeletedEvent(user));
  }

  /**
   * @param detailedUser Process soft delete for a user who has a reference in another table, and we
   *     have constrains on the user id in the another table in this case if we tried to delete this
   *     user we will get data integrity exception.
   */
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void processSoftDeleteUser(DetailedUser detailedUser) {
    log.info("start processing soft delete for user {}", detailedUser.getId());
    detailedUser.setEmail(
        String.format("deleted-user.{%s}@deleted-users.local", detailedUser.getId()));
    detailedUser.setLastName(detailedUser.getLastName() + " " + "(deleted)");
    detailedUser.setPhoneNumber(null);
    detailedUser.setTaxpayerIdentifier(null);
    detailedUser.setIsDeleted(true);
    detailedUserRepository.save(detailedUser);
  }

  public void claimUser(
      DetailedUser user,
      String email,
      String phoneNumber,
      String taxpayerIdentifier,
      String password,
      TinType unverifiedTinType) {
    if (user.isClaimed()) return;

    user.setEmail(email);
    user.setPhoneNumber(phoneNumber);
    if (StringUtils.isEmpty(taxpayerIdentifier)) {
      log.warn("claiming user with empty taxpayerIdentifier " + user);
    }
    user.setTaxpayerIdentifier(taxpayerIdentifier);
    user.setUnverifiedTinType(unverifiedTinType);
    user.setUnverifiedEmail(null);
    user.setUnverifiedPhoneNumber(null);
    user.setUnverifiedTaxpayerIdentifier(null);

    setUserPassword(user, password);
    applyNewPinFromSSN(user.getTaxpayerIdentifier(), user);
  }

  public void setUserPassword(DetailedUser user, String newPassword) {
    passwordComplexityValidator.validatePassword(newPassword, user.getUsernameOrEmail());
    user.setPassword(passwordEncoder.encode(newPassword));
  }

  private void applyNewPinFromSSN(String taxpayerIdentifier, @NonNull DetailedUser toUser) {
    ofNullable(taxpayerIdentifier)
        .filter(StringUtils::hasText)
        .filter(ssn -> ssn.length() > 4)
        .map(ssn -> ssn.substring(ssn.length() - 4))
        .ifPresent(pin -> setUserKioskPin(toUser, pin));
  }

  private void setUserKioskPin(DetailedUser user, String newKioskPin) {
    passwordComplexityValidator.validatePin(newKioskPin);
    user.setKioskPin(passwordEncoder.encode(newKioskPin));
  }

  public DetailedUser updateUserPassword(long userId, String newPassword) {
    var user = getUser(userId);
    setUserPassword(user, newPassword);
    eventPublisher.publishEvent(new UserUpdatedPasswordEvent(user, newPassword));
    return detailedUserRepository.save(user);
  }

  public DetailedUser updateUserPin(long userId, String newPin) {
    DetailedUser user = getUser(userId);
    setUserKioskPin(user, newPin);
    return detailedUserRepository.save(user);
  }

  public Page<DetailedCompany> listMyAuthorizedCompanies(
      String nameFilter, PhaseLookup phaseLookup, Pageable pageable) {
    if (authorizationCheckService.canAdminAllCompanies()) {
      return companyService.listCompanies(pageable, phaseLookup, null, nameFilter);
    } else {
      var authorizedCompanyIds = authorizationCheckService.getAuthorizedCompanyIds();
      return companyService.listCompanies(pageable, phaseLookup, authorizedCompanyIds, nameFilter);
    }
  }

  public Page<CoreCompany> listMyAuthorizedCoreCompanies(
      String nameFilter, PhaseLookup phaseLookup, Pageable pageable
  ) {
    if (authorizationCheckService.canAdminAllCompanies()) {
        return companyService.listCoreCompanies(pageable, phaseLookup, null, nameFilter);
    } else {
        var authorizedCompanyIds = authorizationCheckService.getAuthorizedCompanyIds();
        return companyService.listCoreCompanies(
                pageable, phaseLookup, authorizedCompanyIds, nameFilter
        );
    }
  }

  public Page<CoreCompany> listAuthorizedCoreCompanies(
      Long userId, String nameFilter, PhaseLookup phaseLookup, Pageable pageable) {
    var user = getUser(userId);
    var authorities = user.getAuthorities();

    if (authorizationCheckService.canAdminAllCompanies(authorities)) {
      return companyService.listCoreCompanies(pageable, phaseLookup, null, nameFilter);
    } else {
      var authorizedCompanyIds =
          authorizationCheckService.getAuthorizedCompanyIds(userId, authorities);
      return companyService.listCoreCompanies(
          pageable, phaseLookup, authorizedCompanyIds, nameFilter);
    }
  }

  public DetailedUser getMyDetailedUser() {
    return TenantSecurityContextHolder.getContext()
        .flatMap(TenantSecurityContext::getUser)
        .map(CoreUser::getId)
        .flatMap(detailedUserRepository::findById)
        .map(this::augmentWithMyAdditionalAuthorities)
        .orElseThrow(() -> new AccessDeniedException("Authenticated user not present"));
  }

  @Transactional(readOnly = true)
  public Page<TinVerificationStateUserDetails> listUserDetailsByTinVerificationState(
      @NonNull Long companyId,
      @NonNull TinVerificationState tinVerificationState,
      @NonNull Pageable pageable) {
    var company = companyService.getCompany(companyId);

    List<TinVerificationStateUserDetails> userDetailsList;
    try (var stream =
        tinVerificationFileUserRepository.streamUserDetailsByVerificationState(
            companyId, company.getNowDate(), tinVerificationState, pageable)) {
      userDetailsList = stream.collect(Collectors.toList());
    }

    var totalCount =
        tinVerificationFileUserRepository.countUserDetailsByVerificationState(
            companyId, company.getNowDate(), tinVerificationState);

    return new PageImpl<>(userDetailsList, pageable, totalCount);
  }

  @Transactional(readOnly = true)
  public Long getUserDetailCountByTinVerificationState(
      @NonNull Long companyId, @NonNull TinVerificationState tinVerificationState) {
    var company = companyService.getCompany(companyId);

    return tinVerificationFileUserRepository.countUserDetailsByVerificationState(
        companyId, company.getNowDate(), tinVerificationState);
  }

  @Transactional(readOnly = true)
  public TinVerificationStateCounts getUserCountsByTinVerificationState(@Nullable Long companyId) {
    var counts = new TinVerificationStateCounts();
    detailedUserRepository.getUserCountsByTinVerificationState(companyId).forEach(counts::add);
    return counts;
  }

  @Transactional
  public void updateFieldsForTinVerification(
      Long userId, UpdateFieldsForTinVerificationParams params) {
    var user = detailedUserRepository.findById(userId).orElseThrow();
    user.setTinVerificationState(
        tinVerificationStateService.getStateForUpdateFieldsForTinVerification(params, user));
    user.setFirstName(params.getFirstName());
    user.setMiddleName(params.getMiddleName());
    user.setLastName(params.getLastName());
    user.setTaxpayerIdentifier(params.getTaxpayerIdentifier());
    user.setUnverifiedTinType(params.getUnverifiedTinType());
    eventPublisher.publishEvent(new UserUpdatedEvent(user));
    detailedUserRepository.save(user);

    var payeeType = Optional.ofNullable(payeeTypeRepository.findByUserId(userId)).orElseThrow();
    payeeType.setBusinessName(params.getBusinessName());
    payeeTypeRepository.save(payeeType);
  }

  @Transactional
  public void markTinAsNeedsVerification(Long userId) {
    var user = detailedUserRepository.findById(userId).orElseThrow();
    user.setTinVerificationState(TinVerificationState.NEEDS_VERIFICATION);
    detailedUserRepository.save(user);
  }

  @Transactional
  public DetailedUser updateUserPersonalInformation(
      @NonNull Long userId, @NonNull UserPersonalInfoParams params) {
    var user = detailedUserRepository.getOne(userId);
    user.setFirstName(params.getFirstName());
    user.setLastName(params.getLastName());
    Optional.ofNullable(params.getMiddleName()).ifPresent(user::setMiddleName);
    Optional.ofNullable(params.getDateOfBirth()).ifPresent(user::setDateOfBirth);
    applyAdditionalInfo(user, params);

    eventPublisher.publishEvent(new UserUpdatedEvent(user));

    return user;
  }

  @Transactional
  public DetailedUser updateUserContactInformation(
      @NonNull Long userId, @NonNull UserContactInfoParams params) {
    var user = detailedUserRepository.getOne(userId);
    user.setEmail(trimToNull(params.getEmail()));
    user.setPhoneNumber(trimToNull(params.getPhoneNumber()));
    validateUserLogin(user);

    eventPublisher.publishEvent(new UserUpdatedEvent(user));

    return user;
  }

  /**
   * Adds the company-aware granted authorities from the current {@link Authentication} to the given
   * user. This was added to preserve backward-compatibility because some of the front-end codebases
   * depend on the {@code authorities} field for authorization. Going forward, authorities should be
   * accessed a different way. --kjensen 3/31/22
   */
  private DetailedUser augmentWithMyAdditionalAuthorities(DetailedUser source) {
    var target = new DetailedUser();
    BeanUtils.copyProperties(source, target);
    var additionalAuthorities =
        TenantSecurityContextHolder.getContext().stream()
            .flatMap(TenantSecurityContext::getCompanyAwareGrantedAuthorities)
            .collect(Collectors.toSet());
    target.setAdditionalAuthorities(additionalAuthorities);
    return target;
  }

  private void validateUserLogin(@NonNull DetailedUser user) {
    if (ObjectUtils.allAbsent(user.getUsername(), user.getEmail())) {
      throw new IllegalArgumentException("Username or email must be defined");
    }
  }

  private void applyAdditionalInfo(
      @NonNull DetailedUser user, @NonNull UserPersonalInfoParams params) {
    var shirtSize = trimToNull(params.getShirtSize());
    var partnerShirtSize = trimToNull(params.getPartnerShirtSize());
    var dietaryRestrictions = trimToNull(params.getDietaryRestrictions());
    var preferredName = trimToNull(params.getPreferredName());

    if (ObjectUtils.allAbsent(
        shirtSize,
        partnerShirtSize,
        dietaryRestrictions,
        preferredName,
        user.getUserAdditionalInfo())) {
      return;
    }

    var info = Optional.ofNullable(user.getUserAdditionalInfo()).orElseGet(UserAdditionalInfo::new);
    info.setShirtSize(shirtSize);
    info.setPartnerShirtSize(partnerShirtSize);
    info.setDietaryRestrictions(dietaryRestrictions);
    info.setPreferredName(preferredName);

    user.setUserAdditionalInfo(info);
  }

  public void updateVerifiedTinType(Set<Long> userIds, UserTinValidationStatusCode code) {
    detailedUserLookupService.findAll(new UserLookup().withIds(userIds)).stream()
        .forEach(
            user -> {
              if (UserTinValidationStatusCode.SSA_VERIFIED.equals(code)) {
                user.setTinType(TinType.SSN);
              } else if (UserTinValidationStatusCode.IRS_TIN_NAME_MATCH.equals(code)) {
                if (PayeeTypeName.BUSINESS.equals(user.getPayeeType().getTypeName())) {
                  user.setTinType(TinType.EIN);
                } else if (PayeeTypeName.INDIVIDUAL.equals(user.getPayeeType().getTypeName())) {
                  user.setTinType(TinType.SSN);
                }
              }
            });
  }
}
