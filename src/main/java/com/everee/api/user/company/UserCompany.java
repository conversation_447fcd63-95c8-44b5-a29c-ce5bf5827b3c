package com.everee.api.user.company;

import com.everee.api.auth.role.InternalRole;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.configurations.CompanyTimeTrackingConfiguration;
import com.everee.api.company.configurations.TimeTrackingMode;
import com.everee.api.companyrole.CompanyRole;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.user.CoreUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Objects;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Immutable
@IdClass(UserCompanyIdentifier.class)
public class UserCompany {

  @Id private Long userId;

  @Id private Long companyId;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "userId", updatable = false, insertable = false)
  private CoreUser user;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "companyId", updatable = false, insertable = false)
  private DetailedCompany company;

  private String companyDisplayName;

  @SuppressWarnings("unused")
  public boolean isCompanyTimeTrackingEnabled() {
    return Optional.ofNullable(company)
        .map(DetailedCompany::getTimeTrackingConfiguration)
        .map(CompanyTimeTrackingConfiguration::getTimeTrackingMode)
        .map(TimeTrackingMode.TIME_CLOCK::equals)
        .orElse(false);
  }

  @SuppressWarnings("unused")
  public InternalRole getUserInternalRole() {
    return Optional.ofNullable(user).map(CoreUser::getInternalRole).orElse(null);
  }

  @SuppressWarnings("unused")
  public Set<CompanyRoleType> getUserCompanyRoles() {
    return Optional.ofNullable(user).map(CoreUser::getCompanyRoles).stream()
        .flatMap(Collection::stream)
        .filter(r -> Objects.equal(companyId, r.getCompanyId()))
        .map(CompanyRole::getType)
        .collect(Collectors.toSet());
  }
}
