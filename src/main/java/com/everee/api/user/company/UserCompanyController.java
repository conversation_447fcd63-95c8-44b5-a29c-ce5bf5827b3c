package com.everee.api.user.company;

import com.everee.api.company.CompanyService;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.user.UserService;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class UserCompanyController {

  private final UserCompanyRepository userCompanyRepository;
  private final UserCompanyService userCompanyService;
  private final UserCompanyPreferenceRepository userCompanyPreferenceRepository;

  @GetMapping("/api/v1/users/me/companies")
  public Page<UserCompany> listAccessibleCompanies(Pageable pageable) {
    return userCompanyRepository.findAllByUserId(UserService.getAuthenticatedUserId(), pageable);
  }

  @GetMapping("/api/v2/user-preferences")
  public List<UserCompanyPreference> listUserCompanyPreferences() {
    return userCompanyPreferenceRepository.findAllByUserIdAndCompanyId(
        UserService.getAuthenticatedUserId(), CompanyService.getAuthenticatedCompanyId());
  }

  @GetMapping("/api/v2/user-preferences/{key}")
  public UserCompanyPreference getUserCompanyPreference(@PathVariable String key) {
    return userCompanyPreferenceRepository
        .findByUserIdAndCompanyIdAndKey(
            UserService.getAuthenticatedUserId(), CompanyService.getAuthenticatedCompanyId(), key)
        .orElse(
            new UserCompanyPreference()
                .setCompanyId(CompanyService.getAuthenticatedCompanyId())
                .setUserId(UserService.getAuthenticatedUserId())
                .setKey(key)
                .setValue(null));
  }

  @PutMapping("/api/v2/user-preferences")
  public UserCompanyPreference setUserCompanyPreference(
      @Valid @RequestBody UserCompanyPreference from) {

    if (from.getUserId() == null) from.setUserId(UserService.getAuthenticatedUserId());
    if (from.getCompanyId() == null) from.setCompanyId(CompanyService.getAuthenticatedCompanyId());

    if (!from.getUserId().equals(UserService.getAuthenticatedUserId())
        || !from.getCompanyId().equals(CompanyService.getAuthenticatedCompanyId()))
      throw new InvalidRequestException("User and/or company does not match");
    return userCompanyService.setUserCompanyPreference(from);
  }
}
