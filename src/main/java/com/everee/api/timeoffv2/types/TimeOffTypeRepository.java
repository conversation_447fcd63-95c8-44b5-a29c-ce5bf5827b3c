package com.everee.api.timeoffv2.types;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface TimeOffTypeRepository extends JpaRepository<TimeOffType, Long> {

  @Query(
      "SELECT COUNT(t) > 0 FROM TimeOffType t"
          + "  WHERE (t.companyId IS NULL OR t.companyId = :companyId) "
          + "  AND LOWER(t.name) LIKE LOWER(:name)")
  boolean existsByCompanyIdAndNameIgnoreCase(
      @Param("companyId") Long companyId, @Param("name") String name);

  Page<TimeOffType> findAllByCompanyIdIsNull(Pageable pageable);

  Page<TimeOffType> findAllByCompanyId(Long companyId, Pageable pageable);

  @Query("SELECT t FROM TimeOffType t WHERE t.companyId IS NULL OR t.companyId = :companyId")
  Page<TimeOffType> findAllForCompany(@Param("companyId") Long companyId, Pageable pageable);
}
