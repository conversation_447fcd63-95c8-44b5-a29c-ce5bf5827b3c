package com.everee.api.timeoffv2.policy.lookup;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import com.everee.api.timeoffv2.policy.TimeOffPolicyStatus;
import com.everee.api.util.SetUtils;
import io.swagger.annotations.ApiParam;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@RequestParams
@Accessors(chain = true)
public class TimeOffPolicyLookup implements Lookup {
  private Set<Long> companyIds;
  private Set<Long> policyIds;

  @ApiParam(name = "status", allowMultiple = true)
  private Set<TimeOffPolicyStatus> statuses;

  public TimeOffPolicyLookup setPolicyId(Long policyId) {
    policyIds = SetUtils.toSetOrNull(policyId);
    return this;
  }
}
