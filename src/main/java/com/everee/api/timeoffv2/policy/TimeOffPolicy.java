package com.everee.api.timeoffv2.policy;

import com.everee.api.model.BaseModel;
import java.time.LocalDate;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Entity
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TimeOffPolicy extends BaseModel<TimeOffPolicy> {
  protected static final String STATUS_FORMULA =
      "(SELECT (CASE "
          + "    WHEN p.startDate IS NULL AND p.endDate IS NULL THEN 'DRAFT' "
          + "    WHEN DATE (timezone(c.timezone, CURRENT_TIMESTAMP)) < p.startDate THEN 'UPCOMING' "
          + "    WHEN DATE (timezone(c.timezone, CURRENT_TIMESTAMP)) < COALESCE(p.endDate, 'infinity') THEN 'ACTIVE' "
          + "    ELSE 'ENDED' END) "
          + "  FROM TimeOffPolicy p "
          + "  JOIN Company c ON c.id = p.companyId "
          + "  WHERE p.id = {alias}.id)";

  protected static final String CATEGORY_COUNT_FORMULA =
      "(SELECT COUNT(*) "
          + "  FROM TimeOffPolicyCategory c "
          + "  WHERE c.timeOffPolicyId = {alias}.id)";

  protected static final String PAID_CATEGORY_COUNT_FORMULA =
      "(SELECT COUNT(*) "
          + "  FROM TimeOffPolicyCategory c "
          + "  WHERE c.allotmentType IN ('UNLIMITED','ACCRUED','FRONT_LOADED')"
          + "    AND c.timeOffPolicyId = {alias}.id)";

  protected static final String UNPAID_CATEGORY_COUNT_FORMULA =
      "(SELECT COUNT(*) "
          + "  FROM TimeOffPolicyCategory c "
          + "  WHERE  c.allotmentType NOT IN ('UNLIMITED','ACCRUED','FRONT_LOADED')"
          + "    AND c.timeOffPolicyId = {alias}.id)";

  @NotNull private Long companyId;
  @NotNull private String title;
  private String description;
  private LocalDate startDate;
  private LocalDate endDate;

  @Setter(AccessLevel.NONE)
  @Enumerated(EnumType.STRING)
  @EqualsAndHashCode.Exclude
  @Formula(STATUS_FORMULA)
  private TimeOffPolicyStatus policyStatus;

  @Setter(AccessLevel.NONE)
  @EqualsAndHashCode.Exclude
  @Formula(CATEGORY_COUNT_FORMULA)
  private Integer categoryCount;

  @Setter(AccessLevel.NONE)
  @EqualsAndHashCode.Exclude
  @Formula(PAID_CATEGORY_COUNT_FORMULA)
  private Integer paidCategoryCount;

  @Setter(AccessLevel.NONE)
  @EqualsAndHashCode.Exclude
  @Formula(UNPAID_CATEGORY_COUNT_FORMULA)
  private Integer unpaidCategoryCount;

  public String getSecondaryLabel() {
    if (paidCategoryCount > 0 && unpaidCategoryCount > 0) {
      return "Paid and unpaid time";
    }
    if (paidCategoryCount > 0) {
      return "Paid time";
    }
    if (unpaidCategoryCount > 0) {
      return "Unpaid time";
    }
    return "No categories";
  }
}
