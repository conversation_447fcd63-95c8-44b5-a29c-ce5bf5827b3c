package com.everee.api.timeoffv2.policy.lookup;

import static com.everee.api.query.where.Where.property;

import com.everee.api.lookup.LookupService;
import com.everee.api.query.Query;
import com.everee.api.timeoffv2.policy.TimeOffPolicy;
import com.everee.api.timeoffv2.policy.TimeOffPolicy_;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TimeOffPolicyLookupService
    implements LookupService<TimeOffPolicy, TimeOffPolicyLookup, Query<TimeOffPolicy>> {
  private final EntityManager entityManager;

  @Override
  public void configureQuery(TimeOffPolicyLookup lookup, Query<TimeOffPolicy> query) {
    query
        .where(property(TimeOffPolicy_.ID).in(lookup.getPolicyIds()))
        .where(property(TimeOffPolicy_.POLICY_STATUS).in(lookup.getStatuses()));
  }

  @Override
  public Query<TimeOffPolicy> createQuery() {
    return new Query<>(entityManager, TimeOffPolicy.class);
  }
}
