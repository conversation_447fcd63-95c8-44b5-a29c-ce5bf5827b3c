package com.everee.api.timeoffv2.policy;

import com.everee.api.model.BaseModel;
import java.math.BigDecimal;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TimeOffPolicyCategoryAwardConfig extends BaseModel<TimeOffPolicyCategoryAwardConfig> {
  @NotNull private Long timeOffPolicyCategoryId;

  /** leave 0 for base award config */
  private int employedYears = 0;

  private BigDecimal frontLoadedHours;
  private BigDecimal accrualRatioEarnedHours;
  private int maxBalanceHours;
  private int maxRollOverHours;
  private int maxNegativeBalanceHours;
}
