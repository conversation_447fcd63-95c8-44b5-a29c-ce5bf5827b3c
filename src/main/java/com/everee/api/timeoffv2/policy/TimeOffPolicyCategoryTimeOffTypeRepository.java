package com.everee.api.timeoffv2.policy;

import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface TimeOffPolicyCategoryTimeOffTypeRepository
    extends JpaRepository<TimeOffPolicyCategoryTimeOffType, TimeOffPolicyCategoryTimeOffType> {

  @Query(
      "SELECT ct "
          + "  FROM TimeOffPolicyCategoryTimeOffType ct "
          + "  JOIN TimeOffPolicyCategory c ON ct.timeOffPolicyCategoryId = c.id "
          + "  JOIN TimeOffPolicy p ON c.timeOffPolicyId = p.id "
          + "  WHERE p.id = :policyId")
  Page<TimeOffPolicyCategoryTimeOffType> findAllForPolicy(
      @NonNull @Param("policyId") Long policyId, @NonNull Pageable pageable);
}
