package com.everee.api.timeoffv2.policy.controller.datatransfer;

import com.everee.api.timeoffv2.policy.TimeOffAllotmentType;
import com.everee.api.timeoffv2.policy.TimeOffFrontLoadedPeriodType;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TimeOffPolicyCategoryDTO {
  @NotNull Long id;
  @NotNull Long timeOffPolicyId;
  @NotNull TimeOffAllotmentType allotmentType;
  boolean workerUnpaidTimeOffSelectionEnabled;

  @Min(0)
  int eligibleAfterEmployedDays;

  @Min(0)
  int eligibleAfterWorkedHours;

  TimeOffFrontLoadedPeriodType frontLoadedPeriodType;
  boolean initialFrontLoadedBalanceProrated;

  @Min(0)
  int accrualRatioWorkedHours;

  boolean accruableWhileOnPaidTimeOff;
  boolean maxBalanceHoursEnabled;
  boolean maxRollOverHoursEnabled;
  boolean maxNegativeBalanceHoursEnabled;
  @NotNull List<Long> timeOffTypes = new ArrayList<>();
  @NotNull List<TimeOffPolicyCategoryAwardConfigDTO> awardConfigs = new ArrayList<>();
}
