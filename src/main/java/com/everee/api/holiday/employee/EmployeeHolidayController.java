package com.everee.api.holiday.employee;

import com.everee.api.company.CompanyService;
import com.everee.api.employee.Employee;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.user.UserService;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/employee-holidays")
@RequiredArgsConstructor
public class EmployeeHolidayController {
  private final EmployeeService employeeService;
  private final DetailedEmployeeLookupService employeeLookupService;
  private final EmployeeHolidayLookupService holidayLookupService;

  @GetMapping
  public Page<EmployeeHoliday> listEmployeeHolidays(PhaseLookup phaseLookup, Pageable pageable) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    var userId = UserService.getAuthenticatedUserId();

    var employeeIds =
        employeeLookupService
            .listAll(new EmployeeLookup().withUserIds(Set.of(userId)), Pageable.unpaged()).stream()
            .map(Employee::getId)
            .collect(Collectors.toSet());

    var lookup =
        new EmployeeHolidayLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(employeeIds)
            .withPhaseLookup(phaseLookup);
    return holidayLookupService.listAll(lookup, pageable);
  }

  @GetMapping("/me")
  public EmployeeHolidays listMyEmployeeHolidays(PhaseLookup phaseLookup) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    var userId = UserService.getAuthenticatedUserId();

    var employeeIds =
        employeeLookupService
            .listAll(
                new EmployeeLookup().withUserIds(Set.of(userId)).withOnlyMyEmployees(true),
                Pageable.unpaged())
            .stream()
            .map(Employee::getId)
            .collect(Collectors.toSet());

    var lookup =
        new EmployeeHolidayLookup()
            .withCompanyIds(Set.of(companyId))
            .withEmployeeIds(employeeIds)
            .withPhaseLookup(phaseLookup)
            .withIncludeExtraHolidayEarned(Boolean.TRUE);
    return new EmployeeHolidays()
        .setHolidays(
            holidayLookupService
                .listAll(
                    lookup,
                    PageRequest.of(0, Integer.MAX_VALUE, Sort.by(EmployeeHoliday_.START_DATE)))
                .getContent());
  }
}
