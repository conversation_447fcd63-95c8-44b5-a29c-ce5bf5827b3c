package com.everee.api.holiday.system;

import com.everee.api.config.RequestParams;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@RequestParams
@Accessors(chain = true)
public class SystemHolidayLookup {
  @ApiParam(name = "id")
  private Set<Long> ids;

  @ApiParam(name = "year")
  private Integer year;

  @ApiParam(name = "date")
  private LocalDate date;

  @ApiParam(name = "include-archived")
  private Boolean includeArchived;
}
