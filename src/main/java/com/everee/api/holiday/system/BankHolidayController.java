package com.everee.api.holiday.system;

import com.everee.api.exception.InvalidRequestException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Year;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/bank-holidays")
@RequiredArgsConstructor
public class BankHolidayController {
  private final SystemHolidayService systemHolidayService;

  @GetMapping
  public BankHolidayListDto listBankingHolidays(@RequestParam(name = "for-year", required = false, defaultValue = "#{T(java.time.Year).now().getValue()}") int year) {
    if (year < Year.MIN_VALUE || year > Year.MAX_VALUE) {
      throw new InvalidRequestException("Supply a valid year");
    }

    return new BankHolidayListDto()
               .setHolidays(systemHolidayService.getHolidaysForYear(year, false)
                 .stream()
                 .map(BankHolidayMapper.MAPPER::toDto)
                 .collect(Collectors.toList()));
  }
}
