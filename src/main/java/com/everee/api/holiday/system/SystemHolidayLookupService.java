package com.everee.api.holiday.system;

import static com.everee.api.query.where.Where.property;
import static java.time.temporal.TemporalAdjusters.lastDayOfYear;

import com.everee.api.lookup.TenantlessLookupService;
import com.everee.api.query.Query;
import java.time.Year;
import java.util.Optional;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SystemHolidayLookupService
    implements TenantlessLookupService<SystemHoliday, SystemHolidayLookup, Query<SystemHoliday>> {
  private final EntityManager entityManager;

  @Override
  public void configureQuery(SystemHolidayLookup lookup, Query<SystemHoliday> query) {
    query
        .where(property(SystemHoliday_.ID).in(lookup.getIds()))
        .where(property(SystemHoliday_.DATE).equal(lookup.getDate()));

    if (lookup.getYear() != null) {
      var year = Year.of(lookup.getYear());
      query.where(property(SystemHoliday_.DATE).greaterThanOrEqual(year.atDay(1)));
      query.where(
          property(SystemHoliday_.DATE).lessThanOrEqual(year.atDay(1).with(lastDayOfYear())));
    }

    var includeArchived =
        Optional.ofNullable(lookup.getIncludeArchived())
            .orElse(CollectionUtils.isNotEmpty(lookup.getIds()));
    if (!includeArchived) {
      query.where(property(SystemHoliday_.ARCHIVED_AT).isNull());
    }
  }

  @Override
  public Query<SystemHoliday> createQuery() {
    return new Query<>(entityManager, SystemHoliday.class);
  }
}
