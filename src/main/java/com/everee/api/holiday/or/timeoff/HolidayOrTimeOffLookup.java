package com.everee.api.holiday.or.timeoff;

import com.everee.api.lookup.Lookup;
import com.everee.api.phase.PhaseLookup;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class HolidayOrTimeOffLookup implements Lookup {
  private Set<Long> companyIds;
  private Set<Long> employeeIds;
  private PhaseLookup phaseLookup;
}
