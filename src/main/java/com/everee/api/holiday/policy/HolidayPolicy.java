package com.everee.api.holiday.policy;

import com.everee.api.company.DetailedCompany;
import com.everee.api.config.attributeconverter.YearIntAttributeConverter;
import com.everee.api.model.BaseModel;
import com.everee.api.phase.Phased;
import com.everee.api.time.CompanyLocalTimeService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.Duration;
import java.time.LocalDate;
import java.time.MonthDay;
import java.time.Year;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import javax.persistence.CascadeType;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.SortNatural;

@Data
@Accessors(chain = true)
@Entity
@EqualsAndHashCode(callSuper = true)
public class HolidayPolicy extends BaseModel {
  @NotNull private Long companyId;

  @Convert(converter = YearIntAttributeConverter.class)
  @NotNull
  private Year year;

  @OneToMany(
      mappedBy = "policyId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @SortNatural
  @OrderBy("startDate ASC")
  private Set<HolidayPolicyEligibility> policyEligibilities = new TreeSet<>();

  @OneToMany(
      mappedBy = "policyId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @SortNatural
  @OrderBy("startDate ASC")
  private Set<HolidayPolicyMultiplier> policyMultipliers = new TreeSet<>();

  @OneToMany(
      mappedBy = "policyId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @SortNatural
  @OrderBy("startDate ASC")
  private Set<HolidayPolicyAdditionalHours> policyAdditionalHours = new TreeSet<>();

  @OneToMany(
      mappedBy = "policyId",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  private Set<EnabledHoliday> enabledHolidays = new HashSet<>();

  @ToString.Exclude
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  @JsonIgnore
  private DetailedCompany company;

  @Transient public List<HolidayPolicyEmployeeInfo> employeeHolidayHours = new ArrayList<>();

  @Transient @JsonIgnore private Set<EnabledHoliday> removedHolidays = new HashSet<>();

  public HolidayPolicyEligibilityRuleType getHolidayPolicyEligibilityRuleType() {

    if (isSalaryEligible() && isFullTimeHourlyEligible() && !isPartTimeHourlyEligible()) {
      return HolidayPolicyEligibilityRuleType.SALARY_AND_FULL_TIME_HOURLY;
    }

    if (isSalaryEligible() && isFullTimeHourlyEligible() && isPartTimeHourlyEligible()) {
      return HolidayPolicyEligibilityRuleType.ALL;
    }

    return HolidayPolicyEligibilityRuleType.SALARY;
  }

  public Double getMultiplier() {
    return getFullTimeMultiplier();
  }

  public boolean isSalaryEligible() {
    return getEligibility(CompanyLocalTimeService.companyLocalNowDate(company)).isSalaryEligible();
  }

  public boolean isFullTimeHourlyEligible() {
    return getEligibility(CompanyLocalTimeService.companyLocalNowDate(company))
        .isFullTimeHourlyEligible();
  }

  public boolean isPartTimeHourlyEligible() {
    return getEligibility(CompanyLocalTimeService.companyLocalNowDate(company))
        .isPartTimeHourlyEligible();
  }

  public Double getFullTimeMultiplier() {
    return getMultiplier(CompanyLocalTimeService.companyLocalNowDate(company))
        .getFullTimeMultiplier();
  }

  public Double getPartTimeMultiplier() {
    return getMultiplier(CompanyLocalTimeService.companyLocalNowDate(company))
        .getPartTimeMultiplier();
  }

  public Duration getAdditionalFullTimeHourlyTime() {
    return getAdditionalHours(CompanyLocalTimeService.companyLocalNowDate(company))
        .getAdditionalFullTimeHourlyTime();
  }

  public Duration getAdditionalPartTimeHourlyTime() {
    return getAdditionalHours(CompanyLocalTimeService.companyLocalNowDate(company))
        .getAdditionalPartTimeHourlyTime();
  }

  public LocalDate getStartDate() {
    return year.atDay(1);
  }

  public LocalDate getEndDate() {
    return year.atMonthDay(MonthDay.of(12, 31));
  }

  protected HolidayPolicyEligibility getEligibility(LocalDate forDate) {
    return getActive(forDate, policyEligibilities);
  }

  protected HolidayPolicyMultiplier getMultiplier(LocalDate forDate) {
    return getActive(forDate, policyMultipliers);
  }

  protected HolidayPolicyAdditionalHours getAdditionalHours(LocalDate forDate) {
    return getActive(forDate, policyAdditionalHours);
  }

  private <T extends Phased> T getActive(LocalDate forDate, Set<T> phasedElements) {
    var isCurrentYear = year.getValue() == forDate.getYear();

    // if it's a different year, get the last one for the year
    return phasedElements.stream()
        .filter(
            holidayPolicyMultiplier ->
                (isCurrentYear && holidayPolicyMultiplier.isActive(forDate))
                    || (!isCurrentYear
                        && holidayPolicyMultiplier.getEndDate().getMonthValue() == 12
                        && holidayPolicyMultiplier.getEndDate().getDayOfMonth() == 31))
        .findFirst()
        .get();
  }
}
