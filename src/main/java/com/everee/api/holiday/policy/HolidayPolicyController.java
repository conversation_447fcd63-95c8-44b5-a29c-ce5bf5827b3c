package com.everee.api.holiday.policy;

import com.everee.api.auth.annotation.AccountOwnerAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.time.CompanyLocalTimeService;
import java.time.LocalDate;
import java.time.Year;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/holidaypolicy")
@RequiredArgsConstructor
public class HolidayPolicyController {
  private final HolidayPolicyService holidayPolicyService;
  private final HolidayPolicyPaymentsService holidayPolicyPaymentsService;
  private final CompanyLocalTimeService companyLocalTimeService;

  @GetMapping
  @AccountOwnerAccess
  public Page<HolidayPolicy> listHolidayPolicies(Pageable pageable) {
    return holidayPolicyService.listHolidayPolicies(
        pageable, CompanyService.getAuthenticatedCompanyId());
  }

  @GetMapping("/missing-policy-years")
  @AccountOwnerAccess
  public List<Integer> getYearsWithoutPolicies() {
    return holidayPolicyService.getYearsWithoutPolicies(CompanyService.getAuthenticatedCompanyId());
  }

  @GetMapping("/last-editable-date")
  @AccountOwnerAccess
  public LocalDate getLastEditableDate(
      @RequestParam(required = false, name = "employee-id") Long employeeId) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    var currentYear = Year.of(companyLocalTimeService.companyLocalNowDate(companyId).getYear());
    return holidayPolicyService.getLastEditableDate(companyId, employeeId, currentYear.atDay(1));
  }

  @GetMapping("/{holidayPolicyId}")
  @AccountOwnerAccess
  public HolidayPolicy getHolidayPolicy(@PathVariable(name = "holidayPolicyId") Long policyId) {
    return holidayPolicyService.getHolidayPolicy(policyId);
  }

  @PutMapping("/{holidayPolicyId}")
  @AccountOwnerAccess
  public HolidayPolicy updateHolidayPolicy(
      @PathVariable(name = "holidayPolicyId") Long policyId,
      @RequestBody HolidayPolicyRequest holidayPolicy) {
    return holidayPolicyPaymentsService.updateHolidayPolicyRecord(
        policyId, holidayPolicy, CompanyService.getAuthenticatedCompanyId());
  }

  @PostMapping
  @AccountOwnerAccess
  public HolidayPolicy createHolidayPolicy(@RequestBody HolidayPolicyRequest request) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    return holidayPolicyPaymentsService.createHolidayPolicy(request, companyId);
  }

  @DeleteMapping("/{holidayPolicyId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  @AccountOwnerAccess
  public void deleteHolidayPolicy(@PathVariable(name = "holidayPolicyId") Long holidayPolicyId) {
    holidayPolicyService.deleteHolidayPolicy(
        holidayPolicyId, CompanyService.getAuthenticatedCompanyId());
  }

  @GetMapping("{holidayPolicyId}/enabled")
  @AccountOwnerAccess
  @Deprecated(forRemoval = true)
  public List<EnabledHoliday> listEnabledHolidays_deprecated(
      @PathVariable(name = "holidayPolicyId") Long holidayPolicyId) {
    return holidayPolicyService.listEnabledHolidays(
        CompanyService.getAuthenticatedCompanyId(), holidayPolicyId);
  }

  @PutMapping("{policyId}/enabled")
  @AccountOwnerAccess
  @Deprecated(forRemoval = true)
  public List<EnabledHoliday> replaceEnabledHoliday_deprecated(
      @PathVariable Long policyId, @RequestBody EnabledHolidayRequest request) {
    return holidayPolicyService.replaceEnabledHolidays(
        request.getHolidays(), CompanyService.getAuthenticatedCompanyId(), policyId);
  }

  @PostMapping("{policyId}/enabled")
  @AccountOwnerAccess
  @Deprecated(forRemoval = true)
  public EnabledHoliday addEnabledHoliday_deprecated(
      @PathVariable Long policyId, @RequestBody EnabledHolidayRequestItem requestItem) {
    return holidayPolicyService.addEnabledHoliday(
        requestItem, CompanyService.getAuthenticatedCompanyId(), policyId);
  }

  @DeleteMapping("{policyId}/enabled/{holidayId}")
  @AccountOwnerAccess
  @Deprecated(forRemoval = true)
  public void deleteEnabledHoliday_deprecated(
      @PathVariable Long policyId, @PathVariable Long holidayId) {
    holidayPolicyService.deleteEnabledHoliday(holidayId, policyId);
  }
}
