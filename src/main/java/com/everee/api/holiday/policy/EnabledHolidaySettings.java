package com.everee.api.holiday.policy;

import java.time.Duration;
import java.time.LocalDate;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Entity
@Immutable
public class EnabledHolidaySettings {
  @Id private Long holidayId;

  private LocalDate holidayDate;

  private String holidayLabel;

  @Enumerated(EnumType.STRING)
  private HolidayType holidayType;

  private Long policyId;

  private Long companyId;

  private boolean salaryEligible;

  private boolean fullTimeHourlyEligible;

  private boolean partTimeHourlyEligible;

  private Double fullTimeMultiplier;

  private Double partTimeMultiplier;

  private Duration additionalFullTimeHourlyTime;

  private Duration additionalPartTimeHourlyTime;
}
