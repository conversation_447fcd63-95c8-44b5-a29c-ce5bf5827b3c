package com.everee.api.holiday.policy;

import com.everee.api.model.BaseModel;
import com.everee.api.phase.Phased;
import java.time.Duration;
import java.time.LocalDate;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@Entity
@Accessors(chain = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@EntityListeners(AuditingEntityListener.class)
public class HolidayPolicyAdditionalHours extends BaseModel
    implements Phased, Comparable<HolidayPolicyAdditionalHours> {

  @EqualsAndHashCode.Include @NotNull private Long policyId;

  @EqualsAndHashCode.Include @NotNull private LocalDate startDate;
  @NotNull private LocalDate endDate;

  @NotNull private Duration additionalFullTimeHourlyTime;

  @NotNull private Duration additionalPartTimeHourlyTime;

  @LastModifiedBy private Long lastEditByUserId;

  @Setter(AccessLevel.NONE)
  @Formula("coalesce((select u.firstname from appuser u where u.id = lastEditByUserId))")
  private String lastEditByUserName;

  @Override
  public int compareTo(HolidayPolicyAdditionalHours o) {
    return this.getStartDate().compareTo(o.getStartDate());
  }
}
