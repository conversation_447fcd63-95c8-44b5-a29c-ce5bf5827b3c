package com.everee.api.holiday.policy;

import java.time.Duration;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class HolidayPolicyEmployeeInfo {
  @EqualsAndHashCode.Include private Long id;
  private String displayFullName;

  private Map<LocalDate, Duration> holidayHours = new HashMap<>();

  public HolidayPolicyEmployeeInfo(Long employeeId, String fullName) {
    setId(employeeId);
    setDisplayFullName(fullName);
  }
}
