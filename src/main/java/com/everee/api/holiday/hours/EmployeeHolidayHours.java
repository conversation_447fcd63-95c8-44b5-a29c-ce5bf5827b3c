package com.everee.api.holiday.hours;

import com.everee.api.model.BaseModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.Duration;
import java.time.LocalDate;
import javax.persistence.Entity;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Data
@Accessors(chain = true)
@Entity
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class EmployeeHolidayHours extends BaseModel {

  private static final String DISPLAY_FULL_NAME_FORMULA =
      "(select "
          + "concat(u.lastname, ', ', u.firstname, case when u.middlename is null or u.middlename = '' then null else concat(' ', substring(u.middlename from 1 for 1)) end)"
          + "from employee e join appuser u on u.id = e.userid where e.id=employeeid)";

  private static final String HOLIDAY_DATE_FORMULA =
      "(select h.date from enabledholiday h where h.id = holidayid)";

  @JsonIgnore private Long companyId;

  @EqualsAndHashCode.Include private Long employeeId;

  @EqualsAndHashCode.Include private Long holidayId;

  private Duration paidTime;

  @Setter(AccessLevel.NONE)
  @Formula(HOLIDAY_DATE_FORMULA)
  private LocalDate holidayDate;

  @Setter(AccessLevel.NONE)
  @Formula(DISPLAY_FULL_NAME_FORMULA)
  private String employeeFullName;
}
