package com.everee.api.excel;

import static com.everee.api.util.ObjectUtils.stream;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import java.util.stream.Stream;
import javax.annotation.WillClose;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExcelReader extends ExcelWorkbook {
  public ExcelReader(@WillClose InputStream inputStream) throws IOException {
    if (!inputStream.markSupported()) {
      inputStream = new BufferedInputStream(inputStream);
    }
    try (InputStream willCloseInputStream = inputStream) {
      this.workbook = new XSSFWorkbook(willCloseInputStream);
    }
  }

  public Stream<Cell> cells() {
    return stream(getSelectedSheet().rowIterator()).flatMap(row -> stream(row.cellIterator()));
  }

  public Cell cell(int rownum, int column) {
    final var row = Optional.ofNullable(getSelectedSheet().getRow(rownum)).orElseThrow();
    final var cell =
        Optional.ofNullable(row.getCell(column, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK))
            .orElseThrow();
    return cell;
  }
}
