package com.everee.api.paystub.entity;

import com.everee.api.money.Money;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.Value;

@Value
@RequiredArgsConstructor
public class PayStubOriginalShift {
  Long shiftId;
  LocalDateTime workLocationStartAtInclusive;
  LocalDateTime workLocationEndAtExclusive;
  LocalDate workLocationEndDate;
  Money hourlyPayRate;

  // JPA-specific constructor
  @SuppressWarnings("unused")
  public PayStubOriginalShift(
      Long shiftId,
      LocalDateTime workLocationStartAtInclusive,
      LocalDateTime workLocationEndAtExclusive,
      LocalDate workLocationEndDate,
      BigDecimal hourlyPayRate) {
    this.shiftId = shiftId;
    this.workLocationStartAtInclusive = workLocationStartAtInclusive;
    this.workLocationEndAtExclusive = workLocationEndAtExclusive;
    this.workLocationEndDate = workLocationEndDate;
    this.hourlyPayRate = Optional.ofNullable(hourlyPayRate).map(Money::valueOf).orElse(Money.ZERO);
  }
}
