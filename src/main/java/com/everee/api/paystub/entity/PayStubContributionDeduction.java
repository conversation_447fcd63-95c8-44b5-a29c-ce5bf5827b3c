package com.everee.api.paystub.entity;

import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.money.Money;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.Value;

@Value
@RequiredArgsConstructor
public class PayStubContributionDeduction {
  EmployeeContributionDeductionType type;
  String typeGuid;
  String name;
  Integer ordinal;
  Money amountEE;
  Money amountER;
  Money ytdAmountEE;
  Money ytdAmountER;

  // JPA-specific constructor
  @SuppressWarnings("unused")
  public PayStubContributionDeduction(
      String type,
      String typeGuid,
      String name,
      Integer ordinal,
      BigDecimal amountEE,
      BigDecimal amountER,
      BigDecimal ytdAmountEE,
      BigDecimal ytdAmountER) {
    this.type = EmployeeContributionDeductionType.valueOf(type);
    this.typeGuid = typeGuid;
    this.name = name;
    this.ordinal = ordinal;
    this.amountEE = Money.valueOf(amountEE);
    this.amountER = Money.valueOf(amountER);
    this.ytdAmountEE = Money.valueOf(ytdAmountEE);
    this.ytdAmountER = Money.valueOf(ytdAmountER);
  }
}
