package com.everee.api.paystub.entity;

import com.everee.api.company.DetailedCompany;
import com.everee.api.model.DateRange;
import com.everee.api.money.Money;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.user.DetailedUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.net.URI;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;
import org.hibernate.annotations.Immutable;
import org.hibernate.type.LocalDateTimeType;
import org.hibernate.type.LocalDateType;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@Data
@Entity
@Immutable
@Accessors(chain = true)
@NamedNativeQuery(
    name = "PayStub.listPayStubPayments",
    query =
        "SELECT p.paydate, p.grossearnings, p.totaltaxesee, p.pretaxdeductions, p.posttaxdeductions, p.deferredcompensation, p.netearnings"
            + " FROM payment p"
            + " JOIN employee e ON p.employeeid = e.id"
            + " WHERE p.status = 'PAID' and e.userid = ?1 and p.companypayperiodid = ?2",
    resultSetMapping = "PayStubPayment")
@SqlResultSetMapping(
    name = "PayStubPayment",
    classes =
        @ConstructorResult(
            targetClass = PayStubPayment.class,
            columns = {
              @ColumnResult(name = "paydate"),
              @ColumnResult(name = "grossearnings"),
              @ColumnResult(name = "totaltaxesee"),
              @ColumnResult(name = "pretaxdeductions"),
              @ColumnResult(name = "posttaxdeductions"),
              @ColumnResult(name = "deferredcompensation"),
              @ColumnResult(name = "netearnings"),
            }))
@NamedNativeQuery(
    name = "PayStub.listPayStubEarnings",
    query =
        "SELECT t.type, "
            + "   coalesce(t.unitrate, t.effectivehourlypayrate) as unitrate, "
            + "   sum(t.amount) as amount, "
            + "   max(t.ytdamount) as ytdamount, "
            + "   sum(coalesce(t.unitcount, round(t.time / cast(3600 as numeric), 4))) as unitcount, "
            + "   string_agg(t.note, ', ') as note, "
            + "   m.name as approvalgroupname, "
            + "   r.name as workerrolename, "
            + "   w.name as worklocationname "
            + " FROM paymentearning t"
            + " JOIN payment p ON t.paymentid = p.id"
            + " JOIN employee e ON p.employeeid = e.id"
            + " LEFT OUTER JOIN approvalgroup m ON m.id=t.approvalgroupid"
            + " LEFT JOIN workerrole r ON r.id=t.workerroleid"
            + " LEFT JOIN worklocation w ON w.id=t.worklocationid"
            + " WHERE p.status = 'PAID' AND e.userid = ?1 AND p.companypayperiodid = ?2"
            + " GROUP BY t.type, coalesce(t.unitrate, t.effectivehourlypayrate), m.name, r.name, w.name",
    resultSetMapping = "PayStubEarning")
@SqlResultSetMapping(
    name = "PayStubEarning",
    classes =
        @ConstructorResult(
            targetClass = PayStubEarning.class,
            columns = {
              @ColumnResult(name = "type"),
              @ColumnResult(name = "unitrate"),
              @ColumnResult(name = "amount"),
              @ColumnResult(name = "ytdamount"),
              @ColumnResult(name = "unitcount"),
              @ColumnResult(name = "note"),
              @ColumnResult(name = "approvalgroupname"),
              @ColumnResult(name = "workerrolename"),
              @ColumnResult(name = "worklocationname"),
            }))
@NamedNativeQuery(
    name = "PayStub.listPayStubPtoAccruals",
    query =
        "with timeoffamounts as ("
            + " select"
            + "    t.timeofftype,"
            + "    t.hours as changesinperiod,"
            + "    ("
            + "      case"
            + "      when"
            + "        t.type = 'ACCRUAL'"
            + "        and t.date >= (select startdate from payperiod where id = ?3)"
            + "        and t.date <= (select enddate from payperiod where id = ?3)"
            + "      then t.hours"
            + "      else 0"
            + "      end"
            + " ) as accruedinperiod"
            + " from ptotransaction t"
            + " join employee e on e.id = t.employeeid"
            + " where"
            + "   e.userid = ?1"
            + "   and t.companyid = ?2"
            + "   and t.date <= (select enddate from payperiod where id = ?3)"
            + " )"
            + " select"
            + "   timeofftype,"
            + "   sum(accruedinperiod) as accruedhours,"
            + "   sum(changesinperiod) as currentbalancehours"
            + " from timeoffamounts"
            + " group by timeofftype",
    resultSetMapping = "PayStubPtoAccrual")
@SqlResultSetMapping(
    name = "PayStubPtoAccrual",
    classes =
        @ConstructorResult(
            targetClass = PayStubPtoAccrual.class,
            columns = {
              @ColumnResult(name = "timeofftype"),
              @ColumnResult(name = "accruedhours"),
              @ColumnResult(name = "currentbalancehours"),
            }))
@NamedNativeQuery(
    name = "PayStub.listPayStubEmployeeTaxes",
    query =
        "SELECT t.qualifiedtype, array_to_string(array_remove(array_agg(distinct tj.name), ''), ', ') as description, "
            + " sum(t.amount) AS amount, max(t.ytdamount) AS ytdamount, sum(t.subjectwages) AS subjectwages, max(t.ytdsubjectwages) AS ytdsubjectwages"
            + " FROM paymenttax t"
            + " JOIN payment p ON t.paymentid = p.id"
            + " JOIN employee e ON p.employeeid = e.id"
            + " LEFT OUTER JOIN taxjurisdiction tj on tj.id = t.qualifiedtype"
            + " WHERE p.status = 'PAID' AND t.payertype = 'EMPLOYEE' and e.userid = ?1 and p.companypayperiodid = ?2"
            + " GROUP BY t.qualifiedtype"
            + " ORDER BY t.qualifiedtype",
    resultSetMapping = "PayStubEmployeeTax")
@SqlResultSetMapping(
    name = "PayStubEmployeeTax",
    classes =
        @ConstructorResult(
            targetClass = PayStubEmployeeTax.class,
            columns = {
              @ColumnResult(name = "qualifiedtype"),
              @ColumnResult(name = "description"),
              @ColumnResult(name = "amount"),
              @ColumnResult(name = "ytdamount"),
              @ColumnResult(name = "subjectwages"),
              @ColumnResult(name = "ytdsubjectwages"),
            }))
@NamedNativeQuery(
    name = "PayStub.listPayStubContributionDeductions",
    query =
        "SELECT pcd.type, ecd.typeguid, ecd.name, pcd.ordinal, t.amountee, t.amounter, t.ytdamountee, t.ytdamounter"
            + " FROM"
            + "   (SELECT DISTINCT"
            + "     ecd.typeguid,"
            + "     first_value(pcd.id) OVER (PARTITION BY ecd.typeguid ORDER BY pcd.updatedat DESC) AS pcdid,"
            + "     sum(pcd.amountee) OVER (PARTITION BY ecd.typeguid) AS amountee,"
            + "     sum(pcd.amounter) OVER (PARTITION BY ecd.typeguid) AS amounter,"
            + "     first_value(pcd.ytdamountee) OVER (PARTITION BY ecd.typeguid ORDER BY p.approvedat DESC) AS ytdamountee,"
            + "     first_value(pcd.ytdamounter) OVER (PARTITION BY ecd.typeguid ORDER BY p.approvedat DESC) AS ytdamounter"
            + "     FROM paymentcontributiondeduction pcd"
            + "     JOIN employeecontributiondeduction ecd ON pcd.employeecontributiondeductionid = ecd.id"
            + "     JOIN payment p ON pcd.paymentid = p.id"
            + "     JOIN employee e ON p.employeeid = e.id"
            + "     WHERE"
            + "         p.status = 'PAID'"
            + "         AND e.userid = ?1"
            + "         AND p.companypayperiodid = ?2"
            + "         AND ecd.type <> 'OVERPAY_ADJUSTMENT'"
            + "     ) AS t"
            + " JOIN paymentcontributiondeduction pcd ON t.pcdid = pcd.id"
            + " JOIN employeecontributiondeduction ecd ON pcd.employeecontributiondeductionid = ecd.id",
    resultSetMapping = "PayStubContributionDeduction")
@SqlResultSetMapping(
    name = "PayStubContributionDeduction",
    classes =
        @ConstructorResult(
            targetClass = PayStubContributionDeduction.class,
            columns = {
              @ColumnResult(name = "type"),
              @ColumnResult(name = "typeguid"),
              @ColumnResult(name = "name"),
              @ColumnResult(name = "ordinal"),
              @ColumnResult(name = "amountee"),
              @ColumnResult(name = "amounter"),
              @ColumnResult(name = "ytdamountee"),
              @ColumnResult(name = "ytdamounter"),
            }))
// ACH (for deprecated paymentdeposit)
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.paymentDeposit",
    query =
        "SELECT "
            + "   p.payDate,"
            + "   pd.accountName,"
            + "   pd.accountNumber,"
            + "   pd.amount,"
            + "   COALESCE(STRING_AGG(DISTINCT(TRIM(pe.note)), ', '), '') as note,"
            + "   'ACH' as type"
            + " FROM paymentDeposit pd"
            + " JOIN payment p ON pd.paymentId = p.id"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " JOIN company c ON p.companyId = c.id"
            + " WHERE"
            + "   p.status = 'PAID'"
            + "   and c.distributionFlowType = 'DISABLED'"
            + "   and e.userid = ?1"
            + "   and p.companyPayPeriodId = ?2"
            + " GROUP BY p.id, pd.id",
    resultSetMapping = "PayStubDeposit")
// "employee has already received this payment"
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.alreadyReceivedPayment",
    query =
        "SELECT"
            + "   p.payDate,"
            + "   'Paid manually' AS accountName,"
            + "   '' AS accountNumber,"
            + "   pe.amount,"
            + "   TRIM(pe.note) as note,"
            + "   'CHECK' as type"
            + " FROM payment p"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " WHERE pe.paymentMethod = 'PREVIOUSLY_PAID' "
            + "    AND e.userid = ?1 "
            + "    AND p.companyPayPeriodId = ?2 "
            + "    AND p.status = 'PAID' "
            + " GROUP BY p.id, pe.id",
    resultSetMapping = "PayStubDeposit")
// ACH
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.ach.distributions",
    query =
        "SELECT"
            + "    pd.distributiondate as payDate,"
            + "    pd.accountName,"
            + "    pd.accountNumber,"
            + "    pd.amount,"
            + "    COALESCE(STRING_AGG(DISTINCT(TRIM(pe.note)), ', '), '') as note,"
            + "    pd.type"
            + " FROM paymentDistribution pd"
            + " JOIN payment p ON pd.paymentId = p.id"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " JOIN company c ON p.companyId = c.id"
            + " WHERE"
            + "    c.distributionFlowType <> 'DISABLED'"
            + "    AND p.status = 'PAID'"
            + "    AND pe.paymentMethod <> 'PREVIOUSLY_PAID'"
            + "    AND pd.amount > 0"
            + "    AND pd.type IN ('ACH')"
            + "    AND pd.status <> 'CREATED_NEW_DISTRIBUTION'"
            + "    AND e.userid = ?1"
            + "    AND p.companyPayPeriodId = ?2"
            + " GROUP BY p.id, pd.id",
    resultSetMapping = "PayStubDeposit")
// instant deposit
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.ptpc.admin.distributions",
    query =
        "SELECT"
            + "    pd.distributiondate as payDate,"
            + "    'Debit card' as accountName,"
            + "    pd.cardLast4 as accountNumber,"
            + "    pd.amount,"
            + "    COALESCE(STRING_AGG(DISTINCT(TRIM(pe.note)), ', '), '') as note,"
            + "    pd.type"
            + " FROM paymentDistribution pd"
            + " JOIN payment p ON pd.paymentId = p.id"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " JOIN company c ON p.companyId = c.id"
            + " WHERE"
            + "    c.distributionFlowType <> 'DISABLED'"
            + "    AND p.status = 'PAID'"
            + "    AND pe.paymentMethod <> 'PREVIOUSLY_PAID'"
            + "    AND pd.amount > 0"
            + "    AND pd.type IN ('PTPC')"
            + "    AND pd.distributionInitiatedByType = 'ADMIN'"
            + "    AND pd.status <> 'CREATED_NEW_DISTRIBUTION'"
            + "    AND e.userid = ?1"
            + "    AND p.companyPayPeriodId = ?2"
            + " GROUP BY p.id, pd.id",
    resultSetMapping = "PayStubDeposit")
// worker cash-outs
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.ptpc.worker.distributions",
    query =
        "SELECT"
            + "    min(pd.distributiondate) as payDate,"
            + "    'Debit card' as accountName,"
            + "    COALESCE(STRING_AGG(DISTINCT(TRIM(pd.cardLast4)), ', '), '') as accountNumber,"
            + "    sum(pd.amount) as amount,"
            + "    COALESCE(STRING_AGG(DISTINCT(TRIM(pe.note)), ', '), '') as note,"
            + "    min(pd.type) as type"
            + " FROM paymentDistribution pd"
            + " JOIN payment p ON pd.paymentId = p.id"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " JOIN company c ON p.companyId = c.id"
            + " WHERE"
            + "    c.distributionFlowType <> 'DISABLED'"
            + "    AND p.status = 'PAID'"
            + "    AND pe.paymentMethod <> 'PREVIOUSLY_PAID'"
            + "    AND pd.amount > 0"
            + "    AND pd.type IN ('PTPC')"
            + "    AND pd.distributionInitiatedByType = 'WORKER'"
            + "    AND pd.status <> 'CREATED_NEW_DISTRIBUTION'"
            + "    AND e.userid = ?1"
            + "    AND p.companyPayPeriodId = ?2"
            + " GROUP BY pd.companyDistributionId",
    resultSetMapping = "PayStubDeposit")
// Everee pay card
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.paycard.distributions",
    query =
        "SELECT"
            + "    min(pd.distributiondate) as payDate,"
            + "    'Everee pay card' as accountName,"
            + "    COALESCE(STRING_AGG(DISTINCT(TRIM(pd.cardLast4)), ', '), '') as accountNumber,"
            + "    sum(pd.amount) as amount,"
            + "    COALESCE(STRING_AGG(DISTINCT(TRIM(pe.note)), ', '), '') as note,"
            + "    min(pd.type) as type"
            + " FROM paymentDistribution pd"
            + " JOIN payment p ON pd.paymentId = p.id"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " JOIN company c ON p.companyId = c.id"
            + " WHERE"
            + "    c.distributionFlowType <> 'DISABLED'"
            + "    AND p.status = 'PAID'"
            + "    AND pe.paymentMethod <> 'PREVIOUSLY_PAID'"
            + "    AND pd.amount > 0"
            + "    AND pd.type IN ('PAY_CARD')"
            + "    AND pd.status <> 'CREATED_NEW_DISTRIBUTION'"
            + "    AND e.userid = ?1"
            + "    AND p.companyPayPeriodId = ?2"
            + " GROUP BY pd.companyDistributionId",
    resultSetMapping = "PayStubDeposit")
// Externally Paid
@NamedNativeQuery(
    name = "PayStub.listPayStubDeposits.externally.paid.distributions",
    query =
        "SELECT"
            + "    p.payDate as payDate,"
            + "    'Externally Paid' as accountName,"
            + "    '' as accountNumber,"
            + "    pe.amount,"
            + "    TRIM(pe.note) as note,"
            + "    'EXTERNAL' as type"
            + " FROM payment p"
            + " JOIN paymentEarning pe on pe.paymentId = p.id"
            + " JOIN employee e ON p.employeeId = e.id"
            + " WHERE"
            + "    p.status = 'PAID'"
            + "    AND pe.paymentMethod = 'EXTERNALLY_PAID'"
            + "    AND e.userid = ?1"
            + "    AND p.companyPayPeriodId = ?2"
            + " GROUP BY p.id, pe.id",
    resultSetMapping = "PayStubDeposit")
@SqlResultSetMapping(
    name = "PayStubDeposit",
    classes =
        @ConstructorResult(
            targetClass = PayStubDeposit.class,
            columns = {
              @ColumnResult(name = "payDate"),
              @ColumnResult(name = "accountName"),
              @ColumnResult(name = "accountNumber"),
              @ColumnResult(name = "amount"),
              @ColumnResult(name = "note"),
              @ColumnResult(name = "type"),
            }))
@NamedNativeQuery(
    name = "PayStub.time.adjustment",
    query =
        "select"
            + "    wsc.id as workedShiftCorrectionId,"
            + "    wsc.oldWorkedShiftId,"
            + "    oldShift.workLocationTimezone as oldWorkLocationTimezone,"
            + "    COALESCE(oldShift.overridePunchInAt, oldShift.punchInAt) as oldTimeIn,"
            + "    COALESCE(oldShift.overridePunchOutAt, oldShift.punchOutAt) as oldTimeOut,"
            + "    oldPayable.workLocationName as oldWorkLocationName,"
            + "    COALESCE(oldPayable.regHours, 0) as oldRegHours,"
            + "    COALESCE(oldPayable.overtimeHours, 0) as oldOvertimeHours,"
            + "    COALESCE(oldPayable.doubleTimeHours, 0) as oldDoubleTimeHours,"
            + "    COALESCE(oldPayable.regRate, 0) as oldRegRate,"
            + "    COALESCE(oldPayable.earningAmount, 0) as oldEarningAmount,"
            + "    wsc.newWorkedShiftId,"
            + "    newShift.workLocationTimezone as newWorkLocationTimezone,"
            + "    COALESCE(newShift.overridePunchInAt, newShift.punchInAt) as newTimeIn,"
            + "    COALESCE(newShift.overridePunchOutAt, newShift.punchOutAt) as newTimeOut,"
            + "    newPayable.workLocationName as newWorkLocationName,"
            + "    COALESCE(newPayable.regHours, 0) as newRegHours,"
            + "    COALESCE(newPayable.overtimeHours, 0) as newOvertimeHours,"
            + "    COALESCE(newPayable.doubleTimeHours, 0) as newDoubleTimeHours,"
            + "    COALESCE(newPayable.regRate, 0) as newRegRate,"
            + "    COALESCE(newPayable.earningAmount, 0) as newEarningAmount"
            + " from workedShiftCorrection wsc"
            + " join shift oldShift"
            + "    on wsc.oldWorkedShiftId = oldShift.id"
            + " left outer join lateral ("
            + "        select"
            + "            p.shiftId,"
            + "            max(workLocation.name) as workLocationName,"
            + "            max(case when p.earningType = 'REGULAR_HOURLY' then p.unitCount else 0 end) as regHours,"
            + "            max(case when p.earningType = 'OVERTIME_HOURLY' then p.unitCount else 0 end) as overtimeHours,"
            + "            max(case when p.earningType = 'DOUBLE_TIME_HOURLY' then p.unitCount else 0 end) as doubleTimeHours,"
            + "            max(case when p.earningType = 'REGULAR_HOURLY' then p.unitRate else 0 end) as regRate,"
            + "            sum(p.earningAmount) as earningAmount"
            + "        from payable p"
            + "        left outer join workLocation"
            + "            on workLocation.id = p.workLocationId"
            + "        join payment p2 on p2.id = p.paymentId"
            + "        where"
            + "            p.shiftId = oldShift.id"
            + "        group by p.shiftId"
            + "    ) as oldPayable"
            + "    on oldPayable.shiftId = oldShift.id"
            + " left outer join shift newShift"
            + "    on wsc.newWorkedShiftId = newShift.id"
            + " left outer join lateral ("
            + "        select"
            + "            p.shiftid,"
            + "            max(workLocation.name) as workLocationName,"
            + "            max(case when p.earningType = 'REGULAR_HOURLY' then p.unitCount else 0 end) as regHours,"
            + "            max(case when p.earningType = 'OVERTIME_HOURLY' then p.unitCount else 0 end) as overtimeHours,"
            + "            max(case when p.earningType = 'DOUBLE_TIME_HOURLY' then p.unitCount else 0 end) as doubleTimeHours,"
            + "            max(case when p.earningType = 'REGULAR_HOURLY' then p.unitRate else 0 end) as regRate,"
            + "            sum(p.earningAmount) as earningAmount"
            + "        from payable p"
            + "        left outer join workLocation"
            + "            on workLocation.id = p.workLocationId"
            + "        join payment p2 on p2.id = p.paymentId"
            + "        where"
            + "            p.shiftId = newShift.id"
            + "        group by p.shiftId"
            + "    ) as newPayable"
            + "    on newPayable.shiftId = newShift.id"
            + " where"
            + "    wsc.id in ("
            + "        select"
            + "            wsc.id"
            + "        from employeeContributionDeduction ecd"
            + "        join payable p on p.employeeContributionDeductionId = ecd.id"
            + "        join shift s on s.id = p.shiftId"
            + "        join workedShiftCorrection wsc on wsc.oldWorkedShiftId = s.id"
            + "        where"
            + "            ecd.type = 'OVERPAY_ADJUSTMENT'"
            + "            and wsc.newWorkedShiftId is null"
            + "            and ecd.employeeId = ?1"
            + "            and ecd.createdAt >= ?2"
            + "            and ecd.createdAt <  cast(?3 as date) + interval '1 day'"
            + "        union"
            + "        select"
            + "            wsc.id"
            + "        from payable p"
            + "        join shift s on s.id = p.shiftId"
            + "        join workedShiftCorrection wsc on wsc.newWorkedShiftId = s.id"
            + "        join payment p2 on p2.id = p.paymentId"
            + "        where"
            + "            p2.status = 'PAID'"
            + "            and p2.employeeId = ?1"
            + "            and p2.companyPayPeriodId = ?4"
            + "    )",
    resultSetMapping = "PayStubTimeAdjustment")
@SqlResultSetMapping(
    name = "PayStubTimeAdjustment",
    classes =
        @ConstructorResult(
            targetClass = PayStubTimeAdjustment.class,
            columns = {
              @ColumnResult(name = "workedShiftCorrectionId"),
              @ColumnResult(name = "oldWorkedShiftId"),
              @ColumnResult(name = "oldWorkLocationTimezone"),
              @ColumnResult(name = "oldTimeIn"),
              @ColumnResult(name = "oldTimeOut"),
              @ColumnResult(name = "oldWorkLocationName"),
              @ColumnResult(name = "oldRegHours"),
              @ColumnResult(name = "oldOvertimeHours"),
              @ColumnResult(name = "oldDoubleTimeHours"),
              @ColumnResult(name = "oldRegRate"),
              @ColumnResult(name = "oldEarningAmount"),
              @ColumnResult(name = "newWorkedShiftId"),
              @ColumnResult(name = "newWorkLocationTimezone"),
              @ColumnResult(name = "newTimeIn"),
              @ColumnResult(name = "newTimeOut"),
              @ColumnResult(name = "newWorkLocationName"),
              @ColumnResult(name = "newRegHours"),
              @ColumnResult(name = "newOvertimeHours"),
              @ColumnResult(name = "newDoubleTimeHours"),
              @ColumnResult(name = "newRegRate"),
              @ColumnResult(name = "newEarningAmount"),
            }))
@NamedNativeQuery(
    name = "PayStub.overpayments",
    query =
        "SELECT "
            + "    sum(case when adjustmentDuringThisPeriod=true then earningsDifference else 0.00 end) as currentPayPeriodAmount, "
            + "    sum(case when adjustmentDuringThisPeriod=false and deletedShift then oldgrossearnings-earningsrecoveredinpreviousperiod when adjustmentduringthisperiod=false then least(earningsDifference, abs(earningsRecoveredInPreviousPeriod-newGrossEarnings)) else 0.00 end) as previousPayPeriodAmount, "
            + "    greatest(abs(SUM(totalEarningsRecovered)-SUM(earningsRecoveredInPreviousPeriod)-SUM(newGrossEarnings)), 0.00) amountrepaidinperiod, "
            + "    SUM(oldGrossEarnings-earningsRecoveredInPeriod-earningsRecoveredInPreviousPeriod) as totalBalance "
            + "FROM ( "
            + "    select "
            + "        oldshiftid, "
            + "        adjustmentDuringThisPeriod, "
            + "        deletedShift, "
            + "        sum(oldGrossEarnings) as oldGrossEarnings, "
            + "        sum(newGrossEarnings) as newGrossEarnings, "
            + "        sum(repaidEarnings) as totalEarningsRecovered, "
            + "        SUM(earningsRecoveredInPeriod) as earningsRecoveredInPeriod, "
            + "        SUM(earningsRecoveredInPreviousPeriod) as earningsRecoveredInPreviousPeriod, "
            + "        SUM(oldGrossEarnings-newGrossEarnings) as earningsDifference "
            + "    FROM( "
            + "        SELECT "
            + "          pcd.employeeContributionDeductionId, "
            + "          case when ecd.startdate between ?2 and ?3 then true else false end as adjustmentDuringThisPeriod, "
            + "          pb.shiftId as oldshiftid, "
            + "          case when wc.newWorkedShiftId is null then true else false end as deletedShift, "
            + "          MAX(coalesce(correction.earningAmount, 0.00)) as newGrossEarnings, "
            + "          MAX(ecd.amountEE) as oldGrossEarnings, "
            + "          SUM(case when p.fordate <= ?3 then pcd.amountee ELSE 0.00 END) as repaidEarnings, "
            + "          SUM(case when p.fordate >= ?2 then pcd.amountee ELSE 0.00 END) as earningsRecoveredInPeriod, "
            + "          SUM(case when p.fordate < ?2 then pcd.amountee ELSE 0.00 END) as earningsRecoveredInPreviousPeriod "
            + "        FROM "
            + "          EmployeeContributionDeduction ecd "
            + "        JOIN Payable pb "
            + "            ON pb.employeeContributionDeductionId=ecd.id AND pb.earningType=ecd.earningType "
            + "        JOIN WorkedShiftCorrection wc "
            + "            ON wc.oldWorkedShiftId=pb.shiftId "
            + "        LEFT JOIN Payable correction "
            + "            ON wc.newWorkedShiftId=correction.shiftId "
            + "        LEFT JOIN PaymentContributionDeduction pcd "
            + "            ON pcd.employeeContributionDeductionId=ecd.id "
            + "        LEFT JOIN Payment p "
            + "            ON p.id=pcd.paymentId AND p.status='PAID' AND p.forDate <= ?3 "
            + "        WHERE "
            + "          ecd.type in ('OVERPAY_ADJUSTMENT') "
            + "          AND ecd.startDate <= ?3 "
            + "          AND (ecd.endDate is null OR ecd.endDate >= ?2) "
            + "          AND ecd.employeeId = ?1 "
            + "        group by "
            + "          pcd.employeecontributiondeductionid, "
            + "          ecd.startdate, "
            + "          pb.shiftid, "
            + "          wc.newWorkedShiftId "
            + "    ) adjustments "
            + "    group by oldshiftid, adjustmentDuringThisPeriod, deletedShift "
            + ") alladjustments "
            + "WHERE "
            + "    earningsDifference > 0",
    resultSetMapping = "PayStubOverpayment")
@SqlResultSetMapping(
    name = "PayStubOverpayment",
    classes =
        @ConstructorResult(
            targetClass = PayStubOverpayment.class,
            columns = {
              @ColumnResult(name = "currentpayperiodamount"),
              @ColumnResult(name = "previouspayperiodamount"),
              @ColumnResult(name = "amountrepaidinperiod"),
              @ColumnResult(name = "totalbalance"),
            }))
@NamedNativeQuery(
    name = "PayStub.listOriginalShiftsPaidInPeriod",
    query =
        "SELECT"
            + " s.id shiftId,"
            + " TIMEZONE(s.workLocationTimezone, COALESCE(s.overrideStartAtInclusive, s.startAtInclusive)) workLocationStartAtInclusive,"
            + " TIMEZONE(s.workLocationTimezone, COALESCE(s.overrideEndAtExclusive, s.endAtExclusive)) workLocationEndAtExclusive,"
            + " (CASE "
            + "    WHEN CAST(TIMEZONE(s.workLocationTimezone, COALESCE(s.overrideEndAtExclusive, s.endAtExclusive)) AS time) >= c.workdaystart "
            + "    THEN CAST(TIMEZONE(s.workLocationTimezone, COALESCE(s.overrideEndAtExclusive, s.endAtExclusive)) AS date) "
            + "    ELSE CAST(TIMEZONE(s.workLocationTimezone, COALESCE(s.overrideEndAtExclusive, s.endAtExclusive)) AS date) - 1 "
            + " END) workLocationEndDate,"
            + " s.hourlyPayRate"
            + " FROM shift s"
            + " JOIN payment p ON s.paymentid = p.id AND p.status = 'PAID' AND p.companypayperiodid = ?2"
            + " JOIN company c ON p.companyid = c.id"
            + " WHERE s.correctionforshiftid is null AND s.employeeid = ?1",
    resultSetMapping = "PayStubOriginalShift")
@SqlResultSetMapping(
    name = "PayStubOriginalShift",
    classes =
        @ConstructorResult(
            targetClass = PayStubOriginalShift.class,
            columns = {
              @ColumnResult(name = "shiftId", type = Long.class),
              @ColumnResult(name = "workLocationStartAtInclusive", type = LocalDateTimeType.class),
              @ColumnResult(name = "workLocationEndAtExclusive", type = LocalDateTimeType.class),
              @ColumnResult(name = "workLocationEndDate", type = LocalDateType.class),
              @ColumnResult(name = "hourlyPayRate")
            }))
public class PayStub {

  @Id private String id;

  private LocalDate date;

  private Integer year;

  private Integer month;

  private Long employeeId;

  @Formula("(select e.integrationid from employee e where e.id = employeeid)")
  @Setter(AccessLevel.NONE)
  private String workerId;

  @Formula("(select e.externalworkerid from employee e where e.id = employeeid)")
  @Setter(AccessLevel.NONE)
  private String externalWorkerId;

  private Long companyId;

  @JsonIgnore private Long userId;

  @JsonIgnore private Long companyPayPeriodId;
  private Money grossEarnings;
  private Money ytdGrossEarnings;
  private Money totalTaxesEe;
  private Money preTaxDeductions;
  private Money ytdPreTaxDeductions;
  private Money postTaxDeductions;
  private Money ytdPostTaxDeductions;
  private Money deferredCompensation;
  private Money ytdDeferredCompensation;
  private Money netEarnings;
  private Money ytdNetEarnings;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "userId", insertable = false, updatable = false)
  private DetailedUser user;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "companyId", insertable = false, updatable = false)
  private DetailedCompany company;

  @ManyToOne
  @JsonIgnore
  @JoinColumn(name = "companyPayPeriodId", insertable = false, updatable = false)
  private PayPeriod companyPayPeriod;

  @Transient private List<PayStubPayment> payments = new ArrayList<>();
  @Transient private List<PayStubPtoAccrual> payStubPtoAccruals = new ArrayList<>();
  @Transient private List<PayStubEarning> earnings = new ArrayList<>();
  @Transient private List<PayStubEmployeeTax> employeeTaxes = new ArrayList<>();
  @Transient private List<PayStubContributionDeduction> contributionDeductions = new ArrayList<>();
  @Transient private List<PayStubDeposit> deposits = new ArrayList<>();
  @Transient private PayStubOverpayment overpayment;
  @Transient private List<PayStubTimeAdjustment> payStubTimeAdjustments = new ArrayList<>();
  @Transient private List<PayStubTimeDetail> timeDetails = new ArrayList<>();

  @SuppressWarnings("unused")
  public URI getDownloadUrl() {
    return ServletUriComponentsBuilder.fromCurrentRequestUri()
        .replacePath("/api/v2/pay-stubs/download")
        .replaceQuery(null)
        .replaceQueryParam("date", date)
        .build()
        .toUri();
  }

  @JsonIgnore
  public Money getYtdTotalTaxesEe() {
    return employeeTaxes.stream()
        .map(PayStubEmployeeTax::getYtdAmount)
        .reduce(Money.ZERO, Money::plus);
  }

  public LocalDate getPayPeriodStartDate() {
    return Optional.ofNullable(companyPayPeriod).map(PayPeriod::getStartDate).orElse(date);
  }

  public LocalDate getPayPeriodEndDate() {
    return Optional.ofNullable(companyPayPeriod).map(PayPeriod::getEndDate).orElse(date);
  }

  public String getLocalizedDateRange() {
    return Optional.ofNullable(companyPayPeriod)
        .map(PayPeriod::getLocalizedDateRange)
        .orElseGet(
            () ->
                DateRange.of(date, date).getLocalizedDescription(LocaleContextHolder.getLocale()));
  }
}
