package com.everee.api.paystub.entity;

import com.everee.api.earnings.EarningType;
import com.everee.api.money.Money;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.Value;

@Value
@RequiredArgsConstructor
public class PayStubEarning {
  EarningType type;
  Money unitRate;
  Money amount;
  Money ytdAmount;
  BigDecimal unitCount;
  String note;
  String approvalGroupName;
  String workerRoleName;
  String workLocationName;

  // JPA-specific constructor
  @SuppressWarnings("unused")
  public PayStubEarning(
      String type,
      BigDecimal unitRate,
      BigDecimal amount,
      BigDecimal ytdAmount,
      BigDecimal unitCount,
      String note,
      String approvalGroupName,
      String workerRoleName,
      String workLocationName) {
    this.type = EarningType.valueOf(type);
    this.unitRate = Optional.ofNullable(unitRate).map(Money::valueOf).orElse(null);
    this.amount = Money.valueOf(amount);
    this.ytdAmount = Money.valueOf(ytdAmount);
    this.unitCount = unitCount;
    this.note = note;
    this.approvalGroupName = approvalGroupName;
    this.workerRoleName = workerRoleName;
    this.workLocationName = workLocationName;
  }
}
