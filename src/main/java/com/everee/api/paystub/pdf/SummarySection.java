package com.everee.api.paystub.pdf;

import com.everee.api.paystub.entity.PayStub;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Div;
import com.itextpdf.layout.element.LineSeparator;

public class SummarySection extends Div {

  SummarySection(PayStub stub) {
    setKeepTogether(true);
    add(new SummarySectionContainer(stub).setBorder(Border.NO_BORDER));

    add(new LineSeparator(new SolidLine()).setMarginTop(10));
  }
}
