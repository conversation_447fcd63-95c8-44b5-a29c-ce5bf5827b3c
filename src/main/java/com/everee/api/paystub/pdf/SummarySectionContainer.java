package com.everee.api.paystub.pdf;

import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.brandCellStyle;
import static com.itextpdf.layout.property.UnitValue.createPercentArray;

import com.everee.api.paystub.entity.PayStub;
import com.everee.api.pdf.brandcomponents.BrandSectionHeader;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;

class SummarySectionContainer extends Table {

  SummarySectionContainer(PayStub stub) {
    super(createPercentArray(new float[] {1, 1}), true);
    setFixedLayout();
    addPaySummaryCell(stub);
    addStatementSummaryCell(stub);
  }

  private void addPaySummaryCell(PayStub stub) {
    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .add(new BrandSectionHeader("Pay Summary"))
            .addStyle(brandCellStyle().setPaddingRight(12f))
            .add(new PaySummaryTable(stub)));
  }

  private void addStatementSummaryCell(PayStub stub) {
    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .add(new BrandSectionHeader("Statement Summary"))
            .addStyle(brandCellStyle().setPaddingRight(12f))
            .add(new StatementSummaryTable(stub)));
  }
}
