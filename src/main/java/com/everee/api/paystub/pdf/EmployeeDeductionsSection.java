package com.everee.api.paystub.pdf;

import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.*;

import com.everee.api.employee.contributiondeduction.ContributionDeductionType;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.money.Money;
import com.everee.api.paystub.entity.PayStubContributionDeduction;
import com.everee.api.pdf.brandcomponents.BrandSectionHeader;
import com.everee.api.pdf.brandcomponents.FormatParagraph;
import com.itextpdf.layout.element.Div;
import java.util.Collection;
import java.util.Set;
import lombok.Getter;

class EmployeeDeductionsSection extends Div implements Section {
  private final Set<ContributionDeductionType> preTaxBenefitTypes =
      Set.of(
          ContributionDeductionType.SECTION_125_BENEFIT,
          ContributionDeductionType.DEFERRED_COMPENSATION);

  @Getter private final boolean visible;

  EmployeeDeductionsSection(Collection<PayStubContributionDeduction> items) {
    visible =
        items.stream()
            .map(PayStubContributionDeduction::getYtdAmountEE)
            .reduce(Money.ZERO, Money::sum)
            .isPlus();

    setKeepTogether(true);
    add(new BrandSectionHeader("Deductions"));
    add(new EmployeeDeductionsSectionTable(items));

    var anyPreTaxDeductions =
        items.stream()
            .map(PayStubContributionDeduction::getType)
            .map(EmployeeContributionDeductionType::getType)
            .anyMatch(preTaxBenefitTypes::contains);

    if (anyPreTaxDeductions) {
      add(new FormatParagraph("* Pre-tax deductions").addStyle(brandCellStyle()).setItalic());
    }
  }
}
