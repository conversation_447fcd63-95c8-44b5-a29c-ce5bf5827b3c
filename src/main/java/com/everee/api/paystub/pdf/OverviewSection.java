package com.everee.api.paystub.pdf;

import com.everee.api.paystub.entity.PayStub;
import com.everee.api.pdf.brandcomponents.BrandDocumentHeader;
import com.itextpdf.layout.element.Div;
import java.time.LocalDate;

class OverviewSection extends Div {

  OverviewSection(PayStub stub, LocalDate companyLocalDate) {
    setKeepTogether(true);
    add(
        new BrandDocumentHeader("Statement of Earnings")
            .setMarginBottom(
                stub.getCompanyPayPeriod().getEndDate().isAfter(companyLocalDate) ? 30 : 5));
    add(new OverviewSectionTable(stub));
  }
}
