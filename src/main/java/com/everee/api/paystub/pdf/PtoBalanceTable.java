package com.everee.api.paystub.pdf;

import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.brandCellStyle;
import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.brandHeaderCellStyle;
import static com.itextpdf.layout.property.UnitValue.createPercentArray;

import com.everee.api.paystub.entity.PayStubPtoAccrual;
import com.everee.api.pdf.brandcomponents.FormatParagraph;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;
import java.util.Collection;

class PtoBalanceTable extends Table {

  PtoBalanceTable(Collection<PayStubPtoAccrual> payStubPtoAccruals) {
    super(createPercentArray(new float[] {3.5f, 1, 1}), true);
    setFixedLayout();
    addHeaderCells();
    addDataCells(payStubPtoAccruals);
  }

  private void addHeaderCells() {
    addHeaderCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .addStyle(brandHeaderCellStyle())
            .add(new Paragraph("Type".toUpperCase())));

    addHeaderCell(
        new Cell()
            .setTextAlignment(TextAlignment.RIGHT)
            .addStyle(brandHeaderCellStyle())
            .add(new Paragraph("Accrued".toUpperCase())));

    addHeaderCell(
        new Cell()
            .setTextAlignment(TextAlignment.RIGHT)
            .addStyle(brandHeaderCellStyle())
            .add(new Paragraph("Balance".toUpperCase())));
  }

  private void addDataCells(Collection<PayStubPtoAccrual> payStubPtoAccruals) {
    payStubPtoAccruals.forEach(this::addDataCells);
  }

  private void addDataCells(PayStubPtoAccrual payStubPtoAccrual) {
    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .addStyle(brandCellStyle())
            .add(new FormatParagraph(payStubPtoAccrual.getTimeOffType().getLocalizedTitle())));

    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.RIGHT)
            .addStyle(brandCellStyle())
            .add(new FormatParagraph(payStubPtoAccrual.getAccruedHours().toString())));

    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.RIGHT)
            .addStyle(brandCellStyle())
            .add(new FormatParagraph(payStubPtoAccrual.getCurrentBalanceHours().toString())));
  }
}
