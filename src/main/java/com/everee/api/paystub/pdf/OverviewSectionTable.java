package com.everee.api.paystub.pdf;

import static com.everee.api.pdf.brandcomponents.BrandFontFactory.brandBoldFont;
import static com.everee.api.pdf.brandcomponents.BrandStyleFactory.brandCellStyle;
import static com.itextpdf.layout.property.UnitValue.createPercentArray;
import static java.lang.String.join;

import com.everee.api.company.CompanyAddress;
import com.everee.api.employee.Employee;
import com.everee.api.paystub.entity.PayStub;
import com.everee.api.pdf.brandcomponents.FormatParagraph;
import com.everee.api.tax.state.State;
import com.everee.api.user.address.HomeAddress;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;

class OverviewSectionTable extends Table {

  OverviewSectionTable(PayStub stub) {
    super(createPercentArray(new float[] {3, 3, 2}), true);
    setFixedLayout();
    addCompanyInfoCell(stub);
    addEmployeeInfoCell(stub);
    addPayPeriodInfoCell(stub);
  }

  private void addCompanyInfoCell(PayStub stub) {
    var company = stub.getCompany();
    var address = company.getActiveAddress(stub.getDate());
    var line1 = address.map(CompanyAddress::getLine1).orElse("");
    var line2 = address.map(CompanyAddress::getLine2).orElse("");
    var city = address.map(CompanyAddress::getCity).orElse("");
    var state = address.map(CompanyAddress::getState).map(State::name).orElse("");
    var zip = address.map(CompanyAddress::getPostalCode).orElse("");

    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .addStyle(brandCellStyle())
            .add(new FormatParagraph(company.getDisplayName()).setFont(brandBoldFont()))
            .add(new FormatParagraph(join(" ", line1, line2)))
            .add(
                new FormatParagraph(
                    join(" ", join(address.isPresent() ? ", " : "", city, state), zip)))
            .add(new FormatParagraph("FEIN:", company.getFederalEin())));
  }

  private void addEmployeeInfoCell(PayStub stub) {
    var user = stub.getUser();
    var accountLabel =
        user.getEmployeeAtCompany(stub.getCompanyId())
            .map(Employee::getFullAccountLabel)
            .orElse(user.getDisplayFullName());

    var address = user.findActiveHomeAddress(stub.getDate());
    var line1 = address.map(HomeAddress::getLine1).orElse("");
    var line2 = address.map(HomeAddress::getLine2).orElse("");
    var city = address.map(HomeAddress::getCity).orElse("");
    var state = address.map(HomeAddress::getState).map(State::name).orElse("");
    var zip = address.map(HomeAddress::getPostalCode).orElse("");

    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .addStyle(brandCellStyle())
            .add(new FormatParagraph(accountLabel).setFont(brandBoldFont()))
            .add(new FormatParagraph(join(" ", line1, line2)))
            .add(new FormatParagraph(join(" ", join(", ", city, state), zip)))
            .add(new FormatParagraph("TIN:", user.getMaskedTaxpayerIdentifier())));
  }

  private void addPayPeriodInfoCell(PayStub stub) {
    addCell(
        new Cell()
            .setTextAlignment(TextAlignment.LEFT)
            .addStyle(brandCellStyle())
            .add(new FormatParagraph("Statement Date:", stub.getDate()).setFont(brandBoldFont()))
            .add(new FormatParagraph("Period Start:", stub.getCompanyPayPeriod().getStartDate()))
            .add(new FormatParagraph("Period End:", stub.getCompanyPayPeriod().getEndDate())));
  }
}
