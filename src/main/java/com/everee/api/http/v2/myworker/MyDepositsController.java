package com.everee.api.http.v2.myworker;

import com.everee.api.user.UserService;
import com.everee.api.user.bankaccount.UserBankAccountService;
import com.everee.api.worker.paramconverters.WorkerDirectDepositParamsConverter;
import com.everee.api.worker.params.DirectDepositRuleParams;
import com.everee.api.worker.payload.DirectDepositRulePayload;
import javax.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/me/direct-deposit")
@RequiredArgsConstructor
public class MyDepositsController implements IMyController {
  @Getter private final UserService userService;
  private final UserBankAccountService userBankAccountService;

  @GetMapping("/{bankAccountId}")
  public DirectDepositRulePayload getMyDirectDeposit(@PathVariable Long bankAccountId) {
    var employee = getAuthenticatedEmployee();
    var user = employee.getUser();

    var bankAccount = userBankAccountService.getBankAccount(bankAccountId);
    if (!bankAccount.getUserId().equals(user.getId())) {
      throw new AccessDeniedException("Access is denied");
    }

    return DirectDepositRulePayload.from(bankAccount);
  }

  @PostMapping
  public DirectDepositRulePayload createMyDirectDeposit(
      @Valid @RequestBody DirectDepositRuleParams params) {
    var employee = getAuthenticatedEmployee();
    var user = employee.getUser();

    var bankAccount = WorkerDirectDepositParamsConverter.from(user, params);
    bankAccount = userBankAccountService.addBankAccount(bankAccount);

    return DirectDepositRulePayload.from(bankAccount);
  }

  @PutMapping("/{bankAccountId}")
  public DirectDepositRulePayload updateMyDirectDeposit(
      @PathVariable Long bankAccountId, @Valid @RequestBody DirectDepositRuleParams params) {
    var employee = getAuthenticatedEmployee();
    var user = employee.getUser();

    var bankAccount = userBankAccountService.getBankAccount(bankAccountId);
    if (!bankAccount.getUserId().equals(user.getId())) {
      throw new AccessDeniedException("Access is denied");
    }

    WorkerDirectDepositParamsConverter.copyFrom(bankAccount, params);
    bankAccount = userBankAccountService.updateBankAccount(bankAccount);

    return DirectDepositRulePayload.from(bankAccount);
  }

  @DeleteMapping("/{bankAccountId}")
  public void deleteMyDirectDeposit(@PathVariable Long bankAccountId) {
    var employee = getAuthenticatedEmployee();
    var user = employee.getUser();

    var bankAccount = userBankAccountService.getBankAccount(bankAccountId);
    if (!bankAccount.getUserId().equals(user.getId())) {
      throw new AccessDeniedException("Access is denied");
    }

    userBankAccountService.removeBankAccount(bankAccountId);
  }
}
