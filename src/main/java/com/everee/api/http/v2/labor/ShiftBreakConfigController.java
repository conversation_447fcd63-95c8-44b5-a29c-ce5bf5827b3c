package com.everee.api.http.v2.labor;

import com.everee.api.auth.annotation.AccountOwnerAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.labor.shiftsegmentconfig.ShiftSegmentConfigLookup;
import com.everee.api.labor.shiftsegmentconfig.ShiftSegmentConfigLookupService;
import com.everee.api.labor.shiftsegmentconfig.ShiftSegmentConfigService;
import com.everee.api.labor.shiftsegmentconfig.entity.ShiftSegmentConfig;
import com.everee.api.labor.shiftsegmentconfig.param.ShiftSegmentConfigParamsForCreate;
import com.everee.api.labor.shiftsegmentconfig.param.ShiftSegmentConfigParamsForUpdate;
import java.util.Set;
import javax.validation.Valid;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@AccountOwnerAccess
@RequiredArgsConstructor
@RequestMapping("/api/v2/labor/shift-break-config")
public class ShiftBreakConfigController {
  private final ShiftSegmentConfigService segmentConfigService;
  private final ShiftSegmentConfigLookupService configLookupService;

  @GetMapping
  public Page<ShiftSegmentConfig> listSegmentConfigs(
      @Valid ShiftSegmentConfigLookup lookup, Pageable pageable) {
    return configLookupService.listAll(lookup, pageable);
  }

  @PostMapping
  public ShiftSegmentConfig createSegmentConfig(
      @Valid @RequestBody ShiftSegmentConfigParamsForCreate createParams) {
    var company = CompanyService.getAuthenticatedCompany();

    return segmentConfigService.createSegmentConfig(company, createParams);
  }

  @PutMapping("/{configId}")
  public ShiftSegmentConfig updateSegmentConfig(
      @PathVariable @NonNull Long configId,
      @Valid @RequestBody ShiftSegmentConfigParamsForUpdate updateParams) {
    var segmentConfig =
        configLookupService.findOneOrThrow(new ShiftSegmentConfigLookup().setIds(Set.of(configId)));

    return segmentConfigService.updateSegmentConfig(segmentConfig, updateParams);
  }

  @DeleteMapping("/{configId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteSegmentConfig(@PathVariable @NonNull Long configId) {
    var segmentConfig =
        configLookupService.findOneOrThrow(new ShiftSegmentConfigLookup().setIds(Set.of(configId)));

    segmentConfigService.deleteOrArchiveSegmentConfig(segmentConfig);
  }
}
