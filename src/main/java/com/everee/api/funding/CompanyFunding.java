package com.everee.api.funding;

import com.everee.api.distribution.DistributionFundingApprovalInformation;
import com.everee.api.fundingdistribution.batch.KYCPrecheckResults;
import com.everee.api.model.BaseModelV2;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentFundingType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDate;
import java.time.LocalDateTime;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;

@Entity
@Data
@Accessors(chain = true)
public class CompanyFunding extends BaseModelV2<CompanyFunding> {
  @NotNull private Long companyId;
  @NotNull private LocalDate fundingDate;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PaymentFundingType type;

  @NotNull private Money amount = Money.ZERO;

  @NotNull
  @Enumerated(EnumType.STRING)
  private CompanyFundingStatus status = CompanyFundingStatus.STARTED;

  @Embedded private CompanyFundingConfig fundingConfig = new CompanyFundingConfig();

  private Long createdByUserId;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Getter(AccessLevel.NONE)
  @Formula(
      "(select b.updatedat from bankaccountbalance b where b.companyid = companyid order by b.updatedat desc limit 1)")
  private LocalDateTime accountBalanceLastUpdated;

  @JsonIgnore
  @Setter(AccessLevel.NONE)
  @Getter(AccessLevel.NONE)
  @Formula(
      "(select b.availablebalance from bankaccountbalance b where b.companyid = companyid order by b.updatedat desc limit 1)")
  private Money accountBalance;

  @Embedded
  private DistributionFundingApprovalInformation approvalInformation =
      new DistributionFundingApprovalInformation();

  private String errorMessage;

  public KYCPrecheckResults getKycPrecheckResults() {
    var kycPrecheckResults = new KYCPrecheckResults();
    kycPrecheckResults.setAvailableBalance(accountBalance);
    kycPrecheckResults.setAvailableBalanceLastUpdated(accountBalanceLastUpdated);
    return kycPrecheckResults.forAmount(
        CompanyFundingStatus.FINALIZED.contains(status) ? Money.ZERO : amount);
  }

  private Long payPeriodId;
}
