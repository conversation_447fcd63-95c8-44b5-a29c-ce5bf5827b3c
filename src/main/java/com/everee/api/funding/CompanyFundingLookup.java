package com.everee.api.funding;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import com.everee.api.payment.PaymentFundingType;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@RequestParams
public class CompanyFundingLookup implements Lookup {
  @ApiParam(name = "company-id")
  private Long companyId;

  @ApiParam(name = "id", allowMultiple = true)
  private Set<Long> ids;

  @ApiParam(name = "date")
  private LocalDate date;

  @ApiParam(name = "min-date")
  private LocalDate minDateInclusive;

  @ApiParam(name = "max-date")
  private LocalDate maxDateInclusive;

  @ApiParam(name = "ach-file-id", allowMultiple = true)
  private Set<Long> achFileIds;

  @ApiParam(name = "status", allowMultiple = true)
  private Set<CompanyFundingStatus> statuses;

  @ApiParam(name = "type", allowMultiple = true)
  private Set<PaymentFundingType> types;

  @ApiParam(name = "pay-period-id", allowMultiple = true)
  private Set<Long> payPeriodIds;

  @ApiParam(name = "transfer-type", allowMultiple = true)
  private Set<CompanyFundingTransferType> transferTypes;

  @Override
  public Set<Long> getCompanyIds() {
    return companyId != null ? Set.of(companyId) : Set.of();
  }
}
