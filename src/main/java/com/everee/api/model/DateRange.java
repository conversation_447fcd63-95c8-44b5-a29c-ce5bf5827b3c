package com.everee.api.model;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor(staticName = "of")
public class DateRange {

  @NonNull private final LocalDate startDate;

  @NonNull private final LocalDate endDate;

  public String getLocalizedDescription(@NonNull Locale locale) {
    return getLocalizedDescription(locale, "MMM", "d", "yyyy");
  }

  public String getLocalizedDescription(
      @NonNull Locale locale, @NonNull String month, @NonNull String day, @NonNull String year) {
    var startFormat = (String) null;
    var startMonth = startDate.getMonthValue();
    var startYear = startDate.getYear();
    var endFormat = (String) null;
    var endMonth = endDate.getMonthValue();
    var endYear = endDate.getYear();

    if (startDate.isEqual(endDate)) {
      startFormat = String.format("%s %s, %s", month, day, year);
    } else if (startMonth == endMonth && startYear == endYear) {
      startFormat = String.format("%s %s", month, day);
      endFormat = String.format("%s, %s", day, year);
    } else if (startYear == endYear) {
      startFormat = String.format("%s %s", month, day);
      endFormat = String.format("%s %s, %s", month, day, year);
    } else {
      startFormat = String.format("%s %s, %s", month, day, year);
      endFormat = String.format("%s %s, %s", month, day, year);
    }

    if (endFormat == null) {
      return DateTimeFormatter.ofPattern(startFormat, locale).format(startDate);
    } else {
      var startValue = DateTimeFormatter.ofPattern(startFormat, locale).format(startDate);
      var endValue = DateTimeFormatter.ofPattern(endFormat, locale).format(endDate);
      return String.join(" - ", startValue, endValue);
    }
  }

  public String getDateRangeInfo(@NonNull Locale locale) {
    if (startDate.isEqual(endDate)) {
      return DateTimeFormatter.ofPattern("YYYYMMdd", locale).format(startDate);
    } else {
      return DateTimeFormatter.ofPattern("YYYYMMdd", locale).format(startDate)
          + "-"
          + DateTimeFormatter.ofPattern("YYYYMMdd", locale).format(endDate);
    }
  }

  public boolean contains(LocalDate date) {
    return !startDate.isAfter(date) && !endDate.isBefore(date);
  }
}
