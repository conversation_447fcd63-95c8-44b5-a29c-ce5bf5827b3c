package com.everee.api.service;

import java.net.URI;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
@RequiredArgsConstructor
public abstract class SlackNotifierService {
  private final RestTemplate restTemplate = new RestTemplate();

  public abstract boolean isEnabled();

  public abstract String getWebHookUrl();

  public void notify(String message) {
    if (!isEnabled()) {
      return;
    }

    try {
      final RequestEntity<SlackMessageRequest> entity =
          RequestEntity.post(URI.create(getWebHookUrl()))
              .contentType(MediaType.APPLICATION_JSON)
              .body(new SlackMessageRequest(message));

      restTemplate.exchange(entity, String.class);
    } catch (RestClientException e) {
      log.error("Error sending FinOps Slack webhook request: ", e);
    }
  }
}
