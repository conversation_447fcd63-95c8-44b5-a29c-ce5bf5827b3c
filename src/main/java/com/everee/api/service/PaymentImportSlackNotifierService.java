package com.everee.api.service;

import com.everee.api.payment.imports.PaymentImport;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentImportSlackNotifierService extends SlackNotifierService {

  @Value("${app.notifications.slack.paymentimport.enabled}")
  @Setter
  private boolean paymentImportEnabled;

  @Value("${app.notifications.slack.paymentimport.webhook-url}")
  @Setter
  private String paymentImportWebhookUrl;

  @Override
  public boolean isEnabled() {
    return paymentImportEnabled;
  }

  @Override
  public String getWebHookUrl() {
    return paymentImportWebhookUrl;
  }

  public void notify(PaymentImport paymentImport) {
    notify(
        "HISTORICAL IMPORT"
            + "\n    Company: "
            + paymentImport.getCompany().getDisplayName()
            + "\n    Total Records: "
            + paymentImport.getTotalCount()
            + "\n    Imported Records: "
            + paymentImport.getImportedCount()
            + "\n    Errored Records: "
            + paymentImport.getErroredCount()
            + "\n    Skipped Records: "
            + paymentImport.getSkippedCount()
            + "\n    Source File: "
            + paymentImport.getRequestFileUrl()
            + "\n    Result File: "
            + paymentImport.getResponseFileUrl());
  }
}
