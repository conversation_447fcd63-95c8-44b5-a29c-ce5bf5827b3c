package com.everee.api.timeoff.balance;

import com.everee.api.model.BaseModel;
import com.everee.api.timeoff.types.TimeOffType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class PtoBalanceAdjustment extends BaseModel {

  @NotNull private Long employeeId;

  @NotNull private Long policyId;

  @NotNull private LocalDate date;

  @NotNull
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  private BigDecimal hours;

  @NotNull
  @Enumerated(EnumType.STRING)
  private TimeOffType type;

  @CreatedBy private Long createdByUserId;

  @LastModifiedBy private Long updatedByUserId;
}
