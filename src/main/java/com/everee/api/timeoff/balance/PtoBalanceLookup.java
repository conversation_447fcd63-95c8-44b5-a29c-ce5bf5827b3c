package com.everee.api.timeoff.balance;

import com.everee.api.lookup.Lookup;
import com.everee.api.timeoff.types.TimeOffType;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@With
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PtoBalanceLookup implements Lookup {

  private Set<Long> companyIds;

  private Set<Long> employeeIds;

  private Set<TimeOffType> timeOffTypes;

  private Boolean accrued;

  private Boolean unlimited;
}
