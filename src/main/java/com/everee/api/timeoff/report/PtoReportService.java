package com.everee.api.timeoff.report;

import com.everee.api.approvalgroup.ApprovalGroup;
import com.everee.api.company.DetailedCompany;
import com.everee.api.companyemployeeimport.ExcelService;
import com.everee.api.companyemployeeimport.ExcelService.ExcelMutator;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.employeePosition.EmployeePositionLookupService;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.storage.StorageAccess;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.timeoff.balance.PtoBalanceLookup;
import com.everee.api.timeoff.balance.PtoBalanceLookupService;
import com.everee.api.timeoff.policy.PtoPolicyCoveredEmployeeLookup;
import com.everee.api.timeoff.policy.PtoPolicyCoveredEmployeeLookupService;
import com.everee.api.timeoff.policy.PtoPolicyType;
import com.everee.api.timeoff.transaction.PtoTransactionRepository;
import com.everee.api.util.FileNameUtils;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocation;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocationLookup;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocationLookupService;
import com.google.common.util.concurrent.AtomicDouble;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class PtoReportService {
  private final ExcelService excelService;
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final PtoBalanceLookupService ptoBalanceLookupService;
  private final PtoPolicyCoveredEmployeeLookupService ptoPolicyCoveredEmployeeLookupService;
  private final PtoTransactionRepository ptoTransactionRepository;
  private final StorageService storageService;
  private final MessageSource messageSource;
  private final EmployeePositionLookupService employeePositionLookupService;
  private final WorkerLegalWorkLocationLookupService legalWorkLocationLookupService;

  @Value("${app.r365.integration.pto.report}")
  protected boolean PTO_REPORT_REPLACE_WORK_TO_EMPLOYEE;

  @Transactional(readOnly = true)
  public StoredFileLink generatePtoBalanceHistoryReport(
      DetailedCompany company, LocalDate startDate, LocalDate endDate) throws IOException {
    var companyLocalNowDate = CompanyLocalTimeService.companyLocalNowDate(company);
    var itemStream =
        ptoTransactionRepository.streamPtoTransactionExportItems(
            company.getId(), companyLocalNowDate, startDate, endDate);

    var col = new AtomicInteger(0);
    var row = new AtomicInteger(0);
    var mutator = excelService.create();
    mutator.sheet("PTO Balance History Report");

    List<String> headers = new ArrayList<>();
    headers.addAll(
        List.of(
            "Full name",
            "Last name",
            "First name",
            "Date",
            "Event type",
            "Hours balance change",
            "Policy type",
            getMessage("pto.report.payrollEmployeeID")));
    if (!PTO_REPORT_REPLACE_WORK_TO_EMPLOYEE) {
      addHeader(headers, "External worker ID");
      addHeader(headers, "Everee ID");
    }
    headers.forEach((colName) -> setExcelCell(mutator, row, col, colName));

    col.set(0);
    row.getAndIncrement();

    itemStream.forEach(
        item -> {
          setExcelCell(mutator, row, col, item.getWorkerDisplayFullName());
          setExcelCell(mutator, row, col, item.getWorkerLastName());
          setExcelCell(mutator, row, col, item.getWorkerFirstName());
          setExcelCell(mutator, row, col, item.getDate().toString());
          setExcelCell(mutator, row, col, localize(item.getType().getLocalizedTitle()));
          setExcelCell(mutator, row, col, item.getHours().toPlainString());
          setExcelCell(mutator, row, col, localize(item.getTimeOffType().getLocalizedTitle()));
          setExcelCell(mutator, row, col, item.getWorkerId());
          if (!PTO_REPORT_REPLACE_WORK_TO_EMPLOYEE) {
            setExcelCell(mutator, row, col, item.getExternalWorkerId());
            setExcelCell(mutator, row, col, item.getEmployeeId().toString());
          }

          row.getAndIncrement();
          col.set(0);
        });

    itemStream.close();

    var outputStream = new ByteArrayOutputStream();
    mutator.writeTo(outputStream);

    var path = List.of("pto-balance-history-report", company.getId().toString());
    var safeTitle = FileNameUtils.fileNameSafeString("PTO_Balance_History_Report");
    var companyName = FileNameUtils.fileNameSafeString(company.getDisplayName());
    var fileName = String.join("_", safeTitle, companyName, companyLocalNowDate.toString());

    return storageService.storeForImmediateDownload(
        outputStream.toByteArray(), fileName + ".xlsx", StorageAccess.CUSTOMER_FACING, path);
  }

  public StoredFileLink generatePtoLiabilityReport(DetailedCompany company) throws IOException {
    var companyId = company.getId();
    ExcelMutator mutator = excelService.create();
    mutator.sheet("PTO Liability Report");
    var byteArrayOutputStream = new ByteArrayOutputStream();
    var balanceLookup = new PtoBalanceLookup().withCompanyIds(Set.of(companyId));
    var ptoBalances = ptoBalanceLookupService.listAll(balanceLookup, Pageable.unpaged());

    AtomicDouble totalAccruedAndUnusedHours = new AtomicDouble(0);
    AtomicDouble totalAccruedPtoValue = new AtomicDouble(0);
    AtomicInteger maxCols = new AtomicInteger();
    AtomicInteger col = new AtomicInteger();
    AtomicInteger row = new AtomicInteger();

    Stream.of(
            "Name",
            "Pay type",
            "Current approval group",
            "Current team",
            "Current work location",
            "Termination Date",
            "Eligible for accrual",
            "Accrual rate",
            "Annual salary (effective)",
            "Hourly rate (effective)",
            "PTO value per hour",
            "Accrued and unused hours",
            "Total liability")
        .forEach((colName) -> setExcelCell(mutator, row, col, colName));

    maxCols.set(col.decrementAndGet());
    col.set(0);
    row.getAndIncrement();

    ptoBalances.forEach(
        (ptoBalance) -> {
          var employee = detailedEmployeeLookupService.getEmployee(ptoBalance.getEmployeeId());
          var isHourly = employee.getPayType() == PayType.HOURLY;
          var coverageLookup =
              new PtoPolicyCoveredEmployeeLookup()
                  .withEmployeeIds(Set.of(employee.getId()))
                  .withCompanyIds(Set.of(companyId))
                  .withPhaseLookup(new PhaseLookup(Phase.ACTIVE, LocalDate.now()));
          var coveredEmployee = ptoPolicyCoveredEmployeeLookupService.findOne(coverageLookup);
          var accruesPto =
              coveredEmployee.isPresent()
                  && coveredEmployee.get().getPolicyType() == PtoPolicyType.FLEXIBLE;

          var accrualRate = "—";
          if (accruesPto) {
            var accrual = coveredEmployee.get().getEffectivePtoAccrualRate();
            accrualRate =
                String.format(
                    "%s hrs per 40 hrs worked",
                    accrual.getHoursEarnedForHoursWorked(new BigDecimal(40)));
          }

          var workLocation =
              legalWorkLocationLookupService
                  .findOne(
                      new WorkerLegalWorkLocationLookup()
                          .setEmployeeId(employee.getId())
                          .setPhaseLookup(new PhaseLookup(Phase.ACTIVE, company.getNowDate())))
                  .map(WorkerLegalWorkLocation::getName)
                  .map(
                      name ->
                          name.getLocalizedValue(messageSource, LocaleContextHolder.getLocale()))
                  .orElse("-");

          var position =
              employeePositionLookupService.findEmployeePosition(
                  employee.getId(), LocalDate.now(), Phase.ACTIVE);
          var salary = position.map(EmployeePosition::getAnnualSalary).orElse(Money.ZERO);
          var hourlyRate = position.map(EmployeePosition::getHourlyRate).orElse(Money.ZERO);

          var availableHours = ptoBalance.getAvailableHours();
          var liabilityExpense = hourlyRate.times(availableHours.doubleValue());
          var approvalGroupName =
              Optional.ofNullable(employee.getApprovalGroup())
                  .map(ApprovalGroup::getName)
                  .orElse("-");

          totalAccruedAndUnusedHours.getAndAdd(availableHours.doubleValue());
          totalAccruedPtoValue.getAndAdd(liabilityExpense.getAmount().doubleValue());

          setExcelCell(mutator, row, col, employee.getDisplayFullName()); // Name
          setExcelCell(mutator, row, col, isHourly ? "Hourly" : "Salary"); // Pay Type
          setExcelCell(mutator, row, col, approvalGroupName); // Approval group
          setExcelCell(mutator, row, col, approvalGroupName); // Team (deprecated)
          setExcelCell(mutator, row, col, workLocation); // Work Location
          setExcelCell(
              mutator,
              row,
              col,
              employee.getEndDate() == null
                  ? ""
                  : employee.getEndDate().toString()); // Termination Date
          setExcelCell(mutator, row, col, accruesPto ? "Yes" : "No"); // Eligible to accrue PTO
          setExcelCell(mutator, row, col, accrualRate); // Accrual rate
          setExcelCell(mutator, row, col, salary.toDisplayString()); // Annual comp (salary)
          setExcelCell(mutator, row, col, hourlyRate.toDisplayString()); // Hourly rate
          setExcelCell(mutator, row, col, hourlyRate.toDisplayString()); // pto hourly rate value
          setExcelCell(mutator, row, col, availableHours.toString()); // accrued and unused hours
          setExcelCell(mutator, row, col, liabilityExpense.toDisplayString()); // accrued pto value

          row.getAndIncrement();
          col.set(0);
        });

    // Set totals
    col.set(maxCols.getAndDecrement());
    setTotalValues(
        mutator, row, col, Money.valueOf(totalAccruedPtoValue.toString()).toDisplayString());
    setTotalValues(mutator, row, col, totalAccruedAndUnusedHours.toString());

    mutator.writeTo(byteArrayOutputStream);

    var path = List.of("pto-liability-report", Long.toString(companyId));

    var safeTitle = FileNameUtils.fileNameSafeString("PTO-Liability-Report");
    var companyName = FileNameUtils.fileNameSafeString(company.getDisplayName());
    var companyLocalNowDate = CompanyLocalTimeService.companyLocalNowDate(company);

    var fileName =
        String.join(
            "_", safeTitle, companyName, companyLocalNowDate.format(DateTimeFormatter.ISO_DATE));

    return storageService.storeForImmediateDownload(
        byteArrayOutputStream.toByteArray(),
        fileName + ".xlsx",
        StorageAccess.CUSTOMER_FACING,
        path);
  }

  private void setExcelCell(
      ExcelMutator mutator, AtomicInteger row, AtomicInteger col, String value) {
    mutator.cell(row.get(), col.getAndIncrement()).set(value);
  }

  private void setTotalValues(
      ExcelMutator mutator, AtomicInteger row, AtomicInteger col, String value) {
    mutator.cell(row.get(), col.getAndDecrement()).set(value);
  }

  private String localize(@NonNull LocalizedString localizedString) {
    return localizedString.getLocalizedValue(messageSource, LocaleContextHolder.getLocale());
  }

  private String getMessage(String code, Object... args) {
    return messageSource.getMessage(code, args, Locale.ENGLISH);
  }

  public void addHeader(List<String> headers, String name) {
    headers.add(name);
  }
}
