package com.everee.api.timeoff.request;

import com.everee.api.employee.Employee;
import com.everee.api.event.ResourceEvent;

public class TimeOffRequestChangedEvent extends ResourceEvent {
  public TimeOffRequestChangedEvent(Long companyId, Employee request) {
    super(companyId, request);
  }

  public Employee getEmployee() {
    return (Employee) getSource();
  }

  @Override
  public String getResourceId() {
    return getEmployee().getId().toString();
  }

  @Override
  public String getResourceName() {
    return "employee";
  }

  @Override
  public String getActionName() {
    return "changed";
  }
}
