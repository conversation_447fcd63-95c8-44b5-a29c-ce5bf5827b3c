package com.everee.api.timeoff.request;

import com.everee.api.approvalgroup.ApprovalGroupApprover;
import com.everee.api.approvalgroup.ApprovalGroupApproverRepository;
import com.everee.api.approvalgroup.ApprovalScope;
import com.everee.api.company.CompanyService;
import com.everee.api.company.CoreCompanyRepository;
import com.everee.api.company.DetailedCompany;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.employee.Employee;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.storage.StorageService;
import com.everee.api.timeoff.policy.PtoPolicyCoveredEmployeeRepository;
import com.everee.api.timeoff.request.exception.TimeOffRequestAlreadyRejectedException;
import com.everee.api.user.DetailedUser;
import com.everee.api.user.DetailedUserRepository;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import javax.transaction.Transactional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TimeOffRequestService {

  private final TimeOffRequestRepository timeOffRequestRepository;
  private final TimeOffRequestDayAmountRepository timeOffRequestDayAmountRepository;
  private final TimeOffRequestBuilder timeOffRequestBuilder;
  private final TimeOffRequestValidator timeOffRequestValidator;
  private final PtoPolicyCoveredEmployeeRepository ptoPolicyCoveredEmployeeRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final DetailedUserRepository detailedUserRepository;
  private final NotificationService notificationService;
  private final MessageSource messageSource;
  private final ApprovalGroupApproverRepository approvalGroupApproverRepository;
  private final CoreCompanyRepository coreCompanyRepository;
  private final PartnerRepository partnerRepository;
  private final StorageService storageService;

  @Transactional
  public boolean canRequestTimeOffForEmployee(
      @NonNull DetailedCompany company, @NonNull Employee employee) {
    if (!company.isTimeOffRequestsEnabled()) return false;

    var companyNowDate = company.getNowDate();

    return ptoPolicyCoveredEmployeeRepository
        .findByEmployeeIdAndDate(employee.getId(), companyNowDate)
        .isPresent();
  }

  @Transactional
  public TimeOffRequest createRequest(TimeOffRequestParams params) {
    timeOffRequestValidator.validateParamsForCreate(params);
    return createRequestFromParams(params, false);
  }

  @Transactional
  public TimeOffRequest updateAndApproveRequest(Long requestId, TimeOffRequestParams params) {
    var request = getRequest(requestId);
    timeOffRequestValidator.validateParamsForUpdate(request, params);

    if (timeOffRequestValidator.isFullyMutable(request)) {
      return replaceRequestWithParams(request, params);
    }

    timeOffRequestValidator.validatePartiallyMutableForUpdate(request, params);
    return replaceRequestWithParams(request, params);
  }

  @Transactional
  public TimeOffRequest rejectRequest(Long companyId, Long requestId) {
    return timeOffRequestRepository
        .findByIdAndCompanyId(requestId, companyId)
        .map(this::validateMutable)
        .map(this::markRejected)
        .map(timeOffRequestRepository::saveAndRefresh)
        .map(this::recalculatePayments)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Transactional
  public TimeOffRequest dismissRequest(Long companyId, Long requestId) {
    return timeOffRequestRepository
        .findByIdAndCompanyId(requestId, companyId)
        .map(this::markRejected)
        .map(timeOffRequestRepository::saveAndRefresh)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Transactional
  public TimeOffRequest deleteRequest(TimeOffRequest timeOffRequest) {
    return Optional.of(timeOffRequest)
        .map(this::validateMutable)
        .map(this::delete)
        .map(this::recalculatePayments)
        .orElse(new TimeOffRequest());
  }

  @Transactional
  public TimeOffRequest getRequest(Long requestId) {
    return timeOffRequestRepository
        .findByIdAndCompanyId(requestId, CompanyService.getAuthenticatedCompanyId())
        .orElseThrow(ResourceNotFoundException::new);
  }

  private void markApproved(TimeOffRequest request) {
    if (request.isRejected()) throw new TimeOffRequestAlreadyRejectedException();
    request.setStatus(TimeOffRequestStatus.APPROVED);
  }

  private TimeOffRequest markRejected(TimeOffRequest request) {
    request.setStatus(TimeOffRequestStatus.REJECTED);
    return request;
  }

  private TimeOffRequest validateMutable(TimeOffRequest request) {
    timeOffRequestValidator.validateFullyMutable(request);

    return request;
  }

  private TimeOffRequest recalculatePayments(TimeOffRequest request) {
    eventPublisher.publishEvent(
        new TimeOffRequestChangedEvent(request.getCompanyId(), request.getEmployee()));

    return request;
  }

  private TimeOffRequest delete(TimeOffRequest request) {
    timeOffRequestRepository.delete(request);
    return request;
  }

  /**
   * This method creates and saves a request from the given params, then recalculates any payments
   * related the the employee. No validation is performed within this method. --callan 2020-05-19
   */
  private TimeOffRequest createRequestFromParams(
      TimeOffRequestParams params, boolean approveRequest) {
    var request = timeOffRequestBuilder.build(params);
    if (approveRequest) {
      markApproved(request);
    }
    request = timeOffRequestRepository.save(request);
    var newRequestId = request.getId();
    request.getDayAmounts().forEach(d -> d.setRequestId(newRequestId));
    timeOffRequestDayAmountRepository.saveAll(request.getDayAmounts());
    request = timeOffRequestRepository.saveAndRefresh(request);
    recalculatePayments(request);
    sendNotifications(request);
    return request;
  }

  /**
   * This method replaces the given request with a new one created from the given params. After
   * deleting the existing request, the params are validated to no create an overlapping request.
   * --callan 2020-05-19
   */
  private TimeOffRequest replaceRequestWithParams(
      TimeOffRequest request, TimeOffRequestParams params) {
    timeOffRequestRepository.delete(request);
    timeOffRequestValidator.checkForOverlappingRequests(params);
    return createRequestFromParams(params, true);
  }

  private void sendNotifications(@NonNull TimeOffRequest request) {
    var companyId = request.getCompanyId();
    var partner = partnerRepository.findByCompanyId(companyId).orElseThrow();
    var requesterName = request.getEmployeeDisplayFullName();
    var hoursRequested = request.getPtoHours();
    var startDate = request.getStartDate();
    var endDate = request.getEndDate();
    var notification =
        new TimeOffRequestAdminNotification(
            partner,
            companyId,
            requesterName,
            hoursRequested,
            startDate,
            endDate,
            messageSource,
            storageService);

    getRecipients(request)
        .forEach(userId -> notificationService.enqueueUserNotification(notification, userId));
  }

  private Set<Long> getRecipients(@NonNull TimeOffRequest request) {
    var recipientUserIds = new HashSet<Long>();

    // Add approvers for group if they exist
    if (request.getApprovalGroupId() != null) {
      approvalGroupApproverRepository
          .findByCompanyIdAndApprovalGroupIdAndNotSuspended(
              request.getCompanyId(), request.getApprovalGroupId(), Pageable.unpaged())
          .filter(approver -> approver.hasApprovalScope(ApprovalScope.TIME_OFF))
          .map(ApprovalGroupApprover::getUserId)
          .forEach(recipientUserIds::add);
    }

    // If no approvers exist, add admins for company
    if (recipientUserIds.isEmpty()) {
      // Add admins if no approval group
      detailedUserRepository
          .streamAllByCompanyRole(request.getCompanyId(), CompanyRoleType.FINANCIAL_MANAGER)
          .map(DetailedUser::getId)
          .forEach(recipientUserIds::add);
    }

    return recipientUserIds;
  }

  @Transactional
  public void expireTimeOffRequests() {
    timeOffRequestRepository.expireTimeOffRequests();
  }
}
