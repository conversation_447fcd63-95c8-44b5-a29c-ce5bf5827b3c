package com.everee.api.timeoff.request;

import com.everee.api.approvalgroup.ApprovalScope;
import com.everee.api.lookup.Lookup;
import com.everee.api.phase.PhaseLookup;
import java.util.Set;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class TimeOffRequestLookup implements Lookup<TimeOffRequest> {
  private Set<Long> ids;
  private Set<Long> companyIds;
  private Set<Long> employeeIds;
  private Set<Long> approvalGroupIds;
  private boolean onlyMyPto;
  private Set<TimeOffRequestStatus> statuses;
  private PhaseLookup phaseLookup;
  private ApprovalScope approvalScope = ApprovalScope.TIME_OFF;
  private boolean skipApplyAuthorization;
}
