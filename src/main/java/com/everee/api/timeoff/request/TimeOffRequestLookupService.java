package com.everee.api.timeoff.request;

import static com.everee.api.query.where.Where.property;

import com.everee.api.auth.tenant.TenantSecurityContext;
import com.everee.api.auth.util.AuthorizationCheckService;
import com.everee.api.employee.CoreEmployee;
import com.everee.api.lookup.LookupService;
import com.everee.api.phase.PhaseQuery;
import com.everee.api.timeoff.request.exception.TimeOffRequestAccessDeniedException;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class TimeOffRequestLookupService
    implements LookupService<TimeOffRequest, TimeOffRequestLookup, PhaseQuery<TimeOffRequest>> {
  private final EntityManager entityManager;
  private final AuthorizationCheckService authorizationCheckService;

  @Override
  public void configureQuery(TimeOffRequestLookup lookup, PhaseQuery<TimeOffRequest> query) {
    query
        .byPhase(lookup.getPhaseLookup())
        .where(property(TimeOffRequest_.STATUS).in(lookup.getStatuses()))
        .where(property(TimeOffRequest_.ID).in(lookup.getIds()))
        .where(property(TimeOffRequest_.EMPLOYEE_ID).in(lookup.getEmployeeIds()))
        .where(property(TimeOffRequest_.APPROVAL_GROUP_ID).in(lookup.getApprovalGroupIds()));
  }

  @Override
  public PhaseQuery<TimeOffRequest> createQuery() {
    return new PhaseQuery<>(entityManager, TimeOffRequest.class);
  }

  @Override
  public Consumer<TenantSecurityContext> applyAuthorization(@NonNull TimeOffRequestLookup lookup) {
    return (@NonNull TenantSecurityContext securityContext) -> {
      if (lookup.isSkipApplyAuthorization()) {
        return;
      }

      if (authorizationCheckService.isEvereeAdmin()) {
        return;
      }

      var authenticatedCompanyId = AuthorizationCheckService.getCompanyIdOrThrow(securityContext);

      var authorizedEmployeeIds =
          AuthorizationCheckService.getUserOrThrow(securityContext).getEmployees().stream()
              .filter(ee -> ee.getCompanyId().equals(authenticatedCompanyId))
              .map(CoreEmployee::getId)
              .collect(Collectors.toSet());

      // Workers can see their own pto
      if (lookup.isOnlyMyPto()) {
        lookup.setEmployeeIds(authorizedEmployeeIds);
        return;
      }

      if (authorizationCheckService.isFinancialManager(authenticatedCompanyId)) {
        // Admins can see all employee's shifts
        return;
      }

      var authorizedApprovalGroupIds =
          Optional.ofNullable(lookup.getApprovalScope())
              .map(authorizationCheckService::getAuthorizedApprovalGroupIds)
              .orElseGet(authorizationCheckService::getAuthorizedApprovalGroupIds);
      if (authorizedApprovalGroupIds.size() > 0) {
        // People Managers can filter by employeeId but are limited within approvalGroup authority
        lookup.setApprovalGroupIds(authorizedApprovalGroupIds);
        return;
      }

      if (!authorizedEmployeeIds.isEmpty()) {
        if (CollectionUtils.isEmpty(lookup.getEmployeeIds())) {
          // If requested ids are empty then return all authorized employee ids
          lookup.setEmployeeIds(authorizedEmployeeIds);
          return;
        } else if (authorizedEmployeeIds.containsAll(lookup.getEmployeeIds())) {
          return;
        }
      }

      throw new TimeOffRequestAccessDeniedException();
    };
  }
}
