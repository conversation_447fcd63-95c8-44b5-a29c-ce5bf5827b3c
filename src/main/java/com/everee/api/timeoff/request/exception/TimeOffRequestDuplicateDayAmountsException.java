package com.everee.api.timeoff.request.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class TimeOffRequestDuplicateDayAmountsException extends LocalizedRuntimeException {
  public TimeOffRequestDuplicateDayAmountsException() {
    super("timeoff.TimeOffRequestDuplicateDayAmountsException.message");
  }
}
