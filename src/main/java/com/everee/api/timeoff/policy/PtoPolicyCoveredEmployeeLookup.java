package com.everee.api.timeoff.policy;

import com.everee.api.lookup.Lookup;
import com.everee.api.phase.PhaseLookup;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PtoPolicyCoveredEmployeeLookup implements Lookup {
  private Set<Long> policyIds;
  private Set<Long> companyIds;
  private Set<Long> employeeIds;
  private Set<PtoPolicyType> policyTypes;
  private PhaseLookup phaseLookup;
}
