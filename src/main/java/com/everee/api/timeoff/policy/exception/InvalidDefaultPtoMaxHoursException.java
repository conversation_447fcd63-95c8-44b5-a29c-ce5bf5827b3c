package com.everee.api.timeoff.policy.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class InvalidDefaultPtoMaxHoursException extends LocalizedRuntimeException {
  public InvalidDefaultPtoMaxHoursException() {
    super("timeoff.policy.InvalidDefaultPtoMaxHoursException.message");
  }
}
