package com.everee.api.timeoff.policy;

import static com.everee.api.util.SetUtils.toSetOrNull;

import com.everee.api.auth.annotation.AnyApproverAccess;
import com.everee.api.company.CompanyService;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.timeoff.tenure.TenureDetails;
import com.everee.api.timeoff.tenure.TenureDetailsService;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/pto-policy-covered-employees")
@RequiredArgsConstructor
public class PtoPolicyCoveredEmployeeController {

  private final PtoPolicyCoveredEmployeeLookupService lookupService;
  private final PtoPolicyService ptoPolicyService;
  private final CompanyLocalTimeService companyLocalTimeService;
  private final TenureDetailsService tenureDetailsService;

  @GetMapping
  @AnyApproverAccess
  public Page<PtoPolicyCoveredEmployee> listPtoPolicyCoveredEmployees(
      @RequestParam(name = "policy-id", required = false) Long policyId,
      @RequestParam(name = "employee-id", required = false) Long employeeId,
      PhaseLookup phaseLookup,
      Pageable pageable) {
    var lookup =
        new PtoPolicyCoveredEmployeeLookup()
            .withPhaseLookup(phaseLookup)
            .withPolicyIds(toSetOrNull(policyId))
            .withEmployeeIds(toSetOrNull(employeeId));
    return lookupService.listAll(lookup, pageable);
  }

  @GetMapping("/me")
  public PtoPolicyCoveredEmployee getMyPtoPolicyCoveredEmployee() {
    var employeeId = DetailedEmployeeLookupService.getAuthenticatedActiveEmployeeId();
    var policy =
        ptoPolicyService
            .findPtoPolicy(
                companyLocalTimeService.companyLocalNowDate(
                    CompanyService.getAuthenticatedCompanyId()),
                Phase.ACTIVE,
                CompanyService.getAuthenticatedCompanyId())
            .orElseThrow();
    var lookup =
        new PtoPolicyCoveredEmployeeLookup()
            .withEmployeeIds(Set.of(employeeId))
            .withPolicyIds(Set.of(policy.getId()))
            .withPhaseLookup(PhaseLookup.unphased());
    return lookupService.findOneOrThrow(lookup);
  }

  @GetMapping("/tenure-details")
  @AnyApproverAccess
  public TenureDetails getTenureDetails(
      @RequestParam(name = "policy-id", required = true) long policyId,
      @RequestParam(name = "worker-id", required = true) String workerId) {
    return tenureDetailsService.getTenureDetails(policyId, workerId);
  }
}
