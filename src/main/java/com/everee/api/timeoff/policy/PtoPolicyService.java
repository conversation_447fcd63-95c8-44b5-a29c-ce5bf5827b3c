package com.everee.api.timeoff.policy;

import static com.everee.api.query.where.Where.property;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.phase.PhaseQuery;
import com.everee.api.timeoff.policy.exception.InvalidDefaultAccrualRateException;
import com.everee.api.timeoff.policy.exception.InvalidDefaultPtoMaxHoursException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PtoPolicyService {

  private final EntityManager entityManager;
  private final PtoPolicyRepository ptoPolicyRepository;

  public Page<PtoPolicy> listPtoPolicies(
      @NotNull Pageable pageable, @NotNull PhaseLookup phaseLookup, Long companyId) {
    return new PhaseQuery<>(entityManager, PtoPolicy.class)
        .byPhase(phaseLookup)
        .where(property(PtoPolicy_.COMPANY_ID).equal(companyId))
        .findAll(pageable);
  }

  public Optional<PtoPolicy> findPtoPolicy(LocalDate referenceDate, Phase phase, Long companyId) {
    return new PhaseQuery<>(entityManager, PtoPolicy.class)
        .byPhase(new PhaseLookup(List.of(phase), referenceDate))
        .where(property(PtoPolicy_.COMPANY_ID).equal(companyId))
        .findOne();
  }

  public PtoPolicy getPtoPolicy(Long ptoPolicyId) {
    return ptoPolicyRepository
        .findById(ptoPolicyId)
        .orElseThrow(() -> new ResourceNotFoundException("PTO policy not found."));
  }

  private PtoPolicy addPtoTenureRules(
      PtoPolicy ptoPolicy, List<PtoTenureRuleParamsForCreate> tenures) {

    if (tenures != null) {
      tenures.stream()
          .map(
              (tenure) -> {
                var rule = new PtoTenureRule();
                rule.setPtoPolicy(ptoPolicy);
                rule.setAccrualRateIncrease(tenure.getAccrualRateIncrease());
                rule.setMaxHoursIncrease(tenure.getMaxHoursIncrease());
                rule.setYears(tenure.getYears());
                return rule;
              })
          .forEach(ptoPolicy.getPtoTenureRules()::add);
    }

    return ptoPolicy;
  }

  @Transactional
  public PtoPolicy createPtoPolicy(PtoPolicyParamsForCreate params, Long companyId) {
    var policyType = params.getType();
    var defaultAccrualRate = params.getDefaultAccrualRate();
    var defaultPtoMaxHours = params.getDefaultMaxHours();

    if (policyType == PtoPolicyType.FLEXIBLE) {
      if (defaultAccrualRate == null) {
        throw new InvalidDefaultAccrualRateException();
      }

      if (defaultPtoMaxHours == null || defaultPtoMaxHours.compareTo(BigDecimal.ZERO) < 0) {
        throw new InvalidDefaultPtoMaxHoursException();
      }
    }

    var ptoPolicy = new PtoPolicy();
    ptoPolicy.setCompanyId(companyId);
    ptoPolicy.setSalaryEligible(true);
    ptoPolicy.setStartDate(params.getStartDate());
    ptoPolicy.setDefaultAccrualRate(params.getDefaultAccrualRate());
    ptoPolicy.setDefaultPtoMaxHours(params.getDefaultMaxHours());
    ptoPolicy.setType(policyType);
    ptoPolicy.setAccrualPolicy(params.getAccrualPolicy());
    ptoPolicy.setWaitingPeriod(
        params.getWaitingPeriod() == null ? Integer.valueOf(0) : params.getWaitingPeriod());
    addPtoTenureRules(ptoPolicy, params.getPtoTenureRules());

    switch (params.getEligibilityRuleType()) {
      case ALL:
        ptoPolicy.setFullTimeHourlyEligible(true);
        ptoPolicy.setPartTimeHourlyEligible(true);
        break;
      case SALARY:
        ptoPolicy.setFullTimeHourlyEligible(false);
        ptoPolicy.setPartTimeHourlyEligible(false);
        break;
      case SALARY_AND_FULL_TIME_HOURLY:
        ptoPolicy.setFullTimeHourlyEligible(true);
        ptoPolicy.setPartTimeHourlyEligible(false);
        break;
      default:
        throw new IllegalArgumentException("Unknown PTO policy eligibility rule type.");
    }

    return updatePtoPolicyRecord(ptoPolicy, companyId);
  }

  @Transactional
  public PtoPolicy updatePtoPolicyRecord(PtoPolicy ptoPolicy, Long companyId) {

    findPtoPolicy(LocalDate.now(), Phase.UPCOMING, companyId)
        .ifPresent((ptoPolicyRepository::delete));

    findPtoPolicy(LocalDate.now(), Phase.ACTIVE, companyId)
        .ifPresent(
            (pol) -> {
              pol.setEndDate(ptoPolicy.getStartDate().minusDays(1));
            });

    return ptoPolicyRepository.save(ptoPolicy);
  }

  public void deletePtoPolicy(Long ptoPolicyId, Long companyId) {
    var ptoPolicy = getPtoPolicy(ptoPolicyId);
    var deleteError = ptoPolicy.isValidForDelete();

    if (deleteError != null) {
      throw new InvalidRequestException(deleteError);
    }

    findPtoPolicy(LocalDate.now(), Phase.ACTIVE, ptoPolicy.getCompanyId())
        .ifPresent(
            (activePtoPolicy) -> {
              activePtoPolicy.setEndDate(null);
              ptoPolicyRepository.save(ptoPolicy);
            });

    ptoPolicyRepository.delete(ptoPolicy);
  }
}
