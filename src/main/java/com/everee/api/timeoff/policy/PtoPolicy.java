package com.everee.api.timeoff.policy;

import com.everee.api.model.BaseModel;
import com.everee.api.phase.Phased;
import com.everee.api.timeoff.accrualrate.PtoAccrualRate;
import com.everee.api.timeoff.types.TimeOffType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class PtoPolicy extends BaseModel implements Phased {

  @NotNull private Long companyId;

  @NotNull private boolean salaryEligible;

  @NotNull private boolean fullTimeHourlyEligible;

  @NotNull private boolean partTimeHourlyEligible;

  @NotNull private LocalDate startDate;

  @NotNull
  @Enumerated(EnumType.STRING)
  private PtoPolicyType type;

  private PtoAccrualRate defaultAccrualRate;

  @JsonFormat(shape = Shape.STRING)
  private BigDecimal defaultPtoMaxHours;

  private LocalDate endDate;

  @JsonFormat(shape = Shape.STRING)
  private Integer waitingPeriod;

  @NotNull
  @Enumerated(EnumType.STRING)
  private AccrualPolicy accrualPolicy;

  @OneToMany(
      mappedBy = "ptoPolicy",
      fetch = FetchType.LAZY,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  private List<PtoTenureRule> ptoTenureRules = new ArrayList<>();

  @SuppressWarnings("unused")
  public TimeOffType getTimeOffType() {
    return TimeOffType.PAID_TIME_OFF;
  }

  @SuppressWarnings("unused")
  public PtoPolicyEligibilityRuleType getPtoPolicyEligibilityRuleType() {
    if (this.salaryEligible && fullTimeHourlyEligible && !partTimeHourlyEligible) {
      return PtoPolicyEligibilityRuleType.SALARY_AND_FULL_TIME_HOURLY;
    }

    if (this.salaryEligible && fullTimeHourlyEligible && partTimeHourlyEligible) {
      return PtoPolicyEligibilityRuleType.ALL;
    }

    return PtoPolicyEligibilityRuleType.SALARY;
  }
}
