package com.everee.api.timeoff.accrualrate;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.util.Optional;
import org.springframework.boot.jackson.JsonComponent;

@JsonComponent
public class PtoAccrualRateSerializer extends JsonSerializer<PtoAccrualRate> {

  @Override
  public void serialize(PtoAccrualRate value, JsonGenerator gen, SerializerProvider serializers)
      throws IOException {
    var str = Optional.ofNullable(value).map(PtoAccrualRate::toString).orElse(null);
    serializers.defaultSerializeValue(str, gen);
  }
}
