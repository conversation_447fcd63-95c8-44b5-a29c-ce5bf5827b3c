package com.everee.api.hazelcast;

import com.hazelcast.config.Config;
import com.hazelcast.config.InvalidConfigurationException;
import com.hazelcast.config.YamlConfigBuilder;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.instance.impl.HazelcastInstanceFactory;
import com.hazelcast.internal.config.YamlConfigLocator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HazelcastConfiguration {
  @Value("${spring.profiles.active:dev}")
  private String activeProfile;

  public class ProfileAwareYamlConfigLocator extends YamlConfigLocator {
    @Override
    protected boolean locateOnClasspath() {
      var prefix = "hazelcast";
      var suffix = ".yaml";
      var filename = prefix + "-" + activeProfile + suffix;
      try {
        return locateOnClasspath(filename);
      } catch (InvalidConfigurationException ex) {
        filename = prefix + suffix;
        return locateOnClasspath(filename);
      }
    }

    protected boolean locateOnClasspath(String filename) {
      if (!loadConfigurationFromClasspath(filename)) {
        throw new InvalidConfigurationException(
            "Configuration file not found on classpath: " + filename);
      }
      return true;
    }
  }

  @Bean
  @ConditionalOnProperty(
      value = "app.distributed-cache.enabled",
      havingValue = "true",
      matchIfMissing = false)
  public Config hazelcastConfig() {
    var yamlConfigLocator = new ProfileAwareYamlConfigLocator();
    yamlConfigLocator.locateInWorkDirOrOnClasspath();
    return new YamlConfigBuilder(yamlConfigLocator).build();
  }

  @Bean
  @ConditionalOnProperty(
      value = "app.distributed-cache.enabled",
      havingValue = "true",
      matchIfMissing = false)
  public HazelcastInstance hazelcastInstance() {
    return HazelcastInstanceFactory.getOrCreateHazelcastInstance(hazelcastConfig());
  }

  @Bean
  @ConditionalOnProperty(
      value = "app.distributed-cache.enabled",
      havingValue = "false",
      matchIfMissing = true)
  public HazelcastInstance disabledHazelcastInstance() {
    return new DisabledHazelcastInstance();
  }
}
