package com.everee.api.account;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.money.Money;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AccountService {
  private final AccountRepository accountRepository;

  public Account getAccount(@NotNull Long accountId) {
    return accountRepository
        .findById(accountId)
        .orElseThrow(() -> new ResourceNotFoundException("Account not found"));
  }

  public Page<Account> listAccounts(@NotNull Pageable pageable, Long companyId) {
    if (companyId != null) {
      return accountRepository.findByCompanyId(companyId, pageable);
    }

    return accountRepository.findAll(pageable);
  }

  public Account createAccount(@NotNull Account account) {
    if (account.getId() != null) {
      throw new InvalidRequestException("Account is already persisted");
    }

    return accountRepository.save(account);
  }

  public Account updateAccount(@NotNull Long accountId, @NotNull Account updatedAccount) {
    var account = getAccount(accountId);

    account.setName(updatedAccount.getName());
    account.setBalance(updatedAccount.getBalance());

    return accountRepository.save(account);
  }

  public Money getRunningPtpcSubbalance(Long companyId) {
    var subbalance = accountRepository.getRunningPtpcSubbalance(companyId);
    return (subbalance != null) ? subbalance : Money.ZERO;
  }
}
