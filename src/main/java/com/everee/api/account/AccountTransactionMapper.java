package com.everee.api.account;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AccountTransactionMapper {
  AccountTransactionMapper MAPPER = Mappers.getMapper(AccountTransactionMapper.class);

  AccountTransactionDTO toDto(AccountTransaction accountTransaction);

  @Mapping(
      target = "ptpcSubbalanceAmount",
      defaultExpression = "java(com.everee.api.money.Money.ZERO)")
  AccountTransaction toEntity(AccountTransactionDTO accountTransactionDTO);
}
