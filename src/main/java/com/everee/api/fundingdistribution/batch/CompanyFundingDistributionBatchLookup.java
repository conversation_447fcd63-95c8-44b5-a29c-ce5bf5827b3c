package com.everee.api.fundingdistribution.batch;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import io.swagger.annotations.ApiParam;
import java.time.LocalDate;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@RequestParams
public class CompanyFundingDistributionBatchLookup implements Lookup {
  @ApiParam(name = "company-id")
  private Long companyId;

  @ApiParam(name = "id", allowMultiple = true)
  private Set<String> ids;

  @ApiParam(name = "date")
  private LocalDate date;

  @ApiParam(name = "min-date")
  private LocalDate minDateInclusive;

  @ApiParam(name = "max-date")
  private LocalDate maxDateInclusive;

  @ApiParam(name = "finalized")
  private Boolean finalized;

  @ApiParam(name = "q")
  private String companyNameSearch;

  @ApiParam(name = "min-total-funding")
  private Double minTotalFunding;

  @ApiParam(name = "max-total-funding")
  private Double maxTotalFunding;

  @ApiParam(name = "min-total-distribution")
  private Double minTotalDistribution;

  @ApiParam(name = "max-total-distribution")
  private Double maxTotalDistribution;

  @Override
  public Set<Long> getCompanyIds() {
    return companyId != null ? Set.of(companyId) : Set.of();
  }
}
