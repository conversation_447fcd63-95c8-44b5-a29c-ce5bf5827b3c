package com.everee.api.fundingdistribution.batch;

import com.everee.api.money.Money;
import java.time.LocalDateTime;
import javax.persistence.Embeddable;
import javax.persistence.Transient;
import lombok.Data;
import org.hibernate.annotations.Immutable;

@Data
@Immutable
@Embeddable
public class KYCPrecheckResults {
  private Money availableBalance = Money.ZERO;
  private LocalDateTime availableBalanceLastUpdated;
  @Transient private boolean passed;

  public KYCPrecheckResults forAmount(Money amount) {
    passed = amount != null && availableBalance != null && availableBalance.gteq(amount);
    return this;
  }
}
