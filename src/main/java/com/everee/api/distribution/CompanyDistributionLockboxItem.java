package com.everee.api.distribution;

import com.everee.api.model.BaseModel;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Entity
@Data
@Accessors(chain = true)
public class CompanyDistributionLockboxItem extends BaseModel {
  @NotNull private Long companyDistributionId;

  @NotNull
  @Enumerated(EnumType.STRING)
  private CompanyDistributionLockboxType type;

  @NotNull private Money amount = Money.ZERO;
  private Long lockboxedAchFileId;

  @Transient
  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private List<Long> taxTypeAccumulationIds = new ArrayList<>();
}
