package com.everee.api.distribution;

import java.time.LocalDateTime;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Embeddable
@Accessors(chain = true)
public class DistributionFundingApprovalInformation {
  private Long approvedByUserId;
  private LocalDateTime approvedAt;

  @Enumerated(EnumType.STRING)
  private AutoApprovalFailureReason autoApprovalFailureReason;

  private LocalDateTime autoApprovalFailedAt;
}
