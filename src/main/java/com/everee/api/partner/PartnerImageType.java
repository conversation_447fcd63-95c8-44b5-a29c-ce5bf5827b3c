package com.everee.api.partner;

import java.util.Set;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

public enum PartnerImageType {
  LOGO {
    @Override
    public Set<String> getValidFileExtensions() {
      return LOGO_EXTENSIONS;
    }
  },
  ICON {
    @Override
    public Set<String> getValidFileExtensions() {
      return LOGO_EXTENSIONS;
    }
  },
  FAVICON {
    @Override
    public Set<String> getValidFileExtensions() {
      return FAVICON_EXTENSIONS;
    }
  };

  protected static final long MAX_BYTES = 1 * 1024 * 1024 * 10; // 10 MB
  // Ensure new extensions work in emails.  (SVG was not working in all email clients)
  private static final Set<String> LOGO_EXTENSIONS = Set.of("gif", "jpg", "jpeg", "png");
  private static final Set<String> FAVICON_EXTENSIONS = Set.of("ico");

  public abstract Set<String> getValidFileExtensions();

  public boolean validate(byte[] imageBytes, String imageName) {
    if (imageBytes == null && StringUtils.isBlank(imageName)) {
      return false;
    }

    if (imageBytes == null || StringUtils.isBlank(imageName)) {
      throw new PartnerImageException("imageBytes and imageName are required");
    }

    if (imageBytes.length > MAX_BYTES) {
      throw new PartnerImageException("Images cannot be larger than 10 MB");
    }

    var extension = FilenameUtils.getExtension(imageName).toLowerCase();
    if (!getValidFileExtensions().contains(extension)) {
      throw new PartnerImageException("Invalid file extension " + extension);
    }

    return true;
  }
}
