package com.everee.api.priv;

import com.everee.api.auth.annotation.TaxAdminAccess;
import com.everee.api.tax.jurisdictions.*;
import com.everee.api.tax.jurisdictions.lookup.CompanyTaxJurisdictionLookup;
import com.everee.api.tax.jurisdictions.lookup.CompanyTaxJurisdictionLookupService;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@TaxAdminAccess
@RestController("PrivateCompanyTaxJurisdictionController")
@RequestMapping("/api-private/v1/company-tax-jurisdictions")
@RequiredArgsConstructor
public class CompanyTaxJurisdictionController {

  private final CompanyTaxJurisdictionService service;
  private final CompanyTaxJurisdictionLookupService lookupService;
  private final CompanyTaxJurisdictionRepository repository;
  private final AddedCompanyTaxJurisdictionsReportService addedJurisdictionsReportService;

  @GetMapping("/{companyTaxJurisdictionId}")
  public CompanyTaxJurisdiction getCompanyTaxJurisdiction(
      @PathVariable String companyTaxJurisdictionId) {
    return repository.getOne(companyTaxJurisdictionId);
  }

  @GetMapping
  public Page<CompanyTaxJurisdiction> listCompanyTaxJurisdictions(
      CompanyTaxJurisdictionLookup lookup, Pageable pageable) {
    return lookupService.listAll(lookup, pageable);
  }

    @GetMapping("added-jurisdictions-report")
    public ResponseEntity<Page<CompanyTaxJurisdictionReport>> listCompanyTaxJurisdictions(
            @RequestParam(required = false) LocalDate from,
            @RequestParam(required = false) LocalDate to,
            @RequestParam(required = false, name = "include-demo-companies") Boolean includeDemoCompanies,
            @RequestParam(required = false, name = "include-imported-payments", defaultValue = "true") Boolean includeImportedPayments,
            Pageable pageable) {
        if (from == null) {
            from = LocalDate.now().minusDays(30);
        }
        if (to == null || to.isAfter(LocalDate.now())) {
            to = LocalDate.now();
        }
        if (includeDemoCompanies == null) {
            includeDemoCompanies = false;
        }
        if (to.isBefore(from)) {
            return ResponseEntity.badRequest().build();
        }
        Page<CompanyTaxJurisdictionReport> reportPage = addedJurisdictionsReportService.getReport(from, to, includeDemoCompanies, includeImportedPayments, pageable);
        return ResponseEntity.ok(reportPage);
    }

  @PostMapping
  public CompanyTaxJurisdiction createCompanyTaxJurisdiction(
      @Valid @RequestBody CompanyTaxJurisdiction companyTaxJurisdiction) {
    return service.create(companyTaxJurisdiction);
  }

  @PutMapping("/{companyTaxJurisdictionId}")
  public CompanyTaxJurisdiction updateCompanyTaxJurisdiction(
      @PathVariable String companyTaxJurisdictionId,
      @Valid @RequestBody CompanyTaxJurisdiction companyTaxJurisdiction) {
    return service.update(companyTaxJurisdictionId, companyTaxJurisdiction);
  }

  @DeleteMapping("/{companyTaxJurisdictionId}")
  public ResponseEntity<Void> deleteCompanyTaxJurisdiction(
      @PathVariable String companyTaxJurisdictionId) {
    service.delete(companyTaxJurisdictionId);
    return new ResponseEntity<>(HttpStatus.NO_CONTENT);
  }
}
