package com.everee.api.priv;

import com.everee.api.auth.annotation.TaxAdminAccess;
import com.everee.api.company.*;
import com.everee.api.companyMasterTax.models.MasterTaxCompanyLookup;
import com.everee.api.companyMasterTax.services.MasterTaxCompanyLookupService;
import com.everee.api.companyrole.CompanyRole;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.payperiod.PayPeriodPreference;
import com.everee.api.payperiod.PayPeriodPreferenceUpdateRequest;
import com.everee.api.phase.PhaseLookup;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import javax.transaction.Transactional;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@TaxAdminAccess
@RestController("PrivateCompanyController")
@RequestMapping("/api-private/v1/companies")
@RequiredArgsConstructor
@Transactional
public class CompanyController {
  private final CompanyAddressService addressService;
  private final CompanyService companyService;
  private final CoreCompanyRepository coreCompanyRepository;
  private final CompanyOnboardingService companyOnboardingService;
  private final CompanyFileService companyFileService;
  private final DetailedCompanyLookupService companyLookupService;
  private final CompanyPayPeriodPreferenceService companyPayPeriodPreferenceService;
  private final MasterTaxCompanyLookupService masterTaxCompanyLookupService;

  @GetMapping
  public Page<DetailedCompany> listCompanies(
      DetailedCompanyLookup lookup,
      Pageable pageable,
      @RequestParam(required = false) Boolean unpaged) {
    if (Boolean.TRUE.equals(unpaged)) {
      pageable = Pageable.unpaged();
    }
    return companyLookupService.listAll(lookup, pageable);
  }

  @PostMapping
  public ResponseEntity<DetailedCompany> createCompany(
      @Valid @RequestBody CompanyCreateParams request) {
    var company = companyOnboardingService.createCompany(request);

    var location =
        ServletUriComponentsBuilder.fromCurrentRequest()
            .path("/{id}")
            .buildAndExpand(company.getId())
            .toUri();

    return ResponseEntity.created(location).body(company);
  }

  @GetMapping("{companyId}")
  public DetailedCompany getCompany(@PathVariable Long companyId) {
    return companyService.getCompany(companyId);
  }

  @GetMapping("/core/{companyId}")
  public CoreCompany getCoreCompany(@PathVariable Long companyId) {
    return coreCompanyRepository.getOne(companyId);
  }

  @PutMapping("{companyId}")
  public DetailedCompany updateCompany(
      @PathVariable Long companyId, @Valid @RequestBody CompanyUpdateParams company) {
    return companyService.updateCompany(companyId, company);
  }

  /* Company Pay Period Preference controls */
  @GetMapping("{companyId}/pay-period-preference")
  public Page<PayPeriodPreference> listCompanyPayPeriodPreferences(
      @PathVariable Long companyId, Pageable pageable) {
    return companyPayPeriodPreferenceService.listPreferences(companyId, pageable);
  }

  @ResponseStatus(HttpStatus.OK)
  @PutMapping("{companyId}/pay-period-preference")
  public void scheduleCompanyPayPeriodPreference(
      @PathVariable Long companyId, @Valid @RequestBody PayPeriodPreferenceUpdateRequest request) {
    var company = companyService.getCompany(companyId);
    companyPayPeriodPreferenceService.schedulePreferenceChange(company, request);
  }

  /* Company address control points */
  @GetMapping("/addresses/{addressId}")
  public CompanyAddress getCompanyAddress(@PathVariable Long addressId) {
    return addressService.getCompanyAddress(addressId);
  }

  @GetMapping("/addresses")
  public Page<CompanyAddress> listCompanyAddresses(
      @RequestParam(name = "id", required = false) List<Long> ids,
      @RequestParam(name = "company-id", required = false) Long companyId,
      PhaseLookup phaseLookup,
      Pageable pageable) {
    return addressService.listCompanyAddresses(pageable, phaseLookup, ids, companyId);
  }

  @PostMapping("/addresses")
  public CompanyAddress createCompanyAddress(@RequestBody CompanyAddress companyAddress) {
    return addressService.createCompanyAddress(companyAddress);
  }

  @PutMapping("/addresses/{addressId}")
  public CompanyAddress updateCompanyAddress(
      @PathVariable Long addressId, @RequestBody CompanyAddress companyAddress) {
    return addressService.updateCompanyAddress(addressId, companyAddress);
  }

  @DeleteMapping("/addresses/{addressId}")
  public ResponseEntity deleteCompanyAddress(@PathVariable Long addressId) {
    addressService.deleteCompanyAddress(addressId);
    return ResponseEntity.noContent().build();
  }

  /* Company role control points */
  @GetMapping("/roles")
  public Page<CompanyRole> listCompanyRoles(
      @RequestParam(name = "id", required = false) List<Long> ids,
      @RequestParam(name = "company-id", required = false) Long companyId,
      @RequestParam(name = "user-id", required = false) Long userId,
      @RequestParam(name = "type", required = false) CompanyRoleType type,
      Pageable pageable) {
    return companyService.listCompanyRoles(pageable, ids, companyId, userId, type);
  }

  @PostMapping("/roles")
  public ResponseEntity<CompanyRole> createCompanyRole(@RequestBody CompanyRole role) {
    role = companyService.createCompanyRole(role);

    var location =
        ServletUriComponentsBuilder.fromCurrentRequest()
            .path("/{id}")
            .buildAndExpand(role.getId())
            .toUri();

    return ResponseEntity.created(location).body(role);
  }

  @DeleteMapping("/roles/{companyRoleId}")
  public ResponseEntity<CompanyRole> deleteCompanyRole(@PathVariable Long companyRoleId) {
    companyService.deleteCompanyRoleById(companyRoleId);
    return ResponseEntity.noContent().build();
  }

  @GetMapping("/{companyId}/next-funding-date")
  public LocalDate getNextFundingDate(@PathVariable Long companyId) {
    return companyService.getNextFundingDate(LocalDateTime.now());
  }

  @DeleteMapping("/files")
  public void deleteArchivedCompanyFiles(
      @RequestParam(name = "archived-before", required = false) LocalDate archivedBeforeDate) {
    companyFileService.deleteArchivedFiles(
        Optional.of(archivedBeforeDate).orElse(LocalDate.now().minusDays(7)));
  }

  @GetMapping("/{companyId}/ach-fee-configuration")
  public CompanyAchFeeConfigurationDto getCompanyAchFeeConfiguration(@PathVariable Long companyId) {
    return companyService.getCompanyAchFeeConfiguration(companyId);
  }

  @GetMapping("/master-tax")
  public Page<CoreCompany> listMasterTaxCompanies(
            MasterTaxCompanyLookup lookup,
            Pageable pageable) {

     return masterTaxCompanyLookupService.listAll(lookup, pageable);
  }

  @PutMapping("/disable")
  public void disableCompanies(@RequestBody List<Long> companyIds) {
    companyService.disableCompanies(companyIds);
  }
}
