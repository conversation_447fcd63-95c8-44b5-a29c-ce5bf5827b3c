package com.everee.api.priv.reports;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController("PrivateEmployeeDistributionAccountController")
@RequestMapping("/api-private/v1/reports/employee-payment-preferences")
@RequiredArgsConstructor
public class PrivReportsController {
  private final EmployeeBankAccountRepository employeeBankAccountRepository;

  @PostMapping
  public List<EmployeePaymentPreferences> listEmployeePaymentPreferences(@RequestBody EmployeePaymentPreferencesRequest request) {
    return employeeBankAccountRepository.findByWorkerIds(request.getWorkerIds());
  }
}
