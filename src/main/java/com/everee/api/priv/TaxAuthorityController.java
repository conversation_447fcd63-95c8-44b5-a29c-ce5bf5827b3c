package com.everee.api.priv;

import com.everee.api.auth.annotation.EvereeAdminAccess;
import com.everee.api.tax.state.State;
import com.everee.api.taxauthority.TaxAuthority;
import com.everee.api.taxauthority.TaxAuthorityService;
import com.everee.api.taxauthority.TaxAuthorityVerificationService;
import com.everee.api.taxauthority.jurisdiction.TaxJurisdiction;
import com.everee.api.taxauthority.qbo.TaxAuthorityQboVendorMappingService;
import com.everee.api.taxauthority.qbo.TaxAuthorityWithVendorId;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@EvereeAdminAccess
@RestController("PrivateTaxAuthorityController")
@RequestMapping("/api-private/v1/tax-authority")
@RequiredArgsConstructor
public class TaxAuthorityController {
  private final TaxAuthorityService taxAuthorityService;
  private final TaxAuthorityQboVendorMappingService taxAuthorityQboVendorMappingService;
  private final TaxAuthorityVerificationService taxAuthorityVerificationService;

  @GetMapping
  public Page<TaxAuthority> listTaxAuthorities(
      @RequestParam(name = "id", required = false) Set<Long> ids,
      @RequestParam(name = "state", required = false) String stateString,
      @RequestParam(name = "all", required = false, defaultValue = "false") Boolean isReturnAll,
      Pageable pageable) {

    if (ids != null) {
      return taxAuthorityService.listTaxAuthorities(ids);
    } else {
      var getPagesRequest = Boolean.TRUE.equals(isReturnAll) ? PageRequest.of(0, Integer.MAX_VALUE, pageable.getSort()) : pageable;
      if (StringUtils.isBlank(stateString)) {
        return taxAuthorityService.listTaxAuthorities(getPagesRequest);
      } else {
        return taxAuthorityService.listTaxAuthorities(State.fromCodeNullable(stateString), getPagesRequest);
      }
    }
  }

  @PostMapping
  public ResponseEntity<TaxAuthority> createTaxAuthority(
      @Valid @RequestBody TaxAuthorityWithVendorId taxAuthorityWithVendorId) {
    var taxAuthority = taxAuthorityQboVendorMappingService.create(taxAuthorityWithVendorId);

    var location =
        ServletUriComponentsBuilder.fromCurrentRequest()
            .path("/{id}")
            .buildAndExpand(taxAuthority.getId())
            .toUri();

    return ResponseEntity.created(location).body(taxAuthority);
  }

  @GetMapping("/{taxAuthorityId}")
  public TaxAuthority getTaxAuthority(@PathVariable Long taxAuthorityId) {
    return taxAuthorityQboVendorMappingService.withVendorId(
        taxAuthorityService.getTaxAuthority(taxAuthorityId));
  }

  @PutMapping("/{taxAuthorityId}")
  public TaxAuthority updateTaxAuthority(
      @PathVariable Long taxAuthorityId,
      @Valid @RequestBody TaxAuthorityWithVendorId taxAuthority) {
    return taxAuthorityQboVendorMappingService.update(taxAuthority);
  }

  @DeleteMapping("/{taxAuthorityId}")
  public void deleteTaxAuthority(@PathVariable Long taxAuthorityId) {
    taxAuthorityQboVendorMappingService.deleteByTaxAuthorityId(taxAuthorityId);
  }

  @PostMapping("/verify-mappings-exist")
  public List<TaxJurisdiction> verifyMappingsExist(
      @RequestParam(name = "calculated-date", required = false) LocalDate calculatedDate) {
    return taxAuthorityVerificationService.createJurisdictionsFromPayments(
        Optional.ofNullable(calculatedDate).orElse(LocalDate.now()));
  }
}
