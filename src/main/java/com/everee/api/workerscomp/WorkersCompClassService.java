package com.everee.api.workerscomp;

import com.everee.api.jobWorkersCompClass.JobWorkersCompClassService;
import com.everee.api.company.CompanyService;
import com.everee.api.employee.CoreEmployee;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.employeePosition.EmployeePositionLookup;
import com.everee.api.employeePosition.EmployeePositionLookupService;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.payment.PaymentCalculationService;
import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Optional;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Transactional
public class WorkersCompClassService {
  private final WorkersCompClassRepository workersCompClassRepository;
  private final PaymentCalculationService paymentCalculationService;
  private final JobWorkersCompClassService jobWorkersCompClassService;
  private final EmployeePositionLookupService employeePositionLookupService;

  public WorkersCompClass get(@NotNull Long id) {
    return workersCompClassRepository.findById(id).orElseThrow(ResourceNotFoundException::new);
  }

  public WorkersCompClass create(@Valid WorkersCompClass request) {
    var workersCompClass = workersCompClassRepository.save(request);
    recalculate(getEmployees(workersCompClass));
    return workersCompClass;
  }

  public WorkersCompClass update(@Valid WorkersCompClass request) {
    var workersCompClass = get(request.getId());
    if (!workersCompClass.getCompanyId().equals(request.getCompanyId()))
      throw new InvalidRequestException("Invalid companyId");
    workersCompClass = copy(workersCompClass, request);
    recalculate(getEmployees(workersCompClass));
    workersCompClassRepository.save(workersCompClass);
    return workersCompClass;
  }

  public void delete(@NotNull Long id) {
    var workersCompClass = get(id);
    if (!workersCompClass.getCompanyId().equals(CompanyService.getAuthenticatedCompanyId()))
      throw new InvalidRequestException("Invalid companyId");
    var employees = getEmployees(workersCompClass);
    workersCompClassRepository.delete(workersCompClass);
    recalculate(employees);
  }

  public Optional<WorkersCompClass> getByExternalJobTitleId(@NotNull String externalJobTitleId) {
    var jobWorkersCompClass = jobWorkersCompClassService.getActiveJobWorkersCompClassByExternalJobId(externalJobTitleId);
    return jobWorkersCompClass.map(workersCompClass -> get(workersCompClass.getWorkersCompClassId()));
  }

  public List<WorkersCompClassCompaniesReportDto> getPositionsWithSameWorkersCompClassCodes(List<Long> companyIds){
    return workersCompClassRepository.getPositionsWithSameWorkersCompClassCodes(companyIds)
      .stream()
      .map(p -> new WorkersCompClassCompaniesReportDto()
      .setCompanyId(p.getCompanyId())
      .setWorkersCompClassId(p.getWorkersCompClassId())
      .setTitle(p.getTitle())
      .setState(p.getState()))
      .collect(Collectors.toList());
  }

  public WorkersCompAssignmentInfo getWorkersCompAssignmentInfo(long id) {
    return new WorkersCompAssignmentInfo()
      .setAssigned(workersCompClassRepository.isWorkersCompClassIdAssignedToActiveJob(id, LocalDate.now()));
  }

  private WorkersCompClass copy(WorkersCompClass to, WorkersCompClass from) {
    to.setId(from.getId());
    to.setCompanyId(from.getCompanyId());
    to.setName(from.getName());
    to.setCode(from.getCode());
    to.setState(from.getState());
    to.setRate(from.getRate());
    return to;
  }

  private Collection<CoreEmployee> getEmployees(WorkersCompClass workersCompClass) {
    var lookup =
        new EmployeePositionLookup()
            .withPhaseLookup(new PhaseLookup(Phase.ACTIVE, LocalDate.now(), LocalDate.now()))
            .withWorkersCompClassIds(Set.of(workersCompClass.getId()));
    var positions = employeePositionLookupService.listAll(lookup, Pageable.unpaged()).getContent();
    return positions.stream().map(EmployeePosition::getEmployee).collect(Collectors.toSet());
  }

  private void recalculate(Collection<CoreEmployee> employees) {
    employees.stream()
        .map(CoreEmployee::getId)
        .forEach(paymentCalculationService::bulkRecalculateOutstandingPaymentsForEmployee);
  }
}
