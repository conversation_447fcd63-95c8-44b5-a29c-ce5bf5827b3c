package com.everee.api.workerscomp;

import com.everee.api.model.BaseModelV2;
import com.everee.api.tax.state.State;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Entity
public class WorkersCompClass extends BaseModelV2 {
  @NotNull private Long companyId;

  @Enumerated(EnumType.STRING)
  @NotNull
  private State state;

  @Size(min = 4, max = 4)
  @Pattern(regexp = "[0-9]+")
  @NotNull
  private String code;

  @NotNull private String name;

  @Embedded
  @NotNull private WorkerCompRate rate;

  @Transient
  public String getDisplayName() {
    return state.toString() + " - " + getName();
  }
}
