package com.everee.api.sendgrid;

import com.sendgrid.Request;
import com.sendgrid.Response;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import lombok.Data;
import lombok.NonNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

@Data
@Entity
public class SendGridResponse {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String endpoint;

  private Integer statusCode;

  private String requestBody;

  private String responseBody;

  @CreatedDate private LocalDateTime createdAt;

  @LastModifiedDate private LocalDateTime updatedAt;

  public static SendGridResponse of(@NonNull Request req, @NonNull Response res) {
    var target = new SendGridResponse();
    target.setStatusCode(res.getStatusCode());
    target.setEndpoint(req.getEndpoint());
    target.setRequestBody(req.getBody());
    target.setResponseBody(res.getBody());
    return target;
  }
}
