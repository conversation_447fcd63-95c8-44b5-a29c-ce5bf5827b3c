package com.everee.api.sendgrid;

import com.sendgrid.SendGrid;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class SendGridConfiguration {

  private final SendGridProperties sendGridProperties;

  @Bean
  public SendGrid sendGrid() {
    return new SendGrid(sendGridProperties.getApiKey());
  }
}
