package com.everee.api.taxaccumulator.submit;

import com.everee.api.company.CompanyService;
import com.everee.api.distribution.CompanyDistribution;
import com.everee.api.distribution.CompanyDistributionRepository;
import com.everee.api.distribution.CompanyDistributionStatus;
import com.everee.api.money.Money;
import com.everee.api.payment.distribution.PaymentDistributionRepository;
import com.everee.api.payment.distribution.PaymentDistributionStatus;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.taxaccumulator.TaxTypeAccumulationRepository;
import com.everee.api.taxaccumulator.lookup.TaxTypeAccumulationLookupService;
import com.everee.api.taxauthority.TaxAuthoritySubmission;
import com.everee.api.taxauthority.TaxAuthoritySubmissionRepository;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TaxTypeAccumulationSubmissionService {
  private final TaxTypeAccumulationLookupService lookupService;
  private final TaxAuthoritySubmissionRepository taxAuthoritySubmissionRepository;
  private final TaxTypeAccumulationRepository taxTypeAccumulationRepository;
  private final CompanyDistributionRepository companyDistributionRepository;
  private final PaymentDistributionRepository paymentDistributionRepository;
  private final CompanyService companyService;

  // private final TaxAuthorityRepository taxAuthorityRepository;

  @Transactional
  public TaxAuthoritySubmission submit(TaxAccumulationSubmissionWrapper wrapper) {
    var taxAuthoritySubmission = wrapper.getSubmission();
    var taxAccumulationLookup = wrapper.getTaxAccumulationLookup();

    if (CollectionUtils.size(taxAccumulationLookup.getTaxAuthorityIds()) != 1) {
      throw new RuntimeException("Taxes must be submitted for 1 Tax Authority");
    }

    taxAuthoritySubmission.setFromDate(taxAccumulationLookup.getMinForDateInclusive());
    taxAuthoritySubmission.setToDate(taxAccumulationLookup.getMaxForDateInclusive());

    taxAuthoritySubmissionRepository.save(taxAuthoritySubmission);

    var taxTypeAccumulations = lookupService.listAll(taxAccumulationLookup, Pageable.unpaged());
    var taxTypeAccumulationIdsByCompanyId = new HashMap<Long, Set<Long>>();
    var taxTypeAccumulationTotalsByCompanyId = new HashMap<Long, Money>();
    taxTypeAccumulations.forEach(
        accumulation -> {
          if (accumulation.getTaxAuthoritySubmissionId() != null) {
            throw new RuntimeException(
                "Tax accumulation has already been submitted - " + accumulation.getId());
          }

          accumulation.setTaxAuthoritySubmissionId(taxAuthoritySubmission.getId());
          taxTypeAccumulationIdsByCompanyId.putIfAbsent(
              accumulation.getCompanyId(), new HashSet<>());
          taxTypeAccumulationIdsByCompanyId
              .get(accumulation.getCompanyId())
              .add(accumulation.getId());
          taxTypeAccumulationTotalsByCompanyId.putIfAbsent(accumulation.getCompanyId(), Money.ZERO);
          taxTypeAccumulationTotalsByCompanyId.put(
              accumulation.getCompanyId(),
              taxTypeAccumulationTotalsByCompanyId
                  .get(accumulation.getCompanyId())
                  .plus(accumulation.getNet()));
        });

    handlePaymentDistributions(
        taxAuthoritySubmission,
        taxTypeAccumulationIdsByCompanyId,
        taxTypeAccumulationTotalsByCompanyId);

    taxTypeAccumulationRepository.saveAll(taxTypeAccumulations);

    // TODO: auto submit for a tax authority
    // var taxAuthority =
    // taxAuthorityRepository.getOne(taxAccumulationLookup.getTaxAuthorityIds().iterator().next());

    return taxAuthoritySubmissionRepository.getOne(taxAuthoritySubmission.getId());
  }

  private void handlePaymentDistributions(
      TaxAuthoritySubmission taxAuthoritySubmission,
      HashMap<Long, Set<Long>> taxTypeAccumulationIdsByCompanyId,
      HashMap<Long, Money> taxTypeAccumulationTotalsByCompanyId) {
    taxTypeAccumulationIdsByCompanyId
        .entrySet()
        .forEach(
            taxTypeAccumulationIdsForCompanyEntry -> {
              var company =
                  companyService.getCompany(taxTypeAccumulationIdsForCompanyEntry.getKey());
              var taxAccumulationIds = taxTypeAccumulationIdsForCompanyEntry.getValue();

              var distribution = new CompanyDistribution();
              distribution.setDistributionDate(LocalDate.now());
              distribution.setType(PaymentDistributionType.TAX);
              distribution.setSourceAchBankAccountId(
                  company.getCompanyAchConfiguration().getTaxLockboxAccountId());
              distribution.setStatus(CompanyDistributionStatus.SUBMITTED);
              distribution.setTaxAuthoritySubmissionId(taxAuthoritySubmission.getId());
              distribution.setCompanyId(company.getId());
              distribution.setAmount(taxTypeAccumulationTotalsByCompanyId.get(company.getId()));

              companyDistributionRepository.save(distribution);
              paymentDistributionRepository.updateTaxDistributionsForTaxTypeAccumulations(
                  distribution.getId(), PaymentDistributionStatus.DISTRIBUTED, taxAccumulationIds);
            });
  }
}
