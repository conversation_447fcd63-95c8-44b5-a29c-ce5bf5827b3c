package com.everee.api.taxaccumulator;

import com.everee.api.money.Money;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TaxTypeAccumulationRepository extends JpaRepository<TaxTypeAccumulation, Long> {
  @Query(
      "SELECT sum(t.net) FROM TaxTypeAccumulation t WHERE t.paymentType=?2 AND (t.achFileId IN ?1 OR t.fundingAchFileId in ?1)")
  Money getTotalNetTaxesForAchfileIdsAndPaymentType(
      Collection<Long> achFileIds, TaxTypeAccumulationPaymentType paymentType);

  Long deleteByAchFileIdAndPaymentTypeIn(Long achFileId, Set<TaxTypeAccumulationPaymentType> types);

  Long deleteByFundingAchFileId(Long achFileId);

  @Modifying
  @Query(
      "UPDATE TaxTypeAccumulation tta SET tta.achFileId=null WHERE tta.achFileId=?1 and tta.paymentType in ?2")
  void removeAchFileIdForTypes(Long achFileId, Set<TaxTypeAccumulationPaymentType> types);

  @Modifying
  @Query(
      "UPDATE TaxTypeAccumulation tta SET tta.companyDistributionLockboxItemId=null WHERE companyDistributionLockboxItemId IN (SELECT id FROM CompanyDistributionLockboxItem WHERE lockboxedAchFileId=?1) AND tta.paymentType in ?2")
  void removeLockboxItemIdForAchFileIdAndTypes(
      Long achFileId, Set<TaxTypeAccumulationPaymentType> types);

  @Query(
      "  SELECT "
          + "  t.taxType AS taxType, "
          + "  t.jurisdictionTaxId AS jurisdictionTaxId, "
          + "  t.companyId AS companyId, "
          + "  t.forDate as forDate, "
          + "  t.achFileId AS achFileId, "
          + "  sum(t.net) AS net "
          + "FROM TaxTypeAccumulation t "
          + "WHERE t.achFileId IN ?1 AND t.paymentType in ?2 "
          + "GROUP BY "
          + "  t.taxType, "
          + "  t.jurisdictionTaxId, "
          + "  t.companyId, "
          + "  t.achFileId, "
          + "  t.forDate")
  List<TaxTypeAccumulationTotal> getAccumulationsByJurisdictionForAchFiles(
      Collection<Long> achFileIds, Collection<TaxTypeAccumulationPaymentType> paymentTypes);

  @Query(
      "  SELECT "
          + "  t.taxType AS taxType, "
          + "  t.jurisdictionTaxId AS jurisdictionTaxId, "
          + "  t.companyId AS companyId, "
          + "  min(t.forDate) AS fromDate, "
          + "  max(t.forDate) AS toDate, "
          + "  t.achFileId AS achFileId, "
          + "  sum(t.net) AS net "
          + "FROM TaxTypeAccumulation t "
          + "WHERE t.fundingAchFileId IN ?1 AND t.paymentType='PRE' "
          + "GROUP BY "
          + "  t.taxType, "
          + "  t.jurisdictionTaxId, "
          + "  t.companyId, "
          + "  t.achFileId")
  List<TaxTypeAccumulationTotal> getAccumulationsByJurisdictionForPrefundingAchFiles(
      Collection<Long> fundingAchFileIds);

  List<TaxTypeAccumulation> getByTaxAuthoritySubmissionId(Long taxAuthoritySubmissionId);

  @Modifying
  @Query(
      "UPDATE PaymentTax pt SET pt.taxTypeAccumulationId=:taxTypeAccumulationId WHERE pt.id in :paymentTaxIds AND pt.taxTypeAccumulationId is null")
  void assignTaxTypeAccumulationToPaymentTax(
      @Param("taxTypeAccumulationId") Long taxTypeAccumulationId,
      @Param("paymentTaxIds") Set<Long> paymentTaxIds);
}
