package com.everee.api.taxaccumulator.export;

import static com.everee.api.storage.StorageAccess.EVEREE_INTERNAL;

import com.everee.api.storage.StorageService;
import com.everee.api.taxaccumulator.TaxTypeAccumulationTaxesGroupingStrategyType;
import com.everee.api.taxaccumulator.export.eftps.EftpsExportService;
import com.everee.api.taxaccumulator.export.exception.TaxTypeAccumulationExportError;
import com.everee.api.taxaccumulator.export.state.UtahStateExportService;
import com.everee.api.taxaccumulator.lookup.TaxTypeAccumulationLookup;
import com.everee.api.taxauthority.TaxAuthorityRepository;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaxTypeAccumulationExportService {
  private final EftpsExportService eftpsExportService;
  private final UtahStateExportService utahStateExportService;
  private final TaxAuthorityRepository taxAuthorityRepository;
  private final StorageService storageService;

  public ExportData export(
      TaxTypeAccumulationTaxesGroupingStrategyType strategyType,
      Sort.Direction direction,
      Pageable pageable,
      TaxTypeAccumulationLookup accumulationLookup,
      LocalDate forDate) {
    var data = exportUnstored(strategyType, direction, pageable, accumulationLookup, forDate);
    try {
      var path = List.of("taxes", "tax-remittance", "export");
      storageService.storeFile(data.getContent(), data.getFilename(), EVEREE_INTERNAL, path);
    } catch (Exception e) {
      log.error("Unable to store tax remittance export file " + data.getFilename(), e);
    }
    return data;
  }

  private ExportData exportUnstored(
      TaxTypeAccumulationTaxesGroupingStrategyType strategyType,
      Sort.Direction direction,
      Pageable pageable,
      TaxTypeAccumulationLookup accumulationLookup,
      LocalDate forDate) {

    if (CollectionUtils.size(accumulationLookup.getTaxAuthorityIds()) != 1) {
      throw new TaxTypeAccumulationExportError("Export must be for 1 tax authority.");
    }

    var taxAuthority =
        taxAuthorityRepository.getOne(accumulationLookup.getTaxAuthorityIds().iterator().next());

    var exportType = taxAuthority.getTaxExportType();
    if (exportType == null) {
      throw new TaxTypeAccumulationExportError(
          "No export type setup for tax authority " + taxAuthority.getName());
    }

    switch (exportType) {
      case EFTPS_940:
      case EFTPS_941:
        return eftpsExportService.generateExport(
            exportType, strategyType, direction, pageable, accumulationLookup, forDate);
      case STATE_UT:
        return utahStateExportService.generateExport(
            exportType, strategyType, direction, pageable, accumulationLookup, forDate);
      default:
        throw new TaxTypeAccumulationExportError("No export setup for type " + exportType);
    }
  }
}
