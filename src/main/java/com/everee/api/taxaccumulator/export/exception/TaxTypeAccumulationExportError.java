package com.everee.api.taxaccumulator.export.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class TaxTypeAccumulationExportError extends RuntimeException {
  public TaxTypeAccumulationExportError(String msg) {
    super(msg);
  }

  public TaxTypeAccumulationExportError(String msg, Throwable t) {
    super(msg, t);
  }
}
