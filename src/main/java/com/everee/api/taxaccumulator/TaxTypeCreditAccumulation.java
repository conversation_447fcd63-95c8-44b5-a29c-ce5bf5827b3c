package com.everee.api.taxaccumulator;

import com.everee.api.model.BaseModelV2;
import com.everee.api.money.Money;
import com.everee.api.payment.TaxCreditType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Entity
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@RequiredArgsConstructor
public class TaxTypeCreditAccumulation extends BaseModelV2 {
  @NotNull private Long taxTypeAccumulationId;

  @NotNull
  @NonNull
  @Enumerated(EnumType.STRING)
  private TaxCreditType creditType;

  private Money gross = new Money();
  private Money applied = new Money();
  private Money unapplied = new Money();

  public void add(TaxTypeCreditAccumulation accumulation) {
    gross = gross.plus(accumulation.getGross());
    applied = applied.plus(accumulation.getApplied());
    unapplied = unapplied.plus(accumulation.getUnapplied());
  }
}
