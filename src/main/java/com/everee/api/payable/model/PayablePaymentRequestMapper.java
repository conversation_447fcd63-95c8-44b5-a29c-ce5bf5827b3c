package com.everee.api.payable.model;

import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PayablePaymentRequestMapper {

  PayablePaymentRequestMapper MAPPER = Mappers.getMapper(PayablePaymentRequestMapper.class);

  PayablePaymentRequestDTO toDto(PayablePaymentRequest request);

  PrivPayablePaymentRequestDTO toPrivDto(PayablePaymentRequest payablePaymentRequest);
}
