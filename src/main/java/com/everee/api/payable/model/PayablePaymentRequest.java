package com.everee.api.payable.model;

import com.everee.api.i18n.LocalizedString;
import com.everee.api.model.BaseModelV2;
import com.everee.api.model.DateRange;
import com.everee.api.payment.group.PaymentGroupDescriptor;
import com.everee.api.payperiod.PayPeriod;
import java.time.LocalDate;
import java.util.Optional;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.context.i18n.LocaleContextHolder;

@Data
@Accessors(chain = true)
@Table(name = "payablepaymentrequest")
@Entity
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PayablePaymentRequest extends BaseModelV2<PayablePaymentRequest>
    implements PaymentGroupDescriptor {
  @NotNull private Long companyId;
  @NotNull private int payableCount;
  @NotNull private Long payPeriodId;

  @ToString.Exclude
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "payPeriodId", insertable = false, updatable = false)
  private PayPeriod payPeriod;

  @Override
  public LocalizedString getLocalizedTitle() {
    return LocalizedString.ofUnlocalized("Payable");
  }

  @Override
  public LocalizedString getLocalizedDescription() {
    return LocalizedString.ofUnlocalized("Payable");
  }

  @Override
  public String getLocalizedDateRange() {
    return Optional.ofNullable(getPayPeriod())
        .map(
            pp ->
                DateRange.of(pp.getEffectiveDate(), pp.getEffectiveDate())
                    .getLocalizedDescription(LocaleContextHolder.getLocale()))
        .orElse(null);
  }

  @Override
  public LocalDate getEffectiveDate() {
    return Optional.ofNullable(getPayPeriod()).map(pp -> pp.getEffectiveDate()).orElse(null);
  }
}
