package com.everee.api.payable.model;

import com.everee.api.money.MoneyMapper;
import com.everee.api.util.DateUtil;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    uses = MoneyMapper.class)
public interface PayableMapper {

  PayableMapper MAPPER = Mappers.getMapper(PayableMapper.class);

  @Mapping(target = "id", source = "externalId")
  @Mapping(target = "type", source = "externalType")
  @Mapping(target = "earningTimestamp", source = "externalTimestamp")
  PayableDTO payableToDto(Payable payable);

  PrivPayableDTO toPrivDto(Payable payable);

  @Mapping(target = "externalType", source = "type")
  @Mapping(target = "externalTimestamp", source = "earningTimestamp")
  Payable payableDtoToPayable(CreatePayableDTO payableDTO);

  @Mapping(target = "externalType", source = "type")
  @Mapping(target = "externalTimestamp", source = "earningTimestamp")
  Payable updatePayableFromDto(UpdatePayableDTO payableDto, @MappingTarget Payable payable);

  default LocalDateTime map(Instant value) {
    if (value == null) return null;
    return LocalDateTime.ofInstant(value, DateUtil.UTC_ZONE_ID);
  }

  default Instant map(LocalDateTime value) {
    if (value == null) return null;
    return value.toInstant(ZoneOffset.UTC);
  }
}
