package com.everee.api.payable;

import static com.everee.api.payable.model.PayableMapper.MAPPER;

import com.everee.api.auth.annotation.EvereeAdminAccess;
import com.everee.api.payable.lookup.PayableLookup;
import com.everee.api.payable.lookup.PayableLookupService;
import com.everee.api.payable.model.PrivPayableDTO;
import java.util.Collection;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@EvereeAdminAccess
@RestController("PrivatePayableController")
@RequestMapping("/api-private/v1/payables")
@RequiredArgsConstructor
public class PrivPayableController {
  private final PayableLookupService payableLookupService;
  private final PayableService payableService;

  @GetMapping
  public Page<PrivPayableDTO> listPayables(PayableLookup lookup, Pageable pageable) {
    return payableLookupService.listAll(lookup, pageable).map(MAPPER::toPrivDto);
  }

  @PostMapping("/assign-payment-id")
  public Collection<PrivPayableDTO> assignPaymentId(@RequestBody AssignPaymentIdParams request) {
    return payableService.assignPaymentId(request.getPaymentId(), request.getPayableLookup());
  }
}
