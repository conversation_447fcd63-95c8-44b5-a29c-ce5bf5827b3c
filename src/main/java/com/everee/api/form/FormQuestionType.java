package com.everee.api.form;

import java.util.EnumSet;

public enum FormQuestionType {
  FILE_UPLOAD,
  NUMBER,
  OUTPUT_ONLY,
  OUTPUT_ONLY_ALERT,
  RADIO,
  TEXT,
  SIGNATURE,
  SINGLE_CHOICE,
  //  MULTIPLE_CHOICE,
  CHECKBOX,
  MONE<PERSON>,
  PERCENTAGE,
  DATE,
  WORKER_DOCUMENT,
  COMPANY_DOCUMENT_PREVIEW,
  TEXT_AREA;

  public static final EnumSet NON_ANSWERABLE =
      EnumSet.of(OUTPUT_ONLY, COMPANY_DOCUMENT_PREVIEW, OUTPUT_ONLY_ALERT);
}
