package com.everee.api.form.response.types;

import static com.everee.api.form.FormQuestionType.SIGNATURE;

import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.form.FormQuestion;
import com.everee.api.form.FormQuestionType;
import com.everee.api.form.response.FormQuestionResponse;
import com.everee.api.form.response.FormResponse;
import com.everee.api.form.response.FormResponseContext;
import com.everee.api.form.response.FormResponseException;
import com.everee.api.form.response.FormResponseSignature;
import com.everee.api.form.response.FormResponseSignatureType;
import com.everee.api.form.response.FormSignatureState;
import com.everee.api.form.response.lookup.FormResponseSignatureRepository;
import com.everee.api.form.response.params.FormQuestionResponseParams;
import com.everee.api.form.response.params.FormResponseSignatureParams;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SignatureQuestionTypeResponseDelegate extends BaseFormQuestionTypeResponseDelegate {
  private final DetailedEmployeeRepository detailedEmployeeRepository;
  private final FormResponseSignatureRepository formResponseSignatureRepository;

  @Override
  protected void applyQuestionParams(
      FormQuestion formQuestion,
      FormQuestionResponse questionResponse,
      FormQuestionResponseParams questionResponseParams,
      FormResponseContext formResponseContext) {
    if (questionResponseParams.getFormSignature() != null) {
      var formResponse = formResponseContext.getFormResponse();

      var uploadedSignature = questionResponseParams.getFormSignature();
      var formSignature =
          formResponse.getOrCreateFormResponseSignature(
              questionResponseParams.getFormSignatureId(), uploadedSignature.getSignatureType());
      formSignature.setFormResponseId(formResponse.getId());
      formSignature.setElectronicSignatureAgreedAt(
          uploadedSignature.getElectronicSignatureAgreedAt());

      formSignature.setSignatureType(uploadedSignature.getSignatureType());

      var originalSignatureText = formSignature.getSignatureText();
      formSignature.setSignatureText(uploadedSignature.getSignatureText());
      formSignature.setSignatureAcknowledged(
          Boolean.TRUE.equals(uploadedSignature.getSignatureAcknowledged()));

      if ((StringUtils.isNotBlank(uploadedSignature.getSignatureText())
              && !Objects.equals(uploadedSignature.getSignatureText(), originalSignatureText))
          || Boolean.TRUE.equals(uploadedSignature.getSignatureAcknowledged())) {
        formSignature.setIpAddress(formResponseContext.getIpAddress());
        formSignature.setSignedAt(
            Optional.ofNullable(uploadedSignature.getSignedAt())
                .orElse(formResponseContext.getNow()));
        formSignature.setSignedByUserId(formResponseContext.getUserId());
      }

      moveSignatureState(
          formSignature,
          uploadedSignature,
          formResponse.getEmployeeId(),
          formResponseContext.getUserId());

      if (formResponseContext.isDoSave()) {
        formResponseSignatureRepository.save(formSignature);
      }
      questionResponse.setFormSignatureId(formSignature.getId());
    } else {
      questionResponse.setFormSignatureId(questionResponseParams.getFormSignatureId());
    }
  }

  private void moveSignatureState(
      FormResponseSignature formSignature,
      FormResponseSignatureParams uploadedSignature,
      Long formEmployeeId,
      Long currentUserId) {
    var currentState = formSignature.getState();
    var nextState = uploadedSignature.getSignatureState();

    if (formSignature.isSigned()
        && (nextState != FormSignatureState.EMPLOYEE_DECLINED_AND_ADMIN_SIGNED
            && nextState != FormSignatureState.EMPLOYEE_AGREED_AND_SIGNED)) {
      if (formSignature.getSignatureType() == FormResponseSignatureType.COMPANY_FORM
          || detailedEmployeeRepository
              .findByUserIdAndId(currentUserId, formEmployeeId)
              .isPresent()) {
        nextState = FormSignatureState.EMPLOYEE_AGREED_AND_SIGNED;
      } else {
        nextState = FormSignatureState.EMPLOYEE_DECLINED_AND_ADMIN_SIGNED;
      }
    }
    switch (currentState) {
      case NONE:
        // can move from none to any state
        break;
      case EMPLOYEE_AGREED: // fall through
      case EMPLOYEE_DECLINED:
        if (FormSignatureState.NONE.equals(nextState)) {
          throw new FormResponseException(
              uploadedSignature.getFormResponseId(),
              "Invalid signature state (current: " + currentState + ", next: " + nextState + ")");
        }
        break;
      case EMPLOYEE_AGREED_AND_SIGNED:
        if (!FormSignatureState.EMPLOYEE_AGREED.equals(nextState)
            && !FormSignatureState.EMPLOYEE_AGREED_AND_SIGNED.equals(nextState)) {
          throw new FormResponseException(
              uploadedSignature.getFormResponseId(),
              "Invalid signature state (current: " + currentState + ", next: " + nextState + ")");
        }
        break;
      case EMPLOYEE_DECLINED_AND_ADMIN_SIGNED:
        if (!FormSignatureState.EMPLOYEE_DECLINED.equals(nextState)
            && !FormSignatureState.EMPLOYEE_DECLINED_AND_ADMIN_SIGNED.equals(nextState)
            && !FormSignatureState.EMPLOYEE_AGREED_AND_SIGNED.equals(nextState)) {
          throw new FormResponseException(
              uploadedSignature.getFormResponseId(),
              "Invalid signature state (current: " + currentState + ", next: " + nextState + ")");
        }
        break;
      default:
        break;
    }

    formSignature.setState(nextState);
  }

  @Override
  public boolean isResponded(FormResponse formResponse, FormQuestionResponse questionResponse) {
    return questionResponse.getFormSignatureId() != null
        && formResponse.getSignatures().stream()
            .anyMatch(
                s ->
                    Objects.equals(s.getId(), questionResponse.getFormSignatureId())
                        && StringUtils.isNotBlank(s.getSignatureText()));
  }

  @Override
  public boolean areEqual(
      Optional<FormQuestionResponse> questionResponseFirst,
      Optional<FormQuestionResponse> questionResponseSecond,
      FormResponse formResponse) {
    if (questionResponseFirst.isPresent() != questionResponseSecond.isPresent()
        || (questionResponseSecond.isPresent()
            && !areEqual(
                formResponse.getFormResponseSignature(
                    questionResponseFirst.get().getFormSignatureId(), null),
                formResponse.getFormResponseSignature(
                    questionResponseSecond.get().getFormSignatureId(), null)))) {
      return false;
    }

    return true;
  }

  public boolean areEqual(
      Optional<FormResponseSignature> signatureFirst,
      Optional<FormResponseSignature> signatureSecond) {
    if (signatureFirst.isPresent() != signatureSecond.isPresent()
        || (signatureSecond.isPresent()
            && (!Objects.equals(
                    signatureFirst.get().getSignatureText(),
                    signatureSecond.get().getSignatureText())
                || signatureFirst.get().isSignatureAcknowledged()
                    != signatureSecond.get().isSignatureAcknowledged()))) {
      return false;
    }

    return true;
  }

  @Override
  public FormQuestionType getFormQuestionType() {
    return SIGNATURE;
  }
}
