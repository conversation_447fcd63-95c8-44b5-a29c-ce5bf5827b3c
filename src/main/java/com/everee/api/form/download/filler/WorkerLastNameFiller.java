package com.everee.api.form.download.filler;

import com.everee.api.form.download.FormFillingMappingContext;
import com.everee.api.form.download.FormPdfFieldMapping;
import com.everee.api.form.download.PdfFieldFiller;
import com.everee.api.form.download.PdfFillerFieldDataType;
import lombok.Getter;
import org.springframework.stereotype.Component;

@Component
public class WorkerLastNameFiller implements PdfFieldFiller {
  @Getter private PdfFillerFieldDataType fieldType = PdfFillerFieldDataType.WORKER_LAST_NAME;

  @Override
  public String getValue(FormPdfFieldMapping mapping, FormFillingMappingContext context) {
    return context.getEmployee().getLastName();
  }
}
