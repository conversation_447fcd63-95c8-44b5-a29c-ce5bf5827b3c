package com.everee.api.smartsearch;

import com.everee.api.auth.util.AuthorizationCheckService;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.Employee;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.tax.state.State;
import com.everee.api.util.SetUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SmartSearchService {
  private final DetailedEmployeeLookupService employeeLookupService;
  private final AuthorizationCheckService authorizationCheckService;
  private final CompanyService companyService;

  public List<SmartSearchItem> search(Long companyId, String queryString) {
    List<SmartSearchItem> smartSearchItems = new ArrayList<>();
    smartSearchItems.addAll(getSmartSearchEmployees(companyId));
    smartSearchItems.addAll(getSmartSearchCompanies());

    return smartSearchItems.stream()
        .filter(
            item ->
                item.getFilterTerms().stream()
                    .anyMatch(
                        filterTerm -> filterTerm.toLowerCase().contains(queryString.toLowerCase())))
        .collect(Collectors.toList());
  }

  private List<SmartSearchItem> getSmartSearchEmployees(Long companyId) {
    return employeeLookupService
        .listAll(
            new EmployeeLookup()
                .withCompanyIds(SetUtils.toSetOrNull(companyId))
                .withPhaseLookup(PhaseLookup.unphased()),
            Pageable.unpaged())
        .stream()
        .map(this::createSmartSearchEmployee)
        .collect(Collectors.toList());
  }

  private SmartSearchItem createSmartSearchEmployee(Employee employee) {
    SmartSearchItem smartSearchEmployee = new SmartSearchItem();

    smartSearchEmployee.setType(SmartSearchItemType.EMPLOYEE);
    smartSearchEmployee.setDisplayValue(employee.getDisplayFullName());
    smartSearchEmployee.setAdditionalLabel(employee.getCurrentPhase().toString());
    smartSearchEmployee.setIdentifier(employee.getId().toString());

    smartSearchEmployee.setFilterTerms(
        List.of(
            employee.getFirstName(),
            employee.getLastName(),
            employee.getFullName(),
            employee.getDisplayFullName()));

    return smartSearchEmployee;
  }

  private List<SmartSearchItem> getSmartSearchCompanies() {
    if (authorizationCheckService.isEvereeAdmin()) {
      return companyService.listCompanies(Pageable.unpaged(), PhaseLookup.unphased(), null, null)
          .stream()
          .map(this::createCompanySmartSearchItem)
          .collect(Collectors.toList());
    }

    return companyService
        .listCompanies(
            Pageable.unpaged(),
            PhaseLookup.unphased(),
            authorizationCheckService.getAuthorizedCompanyIds(),
            null)
        .stream()
        .map(this::createCompanySmartSearchItem)
        .collect(Collectors.toList());
  }

  private SmartSearchItem createCompanySmartSearchItem(DetailedCompany company) {
    SmartSearchItem smartSearchCompany = new SmartSearchItem();

    smartSearchCompany.setType(SmartSearchItemType.COMPANY);
    smartSearchCompany.setDisplayValue(company.getDisplayName());
    smartSearchCompany.setAdditionalLabel(company.getCurrentPhase().toString());
    smartSearchCompany.setIdentifier(company.getId().toString());

    smartSearchCompany.setFilterTerms(
        List.of(
            company.getDisplayName(), State.UT.toString(), company.getPhone(), company.getEmail()));

    return smartSearchCompany;
  }
}
