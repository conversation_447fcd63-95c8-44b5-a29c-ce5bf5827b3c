package com.everee.api.phase.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class UnableToRemoveInitialRecordException extends LocalizedRuntimeException {

  public UnableToRemoveInitialRecordException() {
    super("phase.UnableToRemoveInitialRecordException.message");
  }
}
