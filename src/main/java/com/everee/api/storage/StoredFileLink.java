package com.everee.api.storage;

import static java.time.temporal.ChronoUnit.SECONDS;

import java.net.URL;
import java.time.Instant;
import lombok.Value;

@Value
public class StoredFileLink {
  URL url;
  Instant expiresAt;

  @Deprecated(forRemoval = true)
  URL getLink() {
    return url;
  }

  @Deprecated(forRemoval = true)
  Long getExpiresInSeconds() {
    return Instant.now().until(expiresAt, SECONDS);
  }
}
