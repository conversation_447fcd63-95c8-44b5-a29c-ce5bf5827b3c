package com.everee.api.onboarding;

import com.everee.api.company.ExternalBenefitsProvider;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeduction;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionAmountType;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.model.BaseModel;
import com.everee.api.money.Money;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Entity
@Data
@JsonIgnoreProperties(value = {"handler", "hibernateLazyInitializer"})
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Deprecated(forRemoval = true)
public class OnboardingRequestEmployeeContributionDeduction extends BaseModel {

  @Column private Long onboardingRequestId;

  @Enumerated(EnumType.STRING)
  private EmployeeContributionDeductionType type;

  private String description;

  @Enumerated(EnumType.STRING)
  private EmployeeContributionDeductionAmountType amountType;

  private Money amountEE = Money.ZERO;

  private Money amountER = Money.ZERO;

  private BigDecimal percentEE;

  private BigDecimal percentER;

  @Enumerated(EnumType.STRING)
  private ExternalBenefitsProvider managedByProvider;

  public EmployeeContributionDeduction toEmployeeContributionDeduction() {
    final EmployeeContributionDeduction employeeContributionDeduction =
        new EmployeeContributionDeduction();
    employeeContributionDeduction.setType(type);
    employeeContributionDeduction.setDescription(description);
    employeeContributionDeduction.setAmountType(amountType);
    employeeContributionDeduction.setAmountEE(amountEE);
    employeeContributionDeduction.setAmountER(amountER);
    employeeContributionDeduction.setPercentEE(percentEE);
    employeeContributionDeduction.setPercentER(percentER);
    employeeContributionDeduction.setManagedByProvider(managedByProvider);

    return employeeContributionDeduction;
  }
}
