package com.everee.api.worker.event;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.event.ResourceEvent;

public class WorkerEndDateChangedEvent extends ResourceEvent {

  public WorkerEndDateChangedEvent(DetailedEmployee employee) {
    super(employee.getCompanyId(), employee);
  }

  @Override
  public String getResourceId() {
    return getEmployee().getId().toString();
  }

  @Override
  public String getResourceName() {
    return "worker";
  }

  @Override
  public String getActionName() {
    return "end-date-changed";
  }

  public DetailedEmployee getEmployee() {
    return (DetailedEmployee) source;
  }
}
