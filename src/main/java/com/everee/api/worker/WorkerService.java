package com.everee.api.worker;

import static java.util.stream.Collectors.toSet;
import static org.springframework.util.StringUtils.hasText;

import com.everee.api.approvalgroup.ApprovalGroupRepository;
import com.everee.api.bankaccount.BankAccountRuleType;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.DetailedEmployeeRepository;
import com.everee.api.employee.EmployeeFileService;
import com.everee.api.employee.WorkerIdentifiers;
import com.everee.api.employee.CoreEmployeeRepository;
import com.everee.api.employee.event.WorkerUpdatedContactInfoEvent;
import com.everee.api.employee.event.WorkerUpdatedPersonalInfoEvent;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.employeePosition.EmployeePositionService;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.file.DocumentType;
import com.everee.api.form.employee.EmployeeFormService;
import com.everee.api.i18n.LocalizedRuntimeException;
import com.everee.api.keycloak.event.UserUpdatedEvent;
import com.everee.api.model.EmploymentType;
import com.everee.api.paycard.service.EmployeePaymentPreferenceService;
import com.everee.api.payment.PaymentCalculationService;
import com.everee.api.payment.PaymentMethod;
import com.everee.api.payperiod.*;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.phase.PhaseSchedule;
import com.everee.api.time.WorkerLocalTimeService;
import com.everee.api.timeoff.accrualrate.PtoAccrualRate;
import com.everee.api.timeoff.balance.*;
import com.everee.api.user.*;
import com.everee.api.user.address.HomeAddress;
import com.everee.api.user.address.HomeAddressService;
import com.everee.api.user.bankaccount.UserBankAccount;
import com.everee.api.user.bankaccount.UserBankAccountService;
import com.everee.api.user.picture.UserPictureService;
import com.everee.api.worker.event.WorkerApprovalGroupChangedEvent;
import com.everee.api.worker.event.WorkerHireDateChangedEvent;
import com.everee.api.worker.exception.*;
import com.everee.api.worker.lookup.WorkerLookup;
import com.everee.api.worker.lookup.WorkerLookupConverter;
import com.everee.api.worker.lookup.WorkersIdentifiersLookup;
import com.everee.api.worker.payload.ManagementChainPayload;
import com.everee.api.worker.updateparams.*;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocation;
import com.everee.api.worker.workerWorkLocation.WorkerLegalWorkLocationService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.annotation.Nullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkerService {
  private final ApplicationEventPublisher eventPublisher;
  private final ApprovalGroupRepository approvalGroupRepository;
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final DetailedEmployeeRepository detailedEmployeeRepository;
  private final DetailedUserRepository userRepository;
  private final EmployeeFileService employeeFileService;
  private final EmployeeFormService employeeFormService;
  private final EmployeePaymentPreferenceService employeePaymentPreferenceService;
  private final EmployeePositionService employeePositionService;
  private final HomeAddressService homeAddressService;
  private final PayPeriodPreferenceOptionService payPreferenceOptionService;
  private final PayPeriodPreferenceRepository payPeriodPreferenceRepository;
  private final PayPeriodPreferenceService payPeriodPreferenceService;
  private final PayPeriodRepository payPeriodRepository;
  private final PayPeriodService payPeriodService;
  private final PaymentCalculationService paymentCalculationService;
  private final PtoBalanceAdjustmentService ptoBalanceAdjustmentService;
  private final PtoBalanceLookupService ptoBalanceLookupService;
  private final TinVerificationStateService tinVerificationStateService;
  private final UserBankAccountService userBankAccountService;
  private final UserPictureService userPictureService;
  private final WorkerHireDateAdjustmentValidator workerHireDateAdjustmentValidator;
  private final WorkerLegalWorkLocationService legalWorkLocationService;
  private final WorkerLocalTimeService workerLocalTimeService;
  private final CompanyService companyService;
  private final CoreEmployeeRepository coreEmployeeRepository;

  @Transactional(readOnly = true)
  public Worker getWorker(String workerId, DetailedCompany company) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var user = employee.getUser();

    return getWorker(user, employee, company);
  }

    @Transactional(readOnly = true)
    public Page<Worker> getWorkers(WorkerLookup workerLookup, PhaseLookup phaseLookup, Pageable pageable)
    {
        var employeeLookup = WorkerLookupConverter.toEmployeeLookup(workerLookup, phaseLookup, new DetailedCompany());
        employeeLookup.setSkipApplyAuthorization(true);
        var listedEmployees = detailedEmployeeLookupService.listEmployeesUnsafe(employeeLookup,pageable);
        var listedEmployeesMap = listedEmployees.stream().collect(Collectors.toMap(DetailedEmployee::getId, detailedEmployee -> detailedEmployee));

        var ptoLookup = new PtoBalanceLookup().withEmployeeIds(employeeLookup.getIds());
        var ptoBalancesPerEmployee = ptoBalanceLookupService.findAll(ptoLookup);
        var ptoBalanceMap = ptoBalancesPerEmployee.stream().collect(Collectors.toMap(PtoBalance::getEmployeeId, ptoBalance -> ptoBalance));

        var companiesIds = listedEmployees.stream().map(DetailedEmployee::getCompanyId).collect(Collectors.toSet());
        var companiesDetails = companyService.listCompanies(pageable, phaseLookup,companiesIds,null );
        var companiesDetailsMap = companiesDetails.stream().collect(Collectors.toMap(DetailedCompany::getId, detailedCompany -> detailedCompany));

        var localDatePerEmployee = workerLocalTimeService.getLocalDatePerEmployee(listedEmployees, companiesDetailsMap);

        var localDatePerUser = getLocalDatePerUser(listedEmployeesMap, localDatePerEmployee);
        var userHomeAddresses = homeAddressService.getAddressPhaseSchedulePerUser(localDatePerUser);

        var positionPhaseSchedulePerEmployee = employeePositionService.getPositionPhaseSchedulePerEmployee(listedEmployees, localDatePerEmployee);

        var legalWorkLocationsPerEmployee = legalWorkLocationService.getLegalWorkLocationPhaseSchedulePerEmployee(listedEmployees,localDatePerEmployee);

        return getWorkers(listedEmployees, userHomeAddresses, positionPhaseSchedulePerEmployee, legalWorkLocationsPerEmployee, ptoBalanceMap);
    }

    public Page<Worker> getWorkers(Page<DetailedEmployee> detailedEmployees,
                                   Map<Long, PhaseSchedule<HomeAddress>> userHomeAddresses,
                                   Map<Long, PhaseSchedule<EmployeePosition>> positionPhaseSchedulePerEmployee,
                                   Map<Long, PhaseSchedule<WorkerLegalWorkLocation>> legalWorkLocationMap,
                                   Map<Long, PtoBalance> ptoBalancesPerEmployee)
    {
        return detailedEmployees.map(detailedEmployee -> getWorker
                (
                        detailedEmployee.getUser(),
                        detailedEmployee,
                        userHomeAddresses.get(detailedEmployee.getUserId()),
                        positionPhaseSchedulePerEmployee.get(detailedEmployee.getId()),
                        legalWorkLocationMap.get(detailedEmployee.getId()),
                        ptoBalancesPerEmployee.get(detailedEmployee.getId())
                ));
    }

    @Transactional(readOnly = true)
    public Worker getWorker(DetailedUser user, DetailedEmployee employee, PhaseSchedule<HomeAddress> homeAddressPhaseSchedule, PhaseSchedule<EmployeePosition> positionPhaseSchedule, PhaseSchedule<WorkerLegalWorkLocation> legalWorkLocation, PtoBalance ptoBalance)
    {
        var worker = Worker.from(employee);

        worker.setTaxpayerIdentifier(user.getTaxpayerIdentifier());
        worker.setHomeAddress(homeAddressPhaseSchedule);
        worker.setBankAccounts(user.getBankAccounts());
        worker.setLegalWorkAddress(legalWorkLocation);
        worker.setDateOfBirth(user.getDateOfBirth());
        worker.setPosition(positionPhaseSchedule);
        worker.setPtoBalance(ptoBalance);

        return worker;
    }

    @Transactional(readOnly = true)
  public Worker getWorker(DetailedUser user, DetailedEmployee employee, DetailedCompany company) {
    var ptoLookup = new PtoBalanceLookup().withEmployeeIds(Set.of(employee.getId()));
    var ptoBalance = ptoBalanceLookupService.findOne(ptoLookup).orElse(new PtoBalance());
    var nowDate =
        workerLocalTimeService.zonedTimestamp(employee, ZonedDateTime.now()).toLocalDate();
    var approvalGroup =
        Optional.ofNullable(employee.getApprovalGroupId())
            .flatMap(id -> approvalGroupRepository.findByIdAndCompanyId(id, company.getId()))
            .orElse(null);

    var worker = Worker.from(employee);
    worker.setTaxpayerIdentifier(user.getTaxpayerIdentifier());
    worker.setDateOfBirth(user.getDateOfBirth());
    worker.setPosition(employeePositionService.getPositionPhaseSchedule(employee, nowDate));
    worker.setHomeAddress(homeAddressService.getAddressPhaseSchedule(user, nowDate));
    worker.setBankAccounts(user.getBankAccounts());
    worker.setPtoBalance(ptoBalance);
    worker.setApprovalGroup(approvalGroup);
    worker.setLegalWorkAddress(
        legalWorkLocationService.getDecoratedLegalWorkLocationPhaseSchedule(
            user, employee, nowDate));
    worker.setPayPeriodPreferenceOptions(
        payPreferenceOptionService.listPayPeriodPreferenceOptions(
            company, employee, company.getNowDate()));

    return worker;
  }

    public List<WorkerIdentifiers> getWorkersIdentifiers(WorkersIdentifiersLookup workerLookup) {
        var companyIds = workerLookup.getCompanyIds() == null
                ? null
                : new ArrayList<>(workerLookup.getCompanyIds());
        var workerIds = workerLookup.getWorkerIds() == null
                ? null
                : new ArrayList<>(workerLookup.getWorkerIds());

        return coreEmployeeRepository.getEmployeeIdentifiers(
                companyIds,
                workerIds);
    }

    @Transactional(readOnly = true)
    public Map<Long, LocalDate> getWorkersLocalDate(WorkerLookup workerLookup, PhaseLookup phaseLookup, Pageable pageable)
    {
        var employeeLookup = WorkerLookupConverter.toEmployeeLookup(workerLookup, phaseLookup, new DetailedCompany());
        employeeLookup.setSkipApplyAuthorization(true);
        var listedEmployees = detailedEmployeeLookupService.listEmployeesUnsafe(employeeLookup,pageable);

        var companiesIds = listedEmployees.stream().map(DetailedEmployee::getCompanyId).collect(Collectors.toSet());
        var companiesDetails = companyService.listCompanies(pageable, phaseLookup,companiesIds,null );
        var companiesDetailsMap = companiesDetails.stream().collect(Collectors.toMap(DetailedCompany::getId, detailedCompany -> detailedCompany));

        return workerLocalTimeService.getLocalDatePerEmployee(listedEmployees, companiesDetailsMap);
    }

    public Map<DetailedUser, LocalDate> getLocalDatePerUser(Map<Long, DetailedEmployee> listedEmployeesMap, Map<Long, LocalDate> localDatePerEmployee)
    {
        Map<DetailedUser, LocalDate> localDatePerUser = new HashMap<>();

        for (Long employeeId : listedEmployeesMap.keySet())
        {
            var employee = listedEmployeesMap.get(employeeId);
            var localDate = localDatePerEmployee.get(employeeId);

            if (localDate != null) {
                localDatePerUser.put(employee.getUser(), localDate);
            }
        }

        return localDatePerUser;
    }

  @Transactional
  public Worker updatePersonalInfo(
      String workerId, DetailedCompany company, WorkerPersonalInfoParamsForUpdate params) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    return updatePersonalInfo(employee, company, params);
  }

  @Transactional
  public Worker updatePersonalInfo(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerPersonalInfoParamsForUpdate params) {
    var user = employee.getUser();
    var additionalInfo = user.getUserAdditionalInfo();

    if (additionalInfo == null) {
      additionalInfo = new UserAdditionalInfo();
    }

    var worker = getWorker(user, employee, company);

    additionalInfo.setUserId(user.getId());
    additionalInfo.setShirtSize(StringUtils.trimToNull(params.getShirtSize()));
    additionalInfo.setPartnerShirtSize(StringUtils.trimToNull(params.getPartnerShirtSize()));
    additionalInfo.setDietaryRestrictions(StringUtils.trimToNull(params.getDietaryRestrictions()));
    additionalInfo.setPreferredName(StringUtils.trimToNull(params.getPreferredName()));

    user.setTinVerificationState(
        tinVerificationStateService.getStateForUpdatedWorker(params, user));
    user.setFirstName(params.getFirstName());
    user.setMiddleName(params.getMiddleName());
    user.setLastName(params.getLastName());
    user.setUserAdditionalInfo(additionalInfo);

    if (params.getDateOfBirth() != null) {
      user.setDateOfBirth(params.getDateOfBirth());
    }

    eventPublisher.publishEvent(new UserUpdatedEvent(user));
    user.getEmployees()
        .forEach(ee -> eventPublisher.publishEvent(new WorkerUpdatedPersonalInfoEvent(ee)));

    worker.setFirstName(user.getFirstName());
    worker.setMiddleName(user.getMiddleName());
    worker.setLastName(user.getLastName());
    worker.setTaxpayerIdentifier(user.getTaxpayerIdentifier());
    worker.setDateOfBirth(user.getDateOfBirth());
    worker.setOnboardingComplete(employee.isOnboardingComplete());
    worker.setShirtSize(user.getUserAdditionalInfo().getShirtSize());
    worker.setPartnerShirtSize(user.getUserAdditionalInfo().getPartnerShirtSize());
    worker.setDietaryRestrictions(user.getUserAdditionalInfo().getDietaryRestrictions());
    worker.setPreferredName(user.getUserAdditionalInfo().getPreferredName());

    return worker;
  }

  @Transactional
  public Worker updateContactInfo(
      String workerId, DetailedCompany company, WorkerContactInfoParamsForUpdate params) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    return updateContactInfo(employee, company, params);
  }

  // TANG-650
  public ValidateContactInfoResponse validateContactInfo(
      DetailedCompany company, WorkerContactInfoParamsForUpdate params) {

    var validPhoneNumber = false;
    var validEmail = false;

    // validate email
    if (!Objects.isNull(params.getEmail())) {
      validEmail =
          !userRepository.existsByEmailEqualsOrUnverifiedEmailEquals(
              params.getEmail(), params.getEmail());
    }

    // validate phoneNumber
    if (!Objects.isNull(params.getPhoneNumber())) {
      validPhoneNumber =
          !userRepository.existsByPhoneNumberEqualsOrUnverifiedPhoneNumberEquals(
              params.getPhoneNumber(), params.getPhoneNumber());
    }

    // if workerId provided then check if the Email/PhoneNumber not changed
    if (!Objects.isNull(params.getWorkerId()) && (!validEmail || !validPhoneNumber)) {
      var employee =
          detailedEmployeeLookupService.getEmployee(params.getWorkerId(), company.getId());
      var user = employee.getUser();

      if (!Objects.isNull(params.getEmail()) && !validEmail) {
        validEmail =
            params.getEmail().equals(user.getEmail())
                || params.getEmail().equals(user.getUnverifiedEmail());
      }

      if (!Objects.isNull(params.getPhoneNumber()) && !validPhoneNumber) {
        validPhoneNumber =
            params.getPhoneNumber().equals(user.getPhoneNumber())
                || params.getPhoneNumber().equals(user.getUnverifiedPhoneNumber());
      }
    }

    var validateContactInfoResponse = new ValidateContactInfoResponse();
    validateContactInfoResponse.setValidEmail(validEmail);
    validateContactInfoResponse.setValidPhoneNumber(validPhoneNumber);

    return validateContactInfoResponse;
  }

  @Transactional
  public Worker updateContactInfo(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerContactInfoParamsForUpdate params) {
    var user = employee.getUser();
    var worker = getWorker(user, employee, company);
    user.getEmployees()
        .forEach(ee -> eventPublisher.publishEvent(new WorkerUpdatedContactInfoEvent(ee)));

    var onboarding = employee.getOnboarding();
    if (onboarding.isClaimed()) {
      user.setEmail(params.getEmail());
      user.setPhoneNumber(params.getPhoneNumber());

    } else {
      user.setUnverifiedEmail(params.getEmail());
      user.setUnverifiedPhoneNumber(params.getPhoneNumber());
    }

    eventPublisher.publishEvent(new UserUpdatedEvent(user));

    worker.setEmail(params.getEmail());
    worker.setPhoneNumber(params.getPhoneNumber());
    worker.setOnboardingComplete(employee.isOnboardingComplete());

    return worker;
  }

  @Transactional
  public Worker updateWorkerInfo(
      String workerId, DetailedCompany company, WorkerInfoParamsForUpdate params) {
    var worker = getWorker(workerId, company);
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    validateExternalWorkerId(company.getId(), params.getExternalWorkerId());

    employee.setExternalWorkerId(params.getExternalWorkerId());
    worker.setExternalWorkerId(params.getExternalWorkerId());

    return worker;
  }

  @Transactional
  public Worker adjustWorkerHireDate(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull LocalDate newHireDate) {
    var worker = getWorker(employee.getUser(), employee, company);
    var prevHireDate = employee.getStartDate();

    if (prevHireDate.equals(newHireDate)) return worker;

    workerHireDateAdjustmentValidator.validateHireDateAdjustmentIsPermitted(employee, newHireDate);

    // IMPORTANT: the new startDate needs to be set before calling the phased records startDate
    //  adjusters
    employee.setStartDate(newHireDate);

    // sync the initial Position record
    worker.setPosition(employeePositionService.adjustInitialEmployeePositionStartDate(employee));

    // sync the initial Legal Work Location record
    worker.setLegalWorkAddress(
        legalWorkLocationService.adjustInitialWorkerLegalWorkLocationStartDate(
            employee.getUser(), employee));

    // sync the initial Home Address record
    worker.setHomeAddress(
        homeAddressService.adjustInitialHomeAddressStartDate(
            employee.getUser(),
            prevHireDate,
            newHireDate,
            company.getWorkweekConfig().getTimezone()));

    // sync initial Form records
    employeeFormService.adjustInitialFormStartDates(employee, prevHireDate, newHireDate);

    // remove all non-IMPORT pay periods
    payPeriodRepository.findByCompanyIdAndEmployeeId(company.getId(), employee.getId()).stream()
        .map(PayPeriod::getId)
        .forEach(payPeriodRepository::deleteById);

    // sync Pay Period Preference record
    var preferences =
        payPeriodPreferenceRepository.findByCompanyIdAndEmployeeId(
            company.getId(), employee.getId());
    var selectedPeriodType =
        preferences.stream()
            .filter(p -> !p.getPayPeriodType().equals(PayPeriodType.CUSTOM))
            .min(Comparator.comparing(PayPeriodPreference::getStartDate))
            .map(PayPeriodPreference::getPayPeriodType)
            .orElseGet(
                () ->
                    payPeriodPreferenceService
                        .getCompanyActiveOrUpcomingPreference(company.getId(), company.getNowDate())
                        .getPayPeriodType());
    try {
      payPeriodPreferenceService.tryCreateBackdatedPayPeriodPreference(
          company, employee, selectedPeriodType);

    } catch (PayPeriodException e) {
      throw new IllegalStateException(
          "Unable to adjust hire date due to pay period preference issue", e);
    }

    worker.setPayPeriodPreferenceOptions(
        payPreferenceOptionService.listPayPeriodPreferenceOptions(
            company, employee, company.getNowDate()));

    // create pay periods and payments
    var now = LocalDate.now();
    if (newHireDate.isBefore(now) || newHireDate.isEqual(now)) {
      var payPeriodIds = new ArrayList<Long>();
      newHireDate
          .datesUntil(now.plusDays(1))
          .forEach(
              date -> {
                try {
                  // create pay period for each date
                  var payPeriod = payPeriodService.generatePayPeriod(employee, date);
                  if (payPeriod != null && payPeriod.getId() != null) {
                    payPeriodIds.add(payPeriod.getId());
                  }
                } catch (PayPeriodException e) {
                  log.error(
                      "Exception generating pay period for employeeId: {} and date: {}",
                      employee.getId(),
                      date,
                      e);
                }
              });

      // calculate payments for the pay periods
      if (!payPeriodIds.isEmpty()) {
        paymentCalculationService.triggerPayPeriodsCalculation(payPeriodIds);
      }
    }

    eventPublisher.publishEvent(new WorkerHireDateChangedEvent(employee));

    return worker;
  }

  @Transactional
  public Worker updatePosition(
      String workerId, DetailedCompany company, WorkerPositionParamsForUpdate params) {
    var companyId = company.getId();
    var employee = detailedEmployeeLookupService.getEmployee(workerId, companyId);

    return updatePosition(employee, company, params);
  }

  @Transactional
  public Worker updatePosition(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerPositionParamsForUpdate params) {
    var worker = getWorker(employee.getUser(), employee, company);

    updatePosition(worker, employee, company, params);

    return worker;
  }

  @Transactional
  public void updatePosition(
      @NonNull Worker worker,
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerPositionParamsForUpdate params) {
    if (employee.getEmploymentType() == EmploymentType.EMPLOYEE) {
      worker.setPosition(employeePositionService.updateEmployeePosition(employee, params));
    }
  }

  @Transactional
  public Worker updatePositions(
      @NonNull String workerId,
      @NonNull DetailedCompany company,
      @NonNull List<WorkerPositionsParamsForUpdate> params) {
    var companyId = company.getId();
    var employee = detailedEmployeeLookupService.getEmployee(workerId, companyId);
    var worker = getWorker(employee.getUser(), employee, company);

    if (employee.getEmploymentType() == EmploymentType.EMPLOYEE) {
      var allPhaseSchedules = employeePositionService.updateEmployeePositions(employee, params);

      for (var position : allPhaseSchedules) {
        worker.setPosition(position);
      }
    }

    return worker;
  }

  @Transactional
  public void deletePositionScheduledChange(String workerId, DetailedCompany company) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var nowDate =
        workerLocalTimeService.zonedTimestamp(employee, ZonedDateTime.now()).toLocalDate();
    var phaseSchedule = employee.getPositions().toPhasedSchedule(nowDate, null);

    var scheduledChange =
        Optional.ofNullable(phaseSchedule.getScheduledChange())
            .orElseThrow(NoScheduledWorkInformationChangeForWorkerException::new);

    employeePositionService.deleteEmployeePosition(employee, scheduledChange);
  }

  @Transactional
  public Worker updateLegalWorkLocation(
      String workerId, DetailedCompany company, WorkerLegalWorkLocationParamsForUpdate params) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    return updateLegalWorkLocation(employee, company, params);
  }

  @Transactional
  public Worker updateLegalWorkLocation(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerLegalWorkLocationParamsForUpdate params) {
    var worker = getWorker(employee.getUser(), employee, company);

    updateLegalWorkLocation(worker, employee, params);

    return worker;
  }

  @Transactional
  public void updateLegalWorkLocation(
      @NonNull Worker worker,
      @NonNull DetailedEmployee employee,
      @NonNull WorkerLegalWorkLocationParamsForUpdate params) {
    worker.setLegalWorkAddress(
        legalWorkLocationService.updateWorkerLegalWorkLocation(
            employee.getUser(), employee, params));
  }

  @Transactional
  public void deleteScheduledLegalLocationChange(String workerId, DetailedCompany company) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var user = employee.getUser();
    var phaseSchedule =
        employee.getLegalWorkLocations().toPhasedSchedule(company.getNowDate(), null);

    var scheduledChange =
        Optional.ofNullable(phaseSchedule.getScheduledChange())
            .orElseThrow(NoScheduledLegalLocationChangeForWorkerException::new);

    legalWorkLocationService.deleteWorkerLegalWorkLocation(user, employee, scheduledChange);
  }

  @Transactional
  public Worker updateApprovalGroup(
      @NonNull String workerId, @NonNull DetailedCompany company, Long approvalGroupId) {
    if (approvalGroupId == null) {
      return removeWorkerFromApprovalGroup(workerId, company);
    } else {
      return addWorkerToApprovalGroup(workerId, company, approvalGroupId);
    }
  }

  @Transactional
  public Worker updatePtoSettings(
      String workerId, DetailedCompany company, WorkerPtoSettingParamsForUpdate params) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    if (params.getHoursEarned() == null
        || params.getHoursWorked() == null
        || params.getHoursWorked() <= 0) {
      employee.setOverridePtoAccrualRate(null);
    } else {
      var accrualRate = new PtoAccrualRate(params.getHoursWorked(), params.getHoursEarned());
      employee.setOverridePtoAccrualRate(accrualRate);
    }

    employee.setOverridePtoMaxHours(params.getMaxPtoHours());

    return getWorker(workerId, company);
  }

  @Transactional
  public Worker updatePtoBalance(
      String workerId, DetailedCompany company, WorkerPtoBalanceAdjustmentParamsForUpdate params) {
    var worker = getWorker(workerId, company);
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    var ptoBalanceAdjustmentParams = new PtoBalanceAdjustmentParams();
    ptoBalanceAdjustmentParams.setEmployeeId(employee.getId());
    ptoBalanceAdjustmentParams.setType(params.getType());
    ptoBalanceAdjustmentParams.setHours(params.getHours());

    ptoBalanceAdjustmentService.setPtoCurrentHours(ptoBalanceAdjustmentParams);

    var ptoBalance = worker.getPtoBalance();
    ptoBalance.setAvailableHours(ptoBalance.getAvailableHours().add(params.getHours()));
    ptoBalance.setCurrentHours(ptoBalance.getCurrentHours().add(params.getHours()));

    return worker;
  }

  @Transactional
  public Worker updateHomeAddress(
      @NonNull String workerId,
      @NonNull DetailedCompany company,
      @NonNull HomeAddressParamsForUpdate params) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    return updateHomeAddress(employee, company, params);
  }

  @Transactional
  public Worker updateHomeAddress(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull HomeAddressParamsForUpdate params) {
    var worker = getWorker(employee.getUser(), employee, company);

    updateHomeAddress(worker, employee, company, params);

    return worker;
  }

  @Transactional
  public void updateHomeAddress(
      @NonNull Worker worker,
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull HomeAddressParamsForUpdate params) {
    var user = employee.getUser();

    worker.setHomeAddress(
        homeAddressService.scheduleHomeAddressChange(
            user, params, company.getWorkweekConfig().getTimezone()));
    worker.setOnboardingComplete(employee.isOnboardingComplete());
  }

  @Transactional
  public PhaseSchedule<HomeAddress> getScheduledAddressChange(@NonNull String workerId, @NonNull DetailedCompany company) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var user = employee.getUser();
    var date = workerLocalTimeService.zonedTimestamp(employee, ZonedDateTime.now()).toLocalDate();

    return homeAddressService.getAddressPhaseSchedule(user, date);
  }

  @Transactional
  public void deleteScheduledAddressChange(
      @NonNull String workerId, @NonNull DetailedCompany company) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    deleteScheduledAddressChange(employee, company);
  }

  @Transactional
  public void deleteScheduledAddressChange(
      @NonNull DetailedEmployee employee, @NonNull DetailedCompany company) {
    var user = employee.getUser();
    var nowDate =
        workerLocalTimeService.zonedTimestamp(employee, ZonedDateTime.now()).toLocalDate();
    var phaseSchedule = user.getHomeAddresses().toPhasedSchedule(nowDate, null);

    var scheduledChange =
        Optional.ofNullable(phaseSchedule.getScheduledChange())
            .orElseThrow(NoScheduledAddressChangeForWorkerException::new);

    homeAddressService.removeScheduledHomeAddressChange(user, scheduledChange);
  }

  @Transactional
  public Worker updatePayPeriodPreference(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerPayPeriodPreferenceParamsForUpdate params)
      throws PayPeriodException {
    var worker = getWorker(employee.getUser(), employee, company);
    var request =
        new PayPeriodPreferenceUpdateRequest(params.getPayPeriodType(), company.getNowDate());
    var updatedPreferences =
        payPeriodPreferenceService.updateEmployeePayPeriodPreference(company, employee, request);

    worker.setPayPeriodPreferenceOptions(updatedPreferences.getPayPeriodPreferenceOptions());
    worker.setOnboardingComplete(employee.isOnboardingComplete());

    return worker;
  }

  @Transactional
  public Worker updateAvailablePaymentTypes(
      @NonNull DetailedEmployee employee,
      @NonNull DetailedCompany company,
      @NonNull WorkerAvailablePaymentTypesParamsForUpdate params) {
    var worker = getWorker(employee.getUser(), employee, company);
    var paymentTypes = params.toPaymentTypeEnumSet();

    employee.setSupportedPaymentTypes(paymentTypes);

    worker.setSupportedPaymentTypes(paymentTypes);
    return worker;
  }

  @Transactional
  public Worker updateWorkerTaxpayerIdentifier(
      String workerId, DetailedCompany company, WorkerTaxpayerIdentifierParamsForUpdate params) {
    var worker = getWorker(workerId, company);
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var user = userRepository.getOne(employee.getUserId());

    validateUniqueUnverifiedTINIfNeeded(company, params.getTaxpayerIdentifier());

    user.setTinVerificationState(
        tinVerificationStateService.getStateForUpdatedWorker(params, employee, user));

    if (employee.getOnboarding().isClaimed()) {
      user.setTaxpayerIdentifier(params.getTaxpayerIdentifier());
    } else {
      user.setUnverifiedTaxpayerIdentifier(params.getTaxpayerIdentifier());
    }

    if (Optional.ofNullable(params.getUnverifiedTinType()).isPresent()) {
      user.setUnverifiedTinType(params.getUnverifiedTinType());
    }

    eventPublisher.publishEvent(new WorkerUpdatedPersonalInfoEvent(employee));
    return worker;
  }

  @Transactional
  public Worker updateDefaultBankAccount(
      String workerId, DetailedCompany company, WorkerBankAccountParamsForUpdate params) {
    var worker = getWorker(workerId, company);
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var user = userRepository.getOne(employee.getUserId());

    var bankAccountParams = new UserBankAccount();
    bankAccountParams.setUserId(user.getId());
    bankAccountParams.setAccountType(params.getAccountType());
    bankAccountParams.setAccountName(params.getAccountName());
    bankAccountParams.setBankName(params.getBankName());
    bankAccountParams.setAccountNumber(params.getAccountNumber());
    bankAccountParams.setRoutingNumber(params.getRoutingNumber());
    bankAccountParams.setRuleAmountType(BankAccountRuleType.PERCENT);
    bankAccountParams.setRuleAmount(BigDecimal.ONE);

    if (user.getBankAccounts().size() > 0) {
      var currentDefaultAccount = user.getBankAccounts().get(0);
      bankAccountParams.setId(currentDefaultAccount.getId());
      userBankAccountService.updateBankAccount(bankAccountParams);
    } else {
      userBankAccountService.addBankAccount(bankAccountParams);
    }

    worker.setBankAccounts(user.getBankAccounts());

    return worker;
  }

  @Transactional
  public Worker updatePreferredPaymentMethod(
      String workerId, DetailedCompany company, PaymentMethod preferredPaymentMethod) {
    var worker = getWorker(workerId, company);
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    employeePaymentPreferenceService.updatePreferredPaymentMethod(
        company, employee, preferredPaymentMethod);

    return worker;
  }

  @Transactional
  public Worker deleteWorkerPicture(String workerId, DetailedCompany company) {
    var worker = getWorker(workerId, company);
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    userPictureService.deleteUserPicture(employee.getUserId());

    return worker;
  }

  @Transactional
  public Worker updateDirectReports(
      String workerId, DetailedCompany company, WorkerDirectReportsParamsForUpdate params) {
    var managerEmployee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    var employeeLookup = new EmployeeLookup().withWorkerIds(params.getWorkerIds());
    var employees = detailedEmployeeLookupService.listAll(employeeLookup, Pageable.unpaged());

    employees.forEach(employee -> employee.setReportsToWorkerId(managerEmployee.getWorkerId()));
    employees.forEach(employee -> validateManagerWorkerIdUpdate(employee, managerEmployee));

    return getWorker(managerEmployee.getUser(), managerEmployee, company);
  }

  @Transactional
  public Worker updateManager(String workerId, DetailedCompany company, String newManagerWorkerId) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var worker = getWorker(employee.getUser(), employee, company);

    var newManagerEmployee =
        detailedEmployeeLookupService.getEmployee(newManagerWorkerId, company.getId());
    var newManager = getWorker(newManagerEmployee.getUser(), newManagerEmployee, company);

    employee.setReportsToWorkerId(newManager.getWorkerId());
    worker.setReportsToWorkerId(newManager.getWorkerId());

    validateManagerWorkerIdUpdate(employee, newManagerEmployee);

    return worker;
  }

  @Transactional
  public Worker removeManager(String workerId, DetailedCompany company) {
    var worker = getWorker(workerId, company);

    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    employee.setReportsToWorkerId(null);
    worker.setReportsToWorkerId(null);

    return worker;
  }

  public ManagementChainPayload getManagementChain(String workerId, DetailedCompany company) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());

    return new ManagementChainPayload(getManagerIdChain(employee));
  }

  @Transactional
  public void changeWorkerApprovalGroup(
      @NonNull DetailedEmployee employee, Long newApprovalGroupId) {
    employee.setApprovalGroupId(newApprovalGroupId);
    eventPublisher.publishEvent(new WorkerApprovalGroupChangedEvent(employee));
  }

  @Transactional(readOnly = true)
  public Page<WorkerFile> listWorkerFiles(
      @NonNull DetailedEmployee employee,
      Set<DocumentType> documentTypes,
      Set<Year> taxYears,
      @NonNull Pageable pageable) {
    taxYears = Optional.ofNullable(taxYears).orElse(Collections.emptySet());

    return employeeFileService
        .listEmployeePublishedFiles(
            Set.of(employee.getId()),
            documentTypes,
            taxYears.stream().map(Year::getValue).collect(toSet()),
            null,
            pageable)
        .map(WorkerFile::from);
  }

  @Transactional
  public Worker adjustEmployeeCorporateOfficer(
          @NonNull DetailedEmployee employee, boolean corporateOfficer) {
      employee.setCorporateOfficer(corporateOfficer);
      detailedEmployeeRepository.save(employee);
      return Worker.from(employee);
  }

  public void validateExternalWorkerId(Long companyId, String externalWorkerId) {
    if (externalWorkerId == null) return;

    var employeeLookup =
        new EmployeeLookup()
            .withCompanyIds(Set.of(companyId))
            .withExternalWorkerIds(Set.of(externalWorkerId))
            .withSkipApplyAuthorization(true);
    var idAlreadyAssigned = detailedEmployeeLookupService.exists(employeeLookup);

    if (!hasText(externalWorkerId) || idAlreadyAssigned) {
      throw new ExternalWorkerIdAlreadyAssignedException();
    }
  }

  public void validateUniqueUnverifiedTINIfNeeded(
      @NonNull DetailedCompany company, String taxpayerIdentifier) {
    if (taxpayerIdentifier == null) {
      return;
    }

    if (!company.getWorkerOnboardingSettings().isValidateUniqueUnverifiedTINs()) {
      return;
    }

    var currentUserId = UserService.getAuthenticatedUserId();
    var conflictingUserId =
        detailedEmployeeRepository
            .findNonTerminatedEmployeeExistsByTaxpayerIdentifierWithinCompany(
                taxpayerIdentifier, company.getId(), company.getNowDate())
            .filter(ee -> !ee.getUserId().equals(currentUserId))
            .map(DetailedEmployee::getUserId)
            .orElse(null);

    if (conflictingUserId != null) {
      log.info(
          String.format(
              "Taxpayer identifier conflict: attempted to set value '%s' (md5), conflicts with User ID: %s",
              DigestUtils.md5DigestAsHex(taxpayerIdentifier.getBytes()), conflictingUserId));
      throw new TaxpayerIdentifierNotUniqueException();
    }
  }

  public List<WorkerLabel> getWorkerLabels(List<String> workerIds) {
    var workersLabels = new ArrayList<WorkerLabel>();
    var lookup = new EmployeeLookup();
    var workerIdsHashSet = new HashSet<String>(workerIds);
    lookup.setWorkerIds(workerIdsHashSet);
    lookup.setSkipApplyAuthorization(true);
    var employee = this.detailedEmployeeLookupService.findAllUnsafe(lookup);

    for (DetailedEmployee detailedEmployee : employee) {
      workersLabels.add(WorkerLabel.from(detailedEmployee));
    }

    return workersLabels;
  }

  private List<String> getManagerIdChain(DetailedEmployee employee) {
    var workerIds = new ArrayList<String>();
    workerIds.add(employee.getWorkerId());

    var currentEmployee = employee;

    while (!StringUtils.isEmpty(currentEmployee.getReportsToWorkerId())) {
      var managerId = currentEmployee.getReportsToWorkerId();

      if (workerIds.contains(managerId)) {
        var invalidManager =
            detailedEmployeeLookupService.getEmployee(
                employee.getReportsToWorkerId(), employee.getCompanyId());

        throw new ManagerCircularRelationshipException(invalidManager, employee);
      }

      workerIds.add(managerId);

      currentEmployee =
          detailedEmployeeLookupService.getEmployee(managerId, currentEmployee.getCompanyId());
    }

    return workerIds;
  }

  private void validateWorkerDoesNotReportToSelf(
      DetailedEmployee employee, DetailedEmployee newManagerEmployee) {
    if (Objects.equals(employee.getWorkerId(), newManagerEmployee.getWorkerId())) {
      throw new ManagerCircularRelationshipException(newManagerEmployee, employee);
    }
  }

  private void validateManagerWorkerIdUpdate(
      DetailedEmployee employee, DetailedEmployee newManagerEmployee) {
    validateWorkerDoesNotReportToSelf(employee, newManagerEmployee);
    getManagerIdChain(employee);
  }

  private Worker removeWorkerFromApprovalGroup(
      @NonNull String workerId, @NonNull DetailedCompany company) {
    var companyId = company.getId();
    var employee = detailedEmployeeLookupService.getEmployee(workerId, companyId);
    var worker = getWorker(employee.getUser(), employee, company);
    worker.setApprovalGroup(null);
    changeWorkerApprovalGroup(employee, null);
    return worker;
  }

  private Worker addWorkerToApprovalGroup(
      @NonNull String workerId, @NonNull DetailedCompany company, @NonNull Long approvalGroupId) {
    var companyId = company.getId();
    var employee = detailedEmployeeLookupService.getEmployee(workerId, companyId);
    var worker = getWorker(employee.getUser(), employee, company);
    var approvalGroup =
        approvalGroupRepository
            .findByIdAndCompanyId(approvalGroupId, companyId)
            .orElseThrow(() -> new ResourceNotFoundException("Approval group does not exist"));
    worker.setApprovalGroup(approvalGroup);
    changeWorkerApprovalGroup(employee, approvalGroupId);
    return worker;
  }

  public ClaimWorkerTaxpayerIdentifierResponse claimWorkerTaxpayerIdentifier(
      String workerId, DetailedCompany company, WorkerTaxpayerIdentifierParamsForUpdate params) {
    var employee = detailedEmployeeLookupService.getEmployee(workerId, company.getId());
    var user = userRepository.getOne(employee.getUserId());

    String taxpayerIdentifier =
        employee.getOnboarding().isClaimed()
            ? user.getTaxpayerIdentifier()
            : user.getUnverifiedTaxpayerIdentifier();

    ClaimWorkerTaxpayerIdentifierResponse response = new ClaimWorkerTaxpayerIdentifierResponse();
    if (params.getTaxpayerIdentifier().equals(taxpayerIdentifier)) {
      response.setValidTaxpayerIdentifier(true);
    } else {
      response.setValidTaxpayerIdentifier(
          !userRepository.existsByTaxpayerIdentifierEquals(params.getTaxpayerIdentifier()));
    }

    return response;
  }

  @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
  static class ExternalWorkerIdAlreadyAssignedException extends LocalizedRuntimeException {
    public ExternalWorkerIdAlreadyAssignedException() {
      super("worker.exception.ExternalWorkerIdAlreadyAssignedException.message");
    }
  }
}
