package com.everee.api.worker.updateparams;

import com.everee.api.tax.state.State;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class DeprecatedAddressParamsForUpdate {
  @NotNull private String line1;

  private String line2;

  @NotNull private String city;

  @NotNull private State state;

  @NotNull
  @Size(min = 5, max = 10)
  @Pattern(regexp = "[0-9\\-]+")
  private String postalCode;

  @NotNull private LocalDate startDate;

  private Double latitude;

  private Double longitude;

  public HomeAddressParamsForUpdate toAddressParams() {
    var params = new HomeAddressParamsForUpdate();
    params.setLine1(getLine1());
    params.setLine2(getLine2());
    params.setCity(getCity());
    params.setState(getState());
    params.setPostalCode(getPostalCode());
    params.setEffectiveDate(getStartDate());
    params.setLatitude(getLatitude());
    params.setLongitude(getLongitude());
    return params;
  }
}
