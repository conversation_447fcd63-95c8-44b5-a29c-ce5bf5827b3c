package com.everee.api.worker.updateparams;

import com.everee.api.bankaccount.BankAccountType;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class WorkerBankAccountParamsForUpdate {
  @NotNull private String bankName;

  @NotNull private String accountName;

  @NotNull
  @Enumerated(EnumType.STRING)
  private BankAccountType accountType;

  @NotNull private String routingNumber;

  @NotNull private String accountNumber;
}
