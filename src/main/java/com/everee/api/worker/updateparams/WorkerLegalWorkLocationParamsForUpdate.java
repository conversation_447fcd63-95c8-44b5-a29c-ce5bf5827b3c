package com.everee.api.worker.updateparams;

import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkerLegalWorkLocationParamsForUpdate {
  @NotNull private boolean useHomeAddress;

  // Values ignored if useHomeAddress is true; required otherwise
  private Long workLocationId;
  private Long stateUnemploymentTaxLocationId;

  @NotNull private LocalDate effectiveDate;
}
