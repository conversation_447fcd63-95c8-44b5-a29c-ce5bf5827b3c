package com.everee.api.worker.updateparams;

import com.everee.api.user.TinType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class WorkerTaxpayerIdentifierParamsForUpdate {
  @NotBlank
  @Size(min = 9, max = 9)
  @Pattern(regexp = "\\d+")
  private String taxpayerIdentifier;

  private TinType unverifiedTinType;
}
