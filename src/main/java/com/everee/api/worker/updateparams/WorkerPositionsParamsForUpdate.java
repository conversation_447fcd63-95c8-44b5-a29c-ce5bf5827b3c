package com.everee.api.worker.updateparams;

import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import java.time.LocalDate;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkerPositionsParamsForUpdate {
  private String title;

  @Enumerated(EnumType.STRING)
  @NotNull
  private PayType payType;

  @NotNull private Money payRate;

  private Boolean eligibleForOvertime;

  @NotNull private Integer expectedWeeklyHours;

  @JsonDeserialize(using = LocalDateDeserializer.class)
  @NotNull
  private LocalDate effectiveDate;

  private LocalDate endDate;

  private Long workersCompClassId;

  private Long workerRoleId;

  private Long workLocationId;
}
