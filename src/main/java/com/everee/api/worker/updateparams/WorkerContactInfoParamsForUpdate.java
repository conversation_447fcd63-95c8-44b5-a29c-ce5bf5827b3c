package com.everee.api.worker.updateparams;

import javax.annotation.Nullable;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class WorkerContactInfoParamsForUpdate {
  @NotBlank @Email private String email;

  @Nullable
  @Size(min = 10, max = 10)
  @Pattern(regexp = "\\d+")
  private String phoneNumber;

  private String workerId;
}
