package com.everee.api.worker.createparams;

import com.everee.api.worker.updateparams.WorkerBankAccountParamsForUpdate;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public abstract class WorkerParamsForCreate extends WorkerParams {

  @NotNull private LocalDate dateOfBirth;

  @NotNull private String taxpayerIdentifier;

  private String unverifiedTinType;

  @NotNull private HomeAddressParamsForCreate homeAddress;

  @NotNull private WorkerBankAccountParamsForUpdate bankAccount;
}
