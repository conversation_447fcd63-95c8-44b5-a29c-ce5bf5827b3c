package com.everee.api.worker.createparams;

import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkerLegalWorkLocationParamsForCreate {
  @NotNull private boolean useHomeAddress;

  // Values ignored if useHomeAddress is true; required otherwise
  private Long workLocationId;
  private Long stateUnemploymentTaxLocationId;
}
