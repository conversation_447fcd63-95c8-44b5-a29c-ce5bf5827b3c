package com.everee.api.worker.paymenthistory;

import com.everee.api.money.Money;
import com.everee.api.payment.PaymentDeposit;
import lombok.Value;

@Value
public class WorkerPaymentHistoryDepositItem {

  String destinationLabel;
  Money amount;

  static WorkerPaymentHistoryDepositItem from(PaymentDeposit deposit) {
    var depositLabel =
        String.format("%s: %s", deposit.getBankName(), deposit.getObfuscatedAccountNumber());
    return new WorkerPaymentHistoryDepositItem(depositLabel, deposit.getAmounts().getAmount());
  }
}
