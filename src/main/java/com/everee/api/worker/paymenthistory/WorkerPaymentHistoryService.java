package com.everee.api.worker.paymenthistory;

import com.everee.api.employee.Employee;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.lookup.PaymentLookup;
import com.everee.api.payment.lookup.PaymentLookupService;
import java.util.Set;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WorkerPaymentHistoryService {
  private final PaymentLookupService paymentLookupService;

  public Page<WorkerPaymentHistoryItem> listPaymentHistoryItems(
      @NonNull Employee employee, @NonNull Pageable pageable) {
    var lookup =
        new PaymentLookup()
            .setEmployeeId(employee.getId())
            .setPaymentStatuses(Set.of(PaymentStatus.PAID));
    return paymentLookupService.listAll(lookup, pageable).map(WorkerPaymentHistoryItem::from);
  }

  public WorkerPaymentHistoryItem getPaymentHistoryItem(@NonNull Long paymentId) {
    var lookup =
        new PaymentLookup()
            .setIds(Set.of(paymentId))
            .setPaymentStatuses(Set.of(PaymentStatus.PAID));
    var payment = paymentLookupService.findOneOrThrow(lookup);
    return WorkerPaymentHistoryItem.from(payment);
  }
}
