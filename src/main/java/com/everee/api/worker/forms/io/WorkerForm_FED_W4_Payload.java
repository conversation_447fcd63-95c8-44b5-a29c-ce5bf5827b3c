package com.everee.api.worker.forms.io;

import com.everee.api.model.MaritalStatus;
import com.everee.api.money.Money;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkerForm_FED_W4_Payload {
  private Boolean haveExactlyTwoJobs;
  private Long countOfChildren;
  private Long countOfOtherDependents;
  private Money otherIncomeAnnually;
  private Money deductionsAnnually;
  private Money extraWithholdingsMonthly;
  private MaritalStatus maritalStatus;
  private Boolean exempt;

  private LocalDate effectiveDate;
}
