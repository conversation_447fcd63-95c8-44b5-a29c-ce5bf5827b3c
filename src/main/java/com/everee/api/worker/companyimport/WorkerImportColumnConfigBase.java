package com.everee.api.worker.companyimport;

import java.util.Locale;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;

@Setter
public abstract class WorkerImportColumnConfigBase implements WorkerImportColumnConfig {

  @Autowired private MessageSource messageSource;

  protected abstract String applyInternal(
      String value,
      WorkerImportSummary workerImport,
      CompanyWorkerImportJobOptions importJobOptions);

  @Override
  public void apply(
      String value,
      WorkerImportSummary workerImport,
      CompanyWorkerImportJobOptions importJobOptions) {
    var applied = applyInternal(value, workerImport, importJobOptions);
    workerImport.addColumnValue(getColumnHeader(), applied);
  }

  protected String getMessage(String code, Object... args) {
    return messageSource.getMessage(code, args, Locale.ENGLISH);
  }
}
