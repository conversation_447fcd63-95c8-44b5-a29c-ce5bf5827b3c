package com.everee.api.worker.companyimport.configs.workInformation;

import static com.everee.api.model.PayType.SALARY;
import static com.everee.api.worker.companyimport.configs.ImportConfig.*;

import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportColumnConfigBase;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.MissingDataException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = SALARY_ORDER)
public class SalaryPayRateImportColumnConfig extends WorkerImportColumnConfigBase {
  @Override
  public String getColumnHeader() {
    return SALARY_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.SalaryPayRateImportColumnConfig.text");
  }

  @Override
  public String getInputRule() {
    return NUMERIC_CURRENCY_INPUT_RULE;
  }

  @Override
  public String applyInternal(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    var isSalary =
        SALARY.equals(
            workerImport.getEmployeeParams() != null
                ? workerImport.getEmployeeParams().getPayType()
                : workerImport.getEmployeeInviteParams().getPayType());
    if (!isSalary && StringUtils.isNotBlank(value)) {
      workerImport.addError(
          getMessage("workerImportColumnConfig.SalaryPayRateImportColumnConfig.error.text"));
      return value;
    } else if (!isSalary) {
      return value;
    }

    if (StringUtils.isBlank(value)) throw new MissingDataException(getColumnHeader());

    value = value.trim();
    if (workerImport.getEmployeeParams() != null)
      workerImport.getEmployeeParams().setPayRate(Money.valueOf(value));
    else workerImport.getEmployeeInviteParams().setPayRate(Money.valueOf(value));

    return value;
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.isEmployeeImport();
  }
}
