package com.everee.api.worker.companyimport.configs.bank;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;
import static com.everee.api.worker.companyimport.configs.ImportUtils.withBankAccount;

import com.everee.api.bankaccount.BankAccountType;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.PayCardStatus;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportColumnConfigBase;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.MissingDataException;
import java.util.Arrays;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = BANK_ACCT_TYPE_ORDER)
public class BankAccountTypeImportColumnConfig extends WorkerImportColumnConfigBase {
  @Override
  public String getColumnHeader() {
    return BANK_ACCT_TYPE_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return "Either "
        + Arrays.stream(BankAccountType.values())
            .map(BankAccountType::name)
            .map(String::toLowerCase)
            .collect(Collectors.joining(" or "))
        + "."
        + "\n Accepts only \"checking\" or \"savings\".";
  }

  @Override
  public String getInputRule() {
    return BANK_ACCT_TYPE_INPUT_RULE;
  }

  @Override
  public String applyInternal(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    if (StringUtils.isBlank(value)) throw new MissingDataException(getColumnHeader());

    value = value.trim().toUpperCase();
    withBankAccount(workerImport.getWorkerParamsForCreate())
        .setAccountType(BankAccountType.valueOf(value));

    return value;
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.willAdminCompleteOnboarding()
        && importJobOptions.getCompany().getPayCardStatus() != PayCardStatus.EPC_ONLY;
  }
}
