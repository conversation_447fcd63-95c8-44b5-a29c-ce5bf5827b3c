package com.everee.api.worker.companyimport.configs.bank;

import static com.everee.api.worker.companyimport.configs.ImportConfig.BANK_ACCT_NICKNAME_HEADER;
import static com.everee.api.worker.companyimport.configs.ImportConfig.BANK_ACCT_NICKNAME_INPUT_RULE;
import static com.everee.api.worker.companyimport.configs.ImportConfig.BANK_ACCT_NICKNAME_ORDER;
import static com.everee.api.worker.companyimport.configs.ImportUtils.withBankAccount;

import com.everee.api.company.DetailedCompany;
import com.everee.api.company.PayCardStatus;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = BANK_ACCT_NICKNAME_ORDER)
public class BankNicknameImportColumnConfig extends TextColumnConfig {
  @Override
  public String getColumnHeader() {
    return BANK_ACCT_NICKNAME_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.BankNicknameImportColumnConfig.text");
  }

  @Override
  public String getInputRule() {
    return BANK_ACCT_NICKNAME_INPUT_RULE;
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    withBankAccount(workerImport.getWorkerParamsForCreate()).setAccountName(value);
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.willAdminCompleteOnboarding()
        && importJobOptions.getCompany().getPayCardStatus() != PayCardStatus.EPC_ONLY;
  }
}
