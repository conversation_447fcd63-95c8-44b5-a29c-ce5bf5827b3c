package com.everee.api.worker.companyimport.configs.workInformation;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;
import static com.everee.api.worker.companyimport.configs.ImportUtils.parseBoolean;

import com.everee.api.company.DetailedCompany;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = STATUTORY_EMPLOYEE_ORDER)
public class StatutoryEmployeeImportColumnConfig extends TextColumnConfig {
  @Override
  public String getColumnHeader() {
    return STATUTORY_EMPLOYEE_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.StatutoryEmployeeImportColumnConfig.text");
  }

  @Override
  public String getInputRule() {
    return TRUE_FALSE_INPUT_RULE;
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    var val = parseBoolean(value, false);
    if (workerImport.getEmployeeParams() != null) {
      workerImport.getEmployeeParams().setStatutoryEmployee(val);
    } else {
      workerImport.getEmployeeInviteParams().setStatutoryEmployee(val);
    }
  }

  @Override
  protected String validate(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    if (StringUtils.isBlank(value)) return "false";
    return value.trim();
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.isEmployeeImport();
  }

  @Override
  public boolean isOptional(CompanyWorkerImportJobOptions importJobOptions) {
    return true;
  }
}
