package com.everee.api.worker.companyimport.configs.personalInformation;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;

import com.everee.api.company.DetailedCompany;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = FIRST_NAME_ORDER)
public class FirstNameImportColumnConfig extends TextColumnConfig {
  @Override
  public String getColumnHeader() {
    return FIRST_NAME_HEADER;
  }

  @Override
  public String getInputRule() {
    return TEXT_INPUT_RULE;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.FirstNameImportColumnConfig.text");
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    workerImport.getWorkerParams().setFirstName(value);
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return true;
  }
}
