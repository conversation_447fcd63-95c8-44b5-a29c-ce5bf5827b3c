package com.everee.api.worker.companyimport.configs.workInformation;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;
import static com.everee.api.worker.companyimport.configs.ImportUtils.parseDate;

import com.everee.api.company.DetailedCompany;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportColumnConfigBase;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.MissingDataException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = START_DATE_ORDER)
public class StartDateImportColumnConfig extends WorkerImportColumnConfigBase {
  @Override
  public String getColumnHeader() {
    return START_DATE_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.StartDateImportColumnConfig.text");
  }

  @Override
  public String getInputRule() {
    return DATE_FORMAT_INPUT_RULE;
  }

  @Override
  public String applyInternal(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    if (StringUtils.isBlank(value)) throw new MissingDataException(getColumnHeader());

    workerImport.getWorkerParams().setStartDate(parseDate(getColumnHeader(), value));

    return value;
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return true;
  }
}
