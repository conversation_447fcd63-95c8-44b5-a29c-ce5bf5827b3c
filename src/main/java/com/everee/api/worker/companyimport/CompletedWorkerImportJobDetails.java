package com.everee.api.worker.companyimport;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class CompletedWorkerImportJobDetails {
  private Long jobId;
  private List<String> skippedWorkerNames = new ArrayList<>();
  private List<String> importedWorkerNames = new ArrayList<>();

  public void addSkippedWorkerName(String name) {
    skippedWorkerNames.add(name);
  }

  public void addImportedWorkerName(String name) {
    importedWorkerNames.add(name);
  }
}
