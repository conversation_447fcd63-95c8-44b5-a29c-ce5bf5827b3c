package com.everee.api.worker.companyimport.configs.address;

import static com.everee.api.worker.companyimport.configs.ImportConfig.CITY_HEADER;
import static com.everee.api.worker.companyimport.configs.ImportConfig.CITY_ORDER;
import static com.everee.api.worker.companyimport.configs.ImportUtils.withHomeAddress;

import com.everee.api.company.DetailedCompany;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = CITY_ORDER)
public class CityImportColumnConfig extends TextColumnConfig {
  @Override
  public String getColumnHeader() {
    return CITY_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.CityImportColumnConfig.text");
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    withHomeAddress(workerImport.getWorkerParamsForCreate()).setCity(value.trim());
  }
}
