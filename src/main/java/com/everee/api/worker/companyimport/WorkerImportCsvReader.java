package com.everee.api.worker.companyimport;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

public class WorkerImportCsvReader implements WorkerImportFileReader {
  private final BufferedReader reader;

  private String currentRow;

  static final String COMMA_DELIMITER = ",";

  public WorkerImportCsvReader(InputStream inputStream) {
    reader = new BufferedReader(new InputStreamReader(inputStream));
  }

  @Override
  public List<String> getNextRowValues() {
    try {
      currentRow = reader.readLine();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    return getRowValues();
  }

  private List<String> getRowValues() {
    if (StringUtils.isEmpty(currentRow)) return List.of();

    return Arrays.stream(currentRow.split(COMMA_DELIMITER, -1))
        .map(String::trim)
        .collect(Collectors.toList());
  }

  @Override
  public void close() {
    try {
      reader.close();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
}
