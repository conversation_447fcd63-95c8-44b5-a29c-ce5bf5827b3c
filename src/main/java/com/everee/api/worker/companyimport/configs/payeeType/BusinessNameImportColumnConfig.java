package com.everee.api.worker.companyimport.configs.payeeType;

import static com.everee.api.worker.companyimport.configs.ImportConfig.BUSINESS_NAME_HEADER;
import static com.everee.api.worker.companyimport.configs.ImportConfig.BUSINESS_NAME_INPUT_RULE;
import static com.everee.api.worker.companyimport.configs.ImportConfig.BUSINESS_NAME_ORDER;

import com.everee.api.company.DetailedCompany;
import com.everee.api.company.PayCardStatus;
import com.everee.api.payeetype.PayeeTypeName;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.InvalidImportDataException;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = BUSINESS_NAME_ORDER)
public class BusinessNameImportColumnConfig extends TextColumnConfig {

  @Override
  public String getColumnHeader() {
    return BUSINESS_NAME_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.BusinessNameImportColumnConfig.text");
  }

  @Override
  public String getInputRule() {
    return BUSINESS_NAME_INPUT_RULE;
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.isContractorImport()
        && importJobOptions.getCompany().getPayCardStatus() != PayCardStatus.EPC_ONLY;
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    workerImport.getWorkerParams().setBusinessName(value);
  }

  @Override
  protected String validate(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    value = super.validate(value, workerImport, importOptions);
    value = value.trim();

    if (PayeeTypeName.BUSINESS.toString().equals(workerImport.getWorkerParams().getPayeeType())) {
      if (StringUtils.isEmpty(value)) {
        throw new InvalidImportDataException();
      }
    }
    // Don't capture Business name if they are importing an Individual
    else if (PayeeTypeName.INDIVIDUAL
        .toString()
        .equals(workerImport.getWorkerParams().getPayeeType())) {
      return null;
    }

    // Do we want to force the business name to 'null' if the payeeType is INDIVIDUAL
    return value;
  }

  @Override
  public boolean isOptional(CompanyWorkerImportJobOptions importJobOptions) {
    return true;
  }
}
