package com.everee.api.worker.companyimport.configs.address;

import static com.everee.api.worker.companyimport.configs.ImportConfig.LINE1_HEADER;
import static com.everee.api.worker.companyimport.configs.ImportConfig.LINE1_ORDER;
import static com.everee.api.worker.companyimport.configs.ImportUtils.withHomeAddress;

import com.everee.api.company.DetailedCompany;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = LINE1_ORDER)
public class Line1ImportColumnConfig extends TextColumnConfig {
  @Override
  public String getColumnHeader() {
    return LINE1_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.Line1ImportColumnConfig.text");
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    withHomeAddress(workerImport.getWorkerParamsForCreate()).setLine1(value.trim());
  }
}
