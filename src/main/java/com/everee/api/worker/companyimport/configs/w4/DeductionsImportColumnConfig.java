package com.everee.api.worker.companyimport.configs.w4;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;
import static com.everee.api.worker.companyimport.configs.ImportUtils.withWithholdings;

import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportColumnConfigBase;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = W4_DEDUCTIONS_ORDER)
public class DeductionsImportColumnConfig extends WorkerImportColumnConfigBase {
  @Override
  public String getColumnHeader() {
    return W4_DEDUCTIONS_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return getMessage("workerImportColumnConfig.DeductionsImportColumnConfig.text");
  }

  @Override
  public String getInputRule() {
    return NUMERIC_CURRENCY_INPUT_RULE;
  }

  @Override
  public String applyInternal(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    if (StringUtils.isBlank(value)) value = "0.00";

    value = value.trim();

    withWithholdings(workerImport.getEmployeeParams()).setDeductionsAnnually(Money.valueOf(value));

    return value;
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.willAdminCompleteOnboarding() && importJobOptions.isEmployeeImport();
  }

  @Override
  public boolean isOptional(CompanyWorkerImportJobOptions importJobOptions) {
    return true;
  }
}
