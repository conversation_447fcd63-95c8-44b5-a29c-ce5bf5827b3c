package com.everee.api.worker.companyimport.configs.workInformation;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;

import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.onboarding.params.EmployeeParamsForInvite;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.InvalidImportDataException;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import java.time.Duration;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = EXPECTED_WEEKLY_HOURS_ORDER)
public class ExpectedWeeklyHoursImportColumnConfig extends TextColumnConfig {
  private static final int MAX_HOURS = (int) Duration.ofDays(7).toHours();

  @Override
  public String getColumnHeader() {
    return EXPECTED_WEEKLY_HOURS_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return "Number of whole hours employee will typically work each week (e.g. 40) \n Numeric input only.";
  }

  @Override
  public String getInputRule() {
    return NUMERIC_INPUT_RULE;
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    if (workerImport.getWorkerParamsForInvite() != null)
      ((EmployeeParamsForInvite) workerImport.getWorkerParamsForInvite())
          .setExpectedWeeklyHours(Integer.parseInt(value));
    else workerImport.getEmployeeParams().setTypicalWeeklyHours(Integer.parseInt(value));
  }

  @Override
  protected String validate(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    value = super.validate(value, workerImport, importOptions);
    value = value.trim();

    try {
      var val = Integer.parseInt(value);
      if (val < 0 || val > MAX_HOURS) throw new InvalidImportDataException();
    } catch (NumberFormatException e) {
      throw new InvalidImportDataException();
    }

    return value;
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.isEmployeeImport();
  }
}
