package com.everee.api.worker.companyimport.configs.workInformation;

import static com.everee.api.worker.companyimport.configs.ImportConfig.*;

import com.everee.api.company.DetailedCompany;
import com.everee.api.model.PayType;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportColumnConfig;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.InvalidImportEnumException;
import com.everee.api.worker.companyimport.configs.TextColumnConfig;
import java.util.Arrays;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(value = PAYTYPE_ORDER)
public class PayTypeImportColumnConfig extends TextColumnConfig {
  @Override
  public String getColumnHeader() {
    return PAYTYPE_HEADER;
  }

  @Override
  public String helpText(WorkerImportColumnConfig columnConfig, DetailedCompany company) {
    return "Either "
        + Arrays.stream(PayType.values())
            .map(PayType::name)
            .map(String::toLowerCase)
            .collect(Collectors.joining(" or "))
        + "."
        + " \n Accepts “salary” or “hourly” only. ";
  }

  @Override
  public String getInputRule() {
    return PAYTYPE_INPUT_RULE;
  }

  @Override
  protected String validate(
      String value, WorkerImportSummary workerImport, CompanyWorkerImportJobOptions importOptions) {
    super.validate(value, workerImport, importOptions);

    if (StringUtils.isNotBlank(value)) {
      value = value.trim();
      try {
        PayType.valueOf(value.toUpperCase());
      } catch (Exception e) {
        throw new InvalidImportEnumException(getColumnHeader(), value);
      }
    }

    return value;
  }

  @Override
  protected void applyValidValue(String value, WorkerImportSummary workerImport) {
    if (workerImport.getEmployeeParams() != null)
      workerImport.getEmployeeParams().setPayType(PayType.valueOf(value.toUpperCase()));
    else workerImport.getEmployeeInviteParams().setPayType(PayType.valueOf(value.toUpperCase()));
  }

  @Override
  public boolean isApplicable(CompanyWorkerImportJobOptions importJobOptions) {
    return importJobOptions.isEmployeeImport();
  }
}
