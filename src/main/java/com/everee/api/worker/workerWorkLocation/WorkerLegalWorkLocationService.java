package com.everee.api.worker.workerWorkLocation;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.Employee;
import com.everee.api.employee.EmployeeWasTerminatedBeforeEffectiveDateException;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.labor.workedshift.WorkedShiftLookupService;
import com.everee.api.model.PhysicalAddress;
import com.everee.api.payment.PaymentRepository;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.PaymentType;
import com.everee.api.phase.ContiguousPhasedService;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.phase.PhaseSchedule;
import com.everee.api.phase.Phased;
import com.everee.api.phase.exception.PhasedDataStartsInInvalidRangeException;
import com.everee.api.time.WorkerLocalTimeService;
import com.everee.api.user.DetailedUser;
import com.everee.api.user.address.PhysicalAddressConverter;
import com.everee.api.util.DateUtil;
import com.everee.api.worker.createparams.WorkerLegalWorkLocationParamsForCreate;
import com.everee.api.worker.event.WorkerLegalWorkLocationChangedEvent;
import com.everee.api.worker.lookup.WorkerLookup;
import com.everee.api.worker.lookup.WorkerLookupConverter;
import com.everee.api.worker.paramconverters.WorkerLegalWorkLocationParamsConverter;
import com.everee.api.worker.updateparams.WorkerLegalWorkLocationParamsForUpdate;
import com.everee.api.worklocation.WorkLocation;
import com.everee.api.worklocation.WorkLocationLookup;
import com.everee.api.worklocation.WorkLocationLookupService;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.transaction.Transactional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Transactional
@RequiredArgsConstructor
public class WorkerLegalWorkLocationService
    extends ContiguousPhasedService<WorkerLegalWorkLocation, Employee> {
  private final CompanyService companyService;
  private final PaymentRepository paymentRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final WorkLocationLookupService workLocationLookupService;
  private final WorkedShiftLookupService workedShiftLookupService;
  private final WorkerLegalWorkLocationRepository workerLegalWorkLocationRepository;
  private final DetailedEmployeeLookupService detailedEmployeeLookupService;
  private final WorkerLocalTimeService workerLocalTimeService;

  public PhaseSchedule<WorkerLegalWorkLocation> getDecoratedLegalWorkLocationPhaseSchedule(
      DetailedUser user, DetailedEmployee employee, LocalDate nowDate) {
    var phaseSchedule =
        employee.getLegalWorkLocations().toPhasedSchedule(nowDate, getEarliestExpiryDate(employee));
    phaseSchedule.decorate(getAddressDecorator(user, nowDate));

    return phaseSchedule;
  }

  public Map<Long, PhaseSchedule<WorkerLegalWorkLocation>> getLegalWorkLocationPhaseSchedulePerEmployee(
    Page<DetailedEmployee> employees, Map<Long, LocalDate> nowDatePerEmployee) {
    var EarliestExpiryDateMap = getEarliestExpiryDateForEmployees(employees);

    var phaseScheduleMap = employees.stream()
      .collect(Collectors.toMap(
        employee -> employee,
        employee -> {
          LocalDate nowDate = nowDatePerEmployee.getOrDefault(employee.getId(), LocalDate.MIN);
          LocalDate EarliestExpiryDate = EarliestExpiryDateMap.getOrDefault(employee.getId(), LocalDate.MIN);

          return employee.getLegalWorkLocations().toPhasedSchedule(nowDate, EarliestExpiryDate);
        }
      ));

    var distinctLegalWorkLocationIds = phaseScheduleMap.values().stream()
      .flatMap(phasedSchedule -> Stream.of(phasedSchedule.getCurrent()))
      .map(WorkerLegalWorkLocation::getWorkLocationId)
      .distinct()
      .collect(Collectors.toSet());

    var lookup = new WorkLocationLookup().setIds(distinctLegalWorkLocationIds).setIncludeArchived(true);
    var locations = workLocationLookupService.listAllUnsafe(lookup, Pageable.unpaged());
    var locationMap = locations.stream().collect(Collectors.toMap(WorkLocation::getId, location -> location));

   var finalPhasedScheduleMap = phaseScheduleMap.entrySet().stream()
      .collect(Collectors.toMap(entry-> entry.getKey().getId(), entry -> {
        var phasedSchedule = entry.getValue();
        var nowDate = nowDatePerEmployee.getOrDefault(entry.getKey().getId(), LocalDate.MIN);
        var location =locationMap.get(phasedSchedule.getCurrent().getWorkLocationId());

        phasedSchedule.decorate(getAddressDecoratorPerEmployee(entry.getKey().getUser(), nowDate,location));

        return  phasedSchedule;
      }));

    return  finalPhasedScheduleMap;
  }

  @Transactional
  public PhaseSchedule<WorkerLegalWorkLocation> createInitialWorkerLegalWorkLocation(
      @NonNull DetailedUser user,
      @NonNull DetailedEmployee employee,
      @NonNull WorkerLegalWorkLocationParamsForCreate params) {

    var initialRecord =
        WorkerLegalWorkLocationParamsConverter.toInitialWorkerLegalWorkLocation(employee, params);

    // create initial record
    var phaseSchedule =
        createInitialRecord(employee, employee.getLegalWorkLocations(), initialRecord);

    // decorate the address onto the record(s)
    phaseSchedule.decorate(getAddressDecorator(user, employee.getStartDate()));

    return phaseSchedule;
  }

  @Transactional
  public PhaseSchedule<WorkerLegalWorkLocation> adjustInitialWorkerLegalWorkLocationStartDate(
      @NonNull DetailedUser user, @NonNull DetailedEmployee employee) {
    var employeeStartDate = employee.getStartDate();
    var initialRecord =
        employee.getLegalWorkLocations().stream()
            .min(Comparator.comparing(Phased::getStartDate))
            .map(l -> l.setStartDate(employeeStartDate));

    if (initialRecord.isEmpty()) {
      return new PhaseSchedule<>(null, null, employeeStartDate, false);
    }

    // Schedule the requested record change
    var phaseSchedule =
        addScheduledChange(employee, employee.getLegalWorkLocations(), initialRecord.get());

    // decorate the address onto the record(s)
    phaseSchedule.decorate(getAddressDecorator(user, getNowDate(employee)));

    return phaseSchedule;
  }

  @Transactional
  public PhaseSchedule<WorkerLegalWorkLocation> updateWorkerLegalWorkLocation(
      @NonNull DetailedUser user,
      @NonNull DetailedEmployee employee,
      @NonNull WorkerLegalWorkLocationParamsForUpdate params) {
    var newRecord =
        WorkerLegalWorkLocationParamsConverter.toWorkerLegalWorkLocation(employee, params);

    // Schedule the requested record change
    var phaseSchedule = addScheduledChange(employee, employee.getLegalWorkLocations(), newRecord);

    // decorate the address onto the record(s)
    phaseSchedule.decorate(getAddressDecorator(user, getNowDate(employee)));

    // Trigger post-save operations
    eventPublisher.publishEvent(
        new WorkerLegalWorkLocationChangedEvent(
            employee.getCompanyId(),
            employee,
            newRecord,
            getEarliestExpiryDate(employee).plusDays(1)));

    return phaseSchedule;
  }

  @Transactional
  public PhaseSchedule<WorkerLegalWorkLocation> deleteWorkerLegalWorkLocation(
      @NonNull DetailedUser user,
      @NonNull DetailedEmployee employee,
      @NonNull WorkerLegalWorkLocation recordToRemove) {
    // remove the position from the phase schedule
    var phaseSchedule =
        removeScheduledChange(employee, employee.getLegalWorkLocations(), recordToRemove);

    // decorate the address onto the record(s)
    phaseSchedule.decorate(getAddressDecorator(user, getNowDate(employee)));

    // Trigger post-save operations
    eventPublisher.publishEvent(
        new WorkerLegalWorkLocationChangedEvent(
            employee.getCompanyId(),
            employee,
            phaseSchedule.getCurrent(),
            getEarliestExpiryDate(employee).plusDays(1)));

    return phaseSchedule;
  }

  public WorkerLegalWorkLocation withAddress(
      WorkerLegalWorkLocation location,
      @NonNull DetailedUser user,
      @NonNull LocalDate referenceDate) {
    getAddressDecorator(user, referenceDate).accept(location);
    return location;
  }

  @Override
  protected LocalDate getEarliestExpiryDate(@NonNull Employee context) {
    var mostRecentPaymentDate =
        paymentRepository
            .findMostRecentPaymentDate(
                context.getId(), PaymentStatus.FINALIZED, Set.of(PaymentType.PAYROLL))
            .orElse(null);
    var dayBeforeHireDate = context.getStartDate().minusDays(1);
    var mostRecentWorkedShiftEndDate =
        workedShiftLookupService.findMostRecentWorkedShiftEndDate((Set.of(context.getId())));

    return DateUtil.maxDate(dayBeforeHireDate, mostRecentPaymentDate, mostRecentWorkedShiftEndDate);
  }

  protected Map<Long,LocalDate> getEarliestExpiryDateForEmployees(@NonNull Page<DetailedEmployee> employees) {
    var employeeIds = employees.getContent().stream().map(DetailedEmployee::getId).collect(Collectors.toSet());

    var mostRecentPaymentDatePerEmployee = paymentRepository.findMostRecentPaymentDateByEmployees(
      employeeIds,
      PaymentStatus.FINALIZED,
      Set.of(PaymentType.PAYROLL)
    );

    var mostRecentPaymentDatePerEmployeeMap = mostRecentPaymentDatePerEmployee.stream()
      .collect(Collectors.toMap(
        row -> (Long) row[0],
        row -> (LocalDate) row[1],
        (existingDate, newDate) -> newDate.isAfter(existingDate) ? newDate : existingDate
      ));

    var daysBeforeHireDatesPerEmployeeMap = employees.getContent().stream()
      .collect(Collectors.toMap(
        employee -> employee.getId(),
        employee -> employee.getStartDate().minusDays(1)
      ));

    var mostRecentWorkedShiftEndDatePerEmployeeMap = workedShiftLookupService.findMostRecentWorkedShiftEndDateUnsafePerEmployee(employeeIds);

    var maxDates = employeeIds.stream()
      .collect(Collectors.toMap(
        employeeId -> employeeId,
        employeeId -> {
          LocalDate mostRecentPaymentDate = mostRecentPaymentDatePerEmployeeMap.getOrDefault(employeeId, LocalDate.MIN);
          LocalDate daysBeforeHireDate = daysBeforeHireDatesPerEmployeeMap.getOrDefault(employeeId, LocalDate.MIN);
          LocalDate workedShiftEndDate = mostRecentWorkedShiftEndDatePerEmployeeMap.getOrDefault(employeeId, LocalDate.MIN);

          return DateUtil.maxDate(mostRecentPaymentDate, daysBeforeHireDate, workedShiftEndDate);
        }
      ));

    return maxDates;
  }

  @Override
  protected void validateEffectiveDate(
      @NonNull LocalDate effectiveDate, @NonNull Employee context) {
    var earliestExpiryDate = getEarliestExpiryDate(context);
    var employeeTerminationDate = Optional.ofNullable(context.getEndDate());

    if (!effectiveDate.isAfter(earliestExpiryDate))
      throw new PhasedDataStartsInInvalidRangeException(earliestExpiryDate.plusDays(1));

    employeeTerminationDate.ifPresent(
        terminationDate -> {
          if (terminationDate.isBefore(effectiveDate)) {
            throw new EmployeeWasTerminatedBeforeEffectiveDateException();
          }
        });
  }

  @Override
  protected boolean isInitialRecord(
      @NonNull WorkerLegalWorkLocation record, @NonNull Employee context) {
    return record.getStartDate().equals(context.getStartDate());
  }

  @Override
  protected void setInitialRecordAndStartDate(
      @NonNull WorkerLegalWorkLocation record, @NonNull Employee context) {
    record.setStartDate(context.getStartDate());
  }

  @Override
  protected void updateExpiryDate(@NonNull WorkerLegalWorkLocation record, LocalDate expiryDate) {
    record.setEndDate(expiryDate);
  }

  @Override
  protected LocalDate getNowDate(@NonNull Employee context) {
    var company = companyService.getCompany(context.getCompanyId());
    return company.getNowDate();
  }

  private Consumer<WorkerLegalWorkLocation> getAddressDecorator(
      @NonNull DetailedUser user, @NonNull LocalDate referenceDate) {
    return (WorkerLegalWorkLocation location) -> {
      if (location.isHomeAddress()) {
        var forDate = DateUtil.maxDate(referenceDate, location.getStartDate());
        location.setAddress(
            user.findActiveHomeAddress(forDate)
                .map(PhysicalAddressConverter::toEmbeddableAddress)
                .orElse(null));
      } else {
        var workLocation =
            workLocationLookupService.findOneOrThrowUnsafe(
                new WorkLocationLookup().setIds(Set.of(location.getWorkLocationId())));
        location.setAddress(PhysicalAddressConverter.toEmbeddableAddress(workLocation));
        location.setWorkLocationName(workLocation.getName());

        var stateUnemploymentTaxLocation =
            workLocationLookupService.findOneOrThrowUnsafe(
                new WorkLocationLookup()
                    .setIds(Set.of(location.getStateUnemploymentTaxLocationId())));
        location.setStateUnemploymentTaxAddress(
            PhysicalAddressConverter.toEmbeddableAddress(stateUnemploymentTaxLocation));
      }
    };
  }

  private Consumer<WorkerLegalWorkLocation> getAddressDecoratorPerEmployee(
    @NonNull DetailedUser user, @NonNull LocalDate referenceDate, WorkLocation workLocation) {
    return (WorkerLegalWorkLocation location) -> {
      if (location.isHomeAddress()) {
        var forDate = DateUtil.maxDate(referenceDate, location.getStartDate());

        location.setAddress(
          user.findActiveHomeAddress(forDate)
            .map(PhysicalAddressConverter::toEmbeddableAddress)
            .orElse(null));
      }
      else
      {
        location.setAddress(PhysicalAddressConverter.toEmbeddableAddress(workLocation));
        location.setWorkLocationName(workLocation.getName());
      }
    };
  }
  public PhysicalAddress getEmployeeStateUnemploymentTaxAddress(
      Long employeeId, LocalDate forDate) {
    return Optional.ofNullable(
            workerLegalWorkLocationRepository.getEmployeeStateUnemploymentTaxAddress(
                employeeId, forDate))
        .filter(pa -> StringUtils.isNotBlank(pa.getLine1()))
        .orElseThrow(ResourceNotFoundException::new);
  }

  public PhysicalAddress getEmployeeLegalWorkAddress(Long employeeId, LocalDate forDate) {
    return Optional.ofNullable(
            workerLegalWorkLocationRepository.getEmployeeLegalWorkAddress(employeeId, forDate))
        .filter(pa -> StringUtils.isNotBlank(pa.getLine1()))
        .orElseThrow(ResourceNotFoundException::new);
  }

  public List<WorkerLegalWorkLocationPerCompany> getWorkersLegalWorkLocationsPerCompanies(WorkerLookup workerLookup, PhaseLookup phaseLookup)
  {
    var employeeLookup = WorkerLookupConverter.toEmployeeLookup(workerLookup, phaseLookup, new DetailedCompany());
    employeeLookup.setSkipApplyAuthorization(true);
    var listedEmployees = detailedEmployeeLookupService.listEmployeesUnsafe(employeeLookup,Pageable.unpaged());

    var companiesIds = listedEmployees.stream().map(DetailedEmployee::getCompanyId).collect(Collectors.toSet());
    var companiesDetails = companyService.listCompanies(Pageable.unpaged(), phaseLookup,companiesIds,null );
    var companiesDetailsMap = companiesDetails.stream().collect(Collectors.toMap(DetailedCompany::getId, detailedCompany -> detailedCompany));

    var localDatePerEmployee = workerLocalTimeService.getLocalDatePerEmployee(listedEmployees, companiesDetailsMap);
    var legalWorkLocationsPerEmployee = getLegalWorkLocationPhaseSchedulePerEmployee(listedEmployees,localDatePerEmployee);

    var workLocationLookup = new WorkLocationLookup()
      .setCompanyIds(companiesIds)
      .setIncludeArchived(true);
    var companiesLocations = workLocationLookupService.listAllUnsafe(workLocationLookup, Pageable.unpaged());
    var companiesLocationsMap = companiesLocations.stream().collect(Collectors.groupingBy(WorkLocation::getCompanyId));

    List<WorkerLegalWorkLocationPerCompany> resultList = new ArrayList<>();
    for (DetailedEmployee employee : listedEmployees)
    {
        var employeeId = employee.getId();
        var companyId = employee.getCompanyId();

        var company = companiesDetailsMap.get(companyId);
        var legalWorkLocation = legalWorkLocationsPerEmployee.get(employeeId);
        var companyLocations = companiesLocationsMap.get(companyId);
        var nowDateForEmployee = localDatePerEmployee.get(employeeId);
        var workerHomeAddress = employee.getUser().findActiveHomeAddress(nowDateForEmployee);
        var resultItem = new WorkerLegalWorkLocationPerCompany(
                employee.getWorkerId(),
                companyId,
                company.getDisplayName(),
                legalWorkLocation,
                workerHomeAddress
        );
        resultItem.setCompanyWorkLocations(companyLocations);

        resultList.add(resultItem);
    }

    return resultList;
  }

  public Collection<WorkerLegalWorkLocation> getWorkersLegalWorkLocationsPerUser(@NonNull DetailedUser user, Collection<DetailedEmployee> linkedEmployees) {
    var zonedNow = ZonedDateTime.now();
    return linkedEmployees.stream()
      .map(employee ->
      {
        var nowDate = workerLocalTimeService.zonedTimestamp(employee.getId(), zonedNow).toLocalDate();
        return getDecoratedLegalWorkLocationPhaseSchedule(user, employee, nowDate)
          .getCurrent();
      })
      .filter(Objects::nonNull)
      .collect(Collectors.toList());
  }
}
