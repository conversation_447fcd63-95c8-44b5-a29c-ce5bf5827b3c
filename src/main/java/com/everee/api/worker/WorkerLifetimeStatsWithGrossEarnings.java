package com.everee.api.worker;

import com.everee.api.money.Money;
import java.time.LocalDate;

public class WorkerLifetimeStatsWithGrossEarnings implements WorkerLifetimeStats {
    private String workerId;
    private Long employeeId;
    private Long companyId;
    private String fullName;
    private LocalDate minForDate;
    private LocalDate maxForDate;
    private Long finalizedPaymentCount;
    private Long nonFinalizedPaymentCount;
    private Money finalizedGrossEarnings;
    private Money nonFinalizedGrossEarnings;
    private Double finalizedShiftAmount;
    private Double nonFinalizedShiftAmount;
    private boolean hasGrossEarnings;

    public WorkerLifetimeStatsWithGrossEarnings(WorkerLifetimeStats stats, boolean hasEarnings) {
        this.workerId = stats.getWorkerId();
        this.employeeId = stats.getEmployeeId();
        this.companyId = stats.getCompanyId();
        this.fullName = stats.getFullName();
        this.minForDate = stats.getMinForDate();
        this.maxForDate = stats.getMaxForDate();
        this.finalizedPaymentCount = stats.getFinalizedPaymentCount();
        this.nonFinalizedPaymentCount = stats.getNonFinalizedPaymentCount();
        this.finalizedGrossEarnings = stats.getFinalizedGrossEarnings();
        this.nonFinalizedGrossEarnings = stats.getNonFinalizedGrossEarnings();
        this.finalizedShiftAmount = stats.getFinalizedShiftAmount();
        this.nonFinalizedShiftAmount = stats.getNonFinalizedShiftAmount();
        this.hasGrossEarnings = hasEarnings;
    }


    @Override
    public String getWorkerId() {
        return workerId;
    }

    @Override
    public Long getEmployeeId() {
        return employeeId;
    }

    @Override
    public Long getCompanyId() {
        return companyId;
    }

    @Override
    public String getFullName() {
        return fullName;
    }

    @Override
    public LocalDate getMinForDate() {
        return minForDate;
    }

    @Override
    public LocalDate getMaxForDate() {
        return maxForDate;
    }

    @Override
    public Long getFinalizedPaymentCount() {
        return finalizedPaymentCount;
    }

    @Override
    public Long getNonFinalizedPaymentCount() {
        return nonFinalizedPaymentCount;
    }

    @Override
    public Money getFinalizedGrossEarnings() {
        return finalizedGrossEarnings;
    }

    @Override
    public Money getNonFinalizedGrossEarnings() {
        return nonFinalizedGrossEarnings;
    }

    @Override
    public Double getFinalizedShiftAmount() {
        return finalizedShiftAmount;
    }

    @Override
    public Double getNonFinalizedShiftAmount() {
        return nonFinalizedShiftAmount;
    }

    public boolean getHasGrossEarnings() {
        return hasGrossEarnings;
    }

    public void setWorkerId(String workerId) {
        this.workerId = workerId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public void setMinForDate(LocalDate minForDate) {
        this.minForDate = minForDate;
    }

    public void setMaxForDate(LocalDate maxForDate) {
        this.maxForDate = maxForDate;
    }

    public void setFinalizedPaymentCount(Long finalizedPaymentCount) {
        this.finalizedPaymentCount = finalizedPaymentCount;
    }

    public void setNonFinalizedPaymentCount(Long nonFinalizedPaymentCount) {
        this.nonFinalizedPaymentCount = nonFinalizedPaymentCount;
    }

    public void setFinalizedGrossEarnings(Money finalizedGrossEarnings) {
        this.finalizedGrossEarnings = finalizedGrossEarnings;
    }

    public void setNonFinalizedGrossEarnings(Money nonFinalizedGrossEarnings) {
        this.nonFinalizedGrossEarnings = nonFinalizedGrossEarnings;
    }

    public void setFinalizedShiftAmount(Double finalizedShiftAmount) {
        this.finalizedShiftAmount = finalizedShiftAmount;
    }

    public void setNonFinalizedShiftAmount(Double nonFinalizedShiftAmount) {
        this.nonFinalizedShiftAmount = nonFinalizedShiftAmount;
    }

    public void setHasGrossEarnings(boolean hasGrossEarnings) {
        this.hasGrossEarnings = hasGrossEarnings;
    }
}
