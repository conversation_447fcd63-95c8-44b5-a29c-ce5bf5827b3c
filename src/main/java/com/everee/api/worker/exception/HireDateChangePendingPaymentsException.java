package com.everee.api.worker.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class HireDateChangePendingPaymentsException extends LocalizedRuntimeException {
  public HireDateChangePendingPaymentsException() {
    super("worker.exception.HireDateChangePendingPaymentsException.message");
    setErrorCode(HireDateErrorCodes.HIRE_DATE_CHANGE_PENDING_PAYMENTS);
  }
}
