package com.everee.api.worker.exception;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.CONFLICT)
public class ManagerCircularRelationshipException extends LocalizedRuntimeException {
  public ManagerCircularRelationshipException(DetailedEmployee manager, DetailedEmployee employee) {
    super(
        "worker.exception.ManagerCircularRelationshipException.message",
        manager.getFullName(),
        employee.getFullName());
  }
}
