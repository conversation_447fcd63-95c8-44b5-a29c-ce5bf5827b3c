package com.everee.api.worker.exception;

public class HireDateErrorCodes {
  public static final String HIRE_DATE_CHANGE_WORKER_NOT_FOUND = "hire-date-change:worker-not-found";
  public static final String HIRE_DATE_CHANGE_RECEIVED_PAYMENTS = "hire-date-change:worker-has-received-payments";
  public static final String HIRE_DATE_CHANGE_PENDING_PAYMENTS = "hire-date-change:worker-has-pending-payments";
  public static final String HIRE_DATE_CHANGE_AFTER_TIME_WORKED = "hire-date-change:worker-has-time-worked";
  public static final String HIRE_DATE_CHANGE_AFTER_TIME_OFF = "hire-date-change:worker-has-time-off";
  public static final String HIRE_DATE_CHANGE_AFTER_SCHEDULED_TIME = "hire-date-change:worker-has-scheduled-time";
}
