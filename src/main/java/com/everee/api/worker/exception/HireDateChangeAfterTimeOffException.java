package com.everee.api.worker.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class HireDateChangeAfterTimeOffException extends LocalizedRuntimeException {
  public HireDateChangeAfterTimeOffException() {
    super("worker.exception.HireDateChangeAfterTimeOffException.message");
    setErrorCode(HireDateErrorCodes.HIRE_DATE_CHANGE_AFTER_TIME_OFF);
  }
}
