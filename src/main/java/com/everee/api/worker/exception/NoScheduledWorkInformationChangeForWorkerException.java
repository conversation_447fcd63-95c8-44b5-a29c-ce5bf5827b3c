package com.everee.api.worker.exception;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class NoScheduledWorkInformationChangeForWorkerException extends LocalizedRuntimeException {
  public NoScheduledWorkInformationChangeForWorkerException() {
    super("worker.exception.NoScheduledWorkInformationChangeForWorkerException.message");
  }
}
