package com.everee.api.taxauthority;

import static com.everee.api.model.BaseModel_.ID;
import static com.everee.api.query.where.Where.property;
import static com.everee.api.taxauthority.TaxAuthoritySubmission_.EXTERNAL_IDENTIFIER;
import static com.everee.api.taxauthority.TaxAuthoritySubmission_.SENT_AT;
import static com.everee.api.taxauthority.TaxAuthoritySubmission_.STATUS;
import static com.everee.api.taxauthority.TaxAuthoritySubmission_.TAX_AUTHORITY_ID;

import com.everee.api.lookup.TenantlessLookupService;
import com.everee.api.query.Query;
import com.everee.api.query.where.WhereClause;
import java.util.Set;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TaxAuthoritySubmissionLookupService
    implements TenantlessLookupService<
        TaxAuthoritySubmission, TaxAuthoritySubmissionLookup, Query<TaxAuthoritySubmission>> {
  private final EntityManager entityManager;

  public Page<TaxAuthoritySubmission> listUnsent(
      TaxAuthoritySubmissionLookup lookup, Pageable pageable) {
    lookup.setIsSent(Boolean.FALSE);
    return getConfiguredQuery(lookup).findAll(pageable);
  }

  @Override
  public void configureQuery(
      TaxAuthoritySubmissionLookup lookup, Query<TaxAuthoritySubmission> query) {
    WhereClause isSentWhereClause = property(SENT_AT).in(Set.of()); // Default is to ignore sent at
    if (lookup.getIsSent() != null) {
      isSentWhereClause =
          lookup.getIsSent() ? property(SENT_AT).isNotNull() : property(SENT_AT).isNull();
    }

    query
        .where(property(ID).in(lookup.getIds()))
        .where(property(EXTERNAL_IDENTIFIER).in(lookup.getExternalIdentifiers()))
        .where(property(TAX_AUTHORITY_ID).in(lookup.getTaxAuthorityIds()))
        .where(isSentWhereClause)
        .where(property(STATUS).equal(lookup.getSubmissionStatus()));
  }

  @Override
  public Query<TaxAuthoritySubmission> createQuery() {
    return new Query<>(entityManager, TaxAuthoritySubmission.class);
  }
}
