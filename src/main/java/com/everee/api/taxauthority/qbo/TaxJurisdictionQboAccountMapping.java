package com.everee.api.taxauthority.qbo;

import com.everee.api.model.BaseModel;
import javax.persistence.Entity;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Entity
public class TaxJurisdictionQboAccountMapping extends BaseModel {
  @NotNull private String accountIdentifier;
  @NotNull private String accountName;
  @NotNull private String taxJurisdictionId;
}
