package com.everee.api.taxauthority.qbo;

import com.everee.api.integration.accounting.journalentry.EntityType;
import com.everee.api.model.BaseModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.Entity;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Entity
public class TaxAuthorityQboVendorMapping extends BaseModel {
  @NotNull private String vendorIdentifier;
  @NotNull private String vendorName;
  @NotNull private Long taxAuthorityId;

  @JsonIgnore
  @Transient
  public EntityType getEntityType() {
    return EntityType.VENDOR;
  }
}
