package com.everee.api.geocoding.zone;

import com.everee.api.i18n.LocalizedRuntimeException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
class InaccurateGeoTimezoneRequestException extends LocalizedRuntimeException {

  public InaccurateGeoTimezoneRequestException() {
    super("geocoding.InaccurateGeocodingException.message");
  }
}
