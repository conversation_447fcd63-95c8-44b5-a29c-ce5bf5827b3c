package com.everee.api.geocoding;

import com.everee.api.user.address.GeocodedAddress;
import com.everee.api.user.address.GeocodingRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/geocoding")
@RequiredArgsConstructor
public class GeocodingController {
  private final GeocodingService geocodingService;

  @PostMapping
  public List<GeocodedAddress> geocode(@RequestBody GeocodingRequest request) {
    return geocodingService.getGeocodedAddresses(request);
  }
}
