package com.everee.api.payrun.lookup;


import com.everee.api.config.LookupReportCriteria;
import com.everee.api.config.RequestParams;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.lookup.Lookup;
import com.everee.api.payrun.models.PayRunStatus;
import com.everee.api.payrun.models.PayRunType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Set;


@Data
@With
@AllArgsConstructor
@NoArgsConstructor
@RequestParams
@Accessors(chain = true)
public class PayRunLookup implements Lookup {
   
    @JsonProperty("id")
    @ApiParam(name = "id", allowMultiple = true)
    @Parameter(name = "id", description = "Pay run IDs to filter by.")
    private Set<Long> ids;
    
    @ApiParam(name = "status", allowMultiple = true)
    @Parameter(name = "status", description = "Pay run statuses to filter by.")
    private Set<PayRunStatus> status;

    @ApiParam(name = "type", allowMultiple = true)
    @Parameter(name = "type", description = "Pay run Type to filter by.")
    private Set<PayRunType> type;

    @JsonProperty("pay-period-id")
    @ApiParam(name = "pay-period-id")
    @Parameter(name = "pay-period-id", description = "Pay period ID to filter by.")
    private Long payPeriodId;

    @JsonProperty("finalized")
    @ApiParam(name = "finalized")
    @Parameter(name = "finalized", description = "Whether the pay run is finalized.")
    private Boolean finalized;

    @ApiParam(name = "company-id", example = "1", allowMultiple = true)
    @Parameter(name = "company-id", description = "The company ids to filter on")
    private Set<Long> companyIds;

    @JsonProperty("demo-company")
    @ApiParam(name = "demo-company")
    @Parameter(name = "demo-company", description = "Pay runs for demo companies.")
    private Boolean demoCompany;

    @JsonProperty("min-pay-date")
    @ApiParam(name = "min-pay-date")
    @LookupReportCriteria(name = "Pay runs with pay dates on or after this date will be included.")
    @Parameter(name = "min-pay-date", description = "Pay runs with pay dates on or after this date will be included.")
    private LocalDate minPayDate;

    @JsonProperty("max-pay-date")
    @ApiParam(name = "max-pay-date")
    @LookupReportCriteria(name = "Pay runs with pay dates on or before this date will be included.")
    @Parameter(name = "max-pay-date", description = "Pay runs with pay dates on or before this date will be included.")
    private LocalDate maxPayDate;

    @JsonProperty("filter-on-cycle-not-started")
    @ApiParam(name = "filter-on-cycle-not-started")
    @Parameter(name = "filter-on-cycle-not-started", description = "Exclude pay runs with status NOT_STARTED and type ON_CYCLE.")
    private Boolean filterOnCycleNotStarted;

  public Boolean getFilterOnCycleNotStarted() {
    if (filterOnCycleNotStarted != null) {
      return filterOnCycleNotStarted;
    }

    // Apply default filtering only when this is a general list request (i.e., no specific pay run IDs provided)
    boolean isGeneralLookup = ids == null || ids.isEmpty();
    return isGeneralLookup;
  }

    /**
     * Validates that minPayDate is not after maxPayDate.
     *
     * @throws InvalidRequestException if minPayDate is after maxPayDate
     */
    public void validatePayDate() {
      if (minPayDate != null && maxPayDate != null && minPayDate.isAfter(maxPayDate)) {
        throw new InvalidRequestException("min-pay-date cannot be after max-pay-date.");
      }
    }

    public LocalDate getMinPayDate() {
      validatePayDate();
      return minPayDate;
    }

    public LocalDate getMaxPayDate() {
      validatePayDate();
      return maxPayDate;
    }
}
