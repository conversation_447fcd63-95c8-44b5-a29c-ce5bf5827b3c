package com.everee.api.payrun.models;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response returned after a successful deletion of a Pay Run.
 * Includes IDs of deleted payrun, payments, pay period, and a confirmation message.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayRunDeletionResult {
    private Long PayRunId;
    private List<Long> paymentIds;
    private Long payPeriodId;
    private String message;
}
