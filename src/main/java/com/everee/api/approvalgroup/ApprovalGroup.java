package com.everee.api.approvalgroup;

import com.everee.api.team.Team;
import java.time.LocalDateTime;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.Formula;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ApprovalGroup implements Team {
  private static final String ACTIVE_MEMBER_COUNT_FORMULA =
      "(select count(*) from employee e join company c on c.id = e.companyid where "
          + "e.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE and "
          + "(e.enddate IS NULL OR e.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE) and e.approvalgroupid = id )";

  private static final String ACTIVE_OR_UPCOMING_MEMBER_COUNT_FORMULA =
      "(select count(*) from employee e join company c on c.id = e.companyid where "
          + "(e.enddate IS NULL OR e.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE) and e.approvalgroupid = id )";

  private static final String ACTIVE_APPROVER_COUNT_FORMULA =
      "(SELECT "
          + " count(DISTINCT a.userid) "
          + "FROM "
          + " approvalgroupapprover a "
          + " LEFT JOIN employee e ON e.userid = a.userid "
          + " JOIN company c ON a.companyid = c.id "
          + "WHERE "
          + " a.approvalgroupid = id "
          + " AND(e.id IS NULL "
          + "  OR(e.startdate <= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE "
          + "   AND(e.enddate IS NULL "
          + "    OR e.enddate >= timezone(c.timezone, CURRENT_TIMESTAMP)::DATE))))";

  @Id
  @With
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @NotNull private Long companyId;

  private String name;

  @Setter(AccessLevel.NONE)
  @Column(insertable = false, updatable = false)
  private boolean deleted;

  private LocalDateTime deletedAt;

  @Setter(AccessLevel.NONE)
  @Formula(ACTIVE_OR_UPCOMING_MEMBER_COUNT_FORMULA)
  private long membersCount = 0;

  @Setter(AccessLevel.NONE)
  @Formula(ACTIVE_APPROVER_COUNT_FORMULA)
  private long approversCount = 0;

  /**
   * @deprecated This field was carried over from the legacy {@link Team} implementation and is
   *     included for backward-compatibility purposes. At the time of this writing, we plan to
   *     continue using this field to provide a somewhat continuous experience for existing clients
   *     by preselecting the group with this flag set to true (if one exists) in the "add new
   *     worker" experience. It is not clear when or if this field may be removed. --kjensen 6/6/22
   */
  @Setter(AccessLevel.NONE)
  @NotNull
  @Deprecated
  private boolean systemTeam;

  /**
   * @deprecated This field was carried over from the legacy {@link Team} implementation and is
   *     included for backward-compatibility purposes only. At the time of this writing, we aren't
   *     sure what mechanism we're going to use to clean up old data, so this field may or may not
   *     be removed at a future date. Consider using the simpler {@link #membersCount} instead.
   *     --kjensen 6/6/22
   */
  @Setter(AccessLevel.NONE)
  @Formula(ACTIVE_MEMBER_COUNT_FORMULA)
  @Deprecated
  private Integer activeMemberCount;
}
