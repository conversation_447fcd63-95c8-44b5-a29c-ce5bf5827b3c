package com.everee.api.scheduling.shift.conflict.validators;

import com.everee.api.company.CompanyService;
import com.everee.api.scheduling.shift.ScheduledShift;
import com.everee.api.scheduling.shift.ScheduledShiftLookup;
import com.everee.api.scheduling.shift.ScheduledShiftLookupService;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflict;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflictType;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ShiftOvertimeValidator implements ShiftConflictValidator {
  private final ScheduledShiftLookupService scheduledShiftLookupService;
  private final CompanyService companyService;
  private boolean enabled = false;

  @Getter
  private final ScheduledShiftConflictType conflictType = ScheduledShiftConflictType.OVERTIME;

  @Override
  public Optional<ScheduledShiftConflict> validate(ShiftConflictValidatorContext context) {
    if (!enabled) {
      return Optional.empty();
    }

    var existingConflict = getExistingConflict(context);
    if (context.getEmployeeId() == null) {
      removeConflict(context, existingConflict);
      return Optional.empty();
    }

    var shift = context.getShift();
    var existingShifts =
        scheduledShiftLookupService
            .listAll(
                new ScheduledShiftLookup()
                    .setEmployeeIds(Set.of(context.getEmployeeId()))
                    .setStartDate(shift.getStartDateTime().toLocalDate())
                    .setEndDate(shift.getEndDateTime().toLocalDate()),
                Pageable.unpaged())
            .stream()
            .filter(
                s ->
                    !context.getIgnoredShiftIds().contains(s.getId())
                        && !s.getId().equals(shift.getId()))
            .collect(Collectors.toList());

    // FUTURE: consider punched shifts in place of scheduled shifts
    var existingPaidShiftDurations =
        existingShifts.stream()
            .map(ScheduledShift::getPaidDuration)
            .reduce(Duration.ZERO, Duration::plus);

    var paidShiftDurations =
        context.isRemovingShift()
            ? existingPaidShiftDurations
            : existingPaidShiftDurations.plus(shift.getPaidDuration());

    // find existing conflict for overtime that is not for this shift and any for this shift
    var existingOtConflicts =
        context.getExistingConflicts().stream()
            .filter(
                conflict ->
                    !Objects.equals(conflict.getScheduledShiftId(), shift.getId())
                        && getConflictType().equals(conflict.getType()))
            .collect(Collectors.toList());

    var overtimeSettings = companyService.getCompany(shift.getCompanyId()).getOvertimeSettings();
    var isOvertime =
        overtimeSettings.getWeeklyOvertimeHoursThreshold().minus(paidShiftDurations).isNegative();

    if (!context.isRemovingShift() && isOvertime) {
      existingConflict = Optional.of(existingConflict.orElseGet(() -> buildNewConflict(context)));
    } else {
      removeConflict(context, existingConflict);
      existingConflict = Optional.empty();
      if (!isOvertime) existingOtConflicts.forEach(c -> removeConflict(context, Optional.of(c)));
    }

    return existingConflict;
  }

  private boolean shiftsOverlap(ScheduledShift shift, ScheduledShift shift2) {
    var shiftStart = shift.getStartDateTime();
    var shiftEnd = shift.getEndDateTime();

    var shiftStartsDuringShift2 = shiftBoundaryIsDuringUnavailableTime(shiftStart, shift2);
    var shiftEndsDuringShift2 = shiftBoundaryIsDuringUnavailableTime(shiftEnd, shift2);
    var shiftSpansShift2 =
        shiftStart.isBefore(shift2.getStartDateTime()) && shiftEnd.isAfter(shift2.getEndDateTime());

    return shiftStartsDuringShift2 || shiftEndsDuringShift2 || shiftSpansShift2;
  }

  private boolean shiftBoundaryIsDuringUnavailableTime(
      LocalDateTime shiftBoundary, ScheduledShift shift) {
    return !shiftBoundary.isAfter(shift.getEndDateTime())
        && !shiftBoundary.isBefore(shift.getStartDateTime());
  }
}
