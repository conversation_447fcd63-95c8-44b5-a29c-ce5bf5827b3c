package com.everee.api.scheduling.shift.conflict;

import com.everee.api.auth.util.AuthorizationCheckService;
import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.time.WorkLocationLocalTimeService;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ScheduledShiftConflictService {
  private final ScheduledShiftConflictLookupService scheduledShiftConflictLookupService;
  private final ScheduledShiftConflictRepository scheduledShiftConflictRepository;
  private final AuthorizationCheckService authorizationCheckService;
  private final WorkLocationLocalTimeService workLocationLocalTimeService;

  public Page<ScheduledShiftConflict> listConflicts(
      ScheduledShiftConflictLookup scheduledShiftConflictLookup, Pageable pageable) {
    return scheduledShiftConflictLookupService.listAll(scheduledShiftConflictLookup, pageable);
  }

  public ScheduledShiftConflict create(
      @NotNull Long companyId, @NotNull Long scheduledShiftId, Long conflictingShiftId) {
    var scheduledShiftConflict = new ScheduledShiftConflict();
    scheduledShiftConflict.setCompanyId(companyId);
    scheduledShiftConflict.setScheduledShiftId(scheduledShiftId);
    scheduledShiftConflict.setConflictingShiftId(conflictingShiftId);
    return scheduledShiftConflictRepository.save(scheduledShiftConflict);
  }

  public ScheduledShiftConflict get(Long id, Long companyId) {
    return scheduledShiftConflictRepository
        .findByIdAndCompanyId(id, companyId)
        .orElseThrow(ResourceNotFoundException::new);
  }

  public ScheduledShiftConflict save(ScheduledShiftConflict conflict) {
    return scheduledShiftConflictRepository.save(conflict);
  }

  public ScheduledShiftConflict markResolved(Long id, Long companyId, Long userId) {
    var savedConflict = get(id, companyId);
    if (userId != savedConflict.getApprovedByUserId()
        && !authorizationCheckService.isAnyApprover()) {
      throw new ResourceNotFoundException();
    }
    if (ScheduledShiftConflictState.CONFLICT.equals(savedConflict.getState())) {
      savedConflict.setState(ScheduledShiftConflictState.RESOLVED);
      savedConflict.setApprovedDateTime(
          workLocationLocalTimeService.locationLocalNowTimestamp(
              savedConflict.getLocationId(), savedConflict.getCompanyId()));
    }
    return scheduledShiftConflictRepository.save(savedConflict);
  }

  public void delete(Long id, Long companyId) {
    delete(get(id, companyId));
  }

  public void delete(ScheduledShiftConflict conflict) {
    scheduledShiftConflictRepository.delete(conflict);
  }
}
