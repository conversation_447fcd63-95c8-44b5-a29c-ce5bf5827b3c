package com.everee.api.scheduling.shift;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduledShiftRepository extends JpaRepository<ScheduledShift, Long> {
  Optional<ScheduledShift> findByIdAndCompanyId(Long id, Long companyId);

  List<ScheduledShift> findAllByEmployeeIdAndCompanyId(Long employeeId, Long companyId);
}
