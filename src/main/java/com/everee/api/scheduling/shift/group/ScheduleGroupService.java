package com.everee.api.scheduling.shift.group;

import static com.everee.api.query.where.Where.or;
import static com.everee.api.query.where.Where.property;
import static com.everee.api.scheduling.shift.group.ScheduleGroup_.ARCHIVED_AT;
import static com.everee.api.scheduling.shift.group.ScheduleGroup_.COMPANY_ID;
import static com.everee.api.scheduling.shift.group.ScheduleGroup_.LOCATION_ID;

import com.everee.api.exception.ResourceNotFoundException;
import com.everee.api.query.Query;
import com.everee.api.scheduling.shift.ScheduledShiftLookup;
import com.everee.api.scheduling.shift.ScheduledShiftService;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Set;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ScheduleGroupService {
  private final EntityManager entityManager;
  private final ScheduleGroupRepository scheduleGroupRepository;
  private final ScheduledShiftService scheduledShiftService;

  public Page<ScheduleGroup> listGroups(
      Set<Long> companyIds, Set<Long> locationIds, LocalDate archivedAfter, Pageable pageable) {
    var query = new Query<>(entityManager, ScheduleGroup.class);
    query.where(property(COMPANY_ID).in(companyIds));
    query.where(property(LOCATION_ID).in(locationIds));
    query.where(
        or(
            property(ARCHIVED_AT).isNull(),
            property(ARCHIVED_AT).greaterThanOrEqual(archivedAfter.atStartOfDay())));

    return query.findAll(pageable);
  }

  public ScheduleGroup getGroup(Long id, Long companyId) {
    return scheduleGroupRepository
        .findByIdAndCompanyId(id, companyId)
        .orElseThrow(ResourceNotFoundException::new);
  }

  public ScheduleGroup create(ScheduleGroup scheduleGroup) {
    return scheduleGroupRepository.save(scheduleGroup);
  }

  public ScheduleGroup update(Long id, Long companyId, ScheduleGroup scheduleGroup) {
    var savedGroup = getGroup(id, companyId);
    savedGroup.setName(scheduleGroup.getName());
    savedGroup.setArchivedAt(scheduleGroup.getArchivedAt());
    return scheduleGroupRepository.save(savedGroup);
  }

  @Transactional
  public void delete(Long id, Long companyId, LocalDate archiveDate, Boolean forceDelete) {
    var savedGroup = getGroup(id, companyId);

    if (Boolean.TRUE.equals(forceDelete)) {
      scheduleGroupRepository.delete(savedGroup);
    } else {
      scheduledShiftService
          .listShifts(
              new ScheduledShiftLookup()
                  .setCompanyId(companyId)
                  .setGroupIds(Set.of(id))
                  .setStartDate(archiveDate.plusDays(1))
                  .setEndDate(archiveDate.plusYears(1)),
              Pageable.unpaged())
          .forEach(s -> s.setScheduleGroupId(null));

      savedGroup.setArchivedAt(archiveDate.atTime(LocalTime.MAX));
      scheduleGroupRepository.save(savedGroup);
    }
  }
}
