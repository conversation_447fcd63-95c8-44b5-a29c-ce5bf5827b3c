package com.everee.api.scheduling.shift;

import java.time.LocalDateTime;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ScheduledShiftParams {
  @NotNull private LocalDateTime startDateTime;
  @NotNull private LocalDateTime endDateTime;
  @NotNull private Long locationId;

  @NotNull private ScheduledShiftColor color = ScheduledShiftColor.COLOR_1;

  @NotNull private PublishState publishState = PublishState.UNPUBLISHED;
  private LocalDateTime publishedDateTime;

  private Long employeeId;

  private Long approvalGroupId;
  private Long scheduleGroupId;
  private Long shiftTemplateId;

  private String title;
  private String note;
  private Boolean forceRevalidate;
}
