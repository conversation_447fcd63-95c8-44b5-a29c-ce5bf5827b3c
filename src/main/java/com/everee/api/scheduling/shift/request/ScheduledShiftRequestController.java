package com.everee.api.scheduling.shift.request;

import com.everee.api.company.CompanyService;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.user.UserService;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/schedule/requests")
@RequiredArgsConstructor
public class ScheduledShiftRequestController {
  private final ScheduledShiftRequestService scheduledShiftRequestService;
  private final ScheduledShiftRequestRecipientService scheduledShiftRequestRecipientService;
  private final ScheduledShiftRequestRecipientStateService
      scheduledShiftRequestRecipientStateService;

  @GetMapping
  public Page<ScheduledShiftRequest> listScheduledShiftRequests(
      @Valid ScheduledShiftRequestLookup scheduledShiftRequestRequestLookup, Pageable pageable) {
    scheduledShiftRequestRequestLookup.setCompanyId(CompanyService.getAuthenticatedCompanyId());
    return scheduledShiftRequestService.listShiftRequests(
        scheduledShiftRequestRequestLookup, pageable);
  }

  @GetMapping("/{scheduledShiftRequestId}")
  public ScheduledShiftRequest getScheduledShiftRequest(
      @PathVariable("scheduledShiftRequestId") Long scheduledShiftRequestId) {
    return scheduledShiftRequestService.get(
        scheduledShiftRequestId, CompanyService.getAuthenticatedCompanyId());
  }

  @PostMapping
  public ScheduledShiftRequest createScheduledShiftRequest(
      @Valid @RequestBody ScheduledShiftRequestParamsForCreate scheduledShiftRequestParams) {
    return scheduledShiftRequestService.create(
        CompanyService.getAuthenticatedCompanyId(),
        DetailedEmployeeLookupService.getAuthenticatedActiveEmployeeId(),
        scheduledShiftRequestParams);
  }

  @PutMapping("/{scheduledShiftRequestId}")
  public ScheduledShiftRequest updateScheduledShiftRequest(
      @PathVariable("scheduledShiftRequestId") Long scheduledShiftRequestId,
      @Valid @RequestBody ScheduledShiftRequestParamsForUpdate scheduledShiftRequestParams) {
    return scheduledShiftRequestService.update(
        scheduledShiftRequestId,
        CompanyService.getAuthenticatedCompanyId(),
        scheduledShiftRequestParams);
  }

  @PutMapping("/{scheduledShiftRequestId}/recipient/{recipientEmployeeId}")
  public ScheduledShiftRequest updateScheduledShiftRequestRecipient(
      @PathVariable("scheduledShiftRequestId") Long scheduledShiftRequestId,
      @PathVariable("recipientEmployeeId") Long recipientEmployeeId,
      @RequestParam("recipient-state") ScheduledShiftRequestRecipientState recipientState,
      @RequestParam(value = "note", required = false) String note) {
    return scheduledShiftRequestRecipientStateService.updateRecipientState(
        recipientEmployeeId,
        scheduledShiftRequestId,
        CompanyService.getAuthenticatedCompanyId(),
        UserService.getAuthenticatedUserId(),
        recipientState,
        note);
  }

  @DeleteMapping("/{scheduledShiftRequestId}/recipient/{recipientEmployeeId}")
  public ScheduledShiftRequest deleteScheduledShiftRequestRecipient(
      @PathVariable("scheduledShiftRequestId") Long scheduledShiftRequestId,
      @PathVariable("recipientEmployeeId") Long recipientEmployeeId) {
    return scheduledShiftRequestRecipientService.deleteScheduledShiftRequestRecipient(
        recipientEmployeeId, scheduledShiftRequestId, CompanyService.getAuthenticatedCompanyId());
  }

  @DeleteMapping("/{scheduledShiftRequestId}")
  public void deleteScheduledShiftRequest(
      @PathVariable("scheduledShiftRequestId") Long scheduledShiftRequestId) {
    scheduledShiftRequestService.delete(
        scheduledShiftRequestId, CompanyService.getAuthenticatedCompanyId());
  }
}
