package com.everee.api.scheduling.shift.conflict.validators;

import com.everee.api.phase.Phase;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflict;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflictType;
import com.everee.api.timeoff.request.*;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ShiftDuringPTOValidator implements ShiftConflictValidator {
  private final TimeOffRequestLookupService timeOffRequestLookupService;

  @Getter private final ScheduledShiftConflictType conflictType = ScheduledShiftConflictType.PTO;

  @Override
  public Optional<ScheduledShiftConflict> validate(ShiftConflictValidatorContext context) {
    var ptoConflict = getExistingConflict(context);

    if (context.getEmployeeId() == null) {
      removeConflict(context, ptoConflict);
      return Optional.empty();
    }

    var shift = context.getShift();
    var timeOffRequests =
        timeOffRequestLookupService
            .listAll(
                new TimeOffRequestLookup()
                    .withEmployeeIds(Set.of(context.getEmployeeId()))
                    .withSkipApplyAuthorization(true)
                    .withStatuses(
                        Set.of(TimeOffRequestStatus.APPROVED, TimeOffRequestStatus.REQUESTED))
                    .withPhaseLookup(
                        new PhaseLookup(
                            Phase.ACTIVE,
                            shift.getStartDateTime().toLocalDate(),
                            shift.getEndDateTime().toLocalDate())),
                Pageable.unpaged())
            .getContent();

    var daysWithPto =
        timeOffRequests.stream()
            .map(TimeOffRequest::getDayAmounts)
            .flatMap(Collection::stream)
            .map(TimeOffRequestDayAmount::getDate)
            .collect(Collectors.toSet());

    if (!context.isRemovingShift()
        && (daysWithPto.contains(shift.getStartDateTime().toLocalDate())
            || daysWithPto.contains(shift.getEndDateTime().toLocalDate()))) {

      ptoConflict = Optional.of(ptoConflict.orElseGet(() -> buildNewConflict(context)));

    } else {
      removeConflict(context, ptoConflict);
      ptoConflict = Optional.empty();
    }

    return ptoConflict;
  }
}
