package com.everee.api.scheduling.shift;

import com.everee.api.config.PresignedCustomerFacingFileLocationSerializer;
import com.everee.api.util.DisplayFullName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ScheduledShiftEmployee implements DisplayFullName {
  private Long id;
  private String firstName;
  private String lastName;

  @JsonSerialize(using = PresignedCustomerFacingFileLocationSerializer.class)
  private String pictureThumbnailUrl;

  @Override
  public String getMiddleName() {
    return null;
  }
}
