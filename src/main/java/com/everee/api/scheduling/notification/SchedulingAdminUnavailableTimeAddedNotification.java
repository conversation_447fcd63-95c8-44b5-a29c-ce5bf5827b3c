package com.everee.api.scheduling.notification;

import com.everee.api.link.WebappLinkBuilder;
import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.notification.user.UserNotification;
import com.everee.api.notification.user.UserNotificationChannel;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import java.util.Collection;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.MessageSource;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class SchedulingAdminUnavailableTimeAddedNotification
    extends TransactionalSingleActionEmailTemplate implements UserNotification {
  @NonNull Partner partner;
  @NonNull Long companyId;
  @NonNull Collection<String> employeeNames;
  @NonNull MessageSource messageSource;

  @NonNull UserNotificationChannel channel = UserNotificationChannel.ADMIN_SCHEDULING;
  @NonNull StorageService storageService;

  @Override
  public String getTitle() {
    return getLocalizedMessage("scheduling.admin.new-available-time.email.subject");
  }

  @Override
  public String getMessage() {
    return getBodyParagraph1Text();
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage("scheduling.admin.new-available-time.email.preheader");
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage("scheduling.admin.new-available-time.email.banner-text");
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage("scheduling.admin.new-available-time.email.body-paragraph1-text");
  }

  @Override
  public String getBodyParagraph2Text() {
    return getLocalizedMessage(
        "scheduling.admin.new-available-time.email.body-paragraph2-text",
        "<li>" + StringUtils.join(employeeNames, "</li><li>") + "</li>");
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("scheduling.admin.new-available-time.email.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    return getLinkUrl();
  }

  @Override
  public String getSmsBody() {
    return String.join(" ", getMessage(), getLinkUrl());
  }

  private String getLinkUrl() {
    return new WebappLinkBuilder().pathSegment("schedule").build().toString();
  }

  @Override
  public EmailType getEmailType() {
      return EmailType.USER_SCHEDULING_ADMIN_UNAVAILABLE_TIME_ADDED;
  }
}
