package com.everee.api.scheduling.notification;

import static com.everee.api.scheduling.notification.SchedulingEmployeeNotificationService.SHIFT_DAY_TIME;

import com.everee.api.link.DynamicLinkBuilder;
import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.notification.user.UserNotification;
import com.everee.api.notification.user.UserNotificationChannel;
import com.everee.api.partner.Partner;
import com.everee.api.storage.StorageService;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class SchedulingEmployeeShiftUpdatedNotification
    extends TransactionalSingleActionEmailTemplate implements UserNotification {
  @NonNull Partner partner;
  @NonNull Long companyId;
  @NonNull LocalDateTime shiftStartTime;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;

  @Override
  public String getTitle() {
    return getLocalizedMessage("scheduling.employee.shift-updated.email.subject");
  }

  @Override
  public String getMessage() {
    return getBodyParagraph1Text();
  }

  @Override
  public String getPreheader() {
    return getLocalizedMessage("scheduling.employee.shift-updated.email.subject");
  }

  @Override
  public String getBannerText() {
    return getLocalizedMessage("scheduling.employee.shift-updated.email.banner-text");
  }

  @Override
  public String getBodyParagraph1Text() {
    return getLocalizedMessage("scheduling.employee.shift-updated.email.body-paragraph1-text");
  }

  @Override
  public String getBodyParagraph2Text() {
    var formattedTime = SHIFT_DAY_TIME.format(getShiftStartTime());
    return getLocalizedMessage(
        "scheduling.employee.shift-updated.email.body-paragraph2-text", formattedTime);
  }

  @Override
  public String getButtonLabel() {
    return getLocalizedMessage("scheduling.employee.shift-updated.email.button-label");
  }

  @Override
  public String getButtonLinkUrl() {
    return getLinkUrl();
  }

  @Override
  public String getSmsBody() {
    return String.join(" ", getMessage(), getLinkUrl());
  }

  @Override
  public UserNotificationChannel getChannel() {
    return UserNotificationChannel.EMPLOYEE_SCHEDULING;
  }

  private String getLinkUrl() {
    return new DynamicLinkBuilder()
        .pathSegment("my-account", "schedule")
        .queryParam("date", getShiftStartTime().toLocalTime())
        .build()
        .toString();
  }

  @Override
  public EmailType getEmailType() {
      return EmailType.USER_SCHEDULING_EMPLOYEE_SHIFT_UPDATED;
  }
}
