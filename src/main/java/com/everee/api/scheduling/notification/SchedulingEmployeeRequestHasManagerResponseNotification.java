package com.everee.api.scheduling.notification;

import static com.everee.api.scheduling.notification.SchedulingEmployeeNotificationService.SHIFT_DAY_TIME;

import com.everee.api.link.DynamicLinkBuilder;
import com.everee.api.notification.email.EmailType;
import com.everee.api.notification.email.template.TransactionalSingleActionEmailTemplate;
import com.everee.api.notification.user.UserNotification;
import com.everee.api.notification.user.UserNotificationChannel;
import com.everee.api.partner.Partner;
import com.everee.api.scheduling.shift.request.ScheduledShiftRequestRecipientState;
import com.everee.api.scheduling.shift.request.ScheduledShiftRequestType;
import com.everee.api.storage.StorageService;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.hibernate.cfg.NotYetImplementedException;
import org.springframework.context.MessageSource;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class SchedulingEmployeeRequestHasManagerResponseNotification
    extends TransactionalSingleActionEmailTemplate implements UserNotification {
  @NonNull Partner partner;
  @NonNull Long companyId;
  @NonNull ScheduledShiftRequestType requestType;
  String offeringNote;
  @NonNull LocalDateTime shiftStartTime;
  @NonNull ScheduledShiftRequestRecipientState recipientState;
  @NonNull MessageSource messageSource;
  @NonNull StorageService storageService;

  @Override
  public String getTitle() {
    if (getRequestType().equals(ScheduledShiftRequestType.COVERAGE)) {
      return getLocalizedMessage(
          "scheduling.employee.request-result.coverage.email.subject", state());
    }
    throw new NotYetImplementedException();
  }

  @Override
  public String getMessage() {
    return getBodyParagraph1Text();
  }

  @Override
  public String getPreheader() {
    if (getRequestType().equals(ScheduledShiftRequestType.COVERAGE)) {
      return getLocalizedMessage(
          "scheduling.employee.request-result.coverage.email.preheader", state());
    }
    throw new NotYetImplementedException();
  }

  @Override
  public String getBannerText() {
    if (getRequestType().equals(ScheduledShiftRequestType.COVERAGE)) {
      return getLocalizedMessage("scheduling.employee.request-result.coverage.email.banner-text");
    }
    throw new NotYetImplementedException();
  }

  @Override
  public String getBodyParagraph1Text() {
    var formattedTime = SHIFT_DAY_TIME.format(getShiftStartTime());
    if (getRequestType().equals(ScheduledShiftRequestType.COVERAGE)) {
      return getLocalizedMessage(
          "scheduling.employee.request-result.coverage.email.body-paragraph1-text",
          state(),
          formattedTime);
    }
    throw new NotYetImplementedException();
  }

  @Override
  public String getBodyParagraph2Text() {
    var note = StringUtils.defaultIfBlank(getOfferingNote(), "");
    if (getRequestType().equals(ScheduledShiftRequestType.COVERAGE)) {
      return getLocalizedMessage(
          "scheduling.employee.request-result.coverage.email.body-paragraph2-text", note);
    }
    throw new NotYetImplementedException();
  }

  @Override
  public String getButtonLabel() {
    if (getRequestType().equals(ScheduledShiftRequestType.COVERAGE)) {
      return getLocalizedMessage("scheduling.employee.request-result.coverage.email.button-label");
    }
    throw new NotYetImplementedException();
  }

  @Override
  public String getButtonLinkUrl() {
    return getLinkUrl();
  }

  @Override
  public String getSmsBody() {
    return String.join(" ", getMessage(), getLinkUrl());
  }

  @Override
  public UserNotificationChannel getChannel() {
    return UserNotificationChannel.EMPLOYEE_SCHEDULING;
  }

  private String getLinkUrl() {
    return new DynamicLinkBuilder()
        .pathSegment("my-account", "schedule")
        .queryParam("date", getShiftStartTime().toLocalDate())
        .build()
        .toString();
  }

  private String state() {
    return ScheduledShiftRequestRecipientState.MANAGER_APPROVED.equals(recipientState)
        ? "approved"
        : "declined";
  }

  @Override
  public EmailType getEmailType() {
      return EmailType.USER_SCHEDULING_EMPLOYEE_REQUEST_HAS_MANAGER_RESPONSE;
  }
}
