package com.everee.api.scheduling.notification;

import com.everee.api.company.configurations.CompanySchedulingConfigurationService;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.EmployeeService;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.PartnerRepository;
import com.everee.api.scheduling.ScheduleFactory;
import com.everee.api.scheduling.shift.ScheduledShift;
import com.everee.api.scheduling.shift.ScheduledShiftLookup;
import com.everee.api.scheduling.shift.ScheduledShiftRepository;
import com.everee.api.scheduling.shift.ScheduledShiftService;
import com.everee.api.scheduling.shift.request.ScheduledShiftRequest;
import com.everee.api.scheduling.shift.request.ScheduledShiftRequestRecipient;
import com.everee.api.scheduling.shift.request.ScheduledShiftRequestRecipientState;
import com.everee.api.storage.StorageService;
import com.everee.api.worklocation.WorkLocation;
import com.everee.api.worklocation.WorkLocationLookup;
import com.everee.api.worklocation.WorkLocationLookupService;
import com.everee.api.worklocation.WorkLocationService;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SchedulingEmployeeNotificationService {

  private static final Long OPEN_SHIFT_ID = Long.valueOf(-1);
  public static DateTimeFormatter SHIFT_DAY_TIME = DateTimeFormatter.ofPattern("EEEE 'at' hh:mm a");
  public static DateTimeFormatter TIME = DateTimeFormatter.ofPattern("hh:mm a");
  private final MessageSource messageSource;
  private final NotificationService notificationService;
  private final ScheduledShiftRepository scheduledShiftRepository;
  private final EmployeeService employeeService;
  private final ScheduleFactory scheduleFactory;
  private final CompanySchedulingConfigurationService companySchedulingConfigurationService;
  private final PartnerRepository partnerRepository;
  private final WorkLocationService workLocationService;
  private final WorkLocationLookupService workLocationLookupService;
  private final StorageService storageService;
  private final ApplicationContext
      applicationContext; // need to get scheduledShiftService, but avoid circular dependency
  private ScheduledShiftService scheduledShiftService;

  @Async
  public void notifyEmployeeScheduleRequestResponse(
      ScheduledShiftRequest scheduledShiftRequest,
      ScheduledShiftRequestRecipientState recipientState) {
    if (scheduledShiftRequest.getOfferingEmployeeId() == null
        || companySchedulingConfigurationService
            .getCompanySchedulingConfiguration(scheduledShiftRequest.getCompanyId())
            .isTestModeEnabled()) {
      return;
    }

    var scheduledShift = getOfferingShift(scheduledShiftRequest);
    var partner = partnerRepository.findByCompanyId(scheduledShift.getCompanyId()).orElseThrow();
    var companyId = scheduledShiftRequest.getCompanyId();
    var requestType = scheduledShiftRequest.getType();
    var offeringNote = scheduledShiftRequest.getOfferingNote();
    var shiftStartTime = scheduledShift.getStartDateTime();

    var notification =
        new SchedulingEmployeeRequestHasManagerResponseNotification(
            partner,
            companyId,
            requestType,
            offeringNote,
            shiftStartTime,
            recipientState,
            messageSource,
            storageService);

    notificationService.enqueueUserNotification(
        notification,
        getUserId(scheduledShiftRequest.getOfferingEmployeeId(), scheduledShift.getCompanyId()));
  }

  @Async
  public void notifyRecipientEmployeeScheduleRequestResponse(
      ScheduledShiftRequest scheduledShiftRequest,
      Long employeeId,
      ScheduledShiftRequestRecipientState recipientState) {
    if (companySchedulingConfigurationService
        .getCompanySchedulingConfiguration(scheduledShiftRequest.getCompanyId())
        .isTestModeEnabled()) {
      return;
    }
    var partner =
        partnerRepository.findByCompanyId(scheduledShiftRequest.getCompanyId()).orElseThrow();
    var scheduledShift = getOfferingShift(scheduledShiftRequest);
    var companyId = scheduledShiftRequest.getCompanyId();
    var requestType = scheduledShiftRequest.getType();
    var approverNote = scheduledShiftRequest.getApproverNote();
    var shiftId = scheduledShift.getId();
    var shiftStartTime = scheduledShift.getStartDateTime();

    var notification =
        new SchedulingEmployeeRequestClaimHasManagerResponseNotification(
            partner,
            companyId,
            requestType,
            approverNote,
            shiftId,
            shiftStartTime,
            recipientState,
            messageSource,
            storageService);

    notificationService.enqueueUserNotification(
        notification, getUserId(employeeId, scheduledShift.getCompanyId()));
  }

  @Async
  public void notifyRecipientEmployeeRequested(
      ScheduledShiftRequest scheduledShiftRequest, ScheduledShiftRequestRecipient recipient) {
    // Open shifts don't have employees offering the shift.  Don't notify a recipient if this is for
    // an open shift that they claimed themselves.
    if (scheduledShiftRequest.getOfferingEmployeeId() == null
        || companySchedulingConfigurationService
            .getCompanySchedulingConfiguration(scheduledShiftRequest.getCompanyId())
            .isTestModeEnabled()) {
      return;
    }

    var scheduledShift = getOfferingShift(scheduledShiftRequest);
    var companyId = scheduledShift.getCompanyId();
    var partner = partnerRepository.findByCompanyId(companyId).orElseThrow();
    var requestType = scheduledShiftRequest.getType();
    var offeringNote = scheduledShiftRequest.getOfferingNote();
    var shiftStartTime = scheduledShift.getStartDateTime();
    var offeringWorkerName =
        getEmployee(scheduledShiftRequest.getOfferingEmployeeId(), companyId).getFullName();

    var notification =
        new SchedulingEmployeeRequestCreatedNotification(
            partner,
            companyId,
            requestType,
            offeringNote,
            shiftStartTime,
            offeringWorkerName,
            messageSource,
            storageService);

    notificationService.enqueueUserNotification(
        notification, getUserId(recipient.getRequestedEmployeeId(), companyId));
  }

  @Async
  public void notifyEmployeesPublishedShifts(Collection<ScheduledShift> shifts, Long companyId) {
    if (companySchedulingConfigurationService
        .getCompanySchedulingConfiguration(companyId)
        .isTestModeEnabled()) {
      return;
    }
    var partner = partnerRepository.findByCompanyId(companyId).orElseThrow();
    var employeeIdToNotificationDetails = new HashMap<Long, PublishShiftNotificationDetails>();

    for (ScheduledShift shift : shifts) {
      var employeeId = shift.getEmployeeId();
      if (employeeId == null) {
        employeeId = OPEN_SHIFT_ID;
      }

      var details =
          employeeIdToNotificationDetails.getOrDefault(
              employeeId, new PublishShiftNotificationDetails(employeeId));
      employeeIdToNotificationDetails.put(employeeId, details.addShift(shift));
    }

    employeeIdToNotificationDetails.values().stream()
        .map(d -> d.finalizeScheduleDates(scheduleFactory))
        .forEach(
            publishShiftNotificationDetails -> {
              publishShiftNotificationDetails
                  .getLocationPublishDetails()
                  .forEach(
                      locationPublishDetails -> {
                        // Open shifts
                        if (OPEN_SHIFT_ID.equals(publishShiftNotificationDetails.getEmployeeId())) {
                          getScheduledShiftService()
                              .getScheduleLocationEmployees(
                                  locationPublishDetails.getCompanyId(),
                                  Set.of(locationPublishDetails.getLocationId()),
                                  locationPublishDetails.getMinDate(),
                                  locationPublishDetails.getMaxDate())
                              .forEach(
                                  employee -> {
                                    var notification =
                                        new SchedulingEmployeeOpenShiftsPublishedNotification(
                                            partner,
                                            companyId,
                                            locationPublishDetails.getMinDate(),
                                            locationPublishDetails.getMaxDate(),
                                            messageSource,
                                            storageService);

                                    notificationService.enqueueUserNotification(
                                        notification,
                                        getUserId(
                                            employee.getId(),
                                            locationPublishDetails.getCompanyId()));
                                  });
                        } else {
                          var notification =
                              new SchedulingEmployeeShiftsPublishedNotification(
                                  partner,
                                  companyId,
                                  locationPublishDetails.getMinDate(),
                                  locationPublishDetails.getMaxDate(),
                                  messageSource,
                                  storageService);

                          notificationService.enqueueUserNotification(
                              notification,
                              getUserId(
                                  publishShiftNotificationDetails.getEmployeeId(),
                                  locationPublishDetails.getCompanyId()));
                        }
                      });
            });
  }

  @Async
  public void notifyEmployeeShiftUpdated(ScheduledShift shift) {
    var employeeId = shift.getEmployeeId();
    if (employeeId == null
        || companySchedulingConfigurationService
            .getCompanySchedulingConfiguration(shift.getCompanyId())
            .isTestModeEnabled()) {
      return;
    }
    var partner = partnerRepository.findByCompanyId(shift.getCompanyId()).orElseThrow();

    var notification =
        new SchedulingEmployeeShiftUpdatedNotification(
            partner, shift.getCompanyId(), shift.getStartDateTime(), messageSource, storageService);

    notificationService.enqueueUserNotification(
        notification, getUserId(shift.getEmployeeId(), shift.getCompanyId()));
  }

  public void notifyEmployeesOfUpcomingShifts() {
    var zonedDateTime = ZonedDateTime.now();
    var workLocationZones = workLocationService.getUniqueZones();

    workLocationZones.forEach(
        zoneId -> {
          var currentZonedDateTime = zonedDateTime.withZoneSameInstant(zoneId);

          var companyIds =
              companySchedulingConfigurationService.getCompanyIdsWithSchedulingEnabled();

          var workLocationLookup =
              new WorkLocationLookup().setZoneId(zoneId).setCompanyIds(companyIds);

          var companyWorkLocations =
              workLocationLookupService.listAll(workLocationLookup, PageRequest.of(0, 50));

          while (companyWorkLocations.hasContent()) {
            companyWorkLocations.forEach(
                workLocation ->
                    notifyEmployeesOfUpcomingShifts(currentZonedDateTime, workLocation));

            if (companyWorkLocations.hasNext()) {
              companyWorkLocations =
                  workLocationLookupService.listAll(
                      workLocationLookup, companyWorkLocations.nextPageable());
            } else {
              companyWorkLocations = new PageImpl<>(List.of());
            }
          }
        });
  }

  private void notifyEmployeesOfUpcomingShifts(
      ZonedDateTime workLocationZonedDateTime, WorkLocation workLocation) {
    var partner = partnerRepository.findByCompanyId(workLocation.getCompanyId()).orElseThrow();
    var dateTimeTruncatedToHour = workLocationZonedDateTime.truncatedTo(ChronoUnit.HOURS);
    getScheduledShiftService()
        .listShifts(
            new ScheduledShiftLookup()
                .setLocationIds(Set.of(workLocation.getId()))
                .setCompanyId(workLocation.getCompanyId())
                // starts after (non-inclusive) so 1 minute before the start of the next hour
                .setStartsAfter(
                    dateTimeTruncatedToHour.plusHours(1).minusMinutes(1).toLocalDateTime())
                .setStartsBefore(dateTimeTruncatedToHour.plusHours(2).toLocalDateTime()),
            Pageable.unpaged())
        .forEach(
            shift -> {
              var notification =
                  new SchedulingEmployeeUpcomingShiftNotification(
                      partner,
                      shift.getCompanyId(),
                      shift.getStartDateTime(),
                      messageSource,
                      storageService);
              notificationService.enqueueUserNotification(
                  notification, getEmployee(shift.getEmployeeId(), shift.getCompanyId()).getId());
            });
  }

  private ScheduledShift getOfferingShift(ScheduledShiftRequest request) {
    return scheduledShiftRepository
        .findByIdAndCompanyId(request.getOfferingShiftId(), request.getCompanyId())
        .get();
  }

  private Long getUserId(Long employeeId, Long companyId) {
    return getEmployee(employeeId, companyId).getUserId();
  }

  private DetailedEmployee getEmployee(Long employeeId, Long companyId) {
    return employeeService.get(employeeId, companyId);
  }

  private ScheduledShiftService getScheduledShiftService() {
    if (scheduledShiftService == null) {
      scheduledShiftService = applicationContext.getBean(ScheduledShiftService.class);
    }

    return scheduledShiftService;
  }
}
