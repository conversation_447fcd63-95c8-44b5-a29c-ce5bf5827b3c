package com.everee.api.scheduling.totals;

import com.everee.api.company.CompanyService;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/schedule/totals")
@RequiredArgsConstructor
public class ScheduleTotalsController {
  private final ScheduleTotalsService scheduleTotalsService;

  @GetMapping("/by-location")
  public Page<ScheduleTotals> getScheduleTotalsByLocation(Pageable pageable) {
    return scheduleTotalsService.getTotalsByLocation(
        CompanyService.getAuthenticatedCompany(), pageable);
  }

  @GetMapping
  public ScheduleTotals getScheduleTotals(@Valid ScheduleTotalLookup scheduleTotalLookup) {
    var company = CompanyService.getAuthenticatedCompany();
    scheduleTotalLookup.setCompanyId(company.getId());
    return scheduleTotalsService.getTotals(company, scheduleTotalLookup);
  }

  @GetMapping("/by-day")
  public List<ScheduleTotals> getScheduleTotalsByDay(
      @Valid ScheduleTotalLookup scheduleTotalLookup) {
    var company = CompanyService.getAuthenticatedCompany();
    scheduleTotalLookup.setCompanyId(company.getId());
    return scheduleTotalsService.getTotalsByDay(company, scheduleTotalLookup);
  }
}
