package com.everee.api.scheduling.row;

import com.everee.api.employee.DetailedEmployee;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ScheduleDay {
  private final LocalDate date;
  private List<ScheduleItem> scheduleItems = new ArrayList();
  private int countOpenShifts;
  private int countShiftRequests;

  public void sortScheduledItems() {
    Collections.sort(
        scheduleItems, (i1, i2) -> i1.getStartDateTime().compareTo(i2.getStartDateTime()));
  }

  public void addEmployeeDetails(DetailedEmployee employee) {
    scheduleItems.forEach(s -> s.withEmployeeDetails(employee));
  }

  public void addOpenShifts(int addOpenShifts) {
    setCountOpenShifts(countOpenShifts + addOpenShifts);
  }

  public void addShiftRequests(int addShiftRequests) {
    setCountShiftRequests(countShiftRequests + addShiftRequests);
  }
}
