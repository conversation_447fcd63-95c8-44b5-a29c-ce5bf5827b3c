package com.everee.api.scheduling.row;

import com.everee.api.config.PresignedCustomerFacingFileLocationSerializer;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.i18n.LocalizedString;
import com.everee.api.money.Money;
import com.everee.api.scheduling.shift.PublishState;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ScheduleItem {
  private final ScheduleItemType timeType;

  private final String entityId;

  private String id;

  private String label;
  private LocalDateTime startDateTime;
  private LocalDateTime endDateTime;
  private ScheduleItemBoundaryCrossType boundaryCrossType = ScheduleItemBoundaryCrossType.NONE;
  @JsonIgnore private Duration duration = Duration.ZERO;
  @JsonIgnore private Duration paidDuration = Duration.ZERO;
  @JsonIgnore private Money cost = Money.ZERO;

  private ScheduleItemColor color = ScheduleItemColor.COLOR_1;

  private Integer unresolvedConflictCount;
  private Integer swapRequestCount;
  private Integer openShiftWithRecipientCount;
  private Integer coverageRequestCount;
  private PublishState publishState;

  private Long employeeId;
  private String employeeFirstName;
  private String employeeLastName;

  @JsonSerialize(using = PresignedCustomerFacingFileLocationSerializer.class)
  private String employeeImageUrl;

  public ScheduleItem(ScheduleItemType timeType, Long entityId) {
    this(timeType, entityId + "");
  }

  public ScheduleItem(ScheduleItemType timeType, String entityId) {
    this.timeType = timeType;
    this.entityId = entityId;

    this.id = timeType + "_" + entityId;
    switch (timeType) {
      case PTO:
        this.color = ScheduleItemColor.COLOR_PTO;
        break;
      case HOLIDAY:
        this.color = ScheduleItemColor.COLOR_HOLIDAY;
        break;
      case UNAVAILABLE_TIME:
      case AVAILABLE_TIME:
        this.color = ScheduleItemColor.COLOR_UNAVAILABLE;
        break;
    }
  }

  public void withEmployeeDetails(DetailedEmployee employee) {
    setEmployeeId(employee.getId());
    setEmployeeFirstName(employee.getPreferredFirstName());
    setEmployeeLastName(employee.getLastName());
    setEmployeeImageUrl(employee.getPictureThumbnailUrl());
  }

  public LocalizedString getTitle() {
    return timeType.getTitleLocalizedString(this);
  }

  public String getEmployeeName() {
    if (employeeFirstName == null || employeeLastName == null) return null;

    return employeeFirstName + " " + employeeLastName;
  }

  public LocalizedString getItemTimesLabel() {
    var hourFormat =
        DateTimeFormatter.ofPattern(
            LocalizedString.of("scheduling.scheduleItem.times.label.hourformat")
                .getLocalizedValue());
    var dayFormat =
        DateTimeFormatter.ofPattern(
            LocalizedString.of("scheduling.scheduleItem.times.label.dayformat")
                .getLocalizedValue());

    return LocalizedString.of(
        "scheduling.scheduleItem.times.label",
        hourFormat.format(startDateTime),
        dayFormat.format(startDateTime),
        hourFormat.format(endDateTime),
        dayFormat.format(endDateTime));
  }

  public void clearEmployeeDetails() {
    setEmployeeId(null);
    setEmployeeFirstName(null);
    setEmployeeLastName(null);
    setEmployeeImageUrl(null);
  }
}
