package com.everee.api.scheduling.availability;

import com.everee.api.config.RequestParams;
import com.everee.api.lookup.Lookup;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.util.SetUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiParam;
import java.time.LocalDateTime;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@RequestParams
public class ScheduleEmployeeAvailabilityLookup implements Lookup {
  @ApiParam(name = "tenant-id", example = "1")
  private Long companyId;

  @JsonIgnore
  @Override
  public Set<Long> getCompanyIds() {
    return SetUtils.toSetOrNull(companyId);
  }

  @ApiParam(name = "employee-id", required = true, allowMultiple = true)
  @NotNull
  private Set<Long> employeeIds;

  @NotNull PhaseLookup phaseLookup;

  @ApiParam(name = "availability-type", allowMultiple = true)
  private Set<EmployeeAvailabilityType> availabilityTypes;

  @ApiParam(name = "availability-state", allowMultiple = true)
  private Set<EmployeeAvailabilityState> availabilityStates;

  @JsonIgnore private LocalDateTime createdAtAfterInclusive;

  @JsonIgnore private LocalDateTime createdAtBeforeInclusive;
}
