package com.everee.api.scheduling;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ScheduleFactory {

  public Schedule createScheduleForDate(Long companyId, Long locationId, LocalDate forDate) {
    final DayOfWeek firstDayOfWeek = WeekFields.of(Locale.ENGLISH).getFirstDayOfWeek();
    // Calculation that will find the correct last day of week based on the first
    // day of the week.
    // See:
    // https://stackoverflow.com/questions/22890644/get-current-week-start-and-end-date-in-java-monday-to-sunday/22890763
    final DayOfWeek lastDayOfWeek =
        DayOfWeek.of(((firstDayOfWeek.getValue() + 5) % DayOfWeek.values().length) + 1);

    return new Schedule()
        .setCompanyId(companyId)
        .setLocationId(locationId)
        .setStartDate(forDate.with(TemporalAdjusters.previousOrSame(firstDayOfWeek)))
        .setEndDate(forDate.with(TemporalAdjusters.nextOrSame(lastDayOfWeek)));
  }
}
