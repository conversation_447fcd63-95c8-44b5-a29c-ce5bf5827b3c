package com.everee.api.featureflag;

import com.everee.api.company.CompanyService;
import com.everee.api.r365.R365HttpClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerSettingService implements IFeatureFlagService<Long> {

  private final R365HttpClient r365HttpClient;

  @Override
  public boolean boolValue(String featureKey, boolean defaultValue) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return Boolean.parseBoolean(setting.getValue());
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public boolean boolValue(String featureKey, IContextBuilder<Long> context, boolean defaultValue) {
    var companyId = context.build();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return Boolean.parseBoolean(setting.getValue());
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public int intValue(String featureKey, int defaultValue) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return Integer.parseInt(setting.getValue());
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public int intValue(String featureKey, IContextBuilder<Long> context, int defaultValue) {
    var companyId = context.build();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return Integer.parseInt(setting.getValue());
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public double doubleValue(String featureKey, double defaultValue) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return Double.parseDouble(setting.getValue());
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public double doubleValue(String featureKey, IContextBuilder<Long> context, double defaultValue) {
    var companyId = context.build();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return Double.parseDouble(setting.getValue());
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public String stringValue(String featureKey, String defaultValue) {
    var companyId = CompanyService.getAuthenticatedCompanyId();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return setting.getValue();
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public String stringValue(String featureKey, IContextBuilder<Long> context, String defaultValue) {
    var companyId = context.build();
    try {
      var setting = r365HttpClient.getCustomerSetting(companyId, featureKey);
      if (setting != null && setting.getValue() != null) {
        return setting.getValue();
      }
    } catch (Exception e) {
      logError(e, companyId, featureKey);
    }
    return defaultValue;
  }

  @Override
  public FeatureFlagType getSupportedFeatureFlagType() {
    return FeatureFlagType.CUSTOMER_SETTING;
  }

  private void logError(Exception ex, Long companyId, String featureKey) {
    log.error(String.format("Error getting customer setting. Company ID: %d, Feature Key: %s", companyId, featureKey), ex);
  }
}
