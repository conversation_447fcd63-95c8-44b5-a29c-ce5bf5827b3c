package com.everee.api.companyemployeeimport.reader;

import static java.util.Objects.isNull;

import com.everee.api.companyemployeeimport.ExcelService.ExcelMutator.ExcelCellMutator;
import com.everee.api.employee.EmbeddableEmployeeWithholdings;
import com.everee.api.employee.EmbeddableEmployeeWithholdings_;
import com.everee.api.employeeWithholdings.EmployeeWithholdingsVersion;
import com.everee.api.model.MaritalStatus;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import org.springframework.stereotype.Service;

@Service
public class EmployeeWithholdingsTableReader extends TableReader<EmbeddableEmployeeWithholdings> {

  @Getter
  private final Collection<TableField> fields =
      List.of(
          new TableField(
              EmbeddableEmployeeWithholdings_.WITHHOLDINGS_VERSION,
              false,
              excelCellMutator ->
                  excelCellMutator.setSelectOneOf(EmployeeWithholdingsVersion.values())),
          new TableField(
              EmbeddableEmployeeWithholdings_.FEDERAL_ALLOWANCES,
              false,
              ExcelCellMutator::setCellTypeNumeric),
          new TableField(
              EmbeddableEmployeeWithholdings_.FEDERAL_ADDITIONAL_WITHHOLDINGS,
              false,
              ExcelCellMutator::setCellTypeNumeric),
          new TableField(
              EmbeddableEmployeeWithholdings_.MARITAL_STATUS,
              true,
              excelCellMutator -> excelCellMutator.setSelectOneOf(MaritalStatus.values())),
          new TableField(
              EmbeddableEmployeeWithholdings_.FEDERAL_INCOME_TAXABLE,
              true,
              ExcelCellMutator::setCellTypeBoolean),
          new TableField(
              EmbeddableEmployeeWithholdings_.STATE_INCOME_TAXABLE,
              true,
              ExcelCellMutator::setCellTypeBoolean),
          new TableField(
              EmbeddableEmployeeWithholdings_.HAVE_EXACTLY_TWO_JOBS,
              true,
              ExcelCellMutator::setCellTypeBoolean),
          new TableField(
              EmbeddableEmployeeWithholdings_.COUNT_OF_CHILDREN,
              true,
              ExcelCellMutator::setCellTypeNumeric),
          new TableField(
              EmbeddableEmployeeWithholdings_.COUNT_OF_OTHER_DEPENDENTS,
              true,
              ExcelCellMutator::setCellTypeNumeric),
          new TableField(
              EmbeddableEmployeeWithholdings_.OTHER_INCOME_AMOUNT,
              false,
              ExcelCellMutator::setCellTypeNumeric),
          new TableField(
              EmbeddableEmployeeWithholdings_.DEDUCTIONS_AMOUNT,
              false,
              ExcelCellMutator::setCellTypeNumeric),
          new TableField(
              EmbeddableEmployeeWithholdings_.EXTRA_WITHHOLDING_AMOUNT,
              false,
              ExcelCellMutator::setCellTypeNumeric));

  @Override
  public OnboardingRequirableValue<EmbeddableEmployeeWithholdings> extract(
      Map<String, String> keyValueMap) {
    final OnboardingRequirableValue<EmbeddableEmployeeWithholdings> extract =
        extract(keyValueMap, new EmbeddableEmployeeWithholdings());
    final EmbeddableEmployeeWithholdings extractValue = extract.getValue();
    if (extractValue != null) {
      if (extractValue.getWithholdingsVersion() == null
          || extractValue.getWithholdingsVersion() == EmployeeWithholdingsVersion.V2020) {
        boolean onboardingRequired =
            isNull(extractValue.getMaritalStatus())
                || isNull(extractValue.getCountOfChildren())
                || isNull(extractValue.getCountOfOtherDependents());
        return new OnboardingRequirableValue<>(extractValue, onboardingRequired, null);
      } else if (extractValue.getWithholdingsVersion() == EmployeeWithholdingsVersion.V2019) {
        boolean onboardingRequired =
            isNull(extractValue.getMaritalStatus())
                || isNull(extractValue.getFederalAllowances())
                || isNull(extractValue.getFederalAdditionalWithholdings());
        return new OnboardingRequirableValue<>(extractValue, onboardingRequired, null);
      }
    }
    return extract;
  }
}
