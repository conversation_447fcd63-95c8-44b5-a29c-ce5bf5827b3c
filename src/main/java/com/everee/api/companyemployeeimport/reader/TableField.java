package com.everee.api.companyemployeeimport.reader;

import com.everee.api.companyemployeeimport.ExcelService.ExcelMutator.ExcelCellMutator;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import lombok.Value;

@Value
@RequiredArgsConstructor
public class TableField {
  private static final String REQUIRED_MARKER = "*";
  private final String name;
  private final boolean required;
  private final Consumer<ExcelCellMutator> cellMutation;

  public TableField(String name, boolean required) {
    this(name, required, (excelMutation) -> {});
  }

  public String getExcelName() {
    return getName() + (includeRequiredMarker());
  }

  public String getExcelName(int index) {
    return getName() + index + includeRequiredMarker();
  }

  private String includeRequiredMarker() {
    return isRequired() ? REQUIRED_MARKER : "";
  }
}
