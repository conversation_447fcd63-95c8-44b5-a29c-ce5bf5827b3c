package com.everee.api.companyemployeeimport;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class CompanyEmployeeImportJobException extends RuntimeException {

  public CompanyEmployeeImportJobException(String message) {
    super(message);
  }

  public CompanyEmployeeImportJobException(String message, Throwable cause) {
    super(message, cause);
  }
}
