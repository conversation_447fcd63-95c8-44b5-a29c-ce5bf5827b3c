package com.everee.api.companyemployeeimport;

import com.everee.api.employee.DetailedEmployee;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Data;

@Data
public class CompletedEmployeeImportJobDetails {
  private Long companyEmployeeImportJobId;
  private List<String> skippedWorkers = new ArrayList<>();
  private Map<String, DetailedEmployee> importedWorkers = new ConcurrentHashMap<>();
}
