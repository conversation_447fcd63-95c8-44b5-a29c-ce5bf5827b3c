package com.everee.api.bankingday;

import static java.time.DayOfWeek.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Set;

public class Weekdays {
  private static final Set<DayOfWeek> WEEKDAYS =
      Set.of(MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY);

  public static boolean isWeekday(LocalDate date) {
    return WEEKDAYS.contains(date.getDayOfWeek());
  }

  public static boolean isWeekend(LocalDate date) {
    return !isWeekday(date);
  }
}
