package com.everee.api.timeClassification.timeWorked;

import static com.everee.api.timeClassification.intervals.ClassificationType.*;

import com.everee.api.timeClassification.intervals.ClassifiedInterval;
import java.time.Duration;
import java.util.Collection;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.NonNull;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ClassifiedTimeWorkedDurations {
  private Duration regularTimeWorked = Duration.ZERO;
  private Duration overtimeWorked = Duration.ZERO;
  private Duration doubleTimeWorked = Duration.ZERO;

  public static ClassifiedTimeWorkedDurations zeros() {
    return new ClassifiedTimeWorkedDurations();
  }

  public static ClassifiedTimeWorkedDurations fromTimeWorked(
      @NonNull Collection<ClassifiedTimeWorked> timeWorked) {
    var foundDurations =
        timeWorked.stream()
            .collect(
                Collectors.groupingBy(
                    ClassifiedTimeWorked::getClassifiedType,
                    Collectors.mapping(
                        ClassifiedTimeWorked::getDuration,
                        Collectors.reducing(Duration.ZERO, Duration::plus))));

    return new ClassifiedTimeWorkedDurations()
        .setRegularTimeWorked(foundDurations.getOrDefault(REGULAR_TIME, Duration.ZERO))
        .setOvertimeWorked(foundDurations.getOrDefault(OVERTIME, Duration.ZERO))
        .setDoubleTimeWorked(foundDurations.getOrDefault(DOUBLE_TIME, Duration.ZERO));
  }

  public static ClassifiedTimeWorkedDurations fromIntervals(
      @NonNull Collection<ClassifiedInterval> intervals) {
    var foundDurations =
        intervals.stream()
            .collect(
                Collectors.groupingBy(
                    ClassifiedInterval::getClassifiedType,
                    Collectors.mapping(
                        ClassifiedInterval::getDuration,
                        Collectors.reducing(Duration.ZERO, Duration::plus))));

    return new ClassifiedTimeWorkedDurations()
        .setRegularTimeWorked(foundDurations.getOrDefault(REGULAR_TIME, Duration.ZERO))
        .setOvertimeWorked(foundDurations.getOrDefault(OVERTIME, Duration.ZERO))
        .setDoubleTimeWorked(foundDurations.getOrDefault(DOUBLE_TIME, Duration.ZERO));
  }

  public Duration getTotalTimeWorked() {
    return regularTimeWorked.plus(overtimeWorked).plus(doubleTimeWorked);
  }

  public ClassifiedTimeWorkedDurations plus(@NonNull ClassifiedTimeWorkedDurations other) {
    return new ClassifiedTimeWorkedDurations()
        .setOvertimeWorked(this.overtimeWorked.plus(other.overtimeWorked))
        .setDoubleTimeWorked(this.doubleTimeWorked.plus(other.doubleTimeWorked))
        .setRegularTimeWorked(this.regularTimeWorked.plus(other.regularTimeWorked));
  }
}
