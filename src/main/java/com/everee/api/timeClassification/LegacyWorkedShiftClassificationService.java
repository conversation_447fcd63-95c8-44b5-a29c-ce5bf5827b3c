package com.everee.api.timeClassification;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;
import static java.util.Comparator.comparing;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import com.everee.api.company.CompanyService;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.labor.timesheet.event.WorkerTimesheetChangedEvent;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.time.ZonedWorkweeks;
import com.everee.api.timeClassification.intervals.ClassifiedInterval;
import com.everee.api.timeClassification.rules.ClassificationRule;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorked;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorkedDurations;
import com.everee.api.timeIntervals.Interval;
import com.everee.api.timeIntervals.ZonedInterval;
import com.everee.api.timeclock.LegacyWorkedShift;
import com.everee.api.timeclock.LegacyWorkedShiftLookup;
import com.everee.api.timeclock.LegacyWorkedShiftLookupService;
import com.everee.api.util.ZonedIntervalUtils;
import com.everee.api.worker.WorkerLabel;
import com.everee.api.worker.time.WorkerClassificationRuleService;
import com.everee.api.worker.time.WorkerWorkweekService;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class LegacyWorkedShiftClassificationService {
  private final ApplicationEventPublisher eventPublisher;
  private final CompanyService companyService;
  private final LegacyWorkedShiftLookupService shiftLookupService;
  private final WorkerWorkweekService workerWorkweekService;
  private final WorkerClassificationRuleService workerClassificationRuleService;

  public void byCreatingShift(LegacyWorkedShift shift) {
    if (shift.isActive()) return;
    var employee = shift.getEmployee();

    var punchInAt = shift.getUtcEffectivePunchInAt().atZone(UTC_ZONE_ID);
    var punchOutAt = shift.getUtcEffectivePunchOutAt().atZone(UTC_ZONE_ID);

    classifyTimeWorkedForPunches(employee, punchInAt, punchOutAt, null, false);
    eventPublisher.publishEvent(new WorkerTimesheetChangedEvent(employee));
  }

  public void byUpdatingShift(Interval utcEffectInterval, LegacyWorkedShift updatedShift) {
    if (utcEffectInterval == null) return;
    var employee = updatedShift.getEmployee();

    var punchInAt = utcEffectInterval.getStartInclusive().atZone(UTC_ZONE_ID);
    var punchOutAt = utcEffectInterval.getEndExclusive().atZone(UTC_ZONE_ID);

    classifyTimeWorkedForPunches(employee, punchInAt, punchOutAt, null, false);
    eventPublisher.publishEvent(new WorkerTimesheetChangedEvent(employee));
  }

  public void byDeletingShift(LegacyWorkedShift shift) {
    if (shift.isActive()) return;
    var employee = shift.getEmployee();

    var punchInAt = shift.getUtcEffectivePunchInAt().atZone(UTC_ZONE_ID);
    var punchOutAt = shift.getUtcEffectivePunchOutAt().atZone(UTC_ZONE_ID);

    classifyTimeWorkedForPunches(employee, punchInAt, punchOutAt, shift, false);
    eventPublisher.publishEvent(new WorkerTimesheetChangedEvent(employee));
  }

  /**
   * Reclassifies the given collection of shifts, groups results by worker records
   *
   * @param shifts shifts to reclassify
   * @return aggregated classified durations grouped by worker
   */
  public Set<WorkerClassifiedTimeWorkedDurations> reclassifyTimeWorkedDurations(
      Collection<LegacyWorkedShift> shifts, boolean persistOnPaidShifts) {
    var shiftsByEmployee =
        shifts.stream().collect(Collectors.groupingBy(LegacyWorkedShift::getEmployee));
    return shiftsByEmployee.entrySet().stream()
        .map(
            (entry) -> {
              var employee = entry.getKey();
              var eeShifts = entry.getValue();
              var classifiedDurations =
                  reclassifyTimeWorkedDurations(employee, eeShifts, persistOnPaidShifts);
              return new WorkerClassifiedTimeWorkedDurations(
                  WorkerLabel.from(employee), classifiedDurations);
            })
        .collect(Collectors.toCollection(HashSet::new));
  }

  /**
   * Given a collection of shifts, the classified time is pulled from the records and returned as
   * durations.
   *
   * @param employee that worked the given shifts
   * @param shifts list of shifts to get classified time
   * @return aggregated classified durations
   */
  public ClassifiedTimeWorkedDurations getClassifiedTimeWorkedDurationsFromShifts(
      DetailedEmployee employee, Collection<LegacyWorkedShift> shifts) {
    if (shifts.size() < 1) return ClassifiedTimeWorkedDurations.zeros();

    var closedShifts =
        shifts.stream().filter(not(LegacyWorkedShift::isActive)).collect(Collectors.toSet());
    var missingClassifiedTime =
        closedShifts.stream().anyMatch(this::shiftIsMissingClassifiedTimeWorked);

    if (missingClassifiedTime) {
      var startAt =
          closedShifts.stream()
              .map(LegacyWorkedShift::toUtcInterval)
              .min(comparing(Interval::getStartInclusive))
              .map(Interval::getStartInclusive)
              .map(t -> t.atZone(UTC_ZONE_ID))
              .map(ZonedDateTime::toEpochSecond)
              .get();

      var endAt =
          closedShifts.stream()
              .map(LegacyWorkedShift::toUtcInterval)
              .max(comparing(Interval::getEndExclusive))
              .map(Interval::getEndExclusive)
              .map(t -> t.atZone(UTC_ZONE_ID))
              .map(ZonedDateTime::toEpochSecond)
              .get();

      var interval = new ZonedInterval(startAt, endAt, UTC_ZONE_ID);

      var classifiedIntervals = calculateClassifiedIntervals(employee, interval);
      return ClassifiedTimeWorkedDurations.fromIntervals(classifiedIntervals);
    } else {
      return ClassifiedTimeWorkedDurations.fromTimeWorked(
          closedShifts.stream()
              .map(LegacyWorkedShift::getClassifiedTimeWorked)
              .flatMap(Collection::stream)
              .collect(Collectors.toList()));
    }
  }

  public ClassifiedTimeWorkedDurations getClassifiedTimeWorkedDurations(
      DetailedEmployee employee, LegacyWorkedShiftLookup lookup) {
    var shifts = shiftLookupService.listAll(lookup, Pageable.unpaged()).getContent();

    return getClassifiedTimeWorkedDurationsFromShifts(employee, shifts);
  }

  public void calculateClassifiedDurationsIfMissing(Collection<LegacyWorkedShift> shifts) {
    var shiftsByEmployee =
        shifts.stream()
            .filter(not(LegacyWorkedShift::isActive))
            .collect(Collectors.groupingBy(LegacyWorkedShift::getEmployee));
    shiftsByEmployee.forEach(
        (employee, eeShifts) -> {
          if (eeShifts.stream().anyMatch(this::shiftIsMissingClassifiedTimeWorked)) {
            calculateClassifiedTimeWorkedDurationsForShifts(employee, eeShifts);
          }
        });
  }

  // TEMP: LEGACY TIME WORKED SERVICE ==============
  public ClassifiedTimeWorkedDurations getClassifiedTimeWorkedDurationsPayableInRange(
      DetailedEmployee employee, Interval interval) {
    var shifts =
        shiftLookupService
            .listAllPayableInInterval(employee.getId(), interval, Pageable.unpaged())
            .getContent();

    return getClassifiedTimeWorkedDurationsFromShifts(employee, shifts);
  }

  public ClassifiedTimeWorkedDurations getClassifiedTimeWorkedDurationsPayableInPayPeriod(
      DetailedEmployee employee, PayPeriod payPeriod) {
    var company = companyService.getCompany(employee.getCompanyId());
    var interval =
        Interval.ofDateRange(
            payPeriod.getStartDate(), payPeriod.getEndDate(), company.getWorkweekConfig());

    return getClassifiedTimeWorkedDurationsPayableInRange(employee, interval);
  }
  // END TEMP ===================================

  /**
   * Given a collection of shifts the classified time is deleted and replaced with recalculated
   * data. The resulting durations are returned as an aggregate.
   *
   * @param employee worker record that worked the given shifts (assumed, not validated)
   * @param shifts list of shifts that need reclassification
   * @return aggregated classified durations
   */
  private ClassifiedTimeWorkedDurations reclassifyTimeWorkedDurations(
      DetailedEmployee employee,
      Collection<LegacyWorkedShift> shifts,
      boolean persistOnPaidShifts) {
    var closedShifts = shifts.stream().filter(not(LegacyWorkedShift::isActive)).collect(toSet());

    if (shifts.size() < 1) return ClassifiedTimeWorkedDurations.zeros();

    var startAt =
        closedShifts.stream()
            .map(LegacyWorkedShift::toUtcInterval)
            .min(comparing(Interval::getStartInclusive))
            .map(Interval::getStartInclusive)
            .get()
            .atZone(UTC_ZONE_ID);

    var endAt =
        closedShifts.stream()
            .map(LegacyWorkedShift::toUtcInterval)
            .max(comparing(Interval::getEndExclusive))
            .map(Interval::getEndExclusive)
            .get()
            .atZone(UTC_ZONE_ID);

    var shiftIds = shifts.stream().map(LegacyWorkedShift::getId).collect(Collectors.toSet());

    return classifyTimeWorkedForPunches(employee, startAt, endAt, null, persistOnPaidShifts)
        .stream()
        .filter(shift -> shiftIds.contains(shift.getId()))
        .map(LegacyWorkedShift::getClassifiedDurations)
        .reduce(ClassifiedTimeWorkedDurations.zeros(), ClassifiedTimeWorkedDurations::plus);
  }

  private void calculateClassifiedTimeWorkedDurationsForShifts(
      DetailedEmployee employee, Collection<LegacyWorkedShift> shifts) {
    var startAt =
        shifts.stream()
            .map(LegacyWorkedShift::toUtcInterval)
            .map(Interval::getStartInclusive)
            .min(LocalDateTime::compareTo)
            .map(t -> t.atZone(UTC_ZONE_ID))
            .map(ZonedDateTime::toEpochSecond)
            .get();

    var endAt =
        shifts.stream()
            .map(LegacyWorkedShift::toUtcInterval)
            .map(Interval::getEndExclusive)
            .max(LocalDateTime::compareTo)
            .map(t -> t.atZone(UTC_ZONE_ID))
            .map(ZonedDateTime::toEpochSecond)
            .get();
    var interval = new ZonedInterval(startAt, endAt, UTC_ZONE_ID);

    var classifiedIntervals = calculateClassifiedIntervals(employee, interval);

    shifts.forEach(
        shift -> {
          var containedIntervals =
              classifiedIntervals.stream()
                  .filter(i -> shift.toUtcInterval().contains(i.getUtcInterval()))
                  .collect(Collectors.toSet());
          var shiftDurations = ClassifiedTimeWorkedDurations.fromIntervals(containedIntervals);

          shift.setClassifiedDurations(shiftDurations);
        });
  }

  private List<ClassifiedInterval> calculateClassifiedIntervals(
      DetailedEmployee employee, ZonedInterval zonedInterval) {
    var workweekGenerator = workerWorkweekService.getZonedWorkweekGenerator(employee);
    var ruleCriteriaGenerator = workerClassificationRuleService.getRuleGenerator(employee);
    var affectedWorkweeks =
        getAffectedWorkweeks(
            zonedInterval.getZonedStartInclusive(),
            zonedInterval.getZonedEndExclusive(),
            workweekGenerator);
    var employeeId = employee.getId();
    var affectedShifts = getAffectedShifts(employeeId, affectedWorkweeks, null);

    // FIXME: add better docs! expand our workweek context to include a trailing shift if needed
    var lastAffectedShiftEndsAt =
        affectedShifts.stream()
            .map(LegacyWorkedShift::getUtcEffectivePunchOutAt)
            .max(LocalDateTime::compareTo)
            .map(localDateTime -> localDateTime.atZone(UTC_ZONE_ID))
            .orElseThrow();

    if (lastAffectedShiftEndsAt.isAfter(affectedWorkweeks.getLastWeekEndExclusive())) {
      affectedWorkweeks =
          getAffectedWorkweeks(
              zonedInterval.getZonedStartInclusive(), lastAffectedShiftEndsAt, workweekGenerator);
    }

    var classifiedIntervals =
        calculateClassifiedTimeWorked(affectedShifts, affectedWorkweeks, ruleCriteriaGenerator);

    return classifiedIntervals.stream()
        .filter(interval -> zonedInterval.contains(interval.getZonedInterval()))
        .collect(Collectors.toList());
  }

  private Set<LegacyWorkedShift> classifyTimeWorkedForPunches(
      DetailedEmployee employee,
      ZonedDateTime utcStartTimestamp,
      ZonedDateTime utcEndTimestamp,
      LegacyWorkedShift excludeShift,
      boolean persistOnPaidShifts) {
    var workweekGenerator = workerWorkweekService.getZonedWorkweekGenerator(employee);
    var ruleCriteriaGenerator = workerClassificationRuleService.getRuleGenerator(employee);

    var affectedWorkweeks =
        getAffectedWorkweeks(utcStartTimestamp, utcEndTimestamp, workweekGenerator);

    var employeeId = employee.getId();
    var affectedShifts = getAffectedShifts(employeeId, affectedWorkweeks, excludeShift);
    if (affectedShifts.size() == 0) return Collections.emptySet();

    // FIXME: add better docs! expand our workweek context to include a trailing shift if needed
    var lastAffectedShiftEndsAt =
        affectedShifts.stream()
            .map(LegacyWorkedShift::getUtcEffectivePunchOutAt)
            .max(LocalDateTime::compareTo)
            .map(localDateTime -> localDateTime.atZone(UTC_ZONE_ID))
            .orElseThrow();

    if (lastAffectedShiftEndsAt.isAfter(affectedWorkweeks.getLastWeekEndExclusive())) {
      affectedWorkweeks =
          getAffectedWorkweeks(utcStartTimestamp, lastAffectedShiftEndsAt, workweekGenerator);
    }

    var classifiedIntervals =
        calculateClassifiedTimeWorked(affectedShifts, affectedWorkweeks, ruleCriteriaGenerator);

    return persistClassifiedTimeWorked(
        classifiedIntervals,
        affectedShifts,
        affectedWorkweeks.getZonedInterval(),
        persistOnPaidShifts);
  }

  private List<ClassifiedInterval> calculateClassifiedTimeWorked(
      Collection<LegacyWorkedShift> affectedShifts,
      ZonedWorkweeks affectedWorkweeks,
      Function<ZonedDateTime, List<ClassificationRule>> ruleCriteriaGenerator) {
    var unclassifiedIntervals =
        affectedShifts.stream()
            .filter(s -> !s.isActive())
            .map(
                s ->
                    new ZonedInterval(
                        s.getUtcEffectivePunchInAt().atZone(UTC_ZONE_ID).toEpochSecond(),
                        s.getUtcEffectivePunchOutAt().atZone(UTC_ZONE_ID).toEpochSecond(),
                        UTC_ZONE_ID))
            .collect(toSet());

    var classifier = new IntervalClassifier(ruleCriteriaGenerator, affectedWorkweeks);
    List<ClassifiedInterval> classifiedIntervals;
    try {
      classifiedIntervals = classifier.classifyIntervals(unclassifiedIntervals);
    } catch (Exception e) {
      log.warn(e.getMessage(), e);
      return Collections.emptyList();
    }

    return classifiedIntervals;
  }

  private ZonedWorkweeks getAffectedWorkweeks(
      ZonedDateTime utcStartTimestamp,
      ZonedDateTime utcEndTimestamp,
      Function<ZonedDateTime, ZonedInterval> weekGetter) {
    var utcEndAtEndOfWeek = weekGetter.apply(utcEndTimestamp).getZonedEndExclusive();
    var weeks = new ArrayList<ZonedInterval>();

    var timestamp = utcStartTimestamp;
    while (!timestamp.isAfter(utcEndAtEndOfWeek)) {
      weeks.add(weekGetter.apply(timestamp));
      timestamp = timestamp.plusWeeks(1);
    }

    return new ZonedWorkweeks(weeks);
  }

  private Set<LegacyWorkedShift> getAffectedShifts(
      Long employeeId, ZonedWorkweeks affectedWeeks, LegacyWorkedShift excludeShift) {
    var optionalExcludeId = Optional.ofNullable(excludeShift).map(LegacyWorkedShift::getId);
    return shiftLookupService
        .listAllIntersectingZonedInterval(
            employeeId, affectedWeeks.getZonedInterval(), Pageable.unpaged())
        .stream()
        .filter(s -> optionalExcludeId.map(id -> !id.equals(s.getId())).orElse(true))
        .collect(toSet());
  }

  private Set<LegacyWorkedShift> persistClassifiedTimeWorked(
      List<ClassifiedInterval> classifiedIntervals,
      Set<LegacyWorkedShift> affectedShifts,
      ZonedInterval affectedInterval,
      boolean persistOnPaidShifts) {
    var editableShifts =
        affectedShifts.stream().filter(LegacyWorkedShift::isEditable).collect(toSet());
    var finalizedShifts =
        affectedShifts.stream().filter(not(LegacyWorkedShift::isEditable)).collect(toSet());

    editableShifts.forEach(
        shift -> deleteIntersectingClassifiedTimeWorked(affectedInterval, shift));
    editableShifts.forEach(shift -> saveClassifiedTimeWorkedOntoShift(classifiedIntervals, shift));

    finalizedShifts.stream()
        .filter(this::shiftIsMissingClassifiedTimeWorked)
        .forEach(
            shift -> {
              if (persistOnPaidShifts) {
                deleteIntersectingClassifiedTimeWorked(affectedInterval, shift);
                saveClassifiedTimeWorkedOntoShift(classifiedIntervals, shift);
              } else {
                var containedIntervals =
                    classifiedIntervals.stream()
                        .filter(
                            interval -> shift.toUtcInterval().contains(interval.getUtcInterval()))
                        .collect(Collectors.toSet());
                var shiftDurations =
                    ClassifiedTimeWorkedDurations.fromIntervals(containedIntervals);
                shift.setClassifiedDurations(shiftDurations);
              }
            });

    return affectedShifts;
  }

  private void deleteIntersectingClassifiedTimeWorked(
      ZonedInterval affectedInterval, LegacyWorkedShift shift) {
    var classifiedTimeWorked = shift.getClassifiedTimeWorked();
    var intersectingClassifiedTimeWorked =
        classifiedTimeWorked.stream()
            .filter(
                timeWorked ->
                    ZonedIntervalUtils.hasOverlap(affectedInterval, timeWorked.getZonedInterval()))
            .collect(toList());

    var containedClassifiedTimeWorked =
        classifiedTimeWorked.stream()
            .filter(timeWorked -> affectedInterval.contains(timeWorked.getZonedInterval()))
            .collect(toList());

    if (intersectingClassifiedTimeWorked.size() != containedClassifiedTimeWorked.size()) {
      // We've found a classified interval that partially intersects the affected interval. This is
      // a bug.
      throw new IllegalStateException("Previously classified interval intersects week boundary.");
    }

    classifiedTimeWorked.removeAll(containedClassifiedTimeWorked);
  }

  private List<ClassifiedTimeWorked> saveClassifiedTimeWorkedOntoShift(
      List<ClassifiedInterval> intervals, LegacyWorkedShift shift) {
    var shiftInterval = shift.toUtcInterval();
    var classifiedTimeWorked = new ArrayList<ClassifiedTimeWorked>();
    intervals.stream()
        .filter(i -> shiftInterval.contains(i.getUtcInterval()))
        .map(i -> toClassifiedTimeWorked(i, shift))
        .forEach(classifiedTimeWorked::add);

    shift.getClassifiedTimeWorked().addAll(classifiedTimeWorked);

    return classifiedTimeWorked;
  }

  private ClassifiedTimeWorked toClassifiedTimeWorked(
      ClassifiedInterval classifiedInterval, LegacyWorkedShift shift) {
    var interval = classifiedInterval.getZonedInterval();
    return new ClassifiedTimeWorked()
        .setCompanyId(shift.getCompanyId())
        .setEmployeeId(shift.getEmployeeId())
        .setShiftId(shift.getId())
        .setClassifiedType(classifiedInterval.getClassifiedType())
        .setStartAtInclusive(interval.getZonedStartInclusive())
        .setEndAtExclusive(interval.getZonedEndExclusive());
  }

  private boolean shiftIsMissingClassifiedTimeWorked(LegacyWorkedShift shift) {
    return !shift
        .getClassifiedDurations()
        .getTotalTimeWorked()
        .equals(shift.getEffectiveDuration());
  }
}
