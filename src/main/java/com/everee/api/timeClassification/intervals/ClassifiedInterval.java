package com.everee.api.timeClassification.intervals;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;

import com.everee.api.timeIntervals.Interval;
import com.everee.api.timeIntervals.ZonedInterval;
import java.time.Duration;
import lombok.Value;

@Value
public class ClassifiedInterval implements Comparable<ClassifiedInterval> {
  // Source Fields
  ZonedInterval zonedInterval;
  ClassificationType classifiedType;

  /** @deprecated Use {@link #getZonedInterval()} instead. */
  @Deprecated(forRemoval = true)
  public Interval getUtcInterval() {
    var startAt =
        zonedInterval.getZonedStartInclusive().withZoneSameInstant(UTC_ZONE_ID).toLocalDateTime();
    var endAt =
        zonedInterval.getZonedEndExclusive().withZoneSameInstant(UTC_ZONE_ID).toLocalDateTime();

    return new Interval(startAt, endAt);
  }

  public Duration getDuration() {
    return zonedInterval.getDuration();
  }

  @Override
  public int compareTo(ClassifiedInterval other) {
    // Compares by UTC start time
    return zonedInterval.compareTo(other.zonedInterval);
  }

  @Override
  public String toString() {
    return String.format(
        "[%s, %s] (%s: %s)",
        zonedInterval.getZonedStartInclusive(),
        zonedInterval.getZonedEndExclusive(),
        zonedInterval.getDuration(),
        classifiedType);
  }
}
