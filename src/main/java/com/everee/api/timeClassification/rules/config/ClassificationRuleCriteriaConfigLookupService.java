package com.everee.api.timeClassification.rules.config;

import static com.everee.api.query.where.Where.property;
import static com.everee.api.timeClassification.rules.config.ClassificationRuleCriteriaConfig_.STATE;

import com.everee.api.lookup.TenantlessLookupService;
import com.everee.api.phase.PhaseQuery;
import com.everee.api.query.where.Where;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ClassificationRuleCriteriaConfigLookupService
    implements TenantlessLookupService<
        ClassificationRuleCriteriaConfig,
        ClassificationRuleCriteriaConfigLookup,
        PhaseQuery<ClassificationRuleCriteriaConfig>> {
  private final EntityManager entityManager;

  @Override
  public void configureQuery(
      ClassificationRuleCriteriaConfigLookup lookup,
      PhaseQuery<ClassificationRuleCriteriaConfig> query) {
    query.byPhase(lookup.getPhaseLookup());

    if (lookup.isIncludeFederalConfig()) {
      query.where(Where.or(property(STATE).isNull(), property(STATE).in(lookup.getStates())));
    } else {
      query.where(property(STATE).in(lookup.getStates()));
    }
  }

  @Override
  public PhaseQuery<ClassificationRuleCriteriaConfig> createQuery() {
    return new PhaseQuery<>(entityManager, ClassificationRuleCriteriaConfig.class);
  }
}
