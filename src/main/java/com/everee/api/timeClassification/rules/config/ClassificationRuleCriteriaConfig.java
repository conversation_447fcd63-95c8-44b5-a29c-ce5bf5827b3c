package com.everee.api.timeClassification.rules.config;

import com.everee.api.model.BaseModel;
import com.everee.api.phase.Phased;
import com.everee.api.tax.state.State;
import java.time.LocalDate;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import lombok.*;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ClassificationRuleCriteriaConfig extends BaseModel
    implements Phased, RuleCriteriaConfig {
  private Integer weekHoursOvertimeThreshold;
  private Integer dayHoursOvertimeThreshold;
  private Integer dayHoursDoubleTimeThreshold;
  private Integer consecutiveHoursOvertimeThreshold;
  private Integer consecutiveDayOvertimeThreshold;
  private Integer dayHoursAfterConsecutiveDaysDoubleTimeThreshold;

  @Enumerated(EnumType.STRING)
  private State state; // leave null for FED criteria

  @NotNull private LocalDate startDate;
  @NotNull private LocalDate endDate;
}
