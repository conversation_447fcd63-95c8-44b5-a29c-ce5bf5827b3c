package com.everee.api.timeClassification;

import static com.everee.api.timeClassification.intervals.ClassificationType.REGULAR_TIME;
import static com.everee.api.util.DateUtil.UTC_ZONE_ID;
import static com.everee.api.util.ZonedIntervalUtils.getOverlap;
import static com.everee.api.util.ZonedIntervalUtils.hasOverlap;
import static com.everee.api.util.ZonedIntervalUtils.intersectingSubtract;

import com.everee.api.time.ZonedWorkweeks;
import com.everee.api.timeClassification.intervals.ClassifiableInterval;
import com.everee.api.timeClassification.intervals.ClassificationType;
import com.everee.api.timeClassification.intervals.ClassifiedInterval;
import com.everee.api.timeClassification.rules.ClassificationRule;
import com.everee.api.timeClassification.rules.RuleCriteria;
import com.everee.api.timeClassification.rules.RuleCriteria.RuleCriteriaScope;
import com.everee.api.timeIntervals.ZonedInterval;
import com.everee.api.util.ZonedIntervalUtils;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.NonNull;

public class IntervalClassifier {
  private final Function<ZonedDateTime, List<ClassificationRule>> getRules;
  private final ZonedWorkweeks affectedWorkweeks;
  private final List<ClassifiedInterval> classifiedList = new ArrayList<>();
  private final Map<ClassificationType, Integer> daySecondsSoFar = new HashMap<>();
  private final Map<ClassificationType, Integer> weekSecondsSoFar = new HashMap<>();

  private List<ClassificationRule> dayRules;
  private ConsecutiveSeconds consecutiveSecondsSoFar;
  private List<ZonedInterval> orderedUnclassified;
  private int consecutiveDaysSoFar = 0;

  /**
   * Creates a classifier with the provided generators. Use classifier to classify a set of
   * intervals
   *
   * @param getRules Function returning a list of classification rules given a timestamp
   * @param affectedWorkweeks consecutive workweeks to bound classification efforts
   */
  public IntervalClassifier(
      Function<ZonedDateTime, List<ClassificationRule>> getRules,
      ZonedWorkweeks affectedWorkweeks) {
    this.getRules = getRules;
    this.affectedWorkweeks = affectedWorkweeks;
  }

  /**
   * PLEASE READ!
   *
   * <p>This classifier attempts to classify the given intervals (using the given
   * rule-configuration). Classification is based on workweeks, each assumed to be in a single
   * timezone. The affected workweeks is determined by the punch-out-at timestamps from all of the
   * given intervals (using the provided work-week-generator). Therefore, an interval that crosses a
   * week boundary will be assumed to have taken place in the timezone of the punch-out week.
   *
   * <p>This behavior leads to some nuance around a timezone change between adjacent weeks; ie: the
   * interface between two adjacent weeks with a timezone change could produce a LocalTime overlap
   * or gap. To handle this situation, each week is classified separately and merged in the final
   * step before returning. This merge will divide a spanning interval on the second week boundary
   * and favor the second week's resulting classification. This behavior choice was driven by the
   * need to be able to later reclassify the second week (as new intervals are added), so as to not
   * require reclassifying the first week.
   *
   * @param unclassifiedIntervals intervals of time to classify. It is assumed that the intervals do
   *     not overlap when converted to UTC.
   * @return classified intervals of time, in UTC
   */
  public List<ClassifiedInterval> classifyIntervals(
      @NonNull Set<ZonedInterval> unclassifiedIntervals) {
    if (classifiedList.size() != 0) {
      throw new IllegalStateException("Classifier isn't reusable. Create a new one as needed.");
    }

    orderedUnclassified = orderAndValidateUnclassifiedIntervals(unclassifiedIntervals);

    if (orderedUnclassified.size() < 1) return Collections.emptyList();

    // classify the intervals by workweek
    var classifiedByWeek =
        affectedWorkweeks.getWorkweeks().stream()
            .map(this::processUnclassifiedForWeek)
            .collect(Collectors.toList());

    // merge all classified time into the classified list; prefer later classifications
    classifiedByWeek.stream()
        .flatMap(Collection::stream)
        .forEach(this::addOrReplaceClassifiedInterval);

    return classifiedList;
  }

  private List<ZonedInterval> orderAndValidateUnclassifiedIntervals(
      Set<ZonedInterval> unclassifiedSet) {
    var unclassifiedList =
        unclassifiedSet.stream()
            .map(i -> i.withZoneSameInstants(UTC_ZONE_ID))
            .sorted()
            .filter(ZonedInterval::nonZeroDuration)
            .collect(Collectors.toList());
    if (unclassifiedList.size() < 1) return unclassifiedList;

    var timestamp = unclassifiedList.get(0).getZonedStartInclusive();
    for (var z : unclassifiedList) {
      if (z.getZonedStartInclusive().isBefore(timestamp)) {
        throw new IllegalArgumentException(
            "Unable to classify intervals that contain any overlaps");
      }
      timestamp = z.getZonedEndExclusive();
    }

    return unclassifiedList;
  }

  private List<ZonedInterval> splitWorkweekIntoDays(ZonedInterval workweek) {
    var timezone = workweek.getTimezone();
    var startWeekAt = workweek.getZonedStartInclusive();
    var startDayAt = startWeekAt.toLocalTime();

    var days = new ArrayList<ZonedInterval>();
    var startDate = startWeekAt.toLocalDate();
    startDate
        .datesUntil(startDate.plusWeeks(1))
        .map(
            d ->
                new ZonedInterval(
                    d.atTime(startDayAt).atZone(timezone).toEpochSecond(),
                    d.plusDays(1).atTime(startDayAt).atZone(timezone).toEpochSecond(),
                    UTC_ZONE_ID))
        .forEach(days::add);

    return days;
  }

  /**
   * Finds any unclassifiedIntervals that have overlap with process week, then classifies those
   * intervals in day order
   *
   * @param workweek interval representing the process week
   * @return ordered list of classified intervals
   */
  private List<ClassifiedInterval> processUnclassifiedForWeek(ZonedInterval workweek) {
    var timezone = workweek.getTimezone();
    var weekDays = splitWorkweekIntoDays(workweek);
    // filter out unaffected intervals
    var unclassifiedList =
        orderedUnclassified.stream()
            .filter(unclassified -> hasOverlap(unclassified, workweek))
            .collect(Collectors.toList());
    if (unclassifiedList.size() < 1) return Collections.emptyList();

    // initialize counters for week processing
    var startWeekAt = workweek.getZonedStartInclusive();
    var leadingInterval = unclassifiedList.get(0).withZoneSameInstants(timezone);
    initializeForWeekProcessing(leadingInterval, startWeekAt);

    // extend weekDays by one day to allow classification of any trailing interval portion
    var endWeekAt = workweek.getZonedEndExclusive();
    weekDays.add(
        new ZonedInterval(
            endWeekAt.toEpochSecond(), endWeekAt.plusDays(1).toEpochSecond(), UTC_ZONE_ID));

    // for each day interval, classify intersecting unclassified intervals
    return weekDays.stream()
        .peek(d -> dayRules = getRules.apply(d.getZonedStartInclusive()))
        .map(d -> intersectByDay(d, unclassifiedList))
        .map(this::processUnclassifiedInDay)
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .collect(Collectors.toList());
  }

  /**
   * Mutates the classifiedList by either adding the new classified interval or updating previously
   * classified intervals with preferred classification
   *
   * @param interval interval
   */
  private void addOrReplaceClassifiedInterval(ClassifiedInterval interval) {
    var existing =
        classifiedList.stream()
            .filter(c -> hasOverlap(c.getZonedInterval(), interval.getZonedInterval()))
            .collect(Collectors.toList());

    existing.forEach(
        e -> {
          classifiedList.remove(e);
          if (!interval.getZonedInterval().contains(e.getZonedInterval())) {
            intersectingSubtract(e.getZonedInterval(), interval.getZonedInterval()).stream()
                .filter(ZonedInterval::nonZeroDuration)
                .map(z -> new ClassifiedInterval(z, e.getClassifiedType()))
                .forEach(classifiedList::add);
          }
        });

    classifiedList.add(interval);
  }

  /**
   * Given a Day Interval, the portion of the unclassifiedSet that intersects the day is returned as
   * a sorted list
   *
   * @param dayInterval interval representing the work day
   * @param unclassifiedSet unclassified set of intervals
   * @return unclassified intervals contained by the given day interval
   */
  private List<ZonedInterval> intersectByDay(
      ZonedInterval dayInterval, Collection<ZonedInterval> unclassifiedSet) {
    return unclassifiedSet.stream()
        .sorted()
        .map(u -> getOverlap(u, dayInterval))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .collect(Collectors.toList());
  }

  /**
   * Given the unclassified intervals in a Day, each interval in order is classified. Progress
   * collections are updated
   *
   * @param unclassifiedList ordered list of Unclassified Intervals
   */
  private List<ClassifiedInterval> processUnclassifiedInDay(List<ZonedInterval> unclassifiedList) {
    // reset counters and set new processing day
    initializeForDayProcessing();

    // if there isn't anything to classify, bail out
    if (unclassifiedList.size() < 1) return null;

    var classifiedIntervals = new ArrayList<ClassifiedInterval>();
    for (var u : unclassifiedList) {
      resetConsecutiveSecondsIfDiscontinuous(u);
      var validClassifications = new HashSet<ClassifiableInterval>();
      dayRules.stream()
          .map(r -> findClassifiableInterval(r, u))
          .filter(Optional::isPresent)
          .map(Optional::get)
          .forEach(validClassifications::add);

      classifiedIntervals.addAll(chooseBestClassificationsForInterval(u, validClassifications));
    }

    return classifiedIntervals;
  }

  /**
   * Determines a classifiable portion of the given interval using the given classification rule
   *
   * @param rule used to determine if a portion of the given interval can be classified as its type
   * @param unclassifiedInterval interval to classify
   * @return a portion or totality of the given interval, with the classification attached
   */
  private Optional<ClassifiableInterval> findClassifiableInterval(
      ClassificationRule rule, ZonedInterval unclassifiedInterval) {
    var intervals =
        rule.getRuleCriteria().stream()
            .map(rc -> findIntervalSatisfyingCriteria(unclassifiedInterval, rc))
            .collect(Collectors.toList());

    // if any criteria returns empty; bail out early (must satisfy ALL criteria)
    if (intervals.stream().anyMatch(Optional::isEmpty)) return Optional.empty();

    return ZonedIntervalUtils.getOverlap(
            intervals.stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList()))
        .map(overlap -> new ClassifiableInterval(rule.getType(), overlap));
  }

  /**
   * Given a single RuleCriteria and interval, determines a portion (if any) that can be classified
   * by the rule criteria
   *
   * @param interval source interval
   * @param criteria sub rule criteria
   * @return portion of the source interval that satisfies the given rule; empty if threshold isn't
   *     met
   */
  private Optional<ZonedInterval> findIntervalSatisfyingCriteria(
      ZonedInterval interval, RuleCriteria criteria) {
    var scope = criteria.getScope();
    var threshold = criteria.getThreshold();
    if (threshold == null) return Optional.empty();

    threshold *= (scope.equals(RuleCriteriaScope.CONSECUTIVE_DAYS) ? 1 : 3600);
    var intervalDelta =
        interval.getDuration().getSeconds()
            * (scope.equals(RuleCriteriaScope.CONSECUTIVE_DAYS) ? 0 : 1);

    int counterSoFar;
    switch (scope) {
      case CONSECUTIVE_DAYS:
        counterSoFar = consecutiveDaysSoFar;
        break;
      case CONSECUTIVE_HOURS:
        counterSoFar = consecutiveSecondsSoFar.getRegularConsecutiveSeconds();
        break;
      case WEEK_HOURS:
        counterSoFar = getRegularWeekSecondsSoFarTotal();
        break;
      case DAY_HOURS:
        counterSoFar = getRegularDaySecondsSoFarTotal();
        break;
      default:
        throw new IllegalArgumentException("Unknown scope type: " + scope);
    }

    if (counterSoFar >= threshold) return Optional.of(interval);
    if (counterSoFar + intervalDelta <= threshold) return Optional.empty();

    var unclassifiable = threshold - counterSoFar;
    var startAt = interval.getZonedStartInclusive().plusSeconds(unclassifiable).toEpochSecond();
    var endAt = interval.getZonedEndExclusive().toEpochSecond();

    return Optional.of(new ZonedInterval(startAt, endAt, interval.getTimezone()));
  }

  /**
   * Determines the best interval(s) to use for classifying the source interval, then updates the
   * progress collections. Throws if the resulting classifications don't exactly cover to the source
   * interval
   *
   * @param source interval that needs classification
   * @param validClassifiableIntervals set of valid classifiable intervals. From which to chose the
   *     most beneficial
   */
  private List<ClassifiedInterval> chooseBestClassificationsForInterval(
      ZonedInterval source, Set<ClassifiableInterval> validClassifiableIntervals) {
    var remainingSource = List.of(source);
    var bestOrderedClassifiableIntervals =
        orderAndTrimClassifiableIntervals(validClassifiableIntervals);
    var classifiedIntervals = new ArrayList<ClassifiedInterval>();

    for (var i : bestOrderedClassifiableIntervals) {
      classifiedIntervals.add(classifyInterval(i));

      remainingSource =
          remainingSource.stream()
              .map(
                  s -> {
                    var operand = i.getInterval();
                    return intersectingSubtract(s, operand);
                  })
              .flatMap(Collection::stream)
              .filter(ZonedInterval::nonZeroDuration)
              .collect(Collectors.toList());
    }

    // verify no intervals fell through the rules
    if (remainingSource.size() > 0) {
      throw new IllegalArgumentException(
          "Exhausting all valid classifiable intervals failed to classify entire source interval");
    }

    return classifiedIntervals;
  }

  /**
   * Given a set of Classifiable Intervals, they are sorted by most beneficial first, combined by
   * type, then trimmed so there is no overlapping classifications
   *
   * @param validClassifiableIntervals collection of classifiable intervals
   * @return non-overlapping and sorted list of classifiable intervals
   */
  private List<ClassifiableInterval> orderAndTrimClassifiableIntervals(
      Set<ClassifiableInterval> validClassifiableIntervals) {
    var bestOrderedValid =
        validClassifiableIntervals.stream()
            .collect(
                Collectors.groupingBy(
                    ClassifiableInterval::getClassifiedType,
                    Collectors.reducing(ClassifiableInterval::combine)))
            .values()
            .stream()
            .filter(Optional::isPresent)
            .map(Optional::get)
            .sorted(Comparator.comparing(ClassifiableInterval::getClassifiedType).reversed())
            .collect(Collectors.toList());

    // for each item, trim the previously classifiable portion
    ZonedInterval classifiableTotal = null;
    for (var v : bestOrderedValid) {
      var fullValid = v.getInterval();
      v.subtractInterval(classifiableTotal);
      classifiableTotal = fullValid;
    }

    return bestOrderedValid.stream()
        .filter(v -> v.getInterval() != null)
        .sorted(Comparator.comparing(ClassifiableInterval::getClassifiedType))
        .collect(Collectors.toList());
  }

  /**
   * Classifies the given intersection of the given interval and one from the unclassified intervals
   * list; Throws if the given interval isn't contained by exactly one unclassified interval
   *
   * @param classifiableInterval interval to classify
   */
  private ClassifiedInterval classifyInterval(ClassifiableInterval classifiableInterval) {
    var interval = classifiableInterval.getInterval();
    var classifiedType = classifiableInterval.getClassifiedType();

    validateContainingIntervalExists(interval);

    addClassifiedSeconds(interval, classifiedType);

    return new ClassifiedInterval(interval, classifiedType);
  }

  /**
   * Gets the unclassified interval that strictly contains the given interval. Throws exception if
   * not found.
   *
   * @param interval contained interval
   * @return unclassified interval containing the given interval
   */
  private void validateContainingIntervalExists(ZonedInterval interval) {
    var found =
        orderedUnclassified.stream().filter(u -> u.contains(interval)).collect(Collectors.toList());
    if (found.size() > 1) {
      throw new IllegalStateException(
          "Unable to classify intervals that span multiple unclassified intervals");
    }
    if (found.size() < 1) {
      throw new IllegalStateException(
          "Unable to find an unclassified interval containing the interval");
    }
  }

  private void initializeForWeekProcessing(
      ZonedInterval leadingInterval, ZonedDateTime startWeekAt) {
    daySecondsSoFar.clear();
    weekSecondsSoFar.clear();
    consecutiveSecondsSoFar = new ConsecutiveSeconds(startWeekAt);

    if (leadingInterval.contains(startWeekAt)) {
      var prevInterval =
          new ZonedInterval(
              leadingInterval.getZonedStartInclusive().toEpochSecond(),
              startWeekAt.toEpochSecond(),
              startWeekAt.getZone());
      // FIXME: this assumption may technically be incorrect (Should: only include the regular hours
      //  of the previous interval)
      consecutiveSecondsSoFar.seconds.put(
          REGULAR_TIME, (int) prevInterval.getDuration().getSeconds());
    }
  }

  private int getRegularDaySecondsSoFarTotal() {
    return daySecondsSoFar.entrySet().stream()
        .filter(s -> REGULAR_TIME.equals(s.getKey()))
        .map(Entry::getValue)
        .reduce(Integer::sum)
        .orElse(0);
  }

  private boolean dayHasSeconds() {
    return daySecondsSoFar.values().stream().reduce(Integer::sum).orElse(0) > 0;
  }

  private int getRegularWeekSecondsSoFarTotal() {
    return weekSecondsSoFar.entrySet().stream()
        .filter(s -> REGULAR_TIME.equals(s.getKey()))
        .map(Entry::getValue)
        .reduce(Integer::sum)
        .orElse(0);
  }

  private void initializeForDayProcessing() {
    // reset consecutive days if previous day found 0 seconds
    consecutiveDaysSoFar = !dayHasSeconds() ? 0 : consecutiveDaysSoFar + 1;

    // processing a new day, so reset day seconds
    daySecondsSoFar.clear();
  }

  private void addClassifiedSeconds(ZonedInterval interval, ClassificationType type) {
    int seconds = (int) interval.getDuration().getSeconds();
    // counters
    var weekSeconds = weekSecondsSoFar.getOrDefault(type, 0);
    weekSeconds += seconds;
    weekSecondsSoFar.put(type, weekSeconds);

    var daySeconds = daySecondsSoFar.getOrDefault(type, 0);
    daySeconds += seconds;
    daySecondsSoFar.put(type, daySeconds);

    var consecutiveSeconds = consecutiveSecondsSoFar.seconds.getOrDefault(type, 0);
    consecutiveSeconds += seconds;
    consecutiveSecondsSoFar.seconds.put(type, consecutiveSeconds);
    consecutiveSecondsSoFar.prevEndedAt = interval.getZonedEndExclusive();
  }

  private void resetConsecutiveSecondsIfDiscontinuous(ZonedInterval interval) {
    if (!interval.getZonedStartInclusive().isEqual(consecutiveSecondsSoFar.prevEndedAt)) {
      consecutiveSecondsSoFar.seconds.clear();
    }
  }

  @AllArgsConstructor
  private static class ConsecutiveSeconds {
    private final Map<ClassificationType, Integer> seconds = new HashMap<>();
    private ZonedDateTime prevEndedAt;

    public Integer getRegularConsecutiveSeconds() {
      return seconds.entrySet().stream()
          .filter(s -> REGULAR_TIME.equals(s.getKey()))
          .map(Entry::getValue)
          .reduce(Integer::sum)
          .orElse(0);
    }
  }
}
