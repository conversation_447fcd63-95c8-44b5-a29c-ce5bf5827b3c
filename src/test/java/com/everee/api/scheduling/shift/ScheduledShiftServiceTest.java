package com.everee.api.scheduling.shift;

import static com.everee.api.TestTags.TEST_GROUP_4;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.everee.api.auth.role.InternalRole;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.configurations.CompanySchedulingConfigurationService;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employee.lookup.EmployeeLookup;
import com.everee.api.employeePosition.EmployeePosition;
import com.everee.api.holiday.employee.EmployeeHolidayLookupService;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.model.TransactionHandler;
import com.everee.api.money.Money;
import com.everee.api.phase.ContiguousPhasedSet;
import com.everee.api.scheduling.Schedule;
import com.everee.api.scheduling.ScheduleFactory;
import com.everee.api.scheduling.notification.SchedulingEmployeeNotificationService;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflictDetectionService;
import com.everee.api.scheduling.shift.request.*;
import com.everee.api.scheduling.shift.template.ScheduledShiftTemplateService;
import com.everee.api.timeclock.overtime.OvertimeSettings;
import com.everee.api.timeoff.request.TimeOffRequestLookup;
import com.everee.api.timeoff.request.TimeOffRequestLookupService;
import com.everee.api.user.CoreUser;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import javax.persistence.LockModeType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

@Tag(TEST_GROUP_4)
@ExtendWith(MockitoExtension.class)
public class ScheduledShiftServiceTest {
  private static final Long companyId = 10L;
  private static final Long locationId = 101L;

  @Mock ScheduledShiftConflictDetectionService shiftConflictDetectionService;
  @Mock ScheduledShiftLookupService scheduledShiftLookupService;
  @Mock ScheduledShiftRepository scheduledShiftRepository;
  @Mock ScheduledShiftRequestRecipientRepository scheduledShiftRequestRecipientRepository;
  @Mock ScheduledShiftRequestService scheduledShiftRequestService;
  @Mock EmployeeService employeeService;
  @Mock CompanyService companyService;
  @Mock EmployeeHolidayLookupService employeeHolidayLookupService;
  @Mock ScheduleFactory scheduleFactory;
  @Mock DetailedEmployeeLookupService detailedEmployeeLookupService;
  @Mock CompanySchedulingConfigurationService companySchedulingConfigurationService;
  @Mock TimeOffRequestLookupService timeOffRequestLookupService;
  @Mock ScheduledShiftTemplateService scheduledShiftTemplateService;
  @Mock SchedulingEmployeeNotificationService schedulingEmployeeNotificationService;
  @Mock ScheduledShiftRequestRecipientStateService scheduledShiftRequestRecipientStateService;
  @Mock TransactionHandler transactionHandler;

  @InjectMocks ScheduledShiftService shiftService;

  @BeforeEach
  public void beforeEach() {
    var company = new DetailedCompany();
    company.setId(companyId);
    company.setOvertimeSettings(new OvertimeSettings());

    lenient().when(companyService.getCompany(eq(companyId))).thenReturn(company);
  }

  @Test
  public void testListShifts() {
    // === GIVEN === //
    var shiftOne = new ScheduledShift();
    shiftOne.setId(101L);
    shiftOne.setCompanyId(10L);
    shiftOne.setEmployeeId(1001L);
    var shiftTwo = new ScheduledShift();
    shiftTwo.setId(101L);
    shiftTwo.setCompanyId(10L);

    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(shiftOne, shiftTwo)));

    // === RUN === //
    var lookup = mock(ScheduledShiftLookup.class);
    var response = shiftService.listShifts(lookup, Pageable.unpaged());

    // === EXPECT === //
    assertThat(response.getTotalElements()).isEqualTo(2);
    assertThat(response.getContent()).containsExactlyInAnyOrder(shiftOne, shiftTwo);
  }

  @Test
  public void testGet() {
    // === GIVEN === //
    var shiftOne = new ScheduledShift();
    shiftOne.setId(101L);
    shiftOne.setCompanyId(companyId);
    shiftOne.setEmployeeId(1001L);
    when(scheduledShiftRepository.findByIdAndCompanyId(eq(101L), eq(companyId)))
        .thenReturn(Optional.of(shiftOne));

    var user = new CoreUser();
    user.setInternalRole(InternalRole.EVEREE_ADMIN);

    // === RUN === //
    var response = shiftService.get(101L, companyId, user);

    // === EXPECT === //
    assertThat(response).isEqualTo(shiftOne);
  }

  @Test
  public void testCreate() {
    // === GIVEN === //
    doAnswer(
            a -> {
              a.getArgument(0, Runnable.class).run();
              return null;
            })
        .when(transactionHandler)
        .executeInNewTransaction(any(Runnable.class));
    when(scheduledShiftRepository.save(any(ScheduledShift.class)))
        .thenAnswer(
            a -> {
              var shift = a.getArgument(0, ScheduledShift.class);
              shift.setId(1001L);
              return shift;
            });
    when(scheduledShiftRequestService.listShiftRequests(
            any(ScheduledShiftRequestLookup.class), any(Pageable.class)))
        .thenAnswer(a -> Page.empty(a.getArgument(1, Pageable.class)));
    when(scheduledShiftRequestService.create(
            anyLong(), nullable(Long.class), any(ScheduledShiftRequestParamsForCreate.class)))
        .thenReturn(null);

    // === RUN === //
    var params = new ScheduledShiftParams();
    params.setStartDateTime(LocalDateTime.parse("2022-04-11T09:00"));
    params.setEndDateTime(LocalDateTime.parse("2022-04-11T13:00"));
    params.setLocationId(locationId);
    params.setPublishState(PublishState.PUBLISHED);
    var response = shiftService.create(companyId, params);

    // === EXPECT === //
    assertThat(response.getPublishState()).isEqualTo(PublishState.PUBLISHED);

    verify(shiftConflictDetectionService).detectConflicts(any(ScheduledShift.class));
    verify(schedulingEmployeeNotificationService)
        .notifyEmployeesPublishedShifts(anySet(), anyLong());
    verify(scheduledShiftRequestService)
        .create(anyLong(), nullable(Long.class), any(ScheduledShiftRequestParamsForCreate.class));
  }

  @Test
  public void testUpdate_colorChange() {
    // === GIVEN === //
    var schedule = new Schedule();
    schedule.setStartDate(LocalDate.parse("2022-04-07"));
    schedule.setEndDate(LocalDate.parse("2022-04-14"));
    schedule.setCompanyId(companyId);
    schedule.setLocationId(locationId);
    when(scheduleFactory.createScheduleForDate(eq(companyId), eq(locationId), any(LocalDate.class)))
        .thenReturn(schedule);

    var shiftId = 100L;
    var shift = mock(ScheduledShift.class);
    when(shift.getStartDateTime()).thenReturn(LocalDateTime.parse("2022-04-11T06:00:00"));
    when(shift.getEndDateTime()).thenReturn(LocalDateTime.parse("2022-04-11T11:00:00"));
    when(shift.getCompanyId()).thenReturn(companyId);
    when(shift.getLocationId()).thenReturn(locationId);
    when(shift.getColor()).thenReturn(ScheduledShiftColor.COLOR_1);
    when(scheduledShiftRepository.findByIdAndCompanyId(eq(shiftId), eq(companyId)))
        .thenReturn(Optional.of(shift));
    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(shift)));

    var openRequest = new ScheduledShiftRequest();
    openRequest.setType(ScheduledShiftRequestType.OPEN_SHIFT);
    when(scheduledShiftRequestService.listShiftRequests(
            any(ScheduledShiftRequestLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(openRequest)));

    // === RUN === //
    var params = new ScheduledShiftParams();
    params.setStartDateTime(shift.getStartDateTime());
    params.setEndDateTime(shift.getEndDateTime());
    params.setLocationId(shift.getLocationId());
    params.setColor(ScheduledShiftColor.COLOR_3);
    var response = shiftService.update(shiftId, companyId, params);

    // === EXPECT === //
    assertThat(response).isEqualTo(shift);
    verify(shift).setColor(ScheduledShiftColor.COLOR_3);
  }

  @Test
  public void testUpdate_timeChange() {
    // === GIVEN === //
    var schedule = new Schedule();
    schedule.setStartDate(LocalDate.parse("2022-04-07"));
    schedule.setEndDate(LocalDate.parse("2022-04-14"));
    schedule.setCompanyId(companyId);
    schedule.setLocationId(locationId);
    when(scheduleFactory.createScheduleForDate(eq(companyId), eq(locationId), any(LocalDate.class)))
        .thenReturn(schedule);

    var employeeId = 123L;
    var shiftId = 100L;
    var shift = mock(ScheduledShift.class);
    when(shift.getStartDateTime()).thenReturn(LocalDateTime.parse("2022-04-11T06:00:00"));
    when(shift.getEndDateTime()).thenReturn(LocalDateTime.parse("2022-04-11T11:00:00"));
    when(shift.getCompanyId()).thenReturn(companyId);
    when(shift.getLocationId()).thenReturn(locationId);
    when(shift.getEmployeeId()).thenReturn(employeeId);
    when(scheduledShiftRepository.findByIdAndCompanyId(eq(shiftId), eq(companyId)))
        .thenReturn(Optional.of(shift));
    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(shift)));

    when(shiftConflictDetectionService.detectConflicts(any(ScheduledShift.class)))
        .thenReturn(Collections.emptyList());

    var coverId = 345L;
    var coverRequest = mock(ScheduledShiftRequest.class);
    when(coverRequest.getId()).thenReturn(coverId);
    when(coverRequest.getType()).thenReturn(ScheduledShiftRequestType.COVERAGE);
    when(scheduledShiftRequestService.listShiftRequests(
            any(ScheduledShiftRequestLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(coverRequest)));

    // === RUN === //
    var params = new ScheduledShiftParams();
    params.setStartDateTime(shift.getStartDateTime());
    params.setEndDateTime(shift.getEndDateTime().plusHours(2));
    params.setLocationId(shift.getLocationId());
    params.setEmployeeId(employeeId);
    var response = shiftService.update(shiftId, companyId, params);

    // === EXPECT === //
    assertThat(response).isEqualTo(shift);
    verify(shift).setEndDateTime(LocalDateTime.parse("2022-04-11T13:00:00"));
    verify(scheduledShiftRequestService).delete(eq(coverId), eq(companyId));
  }

  @Test
  public void testDelete() {
    // === GIVEN === //
    var shiftId = 123L;
    var shift = new ScheduledShift();
    shift.setId(shiftId);
    shift.setCompanyId(companyId);
    shift.setLocationId(locationId);
    shift.setEmployeeId(31L);
    shift.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    shift.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    when(scheduledShiftRepository.findByIdAndCompanyId(eq(shiftId), eq(companyId)))
        .thenReturn(Optional.of(shift));

    var schedule = new Schedule();
    schedule.setStartDate(LocalDate.parse("2022-04-07"));
    schedule.setEndDate(LocalDate.parse("2022-04-14"));
    schedule.setCompanyId(companyId);
    schedule.setLocationId(locationId);
    when(scheduleFactory.createScheduleForDate(eq(companyId), eq(locationId), any(LocalDate.class)))
        .thenReturn(schedule);

    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());

    // === RUN === //
    shiftService.delete(shiftId, companyId);

    // === EXPECT === //
    verify(shiftConflictDetectionService).deleteConflictsForShift(eq(shift));
    verify(scheduledShiftRequestService).deleteRequestsForShift(eq(shift));
    verify(scheduledShiftRepository).delete(eq(shift));
    verify(shiftConflictDetectionService).detectConflictsWithoutShift(eq(shift));
  }

  @Test
  public void testCopyPreviousWeekShifts() {
    // === GIVEN === //
    doAnswer(
            a -> {
              a.getArgument(0, Runnable.class).run();
              return null;
            })
        .when(transactionHandler)
        .executeInNewTransaction(any(Runnable.class));
    when(scheduledShiftRepository.save(any(ScheduledShift.class)))
        .thenAnswer(
            a -> {
              var shift = a.getArgument(0, ScheduledShift.class);
              shift.setId(1001L);
              return shift;
            });
    var shiftOne = new ScheduledShift();
    shiftOne.setId(1L);
    shiftOne.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    shiftOne.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    var shiftTwo = new ScheduledShift();
    shiftTwo.setId(2L);
    shiftTwo.setStartDateTime(LocalDateTime.parse("2022-04-12T06:00:00"));
    shiftTwo.setEndDateTime(LocalDateTime.parse("2022-04-12T11:00:00"));
    var shifts = List.of(shiftOne, shiftTwo);
    when(scheduledShiftLookupService.streamShifts(
            any(ScheduledShiftLookup.class), any(Pageable.class), any(LockModeType.class)))
        .thenReturn(shifts.stream());

    when(scheduledShiftRequestService.listShiftRequests(
            any(ScheduledShiftRequestLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());

    // === RUN === //
    var lookup = new ScheduledShiftLookup();
    lookup.setStartDate(LocalDate.parse("2022-04-07"));
    lookup.setEndDate(lookup.getStartDate().plusWeeks(1));
    shiftService.copyPreviousWeekShifts(lookup, 1);

    // === EXPECT === //
    verify(scheduledShiftRepository, times(2)).save(any(ScheduledShift.class));
    verify(shiftConflictDetectionService, times(2)).detectConflicts(any(ScheduledShift.class));
    verify(scheduledShiftRequestService, times(2))
        .listShiftRequests(any(ScheduledShiftRequestLookup.class), any(Pageable.class));
  }

  @Test
  public void testCopyPreviousDayShifts() {
    // === GIVEN === //
    doAnswer(
            a -> {
              a.getArgument(0, Runnable.class).run();
              return null;
            })
        .when(transactionHandler)
        .executeInNewTransaction(any(Runnable.class));
    when(scheduledShiftRepository.save(any(ScheduledShift.class)))
        .thenAnswer(
            a -> {
              var shift = a.getArgument(0, ScheduledShift.class);
              shift.setId(1001L);
              return shift;
            });
    var shiftOne = new ScheduledShift();
    shiftOne.setId(1L);
    shiftOne.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    shiftOne.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    var shiftTwo = new ScheduledShift();
    shiftTwo.setId(2L);
    shiftTwo.setStartDateTime(LocalDateTime.parse("2022-04-11T14:00:00"));
    shiftTwo.setEndDateTime(LocalDateTime.parse("2022-04-11T17:00:00"));
    var shifts = List.of(shiftOne, shiftTwo);
    when(scheduledShiftLookupService.streamShifts(
            any(ScheduledShiftLookup.class), any(Pageable.class), any(LockModeType.class)))
        .thenReturn(shifts.stream());

    when(scheduledShiftRequestService.listShiftRequests(
            any(ScheduledShiftRequestLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());

    // === RUN === //
    var lookup = new ScheduledShiftLookup();
    lookup.setStartDate(LocalDate.parse("2022-04-07"));
    lookup.setEndDate(lookup.getStartDate());
    shiftService.copyPreviousDayShifts(lookup);

    // === EXPECT === //
    verify(scheduledShiftRepository, times(2)).save(any(ScheduledShift.class));
    verify(shiftConflictDetectionService, times(2)).detectConflicts(any(ScheduledShift.class));
    verify(scheduledShiftRequestService, times(2))
        .listShiftRequests(any(ScheduledShiftRequestLookup.class), any(Pageable.class));
  }

  @Test
  public void testRemoveShifts() {
    // === GIVEN === //
    var startDate = LocalDate.parse("2022-04-07");
    var endDate = LocalDate.parse("2022-04-14");
    var schedule = new Schedule();
    schedule.setCompanyId(companyId);
    schedule.setLocationId(locationId);
    schedule.setStartDate(startDate);
    schedule.setEndDate(endDate);
    when(scheduleFactory.createScheduleForDate(eq(companyId), eq(locationId), eq(startDate)))
        .thenReturn(schedule);

    var shiftId = 123L;
    var shift = new ScheduledShift();
    shift.setId(shiftId);
    shift.setCompanyId(companyId);
    shift.setLocationId(locationId);
    shift.setEmployeeId(31L);
    shift.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    shift.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    var lookup = new ScheduledShiftLookup();
    lookup.setCompanyId(companyId);
    lookup.setLocationIds(Set.of(locationId));
    lookup.setStartDate(startDate);
    lookup.setEndDate(endDate);
    when(scheduledShiftLookupService.streamShifts(
            eq(lookup), any(Pageable.class), any(LockModeType.class)))
        .thenReturn(Stream.of(shift));

    // === RUN === //
    shiftService.removeShifts(lookup, null);

    // === EXPECT === //
    verify(shiftConflictDetectionService).deleteConflictsForShift(eq(shift));
    verify(scheduledShiftRequestService).deleteRequestsForShift(eq(shift));
    verify(scheduledShiftRepository).delete(eq(shift));
  }

  @Test
  public void testUnpublishShifts() {
    // === GIVEN === //
    doAnswer(
            a -> {
              a.getArgument(0, Runnable.class).run();
              return null;
            })
        .when(transactionHandler)
        .executeInNewTransaction(any(Runnable.class));

    var shiftId = 123L;
    var shift = mock(ScheduledShift.class);
    when(shift.getId()).thenReturn(shiftId);
    when(shift.getCompanyId()).thenReturn(companyId);
    var lookup = new ScheduledShiftLookup();
    lookup.setCompanyId(companyId);
    lookup.setLocationIds(Set.of(locationId));
    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(shift)));

    var openRequest = mock(ScheduledShiftRequest.class);
    when(openRequest.getType()).thenReturn(ScheduledShiftRequestType.OPEN_SHIFT);
    when(scheduledShiftRequestService.listShiftRequests(
            any(ScheduledShiftRequestLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(openRequest)));

    // === RUN === //
    shiftService.unpublishShifts(lookup);

    // === EXPECT === //
    verify(shift).setPublishState(eq(PublishState.UNPUBLISHED));
    verify(openRequest).setState(eq(ScheduledShiftRequestState.AWAITING_RESPONSE));
    verify(scheduledShiftRequestService, times(0))
        .create(anyLong(), nullable(Long.class), any(ScheduledShiftRequestParamsForCreate.class));
  }

  @Test
  public void testClaimOpenShift() {
    // === GIVEN === //
    var userId = 333L;
    var shiftId = 123L;
    var shift = new ScheduledShift();
    shift.setId(shiftId);
    shift.setCompanyId(companyId);
    shift.setLocationId(locationId);
    shift.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    shift.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    when(scheduledShiftRepository.findByIdAndCompanyId(eq(shiftId), eq(companyId)))
        .thenReturn(Optional.of(shift));

    var employee = new DetailedEmployee();
    employee.setSchedulingWorkLocationId(locationId);
    when(employeeService.findActiveByUserIdAndCompanyIdForDates(
            eq(userId), eq(companyId), any(LocalDate.class), any(LocalDate.class)))
        .thenReturn(List.of(employee));

    var request = mock(ScheduledShiftRequest.class);
    when(request.getId()).thenReturn(789L);
    when(request.getOfferingShiftId()).thenReturn(shiftId);
    when(scheduledShiftRequestService.findOneOrThrow(any(ScheduledShiftRequestLookup.class)))
        .thenReturn(request);

    // === RUN === //
    var response = shiftService.claimOpenShift(shiftId, companyId, userId);

    // === EXPECT === //
    assertThat(response).isEqualTo(request);
    verify(request).setState(eq(ScheduledShiftRequestState.AWAITING_MANAGER));
  }

  @Test
  public void testCountUnpublishedShifts() {
    // === GIVEN === //
    var lookup = mock(ScheduledShiftLookup.class);
    when(scheduledShiftLookupService.count(eq(lookup))).thenReturn(13);

    // === RUN === //
    var response = shiftService.countUnpublishedShifts(lookup);

    // === EXPECT === //
    assertThat(response).isEqualTo(13);
    verify(lookup).setPublishState(eq(PublishState.UNPUBLISHED));
    verify(lookup).setWithoutConflicts(eq(Boolean.TRUE));
  }

  @Test
  public void testPublishUnpublishedShifts() {
    // === GIVEN === //
    var lookup = mock(ScheduledShiftLookup.class);
    when(lookup.getCompanyId()).thenReturn(companyId);
    var shiftOne = mock(ScheduledShift.class);
    var shiftTwo = mock(ScheduledShift.class);
    var shifts = List.of(shiftOne, shiftTwo);
    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(shifts));

    // === RUN === //
    var response = shiftService.publishUnpublishedShifts(lookup);

    // === EXPECT === //
    assertThat(response).containsExactlyInAnyOrder(shiftOne, shiftTwo);
    verify(lookup).setPublishState(eq(PublishState.UNPUBLISHED));
    verify(lookup).setWithoutConflicts(eq(Boolean.TRUE));
    shifts.forEach(shift -> verify(shift).setPublishState(eq(PublishState.PUBLISHED)));
    verify(scheduledShiftRepository).saveAll(eq(shifts));
    verify(schedulingEmployeeNotificationService)
        .notifyEmployeesPublishedShifts(eq(shifts), eq(companyId));
  }

  @Test
  public void testGetScheduleLocationEmployees() {
    // === GIVEN === //
    var startDate = LocalDate.parse("2022-04-07");
    var endDate = LocalDate.parse("2022-04-14");

    when(companySchedulingConfigurationService.getEmploymentTypesEnabled(eq(companyId)))
        .thenReturn(Set.of(EmploymentType.CONTRACTOR));

    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());

    var employeeOne = mock(DetailedEmployee.class);
    when(employeeOne.getId()).thenReturn(1L);
    var employeeTwo = mock(DetailedEmployee.class);
    when(employeeTwo.getId()).thenReturn(2L);
    when(detailedEmployeeLookupService.listAll(
            any(EmployeeLookup.class), any(Pageable.class), any(Sort.class)))
        .thenReturn(new PageImpl<>(List.of(employeeOne, employeeTwo)));

    // === RUN === //
    var response =
        shiftService.getScheduleLocationEmployees(
            companyId, Set.of(locationId), startDate, endDate);

    // === EXPECT === //
    var expectedOne = new ScheduledShiftEmployee();
    expectedOne.setId(1L);
    var expectedTwo = new ScheduledShiftEmployee();
    expectedTwo.setId(2L);
    assertThat(response).containsExactlyInAnyOrder(expectedOne, expectedTwo);
  }

  @Test
  public void testFindEmployeesAvailableForShift() {
    // === GIVEN === //
    var shiftId = 123L;
    var shift = new ScheduledShift();
    shift.setId(shiftId);
    shift.setCompanyId(companyId);
    shift.setLocationId(locationId);
    shift.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    shift.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    when(scheduledShiftRepository.findByIdAndCompanyId(eq(shiftId), eq(companyId)))
        .thenReturn(Optional.of(shift));

    when(companySchedulingConfigurationService.getEmploymentTypesEnabled(eq(companyId)))
        .thenReturn(Set.of(EmploymentType.CONTRACTOR));

    when(scheduledShiftLookupService.listAll(any(ScheduledShiftLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());

    var employeeOne = mock(DetailedEmployee.class);
    when(employeeOne.getId()).thenReturn(1L);
    var employeeTwo = mock(DetailedEmployee.class);
    when(employeeTwo.getId()).thenReturn(2L);
    when(detailedEmployeeLookupService.listAll(
            any(EmployeeLookup.class), any(Pageable.class), any(Sort.class)))
        .thenReturn(new PageImpl<>(List.of(employeeOne, employeeTwo)));

    when(timeOffRequestLookupService.listAll(any(TimeOffRequestLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());

    // === RUN === //
    var response = shiftService.findEmployeesAvailableForShift(shiftId, companyId);

    // === EXPECT === //
    var expectedOne = new ScheduledShiftEmployee();
    expectedOne.setId(1L);
    var expectedTwo = new ScheduledShiftEmployee();
    expectedTwo.setId(2L);
    assertThat(response).containsExactlyInAnyOrder(expectedOne, expectedTwo);
  }

  @Test
  public void testUpdateHourlyRate() {
    // === GIVEN === //
    var shiftId = 123L;
    var shift = mock(ScheduledShift.class);
    when(shift.getCompanyId()).thenReturn(companyId);

    var employeeId = 111L;
    var hourlyRate = Money.valueOf("23.00");
    var position = new EmployeePosition();
    position.setPayType(PayType.HOURLY);
    position.setStartDate(LocalDate.parse("2022-01-01"));
    position.setPayRate(hourlyRate);
    var employee = mock(DetailedEmployee.class);
    when(employee.getEmploymentType()).thenReturn(EmploymentType.EMPLOYEE);
    when(employee.getPositions()).thenReturn(new ContiguousPhasedSet<>(Set.of(position)));

    when(employeeService.get(eq(employeeId), eq(companyId))).thenReturn(employee);

    // === RUN === //
    var params = new ScheduledShiftParams();
    params.setEmployeeId(employeeId);
    params.setStartDateTime(LocalDateTime.parse("2022-04-11T06:00:00"));
    params.setEndDateTime(LocalDateTime.parse("2022-04-11T11:00:00"));
    shiftService.updateHourlyRate(params, shift, new OvertimeSettings());

    // === EXPECT === //
    verify(shift).setHourlyRate(eq(hourlyRate));
    verify(shift).setOtRate(eq(hourlyRate.times(1.5)));
  }
}
