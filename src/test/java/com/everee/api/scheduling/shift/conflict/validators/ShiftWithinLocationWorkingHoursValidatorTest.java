package com.everee.api.scheduling.shift.conflict.validators;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflict;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflictState;
import com.everee.api.scheduling.shift.conflict.ScheduledShiftConflictType;
import com.everee.api.user.UserService;
import com.everee.api.worklocation.WorkLocationHours;
import com.everee.api.worklocation.WorkLocationHoursService;
import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class ShiftWithinLocationWorkingHoursValidatorTest extends BaseValidatorTest {

  @Mock private MockedStatic<UserService> mockedStatic;

  @Mock(lenient = true)
  private WorkLocationHoursService workLocationHoursService;

  @InjectMocks private ShiftWithinLocationWorkingHoursValidator validator;

  @BeforeEach
  public void setup() {
    mockedStatic.when(UserService::getAuthenticatedUserId).thenReturn(100L);

    ReflectionTestUtils.setField(validator, "enabled", true);
    when(workLocationHoursService.getWorkingHours(any(Long.class)))
        .thenReturn(
            List.of(
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.SUNDAY)
                    .setStartTime(LocalTime.MIDNIGHT)
                    .setEndTime(LocalTime.MIDNIGHT)
                    .setClosed(true),
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.MONDAY)
                    .setStartTime(LocalTime.parse("08:00:00"))
                    .setEndTime(LocalTime.MIDNIGHT),
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.TUESDAY)
                    .setStartTime(LocalTime.parse("08:00:00"))
                    .setEndTime(LocalTime.MIDNIGHT),
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.WEDNESDAY)
                    .setStartTime(LocalTime.parse("08:00:00"))
                    .setEndTime(LocalTime.MIDNIGHT),
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.THURSDAY)
                    .setStartTime(LocalTime.parse("08:00:00"))
                    .setEndTime(LocalTime.MIDNIGHT),
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.FRIDAY)
                    .setStartTime(LocalTime.parse("08:00:00"))
                    .setEndTime(LocalTime.MIDNIGHT),
                new WorkLocationHours()
                    .setWorkLocationId(locationId)
                    .setDayOfWeek(DayOfWeek.SATURDAY)
                    .setStartTime(LocalTime.MIDNIGHT)
                    .setEndTime(LocalTime.MIDNIGHT)));
  }

  @Test
  public void getConflictType() {
    assertThat(validator.getConflictType()).isEqualTo(ScheduledShiftConflictType.LOCATION_HOURS);
  }

  @Test
  public void validate_no_conflict() {
    buildShiftsForMonTuesWed_36hours();
    var context = context(buildShift("2020-12-03T10:30:00", "2020-12-03T15:30:00"), false);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isFalse();
  }

  @Test
  public void validate() {
    buildShiftsForMonTuesWed_36hours();
    var context = context(buildShift("2020-12-04T07:30:00", "2020-12-04T15:30:00"), false);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isTrue();
    var conflict = conflictOptional.get();
    assertThat(conflict.getScheduledShiftId()).isEqualTo(3L);
    assertThat(conflict.getState()).isEqualTo(ScheduledShiftConflictState.CONFLICT);
    assertThat(context.getExistingConflicts().size()).isEqualTo(1);
  }

  @Test
  public void validate_shift_ends_during_unavailable_hours() {
    buildShiftsForMonTuesWed_36hours();
    var context = context(buildShift("2020-12-03T20:30:00", "2020-12-04T02:30:00"), false);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isTrue();
  }

  @Test
  public void validate_shift_when_closed() {
    buildShiftsForMonTuesWed_36hours();
    var context = context(buildShift("2020-11-29T20:30:00", "2020-11-29T02:30:00"), false);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isTrue();
  }

  @Test
  public void validate_open_all_day() {
    buildShiftsForMonTuesWed_36hours();
    var context = context(buildShift("2020-12-05T01:30:00", "2020-12-05T12:30:00"), false);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isFalse();
  }

  @Test
  public void validate_existing_conflict() {
    buildShiftsForMonTuesWed_36hours();
    conflicts.add(
        new ScheduledShiftConflict()
            .setType(ScheduledShiftConflictType.LOCATION_HOURS)
            .setScheduledShiftId(3L)
            .setState(ScheduledShiftConflictState.RESOLVED));
    var context = context(buildShift("2020-12-04T07:30:00", "2020-12-04T15:30:00"), false);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isTrue();
    var conflict = conflictOptional.get();
    assertThat(conflict.getState()).isEqualTo(ScheduledShiftConflictState.RESOLVED);
    assertThat(context.getExistingConflicts().size()).isEqualTo(1);
  }

  @Test
  public void validate_remove() {
    buildShiftsForMonTuesWed_36hours();
    conflicts.add(
        new ScheduledShiftConflict()
            .setType(ScheduledShiftConflictType.LOCATION_HOURS)
            .setScheduledShiftId(3L)
            .setState(ScheduledShiftConflictState.RESOLVED));
    var context = context(buildShift("2020-12-04T07:30:00", "2020-12-04T15:30:00"), true);
    var conflictOptional = validator.validate(context);

    assertThat(conflictOptional.isPresent()).isFalse();
    assertThat(context.getExistingConflicts().size()).isEqualTo(0);
    assertThat(context.getRemoveConflicts().size()).isEqualTo(1);
  }
}
