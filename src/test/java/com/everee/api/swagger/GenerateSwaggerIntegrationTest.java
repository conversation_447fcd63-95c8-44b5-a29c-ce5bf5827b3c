package com.everee.api.swagger;

import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.io.File;
import java.io.FileWriter;
import java.util.Objects;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@AutoConfigureEmbeddedDatabase
@ActiveProfiles(value = {"nojavers", "integration-test"})
public class GenerateSwaggerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void exportSwaggerToJson() throws Exception {
        MvcResult mvcResult = this.mockMvc.perform(get("/open-api/docs")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        MockHttpServletResponse response = mvcResult.getResponse();
        File file = new File("swagger.json");
        try (FileWriter fileWriter = new FileWriter(file)) {
            fileWriter.write(Objects.requireNonNull(response.getContentAsString()));
        }
    }
}
