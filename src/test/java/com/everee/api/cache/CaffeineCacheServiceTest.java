package com.everee.api.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class CaffeineCacheServiceTest {

    @Mock
    private CacheManager cacheManager;

    @Mock
    private com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache;

    @Mock
    private ObjectMapper mapper;

    private CaffeineCache cache;

    private CaffeineCacheService caffeineCacheService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        this.cache = new CaffeineCache("companiesIds", nativeCache);
        caffeineCacheService = new CaffeineCacheService(cacheManager);
    }

    @Test
    public void givenCacheName_WhenGetCacheNames_thenReturnCache() {
        // When
        when(cacheManager.getCache(anyString()))
                .thenReturn(cache);
        when(cacheManager.getCache(anyString()))
                .thenReturn(cache);
        when(cacheManager.getCacheNames())
                .thenReturn(List.of("companiesIds"));
        Collection<String> cacheNames = caffeineCacheService.getCacheNames();
        // Then
        assertThat(cacheNames).asList().hasSize(1);
    }

    @Test
    public void givenNoCacheName_WhenGetCacheNames_thenReturnEmpty() {
        // When
        when(cacheManager.getCache(anyString()))
                .thenReturn(cache);
        when(cacheManager.getCache(anyString()))
                .thenReturn(cache);
        Collection<String> cacheNames = caffeineCacheService.getCacheNames();
        // Then
        assertThat(cacheNames).asList().hasSize(0);
    }

    @Test
    public void givenCacheNameAndKey_WhenGetEntriesForCache_thenReturnEntries() throws Exception {
        // Given
        String cacheName = "companiesIds";
        Integer key = 176129;
        List<Long> expectedList = new ArrayList<>();
        expectedList.add(1L);
        expectedList.add(2L);
        expectedList.add(3L);
        Map<Integer, Object> expectedMap = new HashMap<>();
        expectedMap.put(key, expectedList);
        // When
        when(cacheManager.getCache(anyString()))
                .thenReturn(cache);
        when(cache.get(cacheName, Map.class))
                .thenReturn(expectedMap);
        when(nativeCache.asMap())
                .thenReturn(new ConcurrentHashMap<>(expectedMap));
        when(mapper.writeValueAsString(anyString()))
                .thenReturn(new String());

        when(mapper.readValue(anyString(), eq(HashMap.class)))
                .thenReturn((HashMap<Integer, Object>) expectedMap);

        Map<String, Object> entriesForCache = caffeineCacheService.getEntriesForCache(cacheName);
        // Then
        assertThat(entriesForCache).isNotNull();
        assertThat(entriesForCache.get(key.toString())).asList().hasSize(3);
    }

    @Test
    public void givenCacheNameAndKey_WhenGetEntriesForCacheAndNoCache_thenReturnEmptyMap() throws Exception {
        Map<String, Object> entriesForCache = caffeineCacheService.getEntriesForCache(anyString());
        // Then
        assertThat(entriesForCache).isNotNull();
    }

    @Test
    public void givenCacheNameAndKey_WhenEvictCacheEntry_thenEvictEntry() throws Exception {
        // Given
        String cacheName = "companiesIds";
        Integer key = 176129;
        Map<Integer, Object> expectedMap = new HashMap<>();
        // When
        when(cacheManager.getCache(anyString()))
                .thenReturn(cache);
        when(cache.get(cacheName, Map.class))
                .thenReturn(expectedMap);
        when(nativeCache.asMap())
                .thenReturn(new ConcurrentHashMap<>(expectedMap));
        when(mapper.writeValueAsString(anyString()))
                .thenReturn(new String());

        when(mapper.readValue(anyString(), eq(HashMap.class)))
                .thenReturn((HashMap<Integer, Object>) expectedMap);

        caffeineCacheService.evictCacheEntry(cacheName, key);
        Map<String, Object> entriesForCache = caffeineCacheService.getEntriesForCache(cacheName);
        // Then
        assertThat(entriesForCache).isNotNull();
        assertThat(entriesForCache.get(key.toString())).isNull();
    }
}
