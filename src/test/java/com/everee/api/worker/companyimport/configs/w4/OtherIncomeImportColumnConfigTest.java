package com.everee.api.worker.companyimport.configs.w4;

import static com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions.options;
import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.company.DetailedCompany;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.createparams.EmployeeParamsForCreate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Import;

@SpringBootTest(classes = {MessageSource.class, MessageSourceHolder.class})
@Import(MessageSourceAutoConfiguration.class)
public class OtherIncomeImportColumnConfigTest {

  @Autowired private MessageSource messageSource;

  private OtherIncomeImportColumnConfig columnConfig = new OtherIncomeImportColumnConfig();

  @Test
  public void getColumnHeader() {
    assertThat(columnConfig.getColumnHeader())
        .as("Changing a column header can affect existing import files!")
        .isEqualTo("W4 other income");
  }

  @Test
  public void helpText() {
    columnConfig.setMessageSource(messageSource);
    assertThat(columnConfig.helpText(null, null)).isNotBlank();
  }

  @Test
  public void getInputRule() {
    columnConfig.setMessageSource(messageSource);
    assertThat(columnConfig.getInputRule()).isNotBlank();
  }

  @Test
  public void isOptional() {
    assertThat(columnConfig.isOptional(options(new DetailedCompany(), false, false))).isTrue();
    assertThat(columnConfig.isOptional(options(new DetailedCompany(), true, false))).isTrue();
    assertThat(columnConfig.isOptional(options(new DetailedCompany(), false, true))).isTrue();
    assertThat(columnConfig.isOptional(options(new DetailedCompany(), true, true))).isTrue();
  }

  @Test
  public void isApplicable() {
    var importOptions = options(new DetailedCompany(), false, false);
    assertThat(columnConfig.isApplicable(importOptions)).isTrue();

    importOptions = options(new DetailedCompany(), false, true);
    assertThat(columnConfig.isApplicable(importOptions)).isFalse();

    importOptions = options(new DetailedCompany(), true, false);
    assertThat(columnConfig.isApplicable(importOptions)).isFalse();

    importOptions = options(new DetailedCompany(), true, true);
    assertThat(columnConfig.isApplicable(importOptions)).isFalse();
  }

  @Test
  public void apply() {
    var importOptions = options((DetailedCompany) new DetailedCompany().setId(5L), false, false);
    var workerImport =
        new WorkerImportSummary().setWorkerParamsForCreate(new EmployeeParamsForCreate());
    columnConfig.apply(" 8", workerImport, importOptions);

    assertThat(workerImport.getEmployeeParams().getWithholdingSettings().getOtherIncomeAnnually())
        .isEqualTo(Money.valueOf("8"));

    workerImport =
        new WorkerImportSummary()
            .setWorkerParamsForCreate(new EmployeeParamsForCreate().setPayType(PayType.SALARY));
    columnConfig.apply(" ", workerImport, importOptions);

    assertThat(workerImport.getEmployeeParams().getWithholdingSettings().getOtherIncomeAnnually())
        .isEqualTo(Money.ZERO);
  }

  @Test
  public void apply_invalid() {
    var importOptions = options((DetailedCompany) new DetailedCompany().setId(5L), false, true);
    var workerImport =
        new WorkerImportSummary()
            .setWorkerParamsForCreate(new EmployeeParamsForCreate().setPayType(PayType.HOURLY));
    Assertions.assertThrows(
        NumberFormatException.class, () -> columnConfig.apply(" abc", workerImport, importOptions));
  }
}
