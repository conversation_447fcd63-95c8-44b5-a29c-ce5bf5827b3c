package com.everee.api.worker.companyimport.configs.bank;

import static com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions.options;
import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.company.DetailedCompany;
import com.everee.api.company.PayCardStatus;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.worker.companyimport.CompanyWorkerImportJobOptions;
import com.everee.api.worker.companyimport.WorkerImportSummary;
import com.everee.api.worker.companyimport.configs.InvalidImportDataException;
import com.everee.api.worker.companyimport.configs.MissingDataException;
import com.everee.api.worker.createparams.ContractorParamsForCreate;
import com.everee.api.worker.createparams.EmployeeParamsForCreate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Import;

@SpringBootTest(classes = {MessageSource.class, MessageSourceHolder.class})
@Import(MessageSourceAutoConfiguration.class)
public class BankAccountNumberImportColumnConfigTest {

  @Autowired private MessageSource messageSource;

  private BankAccountNumberImportColumnConfig columnConfig =
      new BankAccountNumberImportColumnConfig();

  @Test
  public void getColumnHeader() {
    assertThat(columnConfig.getColumnHeader())
        .as("Changing a column header can affect existing import files!")
        .isEqualTo("Account number");
  }

  @Test
  public void helpText() {
    columnConfig.setMessageSource(messageSource);
    assertThat(columnConfig.helpText(null, null)).isNotBlank();
  }

  @Test
  public void getInputRule() {
    columnConfig.setMessageSource(messageSource);
    assertThat(columnConfig.getInputRule()).isNotBlank();
  }

  @Test
  public void isOptional() {
    assertThat(columnConfig.isOptional(new CompanyWorkerImportJobOptions(new DetailedCompany())))
        .isFalse();
  }

  @Test
  public void isApplicable() {
    var importOptions = options((DetailedCompany) new DetailedCompany().setId(5L), false, true);
    assertThat(columnConfig.isApplicable(importOptions)).isFalse();

    importOptions.setWillWorkerCompleteOnboarding(false);
    assertThat(columnConfig.isApplicable(importOptions)).isTrue();

    importOptions.getCompany().setPayCardStatus(PayCardStatus.EPC_ONLY);
    assertThat(columnConfig.isApplicable(importOptions)).isFalse();
  }

  @Test
  public void apply() {
    var importOptions = options((DetailedCompany) new DetailedCompany().setId(5L), false, false);
    var workerImport =
        new WorkerImportSummary().setWorkerParamsForCreate(new EmployeeParamsForCreate());
    columnConfig.apply(" 75645", workerImport, importOptions);

    assertThat(workerImport.getWorkerParamsForCreate().getBankAccount()).isNotNull();
    assertThat(workerImport.getWorkerParamsForCreate().getBankAccount().getAccountNumber())
        .isEqualTo("75645");

    workerImport =
        new WorkerImportSummary().setWorkerParamsForCreate(new ContractorParamsForCreate());
    columnConfig.apply("8781479  ", workerImport, importOptions);

    assertThat(workerImport.getWorkerParamsForCreate().getBankAccount()).isNotNull();
    assertThat(workerImport.getWorkerParamsForCreate().getBankAccount().getAccountNumber())
        .isEqualTo("8781479");
  }

  @Test
  public void apply_invalid() {
    var importOptions = options((DetailedCompany) new DetailedCompany().setId(5L), false, true);
    var workerImport =
        new WorkerImportSummary().setWorkerParamsForCreate(new EmployeeParamsForCreate());
    Assertions.assertThrows(
        MissingDataException.class, () -> columnConfig.apply("", workerImport, importOptions));
  }

  @Test
  public void apply_invalid_characters() {
    var importOptions = options((DetailedCompany) new DetailedCompany().setId(5L), false, true);
    var workerImport =
        new WorkerImportSummary().setWorkerParamsForCreate(new EmployeeParamsForCreate());
    Assertions.assertThrows(
        InvalidImportDataException.class,
        () -> columnConfig.apply("878abc1479  ", workerImport, importOptions));
  }
}
