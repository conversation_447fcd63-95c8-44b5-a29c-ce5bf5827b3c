package com.everee.api.integration.finicity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.everee.api.company.BankAccountProvider;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.DetailedCompanyRepository;
import com.everee.api.company.bankaccount.CompanyBankAccountFinicityService;
import com.everee.api.company.bankaccount.CompanyBankAccountRepository;
import com.finicity.Finicity;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIfSystemProperty;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ContextConfiguration;

@SpringBootTest
@ContextConfiguration(
    classes = {
      CompanyBankAccountFinicityService.class,
      FinicityProperties.class,
      FinicityConfiguration.class
    })
// This test does not run with the main test suite (see the dev profile limitation)
// It runs daily in the github workflow .github/workflows/finicity_scheduled_integration_tests.yml
@EnabledIfSystemProperty(named = "spring.profiles.active", matches = "dev")
@DisabledIfSystemProperty(named = "spring.profiles.active", matches = "(?!dev)")
@EnableConfigurationProperties
public class CompanyBankAccountFinicityVendorIntegrationTest {

  private final Set<String> customerIdsToDelete = new HashSet<>();

  @Autowired private Finicity finicity;
  @Autowired private CompanyBankAccountFinicityService companyBankAccountFinicityService;

  @SpyBean private FinicityProperties finicityProperties;
  @MockBean private ApplicationEventPublisher eventPublisher;
  @MockBean private DetailedCompanyRepository detailedCompanyRepository;
  @MockBean private CompanyBankAccountRepository companyBankAccountRepository;

  @AfterEach
  public void cleanup() {
    customerIdsToDelete.forEach(finicity.customers()::deleteCustomer);
  }

  @Test
  public void testGetConnectUrlForNewCompanyBankAccount() {

    var prefix = String.join("_", getClass().getSimpleName(), UUID.randomUUID().toString());

    var company = new DetailedCompany();
    company.setId(123L);
    company.setDemoCompany(true);
    company.setDisplayName("Boomerang, Inc.");
    company.setFundingBankAccountProvider(BankAccountProvider.FINICITY);

    when(finicityProperties.getCustomerPrefix()).thenReturn(prefix);

    when(detailedCompanyRepository.getOne(any())).thenReturn(company);

    var connectUrl = companyBankAccountFinicityService.getConnectUrlForNewCompanyBankAccount(123L);

    customerIdsToDelete.add(company.getFinicityCustomerId());

    assertThat(connectUrl).isNotNull();
    assertThat(connectUrl.getLink()).isNotBlank();

    var customerId = company.getFinicityCustomerId();
    assertThat(customerId).isNotBlank();

    var customer = finicity.customers().getCustomer(customerId);
    assertThat(customer.getId()).isEqualTo(customerId);
    assertThat(customer.getUsername()).isEqualTo(prefix + ".company123");
    assertThat(customer.getFirstName()).isEqualTo(company.getDisplayName());
  }
}
