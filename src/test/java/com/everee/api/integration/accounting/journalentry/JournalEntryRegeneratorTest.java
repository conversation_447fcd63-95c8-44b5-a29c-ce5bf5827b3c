package com.everee.api.integration.accounting.journalentry;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.everee.api.ach.AchFile;
import com.everee.api.ach.AchService;
import com.everee.api.distribution.CompanyDistribution;
import com.everee.api.distribution.CompanyDistributionService;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.integration.ConfiguredIntegration;
import com.everee.api.integration.ConfiguredIntegrationLookupService;
import com.everee.api.integration.SupportedIntegration;
import com.everee.api.integration.accounting.journalentry.exception.JournalEntryCannotBeRegeneratedException;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;

@Import(MessageSourceAutoConfiguration.class)
@SpringBootTest(classes = {MessageSource.class, MessageSourceHolder.class})
class JournalEntryRegeneratorTest {
  @Mock AchService achService;
  @Mock CompanyDistributionService companyDistributionService;
  @Mock ConfiguredIntegrationLookupService configuredIntegrationLookupService;
  @Mock JournalEntryGenerator journalEntryGenerator;
  @Mock JournalEntryLineLookupService journalEntryLineLookupService;
  @Mock JournalEntryLookupService journalEntryLookupService;
  @Mock JournalEntryLineRepository journalEntryLineRepository;
  @Mock JournalEntryRepository journalEntryRepository;

  @InjectMocks JournalEntryRegenerator journalEntryRegenerator;

  @Test
  void regenerate_ach() {
    when(journalEntryLookupService.findOneOrThrow(any()))
        .thenReturn(
            new JournalEntry().setAchFileId(50L).setIntegration(SupportedIntegration.QUICKBOOKS));
    when(journalEntryLineLookupService.listAll(any(), any())).thenReturn(Page.empty());
    when(achService.getAchFile(50L)).thenReturn(new AchFile());
    when(achService.buildJournalEntryCreationParams(any()))
        .thenReturn(new JournalEntryParamsForCreate());
    when(configuredIntegrationLookupService.findOne(any()))
        .thenReturn(Optional.of(new ConfiguredIntegration()));
    when(journalEntryGenerator.createJournalEntryFromParams(any(), any()))
        .thenReturn((JournalEntry) new JournalEntry().setAchFileId(50L).setId(44L));
    when(journalEntryRepository.save(any())).thenAnswer(returnsFirstArg());

    var regeneratedJE = journalEntryRegenerator.regenerate(new JournalEntryLookup());

    assertThat(regeneratedJE).isNotNull();
    assertThat(regeneratedJE.getId()).isEqualTo(44L);
  }

  @Test
  void regenerate_companyDistribution() {
    when(journalEntryLookupService.findOneOrThrow(any()))
        .thenReturn(
            new JournalEntry()
                .setCompanyDistributionId(50L)
                .setIntegration(SupportedIntegration.QUICKBOOKS));
    when(journalEntryLineLookupService.listAll(any(), any())).thenReturn(Page.empty());
    when(companyDistributionService.getCompanyDistribution(any()))
        .thenReturn(new CompanyDistribution());
    when(companyDistributionService.buildJournalEntryCreationParams(any()))
        .thenReturn(new JournalEntryParamsForCreate());
    when(configuredIntegrationLookupService.findOne(any()))
        .thenReturn(Optional.of(new ConfiguredIntegration()));
    when(journalEntryGenerator.createJournalEntryFromParams(any(), any()))
        .thenReturn((JournalEntry) new JournalEntry().setAchFileId(50L).setId(44L));
    when(journalEntryRepository.save(any())).thenAnswer(returnsFirstArg());

    var regeneratedJE = journalEntryRegenerator.regenerate(new JournalEntryLookup());

    assertThat(regeneratedJE).isNotNull();
    assertThat(regeneratedJE.getId()).isEqualTo(44L);
  }

  @Test
  void regenerate_null() {
    when(journalEntryLookupService.findOneOrThrow(any()))
        .thenReturn(
            new JournalEntry()
                .setCompanyDistributionId(50L)
                .setIntegration(SupportedIntegration.QUICKBOOKS));
    when(journalEntryLineLookupService.listAll(any(), any())).thenReturn(Page.empty());
    when(companyDistributionService.getCompanyDistribution(any()))
        .thenReturn(new CompanyDistribution());
    when(companyDistributionService.buildJournalEntryCreationParams(any()))
        .thenReturn(new JournalEntryParamsForCreate());
    when(configuredIntegrationLookupService.findOne(any()))
        .thenReturn(Optional.of(new ConfiguredIntegration()));
    when(journalEntryGenerator.createJournalEntryFromParams(any(), any())).thenReturn(null);

    var regeneratedJE = journalEntryRegenerator.regenerate(new JournalEntryLookup());

    assertThat(regeneratedJE).isNull();
  }

  @Test
  void regenerate_cannot_regenerate() {
    when(journalEntryLookupService.findOneOrThrow(any()))
        .thenReturn(
            new JournalEntry()
                .setAchFileId(50L)
                .setIntegration(SupportedIntegration.QUICKBOOKS)
                .setStatus(JournalEntryStatus.SENT));

    assertThrows(
        JournalEntryCannotBeRegeneratedException.class,
        () -> journalEntryRegenerator.regenerate(new JournalEntryLookup()));
  }
}
