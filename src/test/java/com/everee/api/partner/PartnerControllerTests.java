package com.everee.api.partner;

import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.everee.api.BaseControllerTestsV2;
import com.everee.api.config.PublicFileLocationSerializer;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;

@WebMvcTest(PartnerController.class)
public class PartnerControllerTests extends BaseControllerTestsV2 {

  @MockBean private PartnerRepository partnerRepository;
  @MockBean private StorageService storageService;

  @TestConfiguration
  static class AdditionalConfig {
    @Bean
    public PublicFileLocationSerializer presignedPublicFileLocationSerializer() {
      return new PublicFileLocationSerializer(mock(StorageService.class));
    }
  }

  @Test
  @SneakyThrows
  public void getPartner() {
    var partner = createMockPartner(15L);
    when(storageService.getStoredFileLink(any(), any()))
        .thenAnswer(
            c ->
                new StoredFileLink(
                    new URL("https://aws.com/public/" + c.getArgument(1, String.class)),
                    Instant.MAX));

    when(partnerRepository.findById(partner.getId())).thenReturn(Optional.of(partner));

    mvc.perform(
            get(BASE_URL + "/partners/{partnerId}", partner.getId())
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id", is(15)))
        .andExpect(jsonPath("$.displayName", is("Partner 15")))
        .andExpect(jsonPath("$.legalName").doesNotExist())
        .andExpect(jsonPath("$.ein").doesNotExist())
        .andExpect(jsonPath("$.notes").doesNotExist())
        .andExpect(jsonPath("$.startDate").doesNotExist())
        .andExpect(jsonPath("$.endDate").doesNotExist())
        .andExpect(jsonPath("$.loginRequestParamName").doesNotExist())
        .andExpect(jsonPath("$.supportEmailAddress", is("canopy-payroll@canopy.com15")))
        .andExpect(
            jsonPath(
                "$.primaryLogoUrl",
                is(
                    "https://aws.com/public/partners/15/images/primary_logo_1310d701-664b-4c35-bb5b-d55ec7e853f3.png")))
        .andExpect(
            jsonPath(
                "$.darkLogoUrl",
                is(
                    "https://aws.com/public/partners/15/images/dark_logo_cc686f90-bdf5-401c-bc55-6829761082a0.gif")))
        .andExpect(
            jsonPath(
                "$.lightLogoUrl",
                is(
                    "https://aws.com/public/partners/15/images/light_logo_702f4676-65af-4db9-a872-546c571f4b21.jpg")))
        .andExpect(
            jsonPath(
                "$.iconUrl",
                is(
                    "https://aws.com/public/partners/15/images/icon_898d0d9e-3132-42c3-8394-62303c08188b.jpeg")))
        .andExpect(
            jsonPath(
                "$.faviconUrl",
                is(
                    "https://aws.com/public/partners/15/images/favicon_03a60b7d-1a71-4526-a7e3-ef9c21087ae5.ico")));
  }

  @Test
  @SneakyThrows
  public void getPartnerByUrlParam() {
    var partner = createMockPartner(15L);
    when(storageService.getStoredFileLink(any(), any()))
        .thenAnswer(
            c ->
                new StoredFileLink(
                    new URL("https://aws.com/public/" + c.getArgument(1, String.class)),
                    Instant.MAX));

    when(partnerRepository.findByLoginRequestParamName(partner.getLoginRequestParamName()))
        .thenReturn(Optional.of(partner));

    mvc.perform(
            get(
                    BASE_URL + "/partners/find-by-url-param?partner-url-param={urlParam}",
                    partner.getLoginRequestParamName())
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id", is(15)))
        .andExpect(jsonPath("$.displayName", is("Partner 15")))
        .andExpect(jsonPath("$.legalName").doesNotExist())
        .andExpect(jsonPath("$.ein").doesNotExist())
        .andExpect(jsonPath("$.notes").doesNotExist())
        .andExpect(jsonPath("$.startDate").doesNotExist())
        .andExpect(jsonPath("$.endDate").doesNotExist())
        .andExpect(jsonPath("$.loginRequestParamName").doesNotExist())
        .andExpect(jsonPath("$.supportEmailAddress", is("canopy-payroll@canopy.com15")))
        .andExpect(
            jsonPath(
                "$.primaryLogoUrl",
                is(
                    "https://aws.com/public/partners/15/images/primary_logo_1310d701-664b-4c35-bb5b-d55ec7e853f3.png")))
        .andExpect(
            jsonPath(
                "$.darkLogoUrl",
                is(
                    "https://aws.com/public/partners/15/images/dark_logo_cc686f90-bdf5-401c-bc55-6829761082a0.gif")))
        .andExpect(
            jsonPath(
                "$.lightLogoUrl",
                is(
                    "https://aws.com/public/partners/15/images/light_logo_702f4676-65af-4db9-a872-546c571f4b21.jpg")))
        .andExpect(
            jsonPath(
                "$.iconUrl",
                is(
                    "https://aws.com/public/partners/15/images/icon_898d0d9e-3132-42c3-8394-62303c08188b.jpeg")))
        .andExpect(
            jsonPath(
                "$.faviconUrl",
                is(
                    "https://aws.com/public/partners/15/images/favicon_03a60b7d-1a71-4526-a7e3-ef9c21087ae5.ico")));
  }

  @Test
  @SneakyThrows
  public void getPartnerByUrlParam_default() {
    var partner = createMockPartner(1L);
    when(storageService.getStoredFileLink(any(), any()))
        .thenAnswer(
            c ->
                new StoredFileLink(
                    new URL("https://aws.com/public/default/" + c.getArgument(1, String.class)),
                    Instant.MAX));

    when(partnerRepository.findDefaultPartner()).thenReturn(Optional.of(partner));

    mvc.perform(
            get(BASE_URL + "/partners/find-by-url-param?partner-url-param=")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id", is(1)))
        .andExpect(jsonPath("$.displayName", is("Partner 1")))
        .andExpect(jsonPath("$.legalName").doesNotExist())
        .andExpect(jsonPath("$.ein").doesNotExist())
        .andExpect(jsonPath("$.notes").doesNotExist())
        .andExpect(jsonPath("$.startDate").doesNotExist())
        .andExpect(jsonPath("$.endDate").doesNotExist())
        .andExpect(jsonPath("$.loginRequestParamName").doesNotExist())
        .andExpect(jsonPath("$.supportEmailAddress", is("canopy-payroll@canopy.com1")))
        .andExpect(
            jsonPath(
                "$.primaryLogoUrl",
                is(
                    "https://aws.com/public/default/partners/1/images/primary_logo_1310d701-664b-4c35-bb5b-d55ec7e853f3.png")))
        .andExpect(
            jsonPath(
                "$.darkLogoUrl",
                is(
                    "https://aws.com/public/default/partners/1/images/dark_logo_cc686f90-bdf5-401c-bc55-6829761082a0.gif")))
        .andExpect(
            jsonPath(
                "$.lightLogoUrl",
                is(
                    "https://aws.com/public/default/partners/1/images/light_logo_702f4676-65af-4db9-a872-546c571f4b21.jpg")))
        .andExpect(
            jsonPath(
                "$.iconUrl",
                is(
                    "https://aws.com/public/default/partners/1/images/icon_898d0d9e-3132-42c3-8394-62303c08188b.jpeg")))
        .andExpect(
            jsonPath(
                "$.faviconUrl",
                is(
                    "https://aws.com/public/default/partners/1/images/favicon_03a60b7d-1a71-4526-a7e3-ef9c21087ae5.ico")));
  }

  public static Partner createMockPartner(Long id) {
    return (Partner)
        new Partner()
            .setDisplayName("Partner " + id)
            .setLegalName("Canopy " + id)
            .setEin("819123320")
            .setStartDate(LocalDate.parse("2022-12-01"))
            .setLoginRequestParamName("canopyhr" + id)
            .setSupportEmailAddress("<EMAIL>" + id)
            .setPrimaryLogoLocation(
                "partners/" + id + "/images/primary_logo_1310d701-664b-4c35-bb5b-d55ec7e853f3.png")
            .setDarkLogoLocation(
                "partners/" + id + "/images/dark_logo_cc686f90-bdf5-401c-bc55-6829761082a0.gif")
            .setLightLogoLocation(
                "partners/" + id + "/images/light_logo_702f4676-65af-4db9-a872-546c571f4b21.jpg")
            .setIconLocation(
                "partners/" + id + "/images/icon_898d0d9e-3132-42c3-8394-62303c08188b.jpeg")
            .setFaviconLocation(
                "partners/" + id + "/images/favicon_03a60b7d-1a71-4526-a7e3-ef9c21087ae5.ico")
            .setId(id);
  }
}
