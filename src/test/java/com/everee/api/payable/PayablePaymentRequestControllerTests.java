package com.everee.api.payable;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.everee.api.BaseControllerTestsV2;
import com.everee.api.company.DetailedCompany;
import com.everee.api.payable.model.CreatePayablePaymentRequest;
import com.everee.api.payable.model.PayablePaymentRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;

@WebMvcTest(PayablePaymentRequestController.class)
class PayablePaymentRequestControllerTests extends BaseControllerTestsV2 {

  @MockBean private PayablePaymentRequestService payablePaymentRequestService;

  @Captor private ArgumentCaptor<CreatePayablePaymentRequest> workersCaptor;

  @Test
  void createPayablePaymentRequest() throws Exception {
    var company = createCompany();
    when(mockTenantSecurityContext.getCompany()).thenReturn(Optional.of(company));

    var entity =
        (PayablePaymentRequest)
            new PayablePaymentRequest()
                .setCompanyId(company.getId())
                .setPayableCount(100)
                .setPayPeriodId(1L)
                .setId(1L)
                .setCreatedAt(LocalDateTime.now())
                .setUpdatedAt(LocalDateTime.now());
    when(payablePaymentRequestService.createPayablePaymentRequest(any(), any())).thenReturn(entity);

    mvc.perform(
            post(BASE_URL + "/payables/payment-request")
                .header("x-everee-tenant-id", company.getId())
                .accept("application/json"))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("$.id").value(entity.getId()))
        .andExpect(jsonPath("$.companyId").value(entity.getCompanyId()))
        .andExpect(jsonPath("$.payableCount").value(entity.getPayableCount()))
        .andExpect(jsonPath("$.createdAt").exists())
        .andExpect(jsonPath("$.updatedAt").doesNotExist())
        .andExpect(jsonPath("$.payPeriodId").doesNotExist());

    verify(payablePaymentRequestService)
        .createPayablePaymentRequest(any(), workersCaptor.capture());
    assertThat(workersCaptor.getValue()).isNull();
  }

  @Test
  void createPayablePaymentRequestWithWorkerIds() throws Exception {
    var company = createCompany();
    when(mockTenantSecurityContext.getCompany()).thenReturn(Optional.of(company));

    var entity =
        (PayablePaymentRequest)
            new PayablePaymentRequest()
                .setCompanyId(company.getId())
                .setPayableCount(3)
                .setPayPeriodId(1L)
                .setId(1L)
                .setCreatedAt(LocalDateTime.now())
                .setUpdatedAt(LocalDateTime.now());
    when(payablePaymentRequestService.createPayablePaymentRequest(any(), any())).thenReturn(entity);

    var workers =
        new CreatePayablePaymentRequest()
            .setExternalWorkerIds(
                Set.of(
                    "fbaa03fd-f7a0-4e3c-b1f7-b268af31857a",
                    "1c7d1399-d47c-48e8-9772-39573d94fd2c",
                    "efa64a52-a676-4c02-8cc2-ca7ee54a9cdc"));

    mvc.perform(
            post(BASE_URL + "/payables/payment-request")
                .header("x-everee-tenant-id", company.getId())
                .accept("application/json")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(workers)))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("$.id").value(entity.getId()))
        .andExpect(jsonPath("$.companyId").value(entity.getCompanyId()))
        .andExpect(jsonPath("$.payableCount").value(entity.getPayableCount()))
        .andExpect(jsonPath("$.createdAt").exists());

    verify(payablePaymentRequestService)
        .createPayablePaymentRequest(any(), workersCaptor.capture());
    assertThat(workersCaptor.getValue()).isEqualTo(workers);
  }

  @Test
  public void createPayablePaymentRequestWithNoPayablesFound() throws Exception {
    var company = createCompany();
    when(mockTenantSecurityContext.getCompany()).thenReturn(Optional.of(company));

    var entity =
        (PayablePaymentRequest)
            new PayablePaymentRequest().setCompanyId(company.getId()).setPayableCount(0);
    when(payablePaymentRequestService.createPayablePaymentRequest(any(), any())).thenReturn(entity);

    mvc.perform(
            post(BASE_URL + "/payables/payment-request")
                .header("x-everee-tenant-id", company.getId())
                .accept("application/json")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id").doesNotExist())
        .andExpect(jsonPath("$.companyId").value(entity.getCompanyId()))
        .andExpect(jsonPath("$.payableCount").value(entity.getPayableCount()))
        .andExpect(jsonPath("$.createdAt").doesNotExist());

    verify(payablePaymentRequestService)
        .createPayablePaymentRequest(any(), workersCaptor.capture());
    assertThat(workersCaptor.getValue()).isNull();
  }

  private DetailedCompany createCompany() {
    return (DetailedCompany) new DetailedCompany().setId(1L);
  }

  private String toJson(Object o) throws Exception {
    var mapper = new ObjectMapper();
    var writer = mapper.writer().withDefaultPrettyPrinter();
    return writer.writeValueAsString(o);
  }
}
