package com.everee.api.labor.workedshift;

import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.labor.workedshift.entity.WorkedShift;
import com.everee.api.labor.workedshift.entity.WorkedShiftPunch;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.util.BooleanApiParam;
import com.everee.api.util.DateUtil;
import com.everee.api.util.DbUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@AutoConfigureEmbeddedDatabase
@Transactional
@ActiveProfiles("integration-test")
class WorkedShiftLookupServiceIntegrationTest {

  @Autowired WorkedShiftLookupService workedShiftLookupService;
  @Autowired DbUtil dbUtil;
  @Autowired WorkedShiftRepository workedShiftRepository;

  @Test
  public void testFindMostRecentWorkedShiftEndDate() {
    var company = dbUtil.createCompany();
    var user = dbUtil.createUser();
    var employee = dbUtil.createEmployee(company, user);
    var latestWorkedShiftNonEditableDate = LocalDate.of(2023, 2, 1);

    createWorkedShift(company.getId(), employee.getId(), latestWorkedShiftNonEditableDate, false);
    createWorkedShift(
        company.getId(), employee.getId(), latestWorkedShiftNonEditableDate.minusDays(5), false);
    createWorkedShift(
        company.getId(), employee.getId(), latestWorkedShiftNonEditableDate.plusDays(3), true);

    var result =
        workedShiftLookupService.findMostRecentWorkedShiftEndDate(Set.of(employee.getId()));

    assertThat(result).isEqualTo(latestWorkedShiftNonEditableDate);
  }

  @Test
  public void testFindUnassignedShifts() {
    var company = dbUtil.createCompany();
    var user = dbUtil.createUser();
    var employee = dbUtil.createEmployee(company, user);
    var date = LocalDate.of(2023, 9, 1);
    var adhocPayPeriod = dbUtil.createAdHocPayPeriod(company.getId());
    var payment =
        dbUtil.createPayment(
            adhocPayPeriod, PaymentStatus.CALCULATED, Money.valueOf("100.00"), employee.getId());

    var unassignedShift1 = createWorkedShift(company.getId(), employee.getId(), date, true);
    var unassignedShift2 =
        createWorkedShift(company.getId(), employee.getId(), date.minusDays(5), false);
    createWorkedShift(company.getId(), employee.getId(), date.plusDays(3), true, payment.getId());

    var result =
        workedShiftLookupService.findAll(
            new WorkedShiftLookup().setAssignedPayment(BooleanApiParam.EXCLUDE_ALL));

    assertThat(result)
        .hasSize(2)
        .extracting(WorkedShift::getId)
        .containsExactlyInAnyOrder(unassignedShift1.getId(), unassignedShift2.getId());
  }

  private WorkedShift createWorkedShift(
      Long companyId, Long employeeId, LocalDate date, boolean editable) {
    return createWorkedShift(companyId, employeeId, date, editable, null);
  }

  private WorkedShift createWorkedShift(
      Long companyId, Long employeeId, LocalDate date, boolean editable, Long paymentId) {
    var workedShift =
        new WorkedShift()
            .setPaymentId(paymentId)
            .setCompanyId(companyId)
            .setEmployeeId(employeeId)
            .setEditable(editable)
            .setWorkLocationTimezone(DateUtil.MOUNTAIN_ZONE_ID)
            .setStartPunch(
                new WorkedShiftPunch()
                    .setPunchAt(
                        ZonedDateTime.of(
                            LocalDateTime.of(
                                date.getYear(), date.getMonth(), date.getDayOfMonth(), 8, 0),
                            DateUtil.MOUNTAIN_ZONE_ID)))
            .setEndPunch(
                new WorkedShiftPunch()
                    .setPunchAt(
                        ZonedDateTime.of(
                            LocalDateTime.of(
                                date.getYear(), date.getMonth(), date.getDayOfMonth(), 12, 0),
                            DateUtil.MOUNTAIN_ZONE_ID)));

    workedShiftRepository.saveAndRefresh(workedShift);
    return workedShift;
  }
}
