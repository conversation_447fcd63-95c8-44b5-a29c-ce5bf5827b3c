package com.everee.api.labor.shiftsegmentconfig;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;

import com.everee.api.company.DetailedCompany;
import com.everee.api.labor.shiftsegmentconfig.entity.ShiftSegmentConfig;
import com.everee.api.labor.shiftsegmentconfig.entity.ShiftSegmentType;
import com.everee.api.labor.shiftsegmentconfig.param.ShiftSegmentConfigParamsForCreate;
import com.everee.api.labor.shiftsegmentconfig.param.ShiftSegmentConfigParamsForUpdate;
import com.everee.api.util.icon.IconIdentifier;
import java.time.Duration;
import lombok.NonNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ShiftSegmentConfigServiceTests {
  private static final Long companyId = 13L;

  private DetailedCompany company;

  @Mock private ShiftSegmentConfigRepository configRepository;

  @InjectMocks private ShiftSegmentConfigService configService;

  @BeforeEach
  public void init() {
    company = new DetailedCompany();
    company.setId(companyId);

    lenient()
        .when(configRepository.save(any(ShiftSegmentConfig.class)))
        .then(args -> args.getArgument(0));
  }

  @Test
  public void testCreateSegmentConfig() {
    // ===== given ===== //
    var createParams = buildCreateParams();

    // ===== when ===== //
    var response = configService.createSegmentConfig(company, createParams);

    // ===== then ===== //
    assertThat(response)
        .as("Created config matches expected")
        .isEqualToComparingFieldByField(mockConfig(createParams));

    verify(configRepository).save(eq(mockConfig(createParams)));
  }

  @Test
  public void testUpdateSegmentConfig() {
    // ===== given ===== //
    var existingConfig = mockConfig(buildCreateParams());
    existingConfig.setId(7L);
    var updateParams = buildUpdateParams();

    // ===== when ===== //
    var response = configService.updateSegmentConfig(existingConfig, updateParams);

    // ===== then ===== //
    assertThat(response)
        .as("Updated config matches expected")
        .isEqualToComparingFieldByField(mockConfig(existingConfig, updateParams));
  }

  @Test
  public void testDeleteOrArchiveSegmentConfig() {
    // ===== given ===== //
    var existingConfig = mockConfig(buildCreateParams());

    // ===== when ===== //
    configService.deleteOrArchiveSegmentConfig(existingConfig);

    // ===== then ===== //
    verify(configRepository).delete(eq(existingConfig));
  }

  private ShiftSegmentConfig mockConfig(@NonNull ShiftSegmentConfigParamsForCreate params) {
    var config = new ShiftSegmentConfig();
    config.setCompanyId(companyId);
    config.setTitle(params.getTitle());
    config.setSegmentType(params.getSegmentType());
    config.setIcon(params.getIcon());
    config.setExpectedDuration(params.getExpectedDuration());
    config.setShiftPortionRequiresBreak(params.getExpectedPriorSegmentDuration());
    config.setPayable(params.getPayable());

    return config;
  }

  private ShiftSegmentConfig mockConfig(
      @NonNull ShiftSegmentConfig existingConfig,
      @NonNull ShiftSegmentConfigParamsForUpdate params) {
    existingConfig.setTitle(params.getTitle());
    existingConfig.setIcon(params.getIcon());

    return existingConfig;
  }

  private ShiftSegmentConfigParamsForCreate buildCreateParams() {
    var params = new ShiftSegmentConfigParamsForCreate();
    params.setTitle("a break config");
    params.setSegmentType(ShiftSegmentType.BREAK);
    params.setIcon(new IconIdentifier("meal", "#0F3B77"));
    params.setExpectedDuration(Duration.ofMinutes(15));
    params.setExpectedPriorSegmentDuration(Duration.ofHours(2));
    params.setPayable(true);

    return params;
  }

  private ShiftSegmentConfigParamsForUpdate buildUpdateParams() {
    var params = new ShiftSegmentConfigParamsForUpdate();
    params.setTitle("an updated break config");
    params.setIcon(new IconIdentifier("rocket", "#3B770F"));

    return params;
  }
}
