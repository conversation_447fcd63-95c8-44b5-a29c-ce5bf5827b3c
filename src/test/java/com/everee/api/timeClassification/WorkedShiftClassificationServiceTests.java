package com.everee.api.timeClassification;

import static com.everee.api.timeClassification.intervals.ClassificationType.*;
import static com.everee.api.timeClassification.intervals.ClassificationType.OVERTIME;
import static com.everee.api.timeClassification.intervals.ClassificationType.REGULAR_TIME;
import static com.everee.api.timeClassification.rules.RuleCriteria.RuleCriteriaScope.WEEK_HOURS;
import static com.everee.api.util.DateUtil.MOUNTAIN_ZONE_ID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.labor.timesheet.WorkerTimesheetData;
import com.everee.api.labor.workedshift.entity.WorkedShift;
import com.everee.api.labor.workedshift.entity.WorkedShiftPunch;
import com.everee.api.time.ZonedWorkweeks;
import com.everee.api.timeClassification.intervals.ClassifiedInterval;
import com.everee.api.timeClassification.rules.ClassificationRule;
import com.everee.api.timeClassification.rules.RuleCriteria;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorked;
import com.everee.api.timeClassification.timeWorked.ClassifiedTimeWorkedDurations;
import com.everee.api.timeIntervals.ZonedInterval;
import com.everee.api.worker.time.WorkerClassificationRuleService;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Import;

@SpringBootTest(classes = {MessageSource.class, MessageSourceHolder.class})
@Import(MessageSourceAutoConfiguration.class)
public class WorkedShiftClassificationServiceTests {
  private static final Long companyId = 10L;
  private static DetailedEmployee employee;

  @Mock private WorkerClassificationRuleService workerClassificationRuleService;

  @InjectMocks private WorkedShiftClassificationService shiftClassificationService;

  @BeforeEach
  public void init() {
    employee = new DetailedEmployee().setCompanyId(companyId);

    given(workerClassificationRuleService.getRuleGenerator(any(DetailedEmployee.class)))
        .willReturn(mockRuleGenerator());
  }

  @Test
  public void testClassifyWorkedShiftsAndPersistEditableChanges_success() {
    // === ASSEMBLE === //
    var affectedWorkweeks = makeAffectedWorkweeks();
    var affectedShifts = makeAffectedShifts();
    var timesheetData = new WorkerTimesheetData(employee, affectedWorkweeks, affectedShifts);

    // === ACT === //
    shiftClassificationService.classifyWorkedShiftsAndPersistEditableChanges(timesheetData);

    // === ASSERT === //
    validateAffectedShifts(affectedShifts);
  }

  @Test
  public void testCalculateClassifiedIntervals() {
    // === ASSEMBLE === //
    var affectedWorkweeks = makeAffectedWorkweeks();
    var affectedShifts = makeAffectedShifts();
    var timesheetData = new WorkerTimesheetData(employee, affectedWorkweeks, affectedShifts);

    // === ACT === //
    var response = shiftClassificationService.calculateClassifiedIntervals(timesheetData);

    // === ASSERT === //
    assertThat(response).hasSize(7);
  }

  @Test
  public void testEvaluateTimesheetChanges() {
    // === ASSEMBLE === //
    var affectedWorkweeks = makeAffectedWorkweeks();
    var affectedShifts = makeAffectedShifts();
    var timesheetData = new WorkerTimesheetData(employee, affectedWorkweeks, affectedShifts);
    var classifiedIntervals =
        affectedShifts.stream()
            .map(WorkedShift::getClassifiedTimeWorked)
            .flatMap(Collection::stream)
            .map(this::convert)
            .collect(Collectors.toList());
    affectedShifts.stream()
        .filter(WorkedShift::isEditable)
        .findFirst()
        .ifPresent(s -> s.getClassifiedTimeWorked().forEach(c -> c.setClassifiedType(OVERTIME)));
    affectedShifts.stream()
        .filter(Predicate.not(WorkedShift::isEditable))
        .findFirst()
        .ifPresent(s -> s.getClassifiedTimeWorked().forEach(c -> c.setClassifiedType(OVERTIME)));

    // === ACT === //
    var response =
        WorkedShiftClassificationService.evaluateTimesheetChanges(
            classifiedIntervals, timesheetData);

    // === ASSERT === //
    assertThat(response.getEditableShiftsWithChanges()).hasSize(1);
    assertThat(response.getFinalizedShiftsWithChanges()).hasSize(1);
  }

  @Test
  public void testReplaceShiftClassification() {
    // === ASSEMBLE === //
    var affectedWorkweeks = makeAffectedWorkweeks();
    var timesheetData = new WorkerTimesheetData(employee, affectedWorkweeks, null);

    var shiftStartAt = LocalDateTime.parse("2021-04-03T20:00:00").atZone(MOUNTAIN_ZONE_ID);
    var shiftEndAt = LocalDateTime.parse("2021-04-04T08:00:00").atZone(MOUNTAIN_ZONE_ID);
    var shift = new WorkedShift();
    shift.setId(123L);
    shift.setWorkLocationTimezone(MOUNTAIN_ZONE_ID);
    shift.setStartPunch(new WorkedShiftPunch().setPunchAt(shiftStartAt));
    shift.setEndPunch(new WorkedShiftPunch().setPunchAt(shiftEndAt));
    var overtimeStartAt = LocalDateTime.parse("2021-04-04T00:00:00").atZone(MOUNTAIN_ZONE_ID);
    shift
        .getClassifiedTimeWorked()
        .add(
            new ClassifiedTimeWorked()
                .setClassifiedType(DOUBLE_TIME)
                .setShiftId(123L)
                .setStartAtInclusive(shiftStartAt)
                .setEndAtExclusive(overtimeStartAt));
    shift
        .getClassifiedTimeWorked()
        .add(
            new ClassifiedTimeWorked()
                .setClassifiedType(REGULAR_TIME)
                .setShiftId(123L)
                .setStartAtInclusive(overtimeStartAt)
                .setEndAtExclusive(shiftEndAt));

    var classifiedIntervals = new ArrayList<ClassifiedInterval>();
    classifiedIntervals.add(
        new ClassifiedInterval(
            new ZonedInterval(
                overtimeStartAt.toEpochSecond(), shiftEndAt.toEpochSecond(), MOUNTAIN_ZONE_ID),
            OVERTIME));

    // === ASSERT === //
    var beforeDurations =
        ClassifiedTimeWorkedDurations.fromTimeWorked(shift.getClassifiedTimeWorked());
    assertThat(beforeDurations.getRegularTimeWorked()).isEqualTo(Duration.ofHours(8));
    assertThat(beforeDurations.getOvertimeWorked()).isEqualTo(Duration.ZERO);
    assertThat(beforeDurations.getDoubleTimeWorked()).isEqualTo(Duration.ofHours(4));

    // === ACT === //
    var consumer =
        WorkedShiftClassificationService.replaceShiftClassification(
            classifiedIntervals, timesheetData);
    consumer.accept(shift);

    // === ASSERT === //
    var afterDurations =
        ClassifiedTimeWorkedDurations.fromTimeWorked(shift.getClassifiedTimeWorked());
    assertThat(afterDurations.getRegularTimeWorked()).isEqualTo(Duration.ZERO);
    assertThat(afterDurations.getOvertimeWorked()).isEqualTo(Duration.ofHours(8));
    assertThat(afterDurations.getDoubleTimeWorked()).isEqualTo(Duration.ofHours(4));
  }

  private ZonedWorkweeks makeAffectedWorkweeks() {
    var weeks = new ArrayList<ZonedInterval>();
    weeks.add(
        new ZonedInterval(
            LocalDateTime.parse("2021-04-04T00:00:00").atZone(MOUNTAIN_ZONE_ID).toEpochSecond(),
            LocalDateTime.parse("2021-04-11T00:00:00").atZone(MOUNTAIN_ZONE_ID).toEpochSecond(),
            MOUNTAIN_ZONE_ID));
    weeks.add(
        new ZonedInterval(
            LocalDateTime.parse("2021-04-11T00:00:00").atZone(MOUNTAIN_ZONE_ID).toEpochSecond(),
            LocalDateTime.parse("2021-04-18T00:00:00").atZone(MOUNTAIN_ZONE_ID).toEpochSecond(),
            MOUNTAIN_ZONE_ID));

    return new ZonedWorkweeks(weeks);
  }

  private Set<WorkedShift> makeAffectedShifts() {
    var affectedShifts = new HashSet<WorkedShift>();

    // === last week finalized shifts === //
    var shiftDuration = Duration.ofHours(10);
    var shiftStartAt = LocalDateTime.parse("2021-04-06T08:00:00").atZone(MOUNTAIN_ZONE_ID);
    affectedShifts.add(
        makeWorkedShift(101L, shiftStartAt, shiftStartAt.plus(shiftDuration), false));
    shiftStartAt = shiftStartAt.plusDays(1);
    affectedShifts.add(
        makeWorkedShift(102L, shiftStartAt, shiftStartAt.plus(shiftDuration), false));
    shiftStartAt = shiftStartAt.plusDays(1);
    affectedShifts.add(
        makeWorkedShift(103L, shiftStartAt, shiftStartAt.plus(shiftDuration), false));
    shiftStartAt = shiftStartAt.plusDays(1);
    affectedShifts.add(
        makeWorkedShift(104L, shiftStartAt, shiftStartAt.plus(shiftDuration), false));

    // === this week editable shifts === //
    shiftStartAt = LocalDateTime.parse("2021-04-13T08:00:00").atZone(MOUNTAIN_ZONE_ID);
    affectedShifts.add(makeWorkedShift(105L, shiftStartAt, shiftStartAt.plus(shiftDuration), true));
    shiftStartAt = shiftStartAt.plusDays(1);
    affectedShifts.add(makeWorkedShift(106L, shiftStartAt, shiftStartAt.plus(shiftDuration), true));
    shiftStartAt = shiftStartAt.plusDays(1);
    affectedShifts.add(makeWorkedShift(107L, shiftStartAt, shiftStartAt.plus(shiftDuration), true));

    return affectedShifts;
  }

  private WorkedShift makeWorkedShift(
      Long shiftId, ZonedDateTime shiftStartAt, ZonedDateTime shiftEndAt, boolean editable) {
    var shift = new WorkedShift();
    shift.setWorkLocationTimezone(MOUNTAIN_ZONE_ID);
    shift.setStartPunch(new WorkedShiftPunch().setPunchAt(shiftStartAt));
    shift.setEndPunch(new WorkedShiftPunch().setPunchAt(shiftEndAt));
    shift.setEditable(editable);

    var regularTime =
        new ClassifiedTimeWorked()
            .setClassifiedType(REGULAR_TIME)
            .setShiftId(shiftId)
            .setStartAtInclusive(shiftStartAt)
            .setEndAtExclusive(shiftEndAt);
    shift.getClassifiedTimeWorked().add(regularTime);

    return shift;
  }

  private ClassifiedInterval convert(ClassifiedTimeWorked timeWorked) {
    return new ClassifiedInterval(timeWorked.getZonedInterval(), timeWorked.getClassifiedType());
  }

  private Function<ZonedDateTime, List<ClassificationRule>> mockRuleGenerator() {
    return (ZonedDateTime timestamp) -> {
      Integer weekOvertimeThreshold = 40;
      List<ClassificationRule> rules = new ArrayList<>();

      var criteria = Set.of(new RuleCriteria(WEEK_HOURS, 0));
      rules.add(new ClassificationRule(criteria, REGULAR_TIME));

      criteria = Set.of(new RuleCriteria(WEEK_HOURS, weekOvertimeThreshold));
      rules.add(new ClassificationRule(criteria, OVERTIME));

      return rules;
    };
  }

  private void validateAffectedShifts(Collection<WorkedShift> shifts) {
    shifts.forEach(
        shift -> {
          var shiftDuration = shift.getDuration();
          var classifiedDurations =
              ClassifiedTimeWorkedDurations.fromTimeWorked(shift.getClassifiedTimeWorked());

          assertThat(shiftDuration)
              .as("All shifts with non-zero duration should be fully classified")
              .isEqualTo(classifiedDurations.getTotalTimeWorked());
        });
  }
}
