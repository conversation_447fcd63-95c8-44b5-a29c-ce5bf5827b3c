package com.everee.api.payrollcalendar.processors;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.payrollcalendar.processors.context.EditPayPeriodContext;
import com.everee.api.payrollcalendar.processors.validate.PayDateNotOnBankClosure;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class PayDateNotOnBankClosureTest {
    @InjectMocks
    private PayDateNotOnBankClosure sut;

    @Test
    public void GivenEditingPayPeriod_AndPayDayNotChanged_ThenNoExceptionThrown(){
        // Arrange
        var input = new EditPayPeriodContext()
                .setHolidays(Set.of(LocalDate.now()));

        // assert
        assertDoesNotThrow(() -> sut.process(input));
    }

    @ParameterizedTest
    @MethodSource("getCurrentWeekWeekdays")
    public void GivenEditingPayDate_AndPayDayWeekday_AndPayDateNotHoliday_ThenNoExceptionThrown(LocalDate date){
        // Arrange
        LocalDate holiday = LocalDate.now().plusMonths(1); // Saturday

        var input = new EditPayPeriodContext()
                .setHolidays(Set.of(holiday))
                .setPayDate(date);

        // assert
        assertDoesNotThrow(() -> sut.process(input));
    }

    @ParameterizedTest
    @MethodSource("getCurrentWeekWeekends")
    public void GivenEditingPayDate_AndPayDayWeekend_ThenExceptionThrown(LocalDate date){
        // Arrange
        LocalDate holiday = LocalDate.now().plusMonths(1); // Saturday

        var input = new EditPayPeriodContext()
                .setHolidays(Set.of(holiday))
                .setPayDate(date);

        // assert
        assertThrows(InvalidRequestException.class, () -> sut.process(input));
    }

    @Test
    public void GivenEditingPayDate_AndPayDayHoliday_ThenExceptionThrown(){
        // Arrange
        LocalDate holiday = LocalDate.now().plusMonths(1); // Saturday

        var input = new EditPayPeriodContext()
                .setHolidays(Set.of(holiday))
                .setPayDate(holiday);

        // assert
        assertThrows(InvalidRequestException.class, () -> sut.process(input));
    }

    public static List<LocalDate> getCurrentWeekWeekends() {
        LocalDate today = LocalDate.now();

        return List.of(today.with(DayOfWeek.SATURDAY), today.with(DayOfWeek.SUNDAY));
    }

    public static List<LocalDate> getCurrentWeekWeekdays() {
        List<LocalDate> weekdays = new ArrayList<>();
        LocalDate today = LocalDate.now();
        LocalDate monday = today.with(DayOfWeek.MONDAY);

        for (int i = 0; i < 5; i++) {
            weekdays.add(monday.plusDays(i));
        }

        return weekdays;
    }


}
