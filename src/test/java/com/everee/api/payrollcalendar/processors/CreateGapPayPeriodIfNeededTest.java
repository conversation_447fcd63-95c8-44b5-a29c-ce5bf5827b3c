package com.everee.api.payrollcalendar.processors;

import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.payperiod.*;
import com.everee.api.payperiod.changeschedule.PreferenceChangeScheduleUtils;
import com.everee.api.payrollcalendar.models.PayrollCalendar;
import com.everee.api.payrollcalendar.processors.context.ArchiveCalendarContext;
import com.everee.api.payrollcalendar.processors.persist.CreateGapPayPeriodIfNeeded;
import com.everee.api.payrun.PayRunRepository;
import com.everee.api.time.CompanyLocalTimeService;
import com.everee.api.user.UserService;
import com.everee.api.util.annotation.MockLocalDateNow;
import com.everee.api.util.extension.MockLocalDateExtension;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ExtendWith(MockLocalDateExtension.class)
class CreateGapPayPeriodIfNeededTest {

    @Mock
    private PayPeriodRepository payPeriodRepository;
    @Mock
    private CompanyService companyService;
    @Mock
    private PayPeriodPreferenceRepository payPeriodPreferenceRepository;
    @Mock
    private PayRunRepository payRunRepository;

    @InjectMocks
    private CreateGapPayPeriodIfNeeded createGapPayPeriodIfNeeded;

    private MockedStatic<UserService> userServiceMockedStatic;
    private MockedStatic<CompanyLocalTimeService> companyLocalTimeServiceMockedStatic;

    @BeforeEach
    void setUp() {
        userServiceMockedStatic = mockStatic(UserService.class);
        companyLocalTimeServiceMockedStatic = mockStatic(CompanyLocalTimeService.class);
        userServiceMockedStatic.when(UserService::getAuthenticatedUserId).thenReturn(99L);
    }

    @AfterEach
    void tearDown() {
        userServiceMockedStatic.close();
        companyLocalTimeServiceMockedStatic.close();
    }

    @Test
    @MockLocalDateNow("2025-04-28")
    void GivenArchivingCalendar_WhenGapExists_ThenCustomPayPeriodAndPreferenceShouldBeCreated() {
        // Arrange
        var companyId = 1L;
        var userId = 99L;
        var today = LocalDate.now();
        var latestEndDate = today.minusDays(2);
        var nextPrefStart = LocalDate.of(2025, 5, 4);
        var calendar = new PayrollCalendar().setCompanyId(companyId).setCreatedByUserId(userId);
        var context = new ArchiveCalendarContext().setCalendar(calendar);
        var defaultPreference = new PayPeriodPreference()
                .setCompanyId(companyId)
                .setStartDate(nextPrefStart)
                .setEndDate(PayPeriodPreference.MAX_DATE)
                .setPayPeriodType(PayPeriodType.WEEKLY);

        mockCompanyWithDate(companyId, today, nextPrefStart);
        doReturn(List.of(new PayPeriod().setEndDate(latestEndDate)))
                .when(payPeriodRepository)
                .findLatestPayrollCalendarPayPeriod(companyId, PageRequest.of(0, 1));

        when(payPeriodPreferenceRepository.findByCompanyIdAndEmployeeIdIsNull(companyId)).thenReturn(List.of(defaultPreference));

        when(payPeriodPreferenceRepository.save(any())).thenAnswer(inv -> {
            PayPeriodPreference pref = inv.getArgument(0);
            pref.setId(1L);
            return pref;
        });

        when(payPeriodRepository.save(any())).thenAnswer(inv -> {
            PayPeriod period = inv.getArgument(0);
            period.setId(2L);
            return period;
        });

        // Act
        createGapPayPeriodIfNeeded.process(context);

        // Assert
        assertThat(calendar.getPayPeriods()).hasSize(1);
        PayPeriod gapPeriod = calendar.getPayPeriods().get(0);
        assertThat(gapPeriod.getStartDate()).isEqualTo(latestEndDate.plusDays(1));
        assertThat(gapPeriod.getEndDate()).isEqualTo(nextPrefStart.minusDays(1));
        assertThat(gapPeriod.getPayPeriodType().name()).isEqualTo("CUSTOM");
        assertThat(gapPeriod.getCreatedByUserId()).isEqualTo(userId);
    }

    @Test
    @MockLocalDateNow("2025-04-28")
    void GivenArchivingCalendar_WhenNoGapNeeded_ThenNothingShouldBeSaved() {
        // Arrange
        var companyId = 2L;
        var calendar = new PayrollCalendar().setCompanyId(companyId);
        var context = new ArchiveCalendarContext().setCalendar(calendar);

        mockCompanyWithTodayOnly(companyId);

        // Act
        createGapPayPeriodIfNeeded.process(context);

        // Assert
        verify(payPeriodRepository, never()).save(any());
        verify(payPeriodPreferenceRepository, never()).save(any());
        assertThat(calendar.getPayPeriods()).isEmpty();
    }

    @Test
    @MockLocalDateNow("2025-04-28")
    void GivenNoPreviousPayPeriod_WhenFindLatestReturnsEmpty_ThenDoNothing() {
        var companyId = 3L;
        var calendar = new PayrollCalendar().setCompanyId(companyId);
        var context = new ArchiveCalendarContext().setCalendar(calendar);

        mockCompanyWithTodayOnly(companyId);
        doReturn(List.of())
                .when(payPeriodRepository)
                .findLatestPayrollCalendarPayPeriod(companyId, PageRequest.of(0, 1));


        createGapPayPeriodIfNeeded.process(context);

        verify(payPeriodRepository, never()).save(any());
        verify(payPeriodPreferenceRepository, never()).save(any());
    }

    @Test
    @MockLocalDateNow("2025-04-28")
    void GivenGapCannotBeCreated_WhenCreateGapReturnsEmpty_ThenDoNothing() {
        var companyId = 4L;
        var today = LocalDate.now();
        var latestEndDate = today.minusDays(2);
        var nextPrefStart = today.plusDays(3);
        var calendar = new PayrollCalendar().setCompanyId(companyId);
        var context = new ArchiveCalendarContext().setCalendar(calendar);
        var defaultPreference = new PayPeriodPreference()
                .setCompanyId(companyId)
                .setStartDate(nextPrefStart)
                .setEndDate(PayPeriodPreference.MAX_DATE)
                .setPayPeriodType(PayPeriodType.WEEKLY);

        when(payPeriodPreferenceRepository.findByCompanyIdAndEmployeeIdIsNull(companyId)).thenReturn(List.of(defaultPreference));

        mockCompanyWithDate(companyId, today, nextPrefStart);
        doReturn(List.of(new PayPeriod().setEndDate(latestEndDate)))
                .when(payPeriodRepository)
                .findLatestPayrollCalendarPayPeriod(companyId, PageRequest.of(0, 1));


        try (MockedStatic<PreferenceChangeScheduleUtils> utils = mockStatic(PreferenceChangeScheduleUtils.class)) {
            utils.when(() -> PreferenceChangeScheduleUtils.createGapSchedule(eq(latestEndDate), eq(nextPrefStart)))
                    .thenReturn(Optional.empty());

            createGapPayPeriodIfNeeded.process(context);
        }

        verify(payPeriodRepository, never()).save(any());
        verify(payPeriodPreferenceRepository, never()).save(any());
    }

    private void mockCompanyWithDate(long companyId, LocalDate today, LocalDate nextPrefStart) {
        DetailedCompany company = new DetailedCompany();
        company.setId(companyId);
        PayPeriodType payPeriodType = mock(PayPeriodType.class);
        company.setActivePayPeriodType(payPeriodType);

        lenient().when(payPeriodType.getNextAvailableStartDate(today)).thenReturn(nextPrefStart);
        when(companyService.getCompany(companyId)).thenReturn(company);
    }

    private void mockCompanyWithTodayOnly(long companyId) {
        DetailedCompany company = new DetailedCompany();
        company.setId(companyId);
        PayPeriodType payPeriodType = mock(PayPeriodType.class);
        company.setActivePayPeriodType(payPeriodType);

        when(companyService.getCompany(companyId)).thenReturn(company);
    }

}
