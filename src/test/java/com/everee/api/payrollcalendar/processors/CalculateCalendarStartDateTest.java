package com.everee.api.payrollcalendar.processors;

import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payrollcalendar.models.PayPeriodDTO;
import com.everee.api.payrollcalendar.models.PayrollCalendarDraftPeriod;
import com.everee.api.payrollcalendar.processors.calculate.CalculateCalendarStartDate;
import com.everee.api.payrollcalendar.processors.context.CreateCalendarContext;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class CalculateCalendarStartDateTest {

    @InjectMocks
    private CalculateCalendarStartDate sut;


    @Test
    public void GivenCalculatingCalendarStartDate_AndNoExistingPayPeriod_ThenGivenStartDateSet() {
        var now = LocalDate.now();
        // Arrange
        var payPeriodDto = new PayPeriodDTO(now, now.plusDays(7));

        var input = new CreateCalendarContext()
                .setDraftPeriods(List.of(new PayrollCalendarDraftPeriod().setCalculatedStartDate(now)))
                .setFirstPayPeriod(payPeriodDto);

        // Act
        sut.process(input);

        // Assert
        assertEquals(now, input.getDraftPeriods().get(0).getAssignedStartDate());
    }

    @Test
    public void GivenCalculatingCalendarStartDate_AfterCurrentPayPeriod_ThenStartDateNextDateSet() {
        var now = LocalDate.now();
        var existingEndDate = now.minusDays(5);
        var expected = existingEndDate.plusDays(1);

        // Arrange
        var payPeriodDto = new PayPeriodDTO(now, now.plusDays(7));

        var input = new CreateCalendarContext()
                .setDraftPeriods(List.of(new PayrollCalendarDraftPeriod().setCalculatedStartDate(now)))
                .setLatestPayPeriod(new PayPeriod().setEndDate(existingEndDate))
                .setFirstPayPeriod(payPeriodDto);

        // Act
        sut.process(input);

        // Assert
        assertEquals(expected, input.getDraftPeriods().get(0).getAssignedStartDate());
    }

    @Test
    public void GivenCalculatingCalendarStartDate_DuringCurrentPayPeriod_WhichHasNoPayments_ThenStartDateCurrentPeriodStartDate() {
        var now = LocalDate.now();
        var existingEndDate = now.plusDays(1);
        var existingStartDate = now.minusDays(6);

        var expected = existingStartDate;

        // Arrange
        var payPeriodDto = new PayPeriodDTO(now, now.minusDays(5));

        var input = new CreateCalendarContext()
                .setDraftPeriods(List.of(new PayrollCalendarDraftPeriod().setCalculatedStartDate(now)))
                .setLatestPayPeriod(new PayPeriod().setStartDate(existingStartDate).setEndDate(existingEndDate))
                .setFirstPayPeriod(payPeriodDto);

        // Act
        sut.process(input);

        // Assert
        assertEquals(expected, input.getDraftPeriods().get(0).getAssignedStartDate());
    }

}
