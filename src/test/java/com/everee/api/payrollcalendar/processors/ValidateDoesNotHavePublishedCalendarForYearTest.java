package com.everee.api.payrollcalendar.processors;

import com.everee.api.payrollcalendar.PayrollCalendarRepository;
import com.everee.api.payrollcalendar.processors.models.CalendarContextBaseImpl;
import com.everee.api.payrollcalendar.processors.validate.ValidateDoesNotHavePublishedCalendarForYear;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ValidateDoesNotHavePublishedCalendarForYearTest {
    private static final long COMPANY_ID = 1L;

    @InjectMocks
    private ValidateDoesNotHavePublishedCalendarForYear sut;

    @Mock
    private PayrollCalendarRepository payrollCalendarRepository;

    @Test
    public void GivenPublishingCalendar_ThenExceptionThrown(){
        // Arrange
        when(payrollCalendarRepository.existsPublishedCalendar(anyLong(), anyInt()))
                .thenReturn(true);

        var input = new CalendarContextBaseImpl()
                .setCompanyId(COMPANY_ID)
                .setCalendarId(0);

        // Assert
        assertThrows(RuntimeException.class, () -> sut.process(input));
    }

    @Test
    public void GivenValidateCalendar_AndNoPublishedCalendarExists_ThenNoExceptionThrown(){
        // Arrange
        when(payrollCalendarRepository.existsPublishedCalendar(anyLong(), anyInt()))
                .thenReturn(false);

        var input = new CalendarContextBaseImpl()
                .setCompanyId(COMPANY_ID)
                .setCalendarId(0);

        assertDoesNotThrow(() -> sut.process(input));
    }
}
