package com.everee.api.timeclock.bulk;

import com.everee.api.employee.Employee;
import com.everee.api.payperiod.*;
import com.everee.api.util.BaseTests;
import com.everee.api.util.DbUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.Duration;
import java.time.LocalDate;
import javax.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

@DataJpaTest
@Transactional
@AutoConfigureEmbeddedDatabase
@ImportAutoConfiguration(DbUtil.class)
public class BulkTimeCardEntryRepositoryIntegrationTests extends BaseTests {
  @Autowired private DbUtil dbUtil;
  @Autowired private PayPeriodPreferenceRepository preferenceRepository;
  @Autowired private PayPeriodRepository payPeriodRepository;
  @Autowired private BulkTimeCardEntryRepository bulkTimeCardEntryRepository;

  @Test
  public void whenFindAll_thenReturnEntries() {

    var company = dbUtil.createCompany();
    final var employee = dbUtil.createEmployee(company);

    var preference1 = mockPreference(company.getId());
    var payPeriod1 = mockPayPeriod(company.getId(), preference1.getId());
    mockTimeWorked(employee, payPeriod1);

    var preference2 = mockPreference(company.getId());
    var payPeriod2 = mockPayPeriod(company.getId(), preference2.getId());
    mockTimeWorked(employee, payPeriod2);

    var found = bulkTimeCardEntryRepository.findAllByEmployeeId(employee.getId());

    assert (found.size() == 2);
    assert (found.get(0).getEmployeeId().equals(employee.getId()));
  }

  private PayPeriodPreference mockPreference(Long companyId) {
    var preference = new PayPeriodPreference();

    preference.setPayPeriodType(PayPeriodType.SEMI_MONTHLY);
    preference.setCompanyId(companyId);
    preference.setStartDate(LocalDate.now().minusMonths(6));

    return preferenceRepository.save(preference);
  }

  private PayPeriod mockPayPeriod(Long companyId, Long preferenceId) {
    var payPeriod = new PayPeriod();

    payPeriod.setCompanyId(companyId);
    payPeriod.setPayPeriodPreferenceId(preferenceId);
    payPeriod.setPayPeriodType(PayPeriodType.SEMI_MONTHLY);
    payPeriod.setStartDate(LocalDate.now().minusWeeks(1));
    payPeriod.setEndDate(LocalDate.now());

    return payPeriodRepository.save(payPeriod);
  }

  private BulkTimeCardEntry mockTimeWorked(Employee employee, PayPeriod payPeriod) {
    var entry = new BulkTimeCardEntry(payPeriod, employee);

    entry.setPayPeriodId(payPeriod.getId());
    entry.setPayPeriod(payPeriod);
    entry.setRegularTimeWorked(Duration.ofHours(40));
    entry.setOvertimeWorked(Duration.ofHours(10));

    return bulkTimeCardEntryRepository.save(entry);
  }
}
