package com.everee.api.payment.PayRun;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.everee.api.earnings.grossearnings.GrossEarning;
import com.everee.api.earnings.grossearnings.GrossEarningRepository;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.Employee;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionAmountDetail;
import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionAmountDetailRepository;
import com.everee.api.payment.Payment;
import com.everee.api.payment.PaymentRepository;
import com.everee.api.payment.PaymentStatus;
import com.everee.api.payment.lookup.PaymentLookupService;
import com.everee.api.payment.paymentdeductionoverride.PaymentDeductionOverride;
import com.everee.api.payment.paymentdeductionoverride.PaymentDeductionOverrideId;
import com.everee.api.payment.paymentdeductionoverride.PaymentDeductionOverrideRepository;
import com.everee.api.payment.payrun.ImportPayRunStatus;
import com.everee.api.payment.payrun.PayRunImportService;
import com.everee.api.payment.payrun.ImportedPayRunStatus;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payperiod.PayPeriodService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PayRunImportServiceTest {
  private static final Long companyId = 246L;
  private static final Long payPeriodId = 4L;

  @Mock private GrossEarningRepository grossEarningRepository;

  @Mock private PaymentRepository paymentRepository;

  @Mock private PaymentDeductionOverrideRepository deductionOverrideRepository;

  @Mock private EmployeeContributionDeductionAmountDetailRepository deductionAmountDetailRepository;

  @Mock private EmployeeService employeeService;

  @Mock private PayPeriodService payPeriodService;

  @Mock private PaymentLookupService paymentLookupService;

  @InjectMocks private PayRunImportService payRunImportService;

  private List<GrossEarning> grossEarnings;
  private List<Payment> payments;
  private List<PaymentDeductionOverride> deductionOverrides;
  private List<EmployeeContributionDeductionAmountDetail> amountDetails;
  private List<String> importIds;

  @BeforeEach
  void setUp() {
    grossEarnings =
        List.of(
            mockGrossEarning("import1", 1L),
            mockGrossEarning("import1", 2L),
            mockGrossEarning("import2", 3L));

    deductionOverrides =
        List.of(
            mockDeductionOverride("import3", 4L),
            mockDeductionOverride("import4", 5L),
            mockDeductionOverride("import5", 6L));

    amountDetails =
        List.of(
            mockAmountDetail("import3", 4L),
            mockAmountDetail("import4", 5L),
            mockAmountDetail("import5", 7L));

    payments =
        List.of(
            mockPayment(1L, PaymentStatus.APPROVED),
            mockPayment(2L, PaymentStatus.APPROVED),
            mockPayment(3L, PaymentStatus.APPROVED),
            mockPayment(4L, PaymentStatus.APPROVED),
            mockPayment(5L, PaymentStatus.APPROVED),
            mockPayment(6L, PaymentStatus.APPROVED),
            mockPayment(7L, PaymentStatus.APPROVED));

    importIds = List.of("import1", "import2", "import3", "import4", "import5");
  }

  @Test
  void testGetPayRunStatusForImports_AllPaid() {
    setupMockRepositories();

    List<ImportPayRunStatus> result = payRunImportService.getPayRunStatusForImports(importIds);

    assertResults(
        result,
        Map.of(
            "import1", ImportedPayRunStatus.PAID,
            "import2", ImportedPayRunStatus.PAID,
            "import3", ImportedPayRunStatus.PAID,
            "import4", ImportedPayRunStatus.PAID,
            "import5", ImportedPayRunStatus.PAID));
  }

  @Test
  void testGetPayRunStatusForImports_PartiallyPaid() {
    payments =
        List.of(
            mockPayment(1L, PaymentStatus.CALCULATED),
            mockPayment(2L, PaymentStatus.APPROVED),
            mockPayment(3L, PaymentStatus.APPROVED),
            mockPayment(4L, PaymentStatus.APPROVED),
            mockPayment(5L, PaymentStatus.CALCULATED),
            mockPayment(6L, PaymentStatus.APPROVED),
            mockPayment(7L, PaymentStatus.CALCULATED));
    setupMockRepositories();

    List<ImportPayRunStatus> result = payRunImportService.getPayRunStatusForImports(importIds);

    assertResults(
        result,
        Map.of(
            "import1", ImportedPayRunStatus.PARTIALLY_PAID,
            "import2", ImportedPayRunStatus.PAID,
            "import3", ImportedPayRunStatus.PAID,
            "import4", ImportedPayRunStatus.PENDING,
            "import5", ImportedPayRunStatus.PARTIALLY_PAID));
  }

  @Test
  void testGetPayRunStatusForImports_Pending_WhenDeletedPaymentsExists(){
      grossEarnings.get(0).setPaymentId(null);
      amountDetails.get(0).setPaymentId(null);
      payments =
              List.of(
                      mockPayment(2L, PaymentStatus.CALCULATED),
                      mockPayment(3L, PaymentStatus.CALCULATED),
                      mockPayment(4L, PaymentStatus.CALCULATED),
                      mockPayment(5L, PaymentStatus.CALCULATED),
                      mockPayment(6L, PaymentStatus.CALCULATED),
                      mockPayment(7L, PaymentStatus.CALCULATED));
      setupMockRepositories();
      when(paymentRepository.findAllById(Set.of(2L, 3L, 4L, 5L, 6L, 7L))).thenReturn(payments);

      List<ImportPayRunStatus> result = payRunImportService.getPayRunStatusForImports(importIds);
      assertResults(
              result,
              Map.of(
                      "import1", ImportedPayRunStatus.PENDING,
                      "import2", ImportedPayRunStatus.PENDING,
                      "import3", ImportedPayRunStatus.PENDING,
                      "import4", ImportedPayRunStatus.PENDING,
                      "import5", ImportedPayRunStatus.PENDING));
  }

  @Test
  void testGetPayRunStatusForImports_Pending() {
    payments =
        List.of(
            mockPayment(1L, PaymentStatus.CALCULATED),
            mockPayment(2L, PaymentStatus.CALCULATED),
            mockPayment(3L, PaymentStatus.CALCULATED),
            mockPayment(4L, PaymentStatus.CALCULATED),
            mockPayment(5L, PaymentStatus.CALCULATED),
            mockPayment(6L, PaymentStatus.CALCULATED),
            mockPayment(7L, PaymentStatus.CALCULATED));
    setupMockRepositories();

    List<ImportPayRunStatus> result = payRunImportService.getPayRunStatusForImports(importIds);

    assertResults(
        result,
        Map.of(
            "import1", ImportedPayRunStatus.PENDING,
            "import2", ImportedPayRunStatus.PENDING,
            "import3", ImportedPayRunStatus.PENDING,
            "import4", ImportedPayRunStatus.PENDING,
            "import5", ImportedPayRunStatus.PENDING));
  }

  @Test
  void testGetPayRunStatusForImports_NoPayments() {
    when(grossEarningRepository.findEarningsByImportIds(importIds)).thenReturn(List.of());
    when(deductionOverrideRepository.findDeductionOverridesByImportIds(importIds))
        .thenReturn(List.of());
    when(deductionAmountDetailRepository.findDeductionAmountDetailsByImportIds(importIds))
        .thenReturn(List.of());
    when(paymentRepository.findAllById(Set.of())).thenReturn(List.of());

    List<ImportPayRunStatus> result = payRunImportService.getPayRunStatusForImports(importIds);

    assertResults(
        result,
        Map.of(
            "import1", ImportedPayRunStatus.PENDING,
            "import2", ImportedPayRunStatus.PENDING,
            "import3", ImportedPayRunStatus.PENDING,
            "import4", ImportedPayRunStatus.PENDING,
            "import5", ImportedPayRunStatus.PENDING));
  }

  @Test
  void getPayRunStatusForImports_whenImportHasAnErroredPayment_showsTheNeedsAttentionPayRunStatus() {
    /*
      Assume the following scenario:
        "import1" -> {1, 2},
        "import2" -> {3, 4, 5},
        "import3" -> {6, 7},
        "import4" -> {8, 9},
        "import5" -> {} // empty
    */

    // Arrange
    grossEarnings =
      List.of(
        mockGrossEarning("import1", 1L),
        mockGrossEarning("import1", 2L),
        mockGrossEarning("import2", 3L));

    deductionOverrides =
      List.of(
        mockDeductionOverride("import2", 4L),
        mockDeductionOverride("import2", 5L),
        mockDeductionOverride("import3", 6L));

    amountDetails =
      List.of(
        mockAmountDetail("import3", 7L),
        mockAmountDetail("import4", 8L),
        mockAmountDetail("import4", 9L)
      );

    payments =
      List.of(
        /*
          - IF a pay run has the payments:
            ----------------------------------
            Payment id    status
            ----------------------------------
            1L            ERRORED
            2L            PAID  (finalized)
            ----------------------------------
            THEN show PayRunStatus.NEEDS_ATTENTION
         */
        mockPayment(1L, PaymentStatus.ERRORED),
        mockPayment(2L, PaymentStatus.PAID),

        /*
          - IF a pay run has the payments:
            ----------------------------------
            Payment id    status
            ----------------------------------
            3L            ERRORED
            4L            APPROVED    (finalized)
            5L            CALCULATED  (not finalized)
            ----------------------------------
            THEN show PayRunStatus.NEEDS_ATTENTION
         */
        mockPayment(3L, PaymentStatus.ERRORED),
        mockPayment(4L, PaymentStatus.APPROVED),
        mockPayment(5L, PaymentStatus.CALCULATED),

        /*
          - IF a pay run has the payments:
            ----------------------------------
            Payment id    status
            ----------------------------------
            6L            CALCULATED  (not finalized)
            7L            ERRORED
            ----------------------------------
            THEN show PayRunStatus.NEEDS_ATTENTION
         */
        mockPayment(6L, PaymentStatus.CALCULATED),
        mockPayment(7L, PaymentStatus.ERRORED),

        /*
          - IF a pay run has the payments:
            ----------------------------------
            Payment id    status
            ----------------------------------
            8L            CALCULATED  (not finalized)
            9L            PAID        (finalized)
            ----------------------------------
            THEN show PayRunStatus.PARTIALLY_PAID
        */
        mockPayment(8L, PaymentStatus.CALCULATED),
        mockPayment(9L, PaymentStatus.PAID)
      );

    setupMockRepositories();

    // Act
    List<ImportPayRunStatus> result = payRunImportService.getPayRunStatusForImports(importIds);

    // Assert
    assertResults(
      result,
      Map.of(
        "import1", ImportedPayRunStatus.NEEDS_ATTENTION,
        "import2", ImportedPayRunStatus.NEEDS_ATTENTION,
        "import3", ImportedPayRunStatus.NEEDS_ATTENTION,
        "import4", ImportedPayRunStatus.PARTIALLY_PAID,
        "import5", ImportedPayRunStatus.PENDING) // because it has no payments
    );
  }

  private Set<Long> getEmployeeIds(List<DetailedEmployee> employees) {
    return employees
      .stream()
      .map(Employee::getId)
      .collect(Collectors.toSet());
  }

  private Set<String> getWorkerIds(List<DetailedEmployee> employees) {
    return employees
      .stream()
      .map(Employee::getWorkerId)
      .collect(Collectors.toSet());
  }

  @ParameterizedTest
  @CsvSource({
    "STARTED,   ERRORED,  CALCULATED, NEEDS_ATTENTION",
    "SCHEDULED, ERRORED,  SCHEDULED,  NEEDS_ATTENTION",
    "ERRORED,   ERRORED,  ERRORED,    NEEDS_ATTENTION",
    "STARTED,   PAID,     CALCULATED, PARTIALLY_PAID",
    "FUNDED,    STARTED,  CALCULATED, PARTIALLY_PAID",
    "STARTED,   ERRORED,  APPROVED,   NEEDS_ATTENTION",
    "PAID,      PAID,     PAID,       PAID",
    "FUNDED,    APPROVED, PAID,       PAID",
    "FUNDED,    FUNDED,   FUNDED,     PAID",
    "CALCULATED,    STARTED,   SCHEDULED,     PENDING",
  })
  void getPayRunStatusForWorkerIds_whenEmployeesHavePaymentsInPayPeriod_returnsCorrectPayRunStatus(
    PaymentStatus paymentStatus1,
    PaymentStatus paymentStatus2,
    PaymentStatus paymentStatus3,
    ImportedPayRunStatus expectedImportedPayRunStatus
  ) {
    // ARRANGE
    var payPeriod = mockPayPeriod(payPeriodId);

    doReturn(payPeriod)
      .when(payPeriodService)
      .getPayPeriod(eq(payPeriodId));

    var employee1 = mockEmployee(26L);
    var employee2 = mockEmployee(36L);
    var employee3 = mockEmployee(46L);

    var employees = List.of(employee1, employee2, employee3);
    var employeeIds = getEmployeeIds(employees);
    var workerIds = getWorkerIds(employees);

    doReturn(employees)
      .when(employeeService)
      .findByCompanyIdAndWorkerIdIn(
        eq(companyId),
        argThat(targetWorkerIds -> targetWorkerIds.containsAll(workerIds))
      );

    var payment1 = mockPayment(501L, paymentStatus1);
    var payment2 = mockPayment(502L, paymentStatus2);
    var payment3 = mockPayment(503L, paymentStatus3);

    var payments = List.of(payment1, payment2, payment3);

    doReturn(payments)
      .when(paymentLookupService)
      .findAll(
        argThat(
          lookup ->
            lookup.getPayPeriodIds().size() == 1 &&
            lookup.getPayPeriodIds().contains(payPeriodId) &&
            lookup.getEmployeeIds().size() == employees.size() &&
            lookup.getEmployeeIds().containsAll(employeeIds)

        )
      );

    // ACT
    var resultStatus =
      payRunImportService.getPayRunStatusForWorkerIds(payPeriodId, workerIds);

    // ASSERT
    assertEquals(resultStatus, expectedImportedPayRunStatus);

    verify(payPeriodService, times(1)).getPayPeriod(eq(payPeriodId));
    verifyNoMoreInteractions(payPeriodService);

    verify(employeeService, times(1))
      .findByCompanyIdAndWorkerIdIn(
        eq(companyId),
        argThat(targetWorkerIds -> targetWorkerIds.containsAll(workerIds))
      );
    verifyNoMoreInteractions(employeeService);

    verify(paymentLookupService, times(1)).findAll(
      argThat(
        lookup ->
          lookup.getPayPeriodIds().size() == 1 &&
          lookup.getPayPeriodIds().contains(payPeriodId) &&
          lookup.getEmployeeIds().size() == employees.size() &&
          lookup.getEmployeeIds().containsAll(employeeIds)
      )
    );
    verifyNoMoreInteractions(paymentLookupService);
  }

  private void setupMockRepositories() {
    when(grossEarningRepository.findEarningsByImportIds(importIds)).thenReturn(grossEarnings);
    when(deductionOverrideRepository.findDeductionOverridesByImportIds(importIds))
        .thenReturn(deductionOverrides);
    when(deductionAmountDetailRepository.findDeductionAmountDetailsByImportIds(importIds))
        .thenReturn(amountDetails);
    lenient().when(paymentRepository.findAllById(
      payments.stream().map(Payment::getId).collect(Collectors.toSet())
    )).thenReturn(payments);
  }

  private void assertResults(
      List<ImportPayRunStatus> result, Map<String, ImportedPayRunStatus> expectedStatusMap) {
    assertEquals(expectedStatusMap.size(), result.size());
    Map<String, ImportedPayRunStatus> actualStatusMap =
        result.stream()
            .collect(
                Collectors.toMap(
                    ImportPayRunStatus::getImportId, ImportPayRunStatus::getPayRunStatus));
    assertEquals(expectedStatusMap, actualStatusMap);
  }

  private Payment mockPayment(Long id, PaymentStatus status) {
    var payment = new Payment();
    payment.setId(id);
    payment.setStatus(status);
    return payment;
  }

  private GrossEarning mockGrossEarning(String importId, Long paymentId) {
    var grossEarning = new GrossEarning();
    grossEarning.setImportId(importId);
    grossEarning.setPaymentId(paymentId);
    return grossEarning;
  }

  private PaymentDeductionOverride mockDeductionOverride(String importId, Long paymentId) {
    var deductionOverride = new PaymentDeductionOverride();
    deductionOverride.setImportId(importId);
    var id = new PaymentDeductionOverrideId();
    id.setPaymentId(paymentId);
    id.setEmployeeContributionDeductionId(5L);
    deductionOverride.setId(id);
    return deductionOverride;
  }

  private EmployeeContributionDeductionAmountDetail mockAmountDetail(
      String importId, Long paymentId) {
    var amountDetail = new EmployeeContributionDeductionAmountDetail();
    amountDetail.setImportId(importId);
    amountDetail.setPaymentId(paymentId);
    return amountDetail;
  }

  private PayPeriod mockPayPeriod(Long payPeriodId) {
    var payPeriod = mock(PayPeriod.class);
    doReturn(companyId).when(payPeriod).getCompanyId();

    return payPeriod;
  }

  private DetailedEmployee mockEmployee(Long employeeId) {
    var employee = mock(DetailedEmployee.class);
    doReturn(employeeId).when(employee).getId();
    doReturn("workerId-" + employeeId).when(employee).getWorkerId();

    return employee;
  }
}
