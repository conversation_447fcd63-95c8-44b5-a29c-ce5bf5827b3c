package com.everee.api.payment.pullback;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.everee.api.storage.StoredFileLink;
import com.everee.api.util.BaseControllerTestsPrivateV1;
import java.net.URL;
import java.time.Instant;
import java.util.List;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;

@WebMvcTest(PrivPaymentPullbackController.class)
class PrivPaymentPullbackControllerTest extends BaseControllerTestsPrivateV1 {
  @MockBean private PaymentPullbackService paymentPullbackService;

  @Test
  @SneakyThrows
  void getPaymentPullbackSummaries() {
    when(paymentPullbackService.getPullbackSummary(eq(123L), isNull(), isNull(), any()))
        .thenReturn(
            new PageImpl(
                List.of(
                    new PaymentPullbackSummary()
                        .setCompanyId(123L)
                        .setInternalPullbackCount(4)
                        .setExternalPullbackCount(1))));

    mvc.perform(
            get(BASE_URL + "/payment-pullbacks/summaries")
                .param("company-id", "123")
                .param("size", "22"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.items", hasSize(1)))
        .andExpect(jsonPath("$.items[0].companyId", is(123)))
        .andExpect(jsonPath("$.items[0].internalPullbackCount", is(4)))
        .andExpect(jsonPath("$.items[0].externalPullbackCount", is(1)))
        .andExpect(jsonPath("$.items[0].pullbackCount", is(5)));
  }

  @Test
  @SneakyThrows
  void getPaymentPullbackSummaryPaymentReport() {
    when(paymentPullbackService.getPullbackSummaryReport(eq(123L), isNull(), isNull()))
        .thenReturn(new StoredFileLink(new URL("https://www.google.com"), Instant.now()));

    mvc.perform(get(BASE_URL + "/payment-pullbacks/payment-report").param("company-id", "123"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.url", is("https://www.google.com")))
        .andExpect(jsonPath("$.expiresAt").exists());
  }
}
