package com.everee.api.payment;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

import com.everee.api.exception.InvalidRequestException;
import com.everee.api.messaging.DistributedStatusService;
import com.everee.api.messaging.StatusMessage;
import com.everee.api.payment.imports.*;
import com.everee.api.properties.AppDistributedCacheProperties;
import com.everee.api.properties.AppPaymentsProperties;
import com.everee.api.service.PaymentImportSlackNotifierService;
import com.everee.api.storage.StorageAccess;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.user.UserService;
import java.io.IOException;
import java.net.URL;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
public class PaymentImportTests {

  @Mock private StorageService storageService;
  @Mock private JmsTemplate defaultJmsTemplate;
  @Mock private AppPaymentsProperties paymentsProperties;
  @Mock private PaymentImportRepository paymentImportRepository;
  @InjectMocks private PaymentImportService paymentImportService;
  @Mock private MockedStatic<UserService> userServiceMockedStatic;
  @Mock private PaymentImportSlackNotifierService slackNotifierService;
  @Mock private AppDistributedCacheProperties distributedCacheProperties;
  @Mock private DistributedStatusService distributedStatusService;

  private MultipartFile init() throws IOException {
    userServiceMockedStatic.when(UserService::getAuthenticatedUserId).thenReturn(1L);
    var queueName = "import-queue";
    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName, fileName, null, new byte[255]);

    when(paymentsProperties.getImportRequestsQueueName()).thenReturn(queueName);
    when(storageService.getStoredFileLink(any(), any()))
        .thenReturn(new StoredFileLink(new URL("http://path/to/file"), Instant.now()));
    when(storageService.storeFile(any(MultipartFile.class), anyString(), any(StorageAccess.class), anyList()))
      .thenReturn("imports/1/fakefile.csv");
    when(paymentImportRepository.countSimilarFilesInFolder(any(),any(), any(), any()))
      .thenReturn(0);

    return file;
  }

  @Test
  public void testRequestPaymentsImport() throws IOException {
    var file = init();

    var paymentImport = paymentImportService.requestImport(1L, file);
    paymentImport.setId(1L);
    paymentImport.setTotalCount(1);

    verify(storageService).storeFile(any(MultipartFile.class), anyString(), any(StorageAccess.class), anyList());
    verify(defaultJmsTemplate).convertAndSend(any(String.class), any(PaymentImport.class));

    verifyNoMoreInteractions(storageService);
    verifyNoMoreInteractions(defaultJmsTemplate);

    assertThat(paymentImport).as("Does requesting payments return a result object?").isNotNull();
    assertThat(paymentImport.getRequestedByUserId().equals(1L));
    assertThat(paymentImport.getCompanyId().equals(1L));
    assertEquals(PaymentImportStatus.STARTED, paymentImport.getStatus());
    assertEquals("http://path/to/file", paymentImport.getRequestFileUrl());

    paymentImport.setImportedCount(1);
    paymentImport.setResponseFileLocation("/fake/location");
    paymentImport.setResponseBytes(new byte[255]);
    paymentImport = paymentImportService.setUrls(paymentImport);
    when(paymentImportService.getPaymentImport(paymentImport.getId())).thenReturn(paymentImport);
    paymentImport = paymentImportService.updateImport(paymentImport);

    assertEquals(PaymentImportStatus.COMPLETED, paymentImport.getStatus());

    verify(slackNotifierService).notify(paymentImport);
    verifyNoMoreInteractions(slackNotifierService);

    paymentImport.setErroredCount(1);
    paymentImport = paymentImportService.updateImport(paymentImport);

    assertEquals(PaymentImportStatus.ERRORED, paymentImport.getStatus());
  }

  @Test
  public void testStatusMessaging() throws IOException {
    var file = init();

    when(distributedCacheProperties.isPaymentImportsEnabled()).thenReturn(true);
    var paymentImport = paymentImportService.requestImport(1L, file);
    paymentImport.setId(1L);
    paymentImport.setTotalCount(1);
    verify(distributedStatusService).start(paymentImport);

    var cacheMap = new ConcurrentHashMap<String, StatusMessage>();
    cacheMap.put(paymentImport.getKey(), paymentImport);
    when(distributedStatusService.getStatusMap(PaymentImport.STATUS_MAP_NAME)).thenReturn(cacheMap);
    paymentImportService.sendImportStatus(paymentImport.getId());
    verify(distributedStatusService).sendStatusToConnectedClients(paymentImport);

    paymentImport.setImportedCount(1);
    paymentImportService.updateImportStatus(paymentImport);
    verify(distributedStatusService).sendStatusToAllClients(paymentImport);

    when(paymentImportService.getPaymentImport(paymentImport.getId())).thenReturn(paymentImport);
    paymentImport = paymentImportService.updateImport(paymentImport);

    verify(distributedStatusService).end(paymentImport);
    verifyNoMoreInteractions(distributedStatusService);
  }

  @Test
  public void testPaymentsImport_WithPostOnlyImportTypes_ReturnSuccess() throws IOException {

    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,import_type,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_SALARY|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401K|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|48-000-0000-SIT-000|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,Post Only,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,Steve,Simmons,PostOnly,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,xxxxx,xxxxxxx,Post_Only,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");

    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName, fileName, null, input.toString().getBytes());

    when(paymentsProperties.getImportRequestsQueueName()).thenReturn("import-queue");
    when(storageService.getStoredFileLink(any(), any()))
      .thenReturn(new StoredFileLink(new URL("http://path/to/file"), Instant.now()));
    when(storageService.storeFile(any(MultipartFile.class), anyString(), any(StorageAccess.class), anyList()))
      .thenReturn("imports/1/fakefile.csv");
    when(paymentImportRepository.countSimilarFilesInFolder(any(),any(), any(), any()))
      .thenReturn(0);
    var paymentImport = paymentImportService.requestImport(1L, file);
    paymentImport.setId(1L);

    verify(storageService).storeFile(any(MultipartFile.class), anyString(), any(StorageAccess.class), anyList());
    verify(defaultJmsTemplate).convertAndSend(any(String.class), any(PaymentImport.class));

    verifyNoMoreInteractions(storageService);
    verifyNoMoreInteractions(defaultJmsTemplate);

    assertThat(paymentImport).isNotNull();
    assertThat(paymentImport.getRequestedByUserId().equals(1L));
    assertThat(paymentImport.getCompanyId().equals(1L));
    assertEquals(PaymentImportStatus.STARTED, paymentImport.getStatus());
    assertEquals("http://path/to/file", paymentImport.getRequestFileUrl());
    assertEquals(ImportType.POST_ONLY, paymentImport.getImportType());
  }

  @Test
  public void testPaymentsImport_WithDefaultImportTypes_ReturnSuccess() throws IOException {

    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_SALARY|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401K|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|48-000-0000-SIT-000|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,Steve,Simmons,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,xxxxx,xxxxxxx,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");

    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName, fileName, null, input.toString().getBytes());
    when(paymentsProperties.getImportRequestsQueueName()).thenReturn("import-queue");
    when(storageService.getStoredFileLink(any(), any()))
      .thenReturn(new StoredFileLink(new URL("http://path/to/file"), Instant.now()));
    when(storageService.storeFile(any(MultipartFile.class), anyString(), any(StorageAccess.class), anyList()))
      .thenReturn("imports/1/fakefile.csv");
    when(paymentImportRepository.countSimilarFilesInFolder(any(),any(), any(), any()))
      .thenReturn(0);

    var paymentImport = paymentImportService.requestImport(1L, file);
    paymentImport.setId(1L);

    verify(storageService).storeFile(any(MultipartFile.class), anyString(), any(StorageAccess.class), anyList());
    verify(defaultJmsTemplate).convertAndSend(any(String.class), any(PaymentImport.class));

    verifyNoMoreInteractions(storageService);
    verifyNoMoreInteractions(defaultJmsTemplate);

    assertThat(paymentImport).isNotNull();
    assertThat(paymentImport.getRequestedByUserId().equals(1L));
    assertThat(paymentImport.getCompanyId().equals(1L));
    assertEquals(PaymentImportStatus.STARTED, paymentImport.getStatus());
    assertEquals("http://path/to/file", paymentImport.getRequestFileUrl());
    assertEquals(ImportType.HISTORICAL, paymentImport.getImportType());
  }

  @Test
  public void testPaymentsImport_WithVaryingImportTypes_ThrowsInvalidRequestException() {
    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,import_type,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_SALARY|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401K|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|48-000-0000-SIT-000|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,Post Only,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,Steve,Simmons,PostOnly,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,xxxxx,xxxxxxx,Post_Only,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    input.append(
      "1,36,xxxxx,xxxxxxx,Historical,10/4/22,10/4/22,10/4/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName,fileName, null, input.toString().getBytes());
    assertThrows(InvalidRequestException.class,()-> paymentImportService.requestImport(1L, file));
  }

  @Test
  public void testPaymentsImport_WithInvalidDeductionNameInHeader_ThrowsInvalidRequestException() {
    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,import_type,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_SALARY|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401(K)|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|48-000-0000-SIT-000|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,Post Only,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");

    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName,fileName, null, input.toString().getBytes());
    assertThrows(InvalidRequestException.class,()-> paymentImportService.requestImport(1L, file));
  }
  @Test
  public void testPaymentsImport_WithInvalidEarningNameInHeader_ThrowsInvalidRequestException() {
    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,import_type,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_WAGE|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401K|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|48-000-0000-SIT-000|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,Post Only,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");

    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName,fileName, null, input.toString().getBytes());
    assertThrows(InvalidRequestException.class,()-> paymentImportService.requestImport(1L, file));
  }

  @Test
  public void testPaymentsImport_WithInvalidHeaderTaxID_ThrowsInvalidRequestException() {
    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,import_type,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_SALARY|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401K|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|48-000-0000-SIT2222-000|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,Post Only,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");

    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName,fileName, null, input.toString().getBytes());
    assertThrows(InvalidRequestException.class,()-> paymentImportService.requestImport(1L, file));
  }

  @Test
  public void testPaymentsImport_WithInvalidTaxHeaderFormat_ThrowsInvalidRequestException() {
    var input = new StringBuffer();
    input.append(
      "company_id,employee_id,first_name,last_name,import_type,pay_period_start_date,pay_period_end_date,pay_date,gross,net,earning|OVERTIME_HOURLY|amount,earning|BONUS|amount,earnings|REGULAR_SALARY|amount,earning|REGULAR_HOURLY|amount,deduct|MEDICAL_INSURANCE|amount_ee,deduct|MEDICAL_INSURANCE|amount_er,deduction|ERISA_401K|amount_ee,deduct|ERISA_401K|amount_er,deduct|GARNISHMENT|amount_ee,deduct|GARNISHMENT|amount_er,deduct|HSA|amount_ee,deduct|HSA|amount_er,tax|00-000-0000-ER_MEDI-000|amount,taxes|49-000-0000-SIT-000|amount,tax|49-000-0000-ER_SUTA-000|amount,tax|00-000-0000-ER_FUTA-000|amount,tax|00-000-0000-ER_FICA-000|amount,tax|00-000-0000-MEDI-000|amount,tax|00-000-0000-FIT-000|amount,tax|00-000-0000-FICA-000|amount,tax|bad-Tax-id|amount,tax|48-000-0000-ER_SUTA-000|amount\n");
    input.append(
      "1,36,Steve,Simmons,Post Only,10/3/22,10/3/22,10/3/22,10315.38,10239.38,200,10000,115.38,,20,,20,,20,,,,6,7,8,2,4,5,1,3,,\n");
    var fileName = "fakefile.csv";
    var file = new MockMultipartFile(fileName,fileName, null, input.toString().getBytes());
    assertThrows(InvalidRequestException.class,()-> paymentImportService.requestImport(1L, file));
  }
}
