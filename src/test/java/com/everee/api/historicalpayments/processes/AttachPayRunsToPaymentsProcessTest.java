package com.everee.api.historicalpayments.processes;

import com.everee.api.historicalpayments.models.HistoricalPaymentDto;
import com.everee.api.historicalpayments.processors.context.PayRunContext;
import com.everee.api.historicalpayments.processors.link.AttachPayRunsToPaymentsProcess;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.payperiod.PayPeriod;
import com.everee.api.payperiod.PayPeriodRepository;
import com.everee.api.payperiod.PayPeriodType;
import com.everee.api.payrun.models.PayRun;
import com.everee.api.payrun.models.PayRunType;
import com.everee.api.user.UserService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;

import javax.persistence.EntityNotFoundException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ImportAutoConfiguration(MessageSourceAutoConfiguration.class)
@SpringBootTest(classes = {MessageSource.class, MessageSourceHolder.class})
public class AttachPayRunsToPaymentsProcessTest {
    @Mock
    PayPeriodRepository payPeriodRepository;

    @Autowired
    private MessageSource messageSource;

    private AttachPayRunsToPaymentsProcess process;

    private MockedStatic<UserService> userServiceMockedStatic;

    @BeforeEach
    public void beforeEach() {
        userServiceMockedStatic = mockStatic(UserService.class);
        userServiceMockedStatic.when(UserService::getAuthenticatedUserId).thenReturn(1L);
        process = new AttachPayRunsToPaymentsProcess(payPeriodRepository);
    }

    @AfterEach
    public void afterEach() {
        userServiceMockedStatic.close();
    }

    @Test
    public void testAttachPayRunsToPayments() {
        // Arrange
        Map<Long, Map<LocalDate, List<HistoricalPaymentDto>>> groupedPayments = new HashMap<>();
        Map<LocalDate, List<HistoricalPaymentDto>> dateGroups = new HashMap<>();

        var mockPeriod = new PayPeriod().setPayPeriodType(PayPeriodType.BI_WEEKLY);
        PayPeriodType.BI_WEEKLY.setMessageSource(messageSource);
        when(payPeriodRepository.getOne(anyLong())).thenReturn(mockPeriod);

        var firstPayment =  new HistoricalPaymentDto();
        firstPayment.setId(1L);

        var secondPayment =  new HistoricalPaymentDto();
        secondPayment.setId(2L);

        List<HistoricalPaymentDto> payments = Arrays.asList(firstPayment, secondPayment);

        LocalDate payDate = LocalDate.now();
        dateGroups.put(payDate, payments);
        groupedPayments.put(1L, dateGroups);

        PayRunContext context = new PayRunContext();
        context.setGroupedPayments(groupedPayments);

        Map<PayRun, List<Long>> expectedPayRunToPaymentsMap = new HashMap<>();
        PayRun payRun = new PayRun();
        payRun.setCompanyId(context.getCompanyId());
        payRun.setPayPeriodId(1L);
        payRun.setPayDate(payDate);
        payRun.setCreatedByUserId(1L);
        payRun.setUpdatedByUserId(1L);
        payRun.setType(PayRunType.ON_CYCLE);
        payRun.setDescription(PayPeriodType.BI_WEEKLY.getDisplayName());
        expectedPayRunToPaymentsMap.put(payRun, payments.stream().map(HistoricalPaymentDto::getId).collect(Collectors.toList()));

        // Act
        process.process(context);

        // Assert
        assertEquals(expectedPayRunToPaymentsMap, context.getPayRunToPaymentsMap(), "Pay runs should be attached to payments");
    }


  @Test
  public void testAttachPayRunsToPayments_PayPeriodNotFound_throwsException() {
    // Arrange
    Map<Long, Map<LocalDate, List<HistoricalPaymentDto>>> groupedPayments = new HashMap<>();
    Map<LocalDate, List<HistoricalPaymentDto>> dateGroups = new HashMap<>();

    when(payPeriodRepository.getOne(anyLong())).thenThrow(new EntityNotFoundException());

    var firstPayment =  new HistoricalPaymentDto();
    firstPayment.setId(1L);

    var secondPayment =  new HistoricalPaymentDto();
    secondPayment.setId(2L);

    List<HistoricalPaymentDto> payments = Arrays.asList(firstPayment, secondPayment);

    LocalDate payDate = LocalDate.now();
    dateGroups.put(payDate, payments);
    groupedPayments.put(1L, dateGroups);

    PayRunContext context = new PayRunContext();
    context.setGroupedPayments(groupedPayments);


    // Act & Assert
    assertThrows(EntityNotFoundException.class, () -> process.process(context));
  }
}
