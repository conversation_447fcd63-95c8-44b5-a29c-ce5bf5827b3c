package com.everee.api.password;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.everee.api.config.ratelimit.RateLimitService;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.onboarding.WorkerOnboardingService;
import com.everee.api.employee.onboarding.entity.WorkerOnboarding;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.properties.AppSupportProperties;
import com.everee.api.user.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class SetPasswordServiceTests {

  @Mock private UserService userService;
  @Mock private DetailedUserRepository detailedUserRepository;
  @Mock private AppSupportProperties appSupportProperties;
  @Mock private SetPasswordTokenRepository setPasswordTokenRepository;
  @Mock private SetPasswordNotificationService setPasswordNotificationService;
  @Mock private SetPasswordSuccessNotificationService setPasswordSuccessNotificationService;
  @Mock private WorkerOnboardingService workerOnboardingService;
  @Mock private RateLimitService rateLimitService;

  @InjectMocks private SetPasswordService setPasswordService;

  @BeforeEach
  public void initMocks() {
    var mockUser = new DetailedUser();
    mockUser.setId(-1L);
    mockUser.setEmail("<EMAIL>");
    var optionalUser = Optional.of(mockUser);
    var firstDuration = Duration.ofMinutes(60);
    var subsequentDuration = Duration.ofMinutes(15);
    var mockToken = new SetPasswordToken();
    var optionalToken = Optional.of(mockToken);
    mockToken.setUserId(mockUser.getId());
    mockToken.setToken("test-token");
    mockToken.setExpiresAt(LocalDateTime.now().plusDays(3));
    lenient().when(userService.updateUserPassword(anyLong(), anyString())).thenReturn(mockUser);
    lenient()
        .when(detailedUserRepository.findByUsernameOrEmail(anyString()))
        .thenReturn(optionalUser);
    lenient().when(appSupportProperties.getForgotPasswordDuration()).thenReturn(subsequentDuration);
    lenient()
        .when(appSupportProperties.getFirstTimeUserPasswordDuration())
        .thenReturn(firstDuration);
    lenient().when(setPasswordTokenRepository.save(any())).thenAnswer(r -> r.getArgument(0));
    lenient().when(setPasswordTokenRepository.findByUserId(any())).thenReturn(optionalToken);
    lenient()
        .when(setPasswordTokenRepository.findByTokenAndUserId(any(), any()))
        .thenReturn(optionalToken);
  }

  @AfterEach
  public void verifyNoUnexpectedInteractions() {
    verifyNoMoreInteractions(detailedUserRepository);
    verifyNoMoreInteractions(appSupportProperties);
    verifyNoMoreInteractions(setPasswordTokenRepository);
    verifyNoMoreInteractions(setPasswordNotificationService);
  }

  @Test
  public void testStartNewUserSetPasswordRequest() {
    var mockRequest = new RecoverPasswordRequest();
    mockRequest.setPhoneNumberOrEmail("<EMAIL>");
    setPasswordService.startNewUserSetPasswordRequest(mockRequest);
    verify(detailedUserRepository).findByUsernameOrEmail(eq("<EMAIL>"));
    verify(appSupportProperties).getFirstTimeUserPasswordDuration();
    verify(setPasswordTokenRepository).findByUserId(eq(-1L));
    verify(setPasswordTokenRepository).save(any());
    verify(setPasswordNotificationService).sendSetPasswordMessage(any());
  }

  @Test
  public void testStartForgotSetPasswordRequest() {
    var mockRequest = new RecoverPasswordRequest();
    mockRequest.setPhoneNumberOrEmail("<EMAIL>");
    setPasswordService.startForgotSetPasswordRequest(mockRequest);
    verify(detailedUserRepository).findByUsernameOrEmail(eq("<EMAIL>"));
    verify(appSupportProperties).getForgotPasswordDuration();
    verify(setPasswordTokenRepository).findByUserId(eq(-1L));
    verify(setPasswordTokenRepository).save(any());
    verify(setPasswordNotificationService).sendSetPasswordMessage(any());
  }

  @Test
  public void testStartNewUserSetPasswordRequestWithBadEmail() {
    var mockRequest = new RecoverPasswordRequest();
    mockRequest.setPhoneNumberOrEmail("<EMAIL>");
    assertThatThrownBy(() -> setPasswordService.startNewUserSetPasswordRequest(mockRequest))
        .isInstanceOf(InvalidRequestException.class);
    verify(detailedUserRepository).findByUsernameOrEmail(eq("<EMAIL>"));
    verify(appSupportProperties).getFirstTimeUserPasswordDuration();
  }

  @Test
  public void testStartForgotSetPasswordRequestWithBadEmail() {
    var mockRequest = new RecoverPasswordRequest();
    mockRequest.setPhoneNumberOrEmail("<EMAIL>");
    assertThatThrownBy(() -> setPasswordService.startForgotSetPasswordRequest(mockRequest))
        .isInstanceOf(InvalidRequestException.class);
    verify(detailedUserRepository).findByUsernameOrEmail(eq("<EMAIL>"));
    verify(appSupportProperties).getForgotPasswordDuration();
  }

  @Test
  public void testStartAccountRecovery_claimedAccount() {
    // <<<< GIVEN >>>> //
    var searchParam = "<EMAIL>";
    var claimedUser = new DetailedUser();
    claimedUser.setId(7L);
    claimedUser.setEmail(searchParam);

    when(detailedUserRepository.findByEmailIgnoreCase(eq(searchParam)))
        .thenReturn(Optional.of(claimedUser));

    // <<<< WHEN >>>> //
    var mockRequest = new RecoverAccountRequest();
    mockRequest.setEmail(searchParam);

    setPasswordService.startAccountRecovery(mockRequest);

    // <<<< THEN >>>> //
    verify(detailedUserRepository).findByEmailIgnoreCase(eq(searchParam));
    verify(appSupportProperties).getForgotPasswordDuration();
    verify(setPasswordTokenRepository).findByUserId(eq(7L));
    verify(setPasswordTokenRepository).save(any());
    verify(setPasswordNotificationService).sendSetPasswordMessage(any());
  }

  @Test
  public void testStartAccountRecovery_unclaimedAccount() {
    // <<<< GIVEN >>>> //
    var searchParam = "<EMAIL>";
    var onboarding = mock(WorkerOnboarding.class);
    when(onboarding.isClaimed()).thenReturn(false);
    var employee = new DetailedEmployee();
    employee.setOnboarding(onboarding);
    var unclaimedUser = new DetailedUser();
    unclaimedUser.setId(7L);
    unclaimedUser.setUnverifiedEmail(searchParam);
    unclaimedUser.setEmployees(Set.of(employee));

    when(detailedUserRepository.findByEmailIgnoreCase(eq(searchParam)))
        .thenReturn(Optional.empty());
    when(detailedUserRepository.findByUnverifiedEmailIgnoreCase(eq(searchParam)))
        .thenReturn(Optional.of(unclaimedUser));

    // <<<< WHEN >>>> //
    var mockRequest = new RecoverAccountRequest();
    mockRequest.setEmail(searchParam);

    setPasswordService.startAccountRecovery(mockRequest);

    // <<<< THEN >>>> //
    verify(detailedUserRepository).findByEmailIgnoreCase(eq(searchParam));
    verify(detailedUserRepository).findByUnverifiedEmailIgnoreCase(eq(searchParam));
    verify(workerOnboardingService).sendOnboardingReminder(eq(onboarding), any());
  }

  @Test
  public void testStartAccountRecovery_notFound() {
    // <<<< GIVEN >>>> //
    var searchParam = "<EMAIL>";

    when(detailedUserRepository.findByEmailIgnoreCase(eq(searchParam)))
        .thenReturn(Optional.empty());
    when(detailedUserRepository.findByUnverifiedEmailIgnoreCase(eq(searchParam)))
        .thenReturn(Optional.empty());

    // <<<< WHEN >>>> //
    var mockRequest = new RecoverAccountRequest();
    mockRequest.setEmail(searchParam);

    setPasswordService.startAccountRecovery(mockRequest);

    // <<<< THEN >>>> //
    verify(detailedUserRepository).findByEmailIgnoreCase(eq(searchParam));
    verify(detailedUserRepository).findByUnverifiedEmailIgnoreCase(eq(searchParam));
  }

  @Test
  public void testFindValidSetPasswordToken() {
    setPasswordService.findValidSetPasswordToken(-1L, "test-token");
    verify(setPasswordTokenRepository).findByTokenAndUserId(eq("test-token"), eq(-1L));
  }

  @Test
  public void testSetPassword() {
    setPasswordService.setPassword(-1L, "test-token", "test-password");
    verify(userService).updateUserPassword(eq(-1L), eq("test-password"));
    verify(setPasswordTokenRepository).findByTokenAndUserId(eq("test-token"), eq(-1L));
    verify(setPasswordTokenRepository).delete(any(SetPasswordToken.class));
    verify(setPasswordSuccessNotificationService).sendSetPasswordSuccessMessage(any(User.class));
  }
}
