package com.everee.api.embedded;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.everee.api.auth.util.AccessTokenSynthesizer;
import com.everee.api.embedded.exception.ExpiredEmbeddedSessionException;
import com.everee.api.embedded.exception.UnauthorizedEmbeddedSessionException;
import com.everee.api.employee.CoreEmployee;
import com.everee.api.user.CoreUser;
import com.everee.api.user.CoreUserRepository;
import java.time.Clock;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.oauth2.common.OAuth2AccessToken;

@ExtendWith(MockitoExtension.class)
public class EmbeddedSessionServiceTest {
  @Mock Clock clock;
  @Mock CoreUserRepository coreUserRepository;
  @Mock AccessTokenSynthesizer accessTokenSynthesizer;
  @Mock EmbeddedSessionRepository embeddedSessionRepository;

  @InjectMocks EmbeddedSessionService embeddedSessionService;

  @BeforeEach
  public void beforeEach() {
    var fixedClock =
        Clock.fixed(
            LocalDate.of(2021, 11, 2).atStartOfDay(ZoneId.systemDefault()).toInstant(),
            ZoneId.systemDefault());
    lenient().doReturn(fixedClock.instant()).when(clock).instant();
    lenient().doReturn(fixedClock.getZone()).when(clock).getZone();

    lenient().when(embeddedSessionRepository.save(any())).thenAnswer(a -> a.getArgument(0));

    lenient().when(coreUserRepository.getOne(any())).thenReturn(new CoreUser().setUsername("test"));

    lenient()
        .when(accessTokenSynthesizer.grantUserToken(any(CoreUser.class), anyString(), any(), any()))
        .thenReturn(mock(OAuth2AccessToken.class));
  }

  @Test
  public void createEmbeddedSession() {
    var employee = new CoreEmployee();
    employee.setId(5L);
    var request = new CreateEmbeddedSessionRequest();
    request.setEventHandlerName("event");
    request.setExperience(EmbeddedExperience.ONBOARDING);
    request.setWorkerId("test1");

    var sessionInfo = embeddedSessionService.createEmbeddedSession(employee, request);

    assertThat(sessionInfo).isNotNull();
    assertThat(sessionInfo.getExperience()).isEqualTo(EmbeddedExperience.ONBOARDING);
    assertThat(sessionInfo.getExperienceVersion()).isEqualTo(EmbeddedExperienceVersion.V1_0);

    verify(embeddedSessionRepository).save(any(EmbeddedSession.class));
  }

  @Test
  public void createEmbeddedSession_exception() {
    var employee = new CoreEmployee();
    employee.setId(5L);
    var request = new CreateEmbeddedSessionRequest();
    request.setEventHandlerName("event");
    request.setExperience(EmbeddedExperience.ONBOARDING);
    request.setWorkerId("test1");

    when(embeddedSessionRepository.save(any(EmbeddedSession.class)))
        .thenThrow(new RuntimeException());

    assertThatThrownBy(() -> embeddedSessionService.createEmbeddedSession(employee, request))
        .isInstanceOf(RuntimeException.class);
  }

  @Test
  public void startEmbeddedSession() {
    var user = new CoreUser();
    user.setId(14L);
    var employee = new CoreEmployee();
    employee.setId(20L);
    employee.setUserId(14L);
    var session =
        new EmbeddedSession()
            .setExperience(EmbeddedExperience.ONBOARDING)
            .setToken("token1")
            .setCompanyId(1L)
            .setEmployee(employee)
            .setExpiresAt(LocalDate.of(2021, 11, 2).atStartOfDay().plusSeconds(1));
    when(coreUserRepository.getOne(anyLong())).thenReturn(user);
    when(embeddedSessionRepository.findById(any())).thenReturn(Optional.of(session));

    var request = new EmbeddedSessionStartRequest().setToken("token1");
    var sessionInfo = embeddedSessionService.startEmbeddedSession(request);

    assertThat(sessionInfo).isNotNull();
    assertThat(sessionInfo.getTokenGrant()).isNotNull();
    assertThat(sessionInfo.isHideEvereeBranding()).isFalse();
  }

  @Test
  public void startEmbeddedSession_hideEvereeBranding() {
    var user = new CoreUser();
    user.setId(14L);
    var employee = new CoreEmployee();
    employee.setId(20L);
    employee.setUserId(14L);
    var session =
        new EmbeddedSession()
            .setExperience(EmbeddedExperience.ONBOARDING)
            .setToken("token1")
            .setCompanyId(1L)
            .setEmployee(employee)
            .setExpiresAt(LocalDate.of(2021, 11, 2).atStartOfDay().plusSeconds(1))
            .setHideEvereeBranding(true);
    when(coreUserRepository.getOne(anyLong())).thenReturn(user);
    when(embeddedSessionRepository.findById(any())).thenReturn(Optional.of(session));

    var request = new EmbeddedSessionStartRequest().setToken("token1");
    var sessionInfo = embeddedSessionService.startEmbeddedSession(request);

    assertThat(sessionInfo).isNotNull();
    assertThat(sessionInfo.getTokenGrant()).isNotNull();
    assertThat(sessionInfo.isHideEvereeBranding()).isTrue();
  }

  @Test
  public void startEmbeddedSession_expired() {
    var session =
        new EmbeddedSession()
            .setExperience(EmbeddedExperience.ONBOARDING)
            .setToken("token1")
            .setCompanyId(1L)
            .setExpiresAt(LocalDate.of(2021, 11, 2).atStartOfDay().minusSeconds(1));
    when(embeddedSessionRepository.findById(any())).thenReturn(Optional.of(session));

    var request = new EmbeddedSessionStartRequest().setToken("token1");
    assertThatThrownBy(() -> embeddedSessionService.startEmbeddedSession(request))
        .isInstanceOf(ExpiredEmbeddedSessionException.class);
  }

  @Test
  public void startEmbeddedSession_missing() {
    when(embeddedSessionRepository.findById(any())).thenReturn(Optional.empty());

    var request = new EmbeddedSessionStartRequest().setToken("token1");
    assertThatThrownBy(() -> embeddedSessionService.startEmbeddedSession(request))
        .isInstanceOf(UnauthorizedEmbeddedSessionException.class);
  }
}
