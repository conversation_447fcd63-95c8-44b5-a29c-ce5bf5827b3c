package com.everee.api.wageandtaxreport;

import com.everee.api.money.Money;
import com.everee.api.report.ReportExporter;
import com.everee.api.wageandtaxreport.domain.RowHeader;
import com.everee.api.wageandtaxreport.domain.models.WageAndTaxReportRequest;

import com.everee.api.wageandtaxreport.domain.models.WageAndTaxSummary;
import com.everee.api.wageandtaxreport.exceptions.MissingDataException;
import com.everee.api.wageandtaxreport.repository.WageTaxDataRepository;
import com.everee.api.wageandtaxreport.service.ReportDivision;
import com.everee.api.wageandtaxreport.service.SummaryService;
import com.everee.api.wageandtaxreport.domain.WageAndTaxReportType;
import com.everee.api.wageandtaxreport.views.GenerateQuarterlyView;
import com.everee.api.wageandtaxreport.views.GenerateSummaryView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.catchThrowable;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.*;

class SummaryServiceTest {

  @Mock
  private WageTaxDataRepository wageTaxDataRepository;

  @Mock
  private ReportExporter reportExporter;

  @InjectMocks
  private SummaryService summaryService;

  @Captor
  private ArgumentCaptor<Money> MoneyCaptor;
  @Captor
  private ArgumentCaptor<Long> longCaptor;
  @Captor
  private ArgumentCaptor<String> stringCaptor;

  private WageAndTaxReportRequest request;
  private String companyName;
  private Map<ReportDivision, WageAndTaxSummary> summaries;
  private WageAndTaxSummary total;
  private Throwable thrownException;

  @BeforeEach
  void setUp() {
    summaries = null;
    MockitoAnnotations.openMocks(this);
    request = new WageAndTaxReportRequest();
    request.setCompanyId(1L);
    request.setReportType(WageAndTaxReportType.FEDERAL);
    request.setStartDate(LocalDate.of(2023, 1, 1));
    request.setEndDate(LocalDate.of(2023, 12, 31));
    companyName = "Test Company";
  }

  @Test
  void addWageAndTaxSummaryView_NoData_ThrowsMissingDataException() {
    givenQuerySummaryReportReturnsNoData();
    whenAddSummaryViewsIsCalled();
    thenQuerySummaryShouldBeCalledAndThrowMissingDataException();
    verifyNoInteractions(reportExporter);
  }

  @Test
  void sumQuarterlyValues_ValidData_ReturnsSummedWageAndTaxSummary() {
    givenAListOfsummaries();
    whenSumQuarterlyValuesIsCalled();
    thenTotalEqualsAllSummedQuarters();
  }

  @Test
  void generateSummaryView_RowHeadersWrittenToExporter_Verified() {
    var rowHeaders = SummaryService.getROW_HEADERS();
    givenAListOfsummariesWithTotal();
    var generateSummaryView = new GenerateSummaryView(reportExporter, "Test Company", LocalDate.now(), LocalDate.now());
    generateSummaryView.generateSummaryView(total, rowHeaders);
    for (var rowHeader : rowHeaders) {
      verify(reportExporter).writeSubheaderCell(rowHeader.getKey());
    }
  }

  @Test
  void generateWageAndTaxSummaryView_RowHeadersWrittenToExporter_Verified() {
    var rowHeaders = SummaryService.getROW_HEADERS();
    givenAListOfsummariesWithTotal();
    var generateQuarterlyView = new GenerateQuarterlyView(reportExporter, "Test Company");
    generateQuarterlyView.generateQuarterlyView(summaries, rowHeaders);
    for (RowHeader<WageAndTaxSummary, ?> rowHeader : rowHeaders) {
      verify(reportExporter).writeSubheaderCell(rowHeader.getKey());
    }
  }

  void givenQuerySummaryReportReturnsNoData() {
    when(wageTaxDataRepository.queryQuarterlySummaryReport(anyLong(), any(LocalDate.class), any(LocalDate.class)))
      .thenReturn(Collections.emptyList());
  }
  void whenAddSummaryViewsIsCalled() {
    thrownException = catchThrowable(() -> summaryService.addSummaryViews(reportExporter, request, companyName));
  }
  void thenQuerySummaryShouldBeCalledAndThrowMissingDataException() {
    verify(wageTaxDataRepository).queryQuarterlySummaryReport(anyLong(), any(LocalDate.class), any(LocalDate.class));
    assertThat(thrownException)
      .isInstanceOf(MissingDataException.class)
      .hasMessageContaining("Quarterly Summary data missing");
  }

  void givenAListOfsummaries() {
    summaries = Map.of(
      ReportDivision.Q1, WageAndTaxSummaryMock.createMockWageAndTaxSummary(),
      ReportDivision.Q2, WageAndTaxSummaryMock.createMockWageAndTaxSummary()
    );
  }

  void whenSumQuarterlyValuesIsCalled() {
    total = summaries.values().stream().reduce(WageAndTaxSummary.ZERO, WageAndTaxSummary::plus);
  }

  void givenAListOfsummariesWithTotal() {
    summaries = new HashMap<>(Map.of(
      ReportDivision.Q1, WageAndTaxSummaryMock.createMockWageAndTaxSummary(),
      ReportDivision.Q2, WageAndTaxSummaryMock.createMockWageAndTaxSummary()
    ));
    total = summaries.values().stream().reduce(WageAndTaxSummary.ZERO, WageAndTaxSummary::plus);
    summaries.put(ReportDivision.TOTAL, total);
  }

  void thenTotalEqualsAllSummedQuarters() {
    assertThat(total.getGrossWages()).isEqualTo(new Money("20000.00"));
    assertThat(total.getFitSubjectWages()).isEqualTo(new Money("16000.00"));
    assertThat(total.getFitTaxedWages()).isEqualTo(new Money("15000.00"));
    assertThat(total.getFitTaxedAdditional()).isEqualTo(1000L);
    assertThat(total.getTotalSscSubjectWages()).isEqualTo(new Money("18000.00"));
    assertThat(total.getSscSubjectWagesWage()).isEqualTo(new Money("17000.00"));
    assertThat(total.getSscTaxDue()).isEqualTo(new Money("2000.00"));
    assertThat(total.getSscSubjectWagesTips()).isEqualTo(new Money("1000.00"));
    assertThat(total.getSscTaxDueOnTips()).isEqualTo(new Money("100.00"));
    assertThat(total.getMedSubjectWages()).isEqualTo(new Money("19000.00"));
    assertThat(total.getMedTax()).isEqualTo(new Money("400.00"));
    assertThat(total.getAdditionalMedicareWage()).isEqualTo(new Money("2000.00"));
    assertThat(total.getAdditionalMedicareTax()).isEqualTo(new Money("50.00"));
    assertThat(total.getTotalUncollectedTaxDue()).isEqualTo(new Money("30.00"));
    assertThat(total.getUncollectedTaxDueOnTips()).isEqualTo(new Money("20.00"));
  }
}
