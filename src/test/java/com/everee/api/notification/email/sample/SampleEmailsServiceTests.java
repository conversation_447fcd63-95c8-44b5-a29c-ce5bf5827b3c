package com.everee.api.notification.email.sample;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.everee.api.employee.onboarding.WorkerOnboardingLinkService;
import com.everee.api.notification.email.EmailNotification;
import com.everee.api.notification.email.EmailRecipient;
import com.everee.api.notification.spi.NotificationProperties;
import com.everee.api.notification.spi.NotificationService;
import com.everee.api.partner.Partner;
import com.everee.api.password.SetPasswordSuccessNotification;
import com.everee.api.properties.AppSupportProperties;
import com.everee.api.report.notification.EmailedReportNotification;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import java.net.URL;
import java.time.Instant;
import lombok.SneakyThrows;
import java.net.URI;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

@ExtendWith(MockitoExtension.class)
public class SampleEmailsServiceTests {

  @Mock MessageSource messageSource;
  @Mock StorageService storageService;
  @Mock AppSupportProperties supportProperties;
  @Mock NotificationService notificationService;
  @Mock NotificationProperties notificationProperties;
  @Mock WorkerOnboardingLinkService workerOnboardingLinkService;

  @InjectMocks SampleEmailsService sampleEmailsService;

  private static final Partner partner = mockPartner();

  @BeforeEach
  public void init() {
    // TODO: mock for all here
  }

  @Test
  public void testSendSampleEmail_singleEmail() {
    // === Assemble === //
    var emailType = "SetPasswordSuccessNotification";

    when(notificationProperties.getEmailOverrideDeliveryAddress())
        .thenReturn("<EMAIL>");

    // === Act === //
    sampleEmailsService.sendSampleEmail(partner, emailType, null);

    // === Assert === //
    verify(notificationService, times(1))
        .enqueueEmailNotification(
            any(SetPasswordSuccessNotification.class), any(EmailRecipient.class));
  }

  @Test
  @Disabled
  public void testSendSampleEmail_allEmails() {
    // === Assemble === //
    var emailType = "SendAll";
    var onboardingLink = mock(URI.class);

    when(notificationProperties.getEmailOverrideDeliveryAddress()).thenReturn(null);
    when(workerOnboardingLinkService.buildLink(
            anyString(), anyString(), anyString(), anyString(), anyString(), anyBoolean()))
        .thenReturn(onboardingLink);

    // === Act === //
    sampleEmailsService.sendSampleEmail(partner, emailType, "<EMAIL>");

    // === Assert === //
    verify(notificationService, times(38))
        .enqueueEmailNotification(any(EmailNotification.class), any(EmailRecipient.class));
  }

  @Test
  public void testSendSampleEmail_missingRecipient() {
    // === Assemble === //
    var emailType = "emailTypeValue";

    when(notificationProperties.getEmailOverrideDeliveryAddress()).thenReturn(null);

    // === Act === //
    // === Assert === //
    assertThatThrownBy(() -> sampleEmailsService.sendSampleEmail(partner, emailType, null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Recipient email must be supplied");
  }

  @Test
  @SneakyThrows
  public void testSendSampleEmail_EmailedReportNotification() {
    // === Assemble === //
    var emailType = "EmailedReportNotification";

    when(notificationProperties.getEmailOverrideDeliveryAddress())
        .thenReturn("<EMAIL>");
    when(storageService.getStoredFileLink(any(), any()))
        .thenReturn(new StoredFileLink(new URL("https://www.google.com"), Instant.now()));

    // === Act === //
    sampleEmailsService.sendSampleEmail(partner, emailType, null);

    // === Assert === //
    verify(notificationService, times(1))
        .enqueueEmailNotification(any(EmailedReportNotification.class), any(EmailRecipient.class));
  }

  private static Partner mockPartner() {
    var partner = new Partner();
    partner.setId(1L);
    partner.setDisplayName("Everee");
    partner.setLoginRequestParamName("Everee");
    partner.setPrimaryLogoLocation("logo.com/everee");
    partner.setLegalName("Everee Inc.");
    partner.setLegalPhysicalAddress("26 Rio Grande Street, Ste 2072, Salt Lake City, Utah 84101");
    partner.setSecondaryBrandColor("#604F7B");
    partner.setDefaultPartner(true);

    return partner;
  }
}
