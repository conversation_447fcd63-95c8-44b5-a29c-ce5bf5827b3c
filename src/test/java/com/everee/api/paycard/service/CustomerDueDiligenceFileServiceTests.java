package com.everee.api.paycard.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.AdditionalMatchers.aryEq;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.everee.api.featureflag.FeatureFlagService;
import com.everee.api.ftp.SftpClient;
import com.everee.api.paycard.config.CustomerDueDiligenceFileProperties;
import com.everee.api.storage.StorageAccess;
import com.everee.api.storage.StorageService;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerDueDiligenceFileServiceTests {

  @Mock private CustomerDueDiligenceFileProperties properties;
  @Mock private CddFileGeneratorService cddFileGeneratorService;
  @Mock private UboFileGeneratorService uboFileGeneratorService;
  @Mock private PhysicalDocFileGeneratorService physicalDocFileGeneratorService;
  @Mock private FeatureFlagService featureFlagService;
  @Mock private StorageService storageService;
  @Mock private SftpClient sftpClient;
  @InjectMocks private CustomerDueDiligenceFileService service;
  private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MMddyyyy");

  @BeforeEach
  public void setup() throws IOException {
    setupProperties();
  }

  @AfterEach
  public void teardown() {
    verifyNoMoreInteractions(storageService, sftpClient);
  }

  @Test
  public void success() throws IOException {
    var today = LocalDate.now().format(dateFormatter);
    var yesterday = LocalDate.now().minusDays(1).format(dateFormatter);
    when(featureFlagService.boolValue(any(), eq(true))).thenReturn(true);
    // setup CDD
    var cddBytesStr = "H|HEADER|EVEREE INC.|CDDINFORMATION|" + today + "|" + yesterday;
    when(cddFileGeneratorService.generate())
        .thenReturn(cddBytesStr.getBytes(StandardCharsets.UTF_8));
    // setup UBO
    var uboBytesStr = "H|HEADER|EVEREE INC.|UBOINFORMATION|" + today + "|" + yesterday;
    ;
    when(uboFileGeneratorService.generate())
        .thenReturn(uboBytesStr.getBytes(StandardCharsets.UTF_8));
    // setup physicalDocs
    var physicalDocsTemplateStr = "H|HEADER|EVEREE INC.|PhysicalDoc|" + today + "|" + yesterday;
    when(physicalDocFileGeneratorService.generate())
        .thenReturn(physicalDocsTemplateStr.getBytes(StandardCharsets.UTF_8));
    // setup sftp
    when(sftpClient.put(any(), any())).thenReturn(true);

    // call
    assertTrue(service.sendFiles(sftpClient));

    // verify CDD
    var cddFilename =
        CustomerDueDiligenceFileService.CDD_EDD_TEMPLATE.replace(
            "_template.txt", "_" + today + ".txt");
    var cddStoragePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH + "/archive/" + cddFilename;
    verify(storageService, times(1))
        .storeFile(
            aryEq(cddBytesStr.getBytes()),
            eq(cddStoragePath),
            eq(StorageAccess.EVEREE_INTERNAL),
            eq(Collections.emptyList()));
    // verify UBO
    var uboFilename =
        CustomerDueDiligenceFileService.UBO_FILE_TEMPLATE.replace(
            "_template.txt", "_" + today + ".txt");
    var uboStoragePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH + "/archive/" + uboFilename;
    verify(storageService, times(1))
        .storeFile(
            aryEq(uboBytesStr.getBytes()),
            eq(uboStoragePath),
            eq(StorageAccess.EVEREE_INTERNAL),
            eq(Collections.emptyList()));
    // verify physicalDocs
    var physicalDocsFilename =
        CustomerDueDiligenceFileService.PHYSICAL_DOCS_FILE_TEMPLATE.replace(
            "_template.txt", "_" + today + ".txt");
    var physicalDocsStoragePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH
            + "/archive/"
            + physicalDocsFilename;
    verify(storageService, times(1))
        .storeFile(
            aryEq(physicalDocsTemplateStr.getBytes()),
            eq(physicalDocsStoragePath),
            eq(StorageAccess.EVEREE_INTERNAL),
            eq(Collections.emptyList()));
    // verify sftp
    verify(sftpClient, times(1)).open("localhost", 22, "foo", "pass");
    verify(sftpClient, times(1)).put(eq("IN/" + cddFilename + ".pgp"), any(InputStream.class));
    verify(sftpClient, times(1)).put(eq("IN/" + uboFilename + ".pgp"), any(InputStream.class));
    verify(sftpClient, times(1))
        .put(eq("IN/" + physicalDocsFilename + ".pgp"), any(InputStream.class));
    verify(sftpClient, times(1)).close();
  }

  @Test
  public void success_featureFlagFalse() throws IOException {
    // setup
    when(featureFlagService.boolValue(any(), eq(true))).thenReturn(false);
    var cddTemplatePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH
            + "/"
            + CustomerDueDiligenceFileService.CDD_EDD_TEMPLATE;
    var cddTemplateStr = "H|HEADER|EVEREE INC.|CDDINFORMATION|<TODAY>|<YESTERDAY>";
    when(storageService.getFile(any(), eq(cddTemplatePath)))
        .thenReturn(new ByteArrayInputStream(cddTemplateStr.getBytes(StandardCharsets.UTF_8)));

    var uboTemplatePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH
            + "/"
            + CustomerDueDiligenceFileService.UBO_FILE_TEMPLATE;
    var uboTemplateStr = "H|HEADER|EVEREE INC.|UBOINFORMATION|<TODAY>|<YESTERDAY>";
    when(storageService.getFile(any(), eq(uboTemplatePath)))
        .thenReturn(new ByteArrayInputStream(uboTemplateStr.getBytes(StandardCharsets.UTF_8)));
    var physicalDocsTemplatePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH
            + "/"
            + CustomerDueDiligenceFileService.PHYSICAL_DOCS_FILE_TEMPLATE;
    var physicalDocsTemplateStr = "H|HEADER|EVEREE INC.|PhysicalDoc|<TODAY>|<YESTERDAY>";
    when(storageService.getFile(any(), eq(physicalDocsTemplatePath)))
        .thenReturn(
            new ByteArrayInputStream(physicalDocsTemplateStr.getBytes(StandardCharsets.UTF_8)));
    when(sftpClient.put(any(), any())).thenReturn(true);

    // call
    assertTrue(service.sendFiles(sftpClient));

    // verify
    var today = LocalDate.now().format(dateFormatter);
    var yesterday = LocalDate.now().minusDays(1).format(dateFormatter);
    verify(storageService, times(1)).getFile(StorageAccess.EVEREE_INTERNAL, cddTemplatePath);
    var cddFilename =
        CustomerDueDiligenceFileService.CDD_EDD_TEMPLATE.replace(
            "_template.txt", "_" + today + ".txt");
    var cddStoragePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH + "/archive/" + cddFilename;
    var cddReplaced = cddTemplateStr.replace("<TODAY>", today).replace("<YESTERDAY>", yesterday);
    verify(storageService, times(1))
        .storeFile(
            aryEq(cddReplaced.getBytes()),
            eq(cddStoragePath),
            eq(StorageAccess.EVEREE_INTERNAL),
            eq(Collections.emptyList()));
    verify(storageService, times(1)).getFile(StorageAccess.EVEREE_INTERNAL, uboTemplatePath);
    var uboFilename =
        CustomerDueDiligenceFileService.UBO_FILE_TEMPLATE.replace(
            "_template.txt", "_" + today + ".txt");
    var uboStoragePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH + "/archive/" + uboFilename;
    var uboReplaced = uboTemplateStr.replace("<TODAY>", today).replace("<YESTERDAY>", yesterday);
    verify(storageService, times(1))
        .storeFile(
            aryEq(uboReplaced.getBytes()),
            eq(uboStoragePath),
            eq(StorageAccess.EVEREE_INTERNAL),
            eq(Collections.emptyList()));
    verify(storageService, times(1))
        .getFile(StorageAccess.EVEREE_INTERNAL, physicalDocsTemplatePath);
    var physicalDocsFilename =
        CustomerDueDiligenceFileService.PHYSICAL_DOCS_FILE_TEMPLATE.replace(
            "_template.txt", "_" + today + ".txt");
    var physicalDocsStoragePath =
        CustomerDueDiligenceFileService.DEFAULT_CUSTOMER_FILE_PATH
            + "/archive/"
            + physicalDocsFilename;
    var physicalDocsReplaced =
        physicalDocsTemplateStr.replace("<TODAY>", today).replace("<YESTERDAY>", yesterday);
    verify(storageService, times(1))
        .storeFile(
            aryEq(physicalDocsReplaced.getBytes()),
            eq(physicalDocsStoragePath),
            eq(StorageAccess.EVEREE_INTERNAL),
            eq(Collections.emptyList()));
    verify(sftpClient, times(1)).open("localhost", 22, "foo", "pass");
    verify(sftpClient, times(1)).put(eq("IN/" + cddFilename + ".pgp"), any(InputStream.class));
    verify(sftpClient, times(1)).put(eq("IN/" + uboFilename + ".pgp"), any(InputStream.class));
    verify(sftpClient, times(1))
        .put(eq("IN/" + physicalDocsFilename + ".pgp"), any(InputStream.class));
    verify(sftpClient, times(1)).close();
  }

  @Test
  public void badProperties() {
    when(properties.getPgpKey()).thenReturn(null);
    assertFalse(service.sendFiles());

    setupProperties();
    when(properties.getSftpHost()).thenReturn(null);
    assertFalse(service.sendFiles());

    setupProperties();
    when(properties.getSftpPort()).thenReturn(null);
    assertFalse(service.sendFiles());

    setupProperties();
    when(properties.getSftpPort()).thenReturn("-22");
    assertFalse(service.sendFiles());

    setupProperties();
    when(properties.getSftpUsername()).thenReturn(null);
    assertFalse(service.sendFiles());

    setupProperties();
    when(properties.getSftpPassword()).thenReturn("");
    assertFalse(service.sendFiles());
  }

  @Test
  public void customerFileNotFound() {
    when(storageService.getFile(any(), any())).thenThrow(AmazonS3Exception.class);

    assertFalse(service.sendFiles(sftpClient));
  }

  @Test
  public void sftpPutFails() throws IOException {
    when(featureFlagService.boolValue(any(), eq(true))).thenReturn(true);
    when(cddFileGeneratorService.generate()).thenReturn("foo".getBytes());
    when(uboFileGeneratorService.generate()).thenReturn("foo".getBytes());
    when(physicalDocFileGeneratorService.generate()).thenReturn("foo".getBytes());
    when(sftpClient.put(any(), any())).thenReturn(false);

    assertFalse(service.sendFiles(sftpClient));

    verify(storageService, times(3)).storeFile(any(byte[].class), any(), any(), any());
    verify(sftpClient, times(1)).open("localhost", 22, "foo", "pass");
    verify(sftpClient, times(1)).put(any(), any());
    verify(sftpClient, times(1)).close();
  }

  @Test
  public void notEnabled() throws IOException {
    when(properties.isEnabled()).thenReturn(false);

    assertTrue(service.sendFiles(sftpClient));

    verify(storageService, never()).storeFile(any(byte[].class), any(), any(), any());
    verify(sftpClient, never()).open("localhost", 22, "foo", "pass");
    verify(sftpClient, never()).put(any(), any());
  }

  private void setupProperties() {
    var publicKey =
        "-----BEGIN PGP PUBLIC KEY BLOCK-----\n"
            + "\n"
            + "mI0EY9Md8wEEAMK7LwDln0FywHowASg7qS/+qIeehRXQpszctCsKohzzcsjZGKLb\n"
            + "cPNSCl8hg/opkz3anYk64XT5Qb1904HAjNVZWJszzDepNGXhGF4zAvwo4Lc475/L\n"
            + "tS5npyCqa4NyBYdnpJaj2aQcqoCrNuICdEnV5JoqrGSDPjG2RtQVRR+tABEBAAG0\n"
            + "IFRlc3QgVXNlciAoVGVzdCkgPHRlc3RAdGVzdC5jb20+iNEEEwEIADsWIQSPTJ6v\n"
            + "YFq67SyH8MhVHkUtrTDYKQUCY9Md8wIbAwULCQgHAgIiAgYVCgkICwIEFgIDAQIe\n"
            + "BwIXgAAKCRBVHkUtrTDYKW8RBAC77WuGxiTWv8l4Y4OptSacz6mR09kpxo/f8BbE\n"
            + "6wpAR+zY7gr+pLmKuT4vmYk7sjazFS9vbKEsQDwM/LSQeXEy/YAtf/xwbXhbePeF\n"
            + "IbHqpqVP6nVQrxj7ByBvCGHkNLkl0oCL1dfw3qvkvfW4oTORFDkUYiQrX0B1dryS\n"
            + "d4tXSLiNBGPTHfMBBADgA+c6fd6p3Fb2KHWpzgG3GHngd0dlMDtXc33RHItvFmfY\n"
            + "DVRNRyp8wLWVTmrKjGpgEKqAkQVqusYiQVlEawS++YkqVP+b7/E2oSZldg1/6trK\n"
            + "aWdqg6KhU+gHCxie9/nQIq0bVG5u0si1KtHEz5Q0dhfk7DasJfT3prxXGAtI/QAR\n"
            + "AQABiLUEGAEIACAWIQSPTJ6vYFq67SyH8MhVHkUtrTDYKQUCY9Md8wIbDAAKCRBV\n"
            + "HkUtrTDYKfWgA/dvaJodLHVjbU29fei8Kk4+Os6lnAiQvVwq2ke4ZR+cKzuCrrVD\n"
            + "wWHvMdieVIjpEzeYT5IbxTnFfpw2lBRBT07yWccua4/yuZNAWwY56/o64Rp/F2Qf\n"
            + "+o+9gasz5s50JdHCKNKT9n9sWUDeTfJQ6cLwmrN/Hl5Nfg4ht+85nRjb\n"
            + "=E5qo\n"
            + "-----END PGP PUBLIC KEY BLOCK-----";
    when(properties.getPgpKey()).thenReturn(publicKey);
    when(properties.getSftpHost()).thenReturn("localhost");
    when(properties.getSftpPort()).thenReturn("22");
    when(properties.getSftpUsername()).thenReturn("foo");
    when(properties.getSftpPassword()).thenReturn("pass");
    lenient().when(properties.isEnabled()).thenReturn(true);
  }
}
