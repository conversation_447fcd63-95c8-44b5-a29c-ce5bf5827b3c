package com.everee.api.quarterlies.suta.icesa;

import static com.everee.api.annuals.w2.efw2.EFW2GeneratorTest.emp;
import static com.everee.api.employee.statuschange.exporter.EmploymentStatusExporterTestUtils.compareOutputToExpectedExportData;
import static com.everee.api.tax.state.State.IL;
import static com.everee.api.tax.state.State.UT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.UnemploymentRates.CompanyUnemploymentRates;
import com.everee.api.company.UnemploymentRates.CompanyUnemploymentRatesService;
import com.everee.api.companyrole.CompanyRole;
import com.everee.api.companyrole.CompanyRoleType;
import com.everee.api.employee.DetailedEmployee;
import com.everee.api.money.Money;
import com.everee.api.quarterlies.Quarter;
import com.everee.api.quarterlies.suta.icesa.state.IL_IcesaQuarterlyStateGenerator;
import com.everee.api.quarterlies.suta.mmref.EmployeeTaxSummary;
import com.everee.api.tax.filing.bulk.exporter.TaxFilingExporterContext;
import com.everee.api.tax.filing.generate.TaxFilingEmployeeTaxSummaries;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.state.State;
import com.everee.api.user.CoreUser;
import com.everee.api.user.DetailedUserRepository;
import com.everee.api.user.UserService;
import com.everee.api.util.MockClockUtil;
import java.io.ByteArrayOutputStream;
import java.time.Clock;
import java.time.LocalDate;
import java.time.Year;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class IcesaQuarterlyGeneratorTests {

  @Mock CompanyService companyService;
  @Mock CompanyUnemploymentRatesService companyUnemploymentRatesService;
  @Mock DetailedUserRepository detailedUserRepository;
  @Mock UserService userService;
  @Mock Clock clock;

  @InjectMocks IcesaQuarterlyGenerator icesaQuarterlyGenerator;

  List<DetailedEmployee> employees = new ArrayList<>();

  @BeforeEach
  public void beforeEach() {
    icesaQuarterlyGenerator.setStateGenerators(List.of(new IL_IcesaQuarterlyStateGenerator()));
    MockClockUtil.mockClock(clock, LocalDate.parse("2023-06-03"));

    when(companyService.getCompany(100L))
        .thenReturn(
            new DetailedCompany()
                .setLegalEntityName("Everee Inc")
                .setFederalEin("*********")
                .setAddresses(
                    Set.of(
                        new CompanyAddress()
                            .setStartDate(LocalDate.parse("2019-01-01"))
                            .setLine1("26 Rio Grande St.")
                            .setLine2("#2072")
                            .setCity("Salt Lake City")
                            .setState(UT)
                            .setPostalCode("84101")))
                .setId(100L));

    when(detailedUserRepository.streamAllByCompanyRole(
            any(Long.class), eq(CompanyRoleType.FINANCIAL_MANAGER)))
        .thenAnswer(
            a ->
                employees.stream()
                    .filter(
                        e ->
                            e.getCompanyId().equals(a.getArgument(0))
                                && e.getUser() != null
                                && e.getUser().getCompanyRoles() != null
                                && e.getUser().getCompanyRoles().stream()
                                    .anyMatch(r -> r.getType() == a.getArgument(1)))
                    .map(DetailedEmployee::getUser));

    when(companyUnemploymentRatesService.getUnemploymentRates(anyLong(), any(), any()))
        .thenReturn(new CompanyUnemploymentRates().setSuiRate(Double.valueOf(1.02d)));
  }

  @Test
  @SneakyThrows
  void generateExport() {
    var state = IL;
    var year = Year.of(2023);
    var quarter = Quarter.Q2;

    var company =
        new DetailedCompany()
            .setLegalEntityName("OfferSpace LLC")
            .setFederalEin("*********")
            .setPhone("**********")
            .setCompanySutaInfo(
                Map.of(state, new CompanyTaxJurisdiction().setAccountNumber("5128281")))
            .setAddresses(
                Set.of(
                    new CompanyAddress()
                        .setStartDate(LocalDate.parse("2020-04-01"))
                        .setLine1("1261 S 820 E")
                        .setLine2("210")
                        .setCity("American Fork")
                        .setState(state)
                        .setPostalCode("84003")))
            .setId(208L);
    var evereeFinManager =
        emp(
            100L,
            100L,
            "Michael",
            "Ron",
            "Ross",
            null,
            null,
            null,
            null,
            null,
            null,
            Set.of(new CompanyRole().setType(CompanyRoleType.FINANCIAL_MANAGER)));
    evereeFinManager.getUser().setEmail("<EMAIL>").setPhoneNumber("************");
    employees.add(evereeFinManager);

    var taxSummaries = createTaxSummaries(company, year, quarter, state);

    var baos = new ByteArrayOutputStream();
    try (var exportContext =
        new TaxFilingExporterContext(baos).setState(state).setQuarter(quarter).setYear(year)) {
      icesaQuarterlyGenerator.generate(exportContext, company, taxSummaries);
    }

    compareOutputToExpectedExportData(
        this.getClass().getResourceAsStream("2023_IL-IceSaQuarterly-OfferSpace_test.txt"),
        Optional.of(baos));
  }

  private List<EmployeeTaxSummary> createTaxSummaries(
      DetailedCompany company, Year year, Quarter quarter, State state) {
    return List.of(
        new TaxFilingEmployeeTaxSummaries()
            .setUser(new CoreUser().setLastName("Bodee").setFirstName("Jumper"))
            .setCompany(company)
            .setYear(year)
            .setQuarter(quarter)
            .setState(state)
            .setMonthsEmployed(quarter.getMonths())
            .setTotalWages(Money.valueOf("15000.99"))
            .setTotalSubjectWages(Money.valueOf("14000.22"))
            .setSubjectWages(Money.valueOf("5000.21"))
            .setTaxAmount(Money.valueOf("301.23")),
        new TaxFilingEmployeeTaxSummaries()
            .setUser(new CoreUser().setLastName("Inkling").setFirstName("Justin"))
            .setCompany(company)
            .setYear(year)
            .setQuarter(quarter)
            .setState(state)
            .setMonthsEmployed(List.of(quarter.getMonth1(), quarter.getMonth2()))
            .setTotalWages(Money.valueOf("11000.46"))
            .setTotalSubjectWages(Money.valueOf("11000.46"))
            .setSubjectWages(Money.valueOf("11000.46"))
            .setTaxAmount(Money.valueOf("501.64")));
  }
}
