package com.everee.api.taxaccumulator.export.state;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.everee.api.ach.AchBankAccount;
import com.everee.api.company.CompanyAchConfiguration;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.money.Money;
import com.everee.api.tax.FilingFrequency;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.state.State;
import com.everee.api.taxaccumulator.TaxTypeAccumulationGroup;
import com.everee.api.taxaccumulator.TaxTypeAccumulationGroupService;
import com.everee.api.taxaccumulator.TaxTypeAccumulationTaxesGroupingStrategyType;
import com.everee.api.taxaccumulator.export.TaxExportType;
import com.everee.api.taxaccumulator.export.exception.TaxTypeAccumulationExportError;
import com.everee.api.taxaccumulator.lookup.TaxTypeAccumulationLookup;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

@ExtendWith(MockitoExtension.class)
public class UtahStateExportServiceTests {
  @Mock TaxTypeAccumulationGroupService taxTypeAccumulationGroupService;
  @Mock CompanyService companyService;

  @InjectMocks UtahStateExportService service;

  private Map<Long, DetailedCompany> companyMap = new HashMap<>();
  private List<TaxTypeAccumulationGroup> taxTypeAccumulationGroups = new ArrayList<>();

  @BeforeEach
  public void beforeEach() {
    when(companyService.getCompany(any(Long.class)))
        .thenAnswer(a -> companyMap.get(a.getArgument(0)));
    when(taxTypeAccumulationGroupService.listGroups(
            any(TaxTypeAccumulationTaxesGroupingStrategyType.class),
            any(Sort.Direction.class),
            any(Pageable.class),
            any(TaxTypeAccumulationLookup.class)))
        .thenAnswer(a -> new PageImpl<>(taxTypeAccumulationGroups));

    var companySitInfo = new CompanyTaxJurisdiction();
    companySitInfo.setAccountNumber("WTH-**********");
    companySitInfo.setFilingFrequencies(
        new LinkedHashSet<>(List.of(FilingFrequency.ANNUAL, FilingFrequency.QUARTERLY)));
    companySitInfo.setState(State.UT);

    var achBankAccount = new AchBankAccount();
    achBankAccount.setAccountNumber("********");
    achBankAccount.setRoutingNumber("********");
    var companyAchConfiguration = new CompanyAchConfiguration();
    companyAchConfiguration.setTaxLockboxAccount(achBankAccount);

    var company = new DetailedCompany();
    company.setId(123l);
    company.setFederalEin("82-3926827");
    company.setDisplayName("Test Company");
    company.setCompanySitInfo(Map.of(State.UT, companySitInfo));
    company.setCompanyAchConfiguration(companyAchConfiguration);
    companyMap.put(company.getId(), company);
  }

  @Test
  public void generateRecord_QUARTERLY() {
    LocalDate forDate = LocalDate.of(2020, 6, 15);

    taxTypeAccumulationGroups.add(
        new TaxTypeAccumulationGroup()
            .setCompanyId(123l)
            .setNet(Money.valueOf("867.70"))
            .setFromDate(forDate)
            .setToDate(forDate));

    List<UtahStateExportService.UtahExportRecord> records =
        service.generateExportLines(
            TaxTypeAccumulationTaxesGroupingStrategyType.BASE_TAX_TYPE,
            Sort.Direction.ASC,
            Pageable.unpaged(),
            new TaxTypeAccumulationLookup(),
            forDate);

    records.forEach(record -> System.out.println(record.toString()));
    assertThat(records).hasSize(1);
    assertThat(records.get(0).getAmount()).isEqualTo(Money.valueOf("867.70"));
    assertThat(records.get(0).getFilingPeriodEnd()).isEqualTo(LocalDate.of(2020, 6, 30));
  }

  @Test
  public void generateRecord_MONTHLY() {
    LocalDate forDate = LocalDate.of(2020, 7, 15);
    companyMap
        .get(123l)
        .getCompanySitInfo()
        .get(State.UT)
        .setFilingFrequencies(Set.of(FilingFrequency.MONTHLY));

    taxTypeAccumulationGroups.add(
        new TaxTypeAccumulationGroup()
            .setCompanyId(123l)
            .setNet(Money.valueOf("867.70"))
            .setFromDate(forDate)
            .setToDate(forDate));

    List<UtahStateExportService.UtahExportRecord> records =
        service.generateExportLines(
            TaxTypeAccumulationTaxesGroupingStrategyType.BASE_TAX_TYPE,
            Sort.Direction.ASC,
            Pageable.unpaged(),
            new TaxTypeAccumulationLookup(),
            forDate);

    records.forEach(record -> System.out.println(record.toString()));
    assertThat(records).hasSize(1);
    assertThat(records.get(0).getAmount()).isEqualTo(Money.valueOf("867.70"));
    assertThat(records.get(0).getFilingPeriodEnd()).isEqualTo(LocalDate.of(2020, 7, 31));
  }

  @Test
  public void generateRecord_ANNUALY() {
    LocalDate forDate = LocalDate.of(2020, 6, 15);
    companyMap
        .get(123l)
        .getCompanySitInfo()
        .get(State.UT)
        .setFilingFrequencies(Set.of(FilingFrequency.ANNUAL));

    taxTypeAccumulationGroups.add(
        new TaxTypeAccumulationGroup()
            .setCompanyId(123l)
            .setNet(Money.valueOf("867.70"))
            .setFromDate(forDate)
            .setToDate(forDate));

    List<UtahStateExportService.UtahExportRecord> records =
        service.generateExportLines(
            TaxTypeAccumulationTaxesGroupingStrategyType.BASE_TAX_TYPE,
            Sort.Direction.ASC,
            Pageable.unpaged(),
            new TaxTypeAccumulationLookup(),
            forDate);

    records.forEach(record -> System.out.println(record.toString()));
    assertThat(records).hasSize(1);
    assertThat(records.get(0).getAmount()).isEqualTo(Money.valueOf("867.70"));
    assertThat(records.get(0).getFilingPeriodEnd()).isEqualTo(LocalDate.of(2020, 12, 31));
  }

  @Test
  public void generateRecord_noStateInfo() {
    LocalDate forDate = LocalDate.of(2020, 6, 15);
    companyMap.get(123l).setCompanySitInfo(Map.of());

    taxTypeAccumulationGroups.add(
        new TaxTypeAccumulationGroup()
            .setCompanyId(123l)
            .setNet(Money.valueOf("867.70"))
            .setFromDate(forDate)
            .setToDate(forDate));

    assertThrows(
        TaxTypeAccumulationExportError.class,
        () ->
            service.generateExportLines(
                TaxTypeAccumulationTaxesGroupingStrategyType.BASE_TAX_TYPE,
                Sort.Direction.ASC,
                Pageable.unpaged(),
                new TaxTypeAccumulationLookup(),
                forDate));
  }

  @Test
  public void generateRecord_noFilingFrequency() {
    LocalDate forDate = LocalDate.of(2020, 6, 15);
    companyMap.get(123l).getCompanySitInfo().get(State.UT).setFilingFrequencies(Set.of());

    taxTypeAccumulationGroups.add(
        new TaxTypeAccumulationGroup()
            .setCompanyId(123l)
            .setNet(Money.valueOf("867.70"))
            .setFromDate(forDate)
            .setToDate(forDate));

    assertThrows(
        TaxTypeAccumulationExportError.class,
        () ->
            service.generateExportLines(
                TaxTypeAccumulationTaxesGroupingStrategyType.BASE_TAX_TYPE,
                Sort.Direction.ASC,
                Pageable.unpaged(),
                new TaxTypeAccumulationLookup(),
                forDate));
  }

  @Test
  @Disabled
  public void generateFile() throws IOException {
    LocalDate forDate = LocalDate.of(2020, 8, 15);

    for (int i = 0; i < 80; i++) {
      taxTypeAccumulationGroups.add(
          new TaxTypeAccumulationGroup()
              .setCompanyId(123l)
              .setNet(Money.valueOf("867.70"))
              .setFromDate(forDate)
              .setToDate(forDate));
    }

    var exportData =
        service.generateExport(
            TaxExportType.STATE_UT,
            TaxTypeAccumulationTaxesGroupingStrategyType.BASE_TAX_TYPE,
            Sort.Direction.ASC,
            Pageable.unpaged(),
            new TaxTypeAccumulationLookup(),
            forDate);

    var filePath = Path.of("/tmp", exportData.getFilename());
    System.out.println("Writing file: " + filePath.toString());
    Files.write(filePath, exportData.getContent());
  }
}
