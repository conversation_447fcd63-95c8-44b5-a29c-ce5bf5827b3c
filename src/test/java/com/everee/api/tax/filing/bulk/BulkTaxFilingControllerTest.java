package com.everee.api.tax.filing.bulk;

import static com.everee.api.util.ObjectToJsonBytes.convertObjectToJsonBytes;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.everee.api.BaseControllerTestsV2;
import com.everee.api.config.PresignedInternalFileLocationSerializer;
import com.everee.api.exception.InvalidRequestException;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.filing.form.CompanyTaxFormFilingInstance;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

@WebMvcTest(BulkTaxFilingController.class)
public class BulkTaxFilingControllerTest extends BaseControllerTestsV2 {

  @Autowired private MockMvc mockMvc;
  @MockBean private BulkTaxFormFilingInstanceRepository bulkTaxFormFilingInstanceRepository;

  @MockBean
  private BulkTaxFilingCompanySubmissionStatusRepository
      bulkTaxFilingCompanySubmissionStatusRepository;

  @MockBean private BulkTaxFilingService bulkTaxFilingService;
  @MockBean private StorageService storageService;
  @MockBean private PresignedInternalFileLocationSerializer presignedInternalFileLocationSerializer;
  @MockBean private ApplicationEventPublisher eventPublisher;

  @Captor ArgumentCaptor<MultipartFile[]> filesCaptor;

  private static final String BASE_PATH = "/api-private/v1/bulk-tax-filings";
  private static final long BULK_TAX_FILING_ID = 123L;

  @Test
  public void listBulkTaxFilings() throws Exception {
    setDefaultContextUser();

    var bulkTaxFiling = createBulkTaxFormFilingInstance();
    when(bulkTaxFormFilingInstanceRepository.findAll(any(Pageable.class)))
        .thenReturn(new PageImpl<>(List.of(bulkTaxFiling)));

    mockMvc
        .perform(get(BASE_PATH).accept("application/json").param("page", "0").param("size", "10"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.items[0].id", is(bulkTaxFiling.getId().intValue())));
  }

  @Test
  public void getBulkTaxFiling() throws Exception {
    setDefaultContextUser();

    var bulkTaxFiling = createBulkTaxFormFilingInstance();
    when(bulkTaxFilingService.getById(any(Long.class))).thenReturn(bulkTaxFiling);

    mockMvc
        .perform(get(BASE_PATH + "/" + BULK_TAX_FILING_ID).accept("application/json"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id", is(bulkTaxFiling.getId().intValue())));
  }

  @Test
  public void getBulkTaxFiling_missing() throws Exception {
    setDefaultContextUser();

    when(bulkTaxFilingService.getById(any(Long.class)))
        .thenThrow(new InvalidRequestException("exception message"));

    mockMvc
        .perform(get(BASE_PATH + "/" + BULK_TAX_FILING_ID).accept("application/json"))
        .andExpect(status().is4xxClientError())
        .andExpect(jsonPath("$.errorMessage", is("exception message")));
  }

  @Test
  public void generateBulkTaxFiling() throws Exception {
    setDefaultContextUser();

    var bulkTaxFiling = createBulkTaxFormFilingInstance();
    when(bulkTaxFormFilingInstanceRepository.saveAndFlush(any(BulkTaxFormFilingInstance.class)))
        .thenReturn(bulkTaxFiling);
    when(bulkTaxFormFilingInstanceRepository.getOne(any()))
      .thenReturn(bulkTaxFiling);

    var generationParams = new BulkTaxFilingGenerationParams().setTaxFilingFormDefinitionId(1L);
    mockMvc
        .perform(
            post(BASE_PATH)
                .contentType("application/json")
                .content(convertObjectToJsonBytes(generationParams))
                .accept("application/json"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id", is(bulkTaxFiling.getId().intValue())));

    verify(bulkTaxFormFilingInstanceRepository).saveAndFlush(any(BulkTaxFormFilingInstance.class));
  }

  @Test
  public void fileBulkTaxFiling() throws Exception {
    setDefaultContextUser();

    var employerIds = List.of("123", "456");

    mockMvc
        .perform(
            post(BASE_PATH + "/" + BULK_TAX_FILING_ID)
                .contentType("application/json")
                .content(convertObjectToJsonBytes(employerIds))
                .accept("application/json"))
        .andExpect(status().isOk());
  }

  @Test
  public void listFilingInstancesByBulkTaxFilingId() throws Exception {
    setDefaultContextUser();
    when(bulkTaxFilingCompanySubmissionStatusRepository.findByBulkTaxFormFilingInstanceId(
            anyLong(), any()))
        .thenReturn(new PageImpl<>(List.of(submissionStatus(1L), submissionStatus(2L))));

    mockMvc
        .perform(get(BASE_PATH + "/instances/123").accept("application/json"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.items.length()", is(2)))
        .andExpect(jsonPath("$.items[0].id", is(1)));
  }

  @Test
  public void listBulkTaxFilingCompanySubmissionStatuses() throws Exception {
    setDefaultContextUser();
    when(bulkTaxFilingCompanySubmissionStatusRepository.findByBulkTaxFormFilingInstanceId(
            anyLong(), any()))
        .thenReturn(new PageImpl<>(List.of(submissionStatus(1L), submissionStatus(2L))));

    mockMvc
        .perform(get(BASE_PATH + "/123/submissions").accept("application/json"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.items.length()", is(2)))
        .andExpect(jsonPath("$.items[0].id", is(1)));
  }

  @Test
  public void processResultsFiles() throws Exception {
    // Setup
    setDefaultContextUser();
    when(bulkTaxFilingService.processResultsFiles(anyLong(), filesCaptor.capture(), anyLong()))
        .thenReturn(new BulkTaxFormFilingInstance().setId(31L));
    var firstFile =
        new MockMultipartFile("files", "firstFile.txt", "text/plain", "some content".getBytes());
    var secondFile =
        new MockMultipartFile("files", "secondFile.txt", "text/plain", "some content".getBytes());
    var thirdFile =
        new MockMultipartFile("files", "thirdFile.txt", "text/plain", "some content".getBytes());

    // Perform
    mockMvc
        .perform(
            multipart(BASE_PATH + "/{bulkTaxFilingId}/results", 31L)
                .file(firstFile)
                .file(secondFile)
                .file(thirdFile))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id", is(31)));

    assertThat(filesCaptor.getValue())
        .hasSize(3)
        .extracting(MultipartFile::getName)
        .containsExactlyInAnyOrder(firstFile.getName(), secondFile.getName(), thirdFile.getName());
  }

  private BulkTaxFilingCompanySubmissionStatus submissionStatus(Long id) {
    var submissionStatus = new BulkTaxFilingCompanySubmissionStatus();
    submissionStatus.setId(id);
    submissionStatus.setCompanyTaxFormFilingInstance(new CompanyTaxFormFilingInstance().setId(id));
    return submissionStatus;
  }

  private BulkTaxFormFilingInstance createBulkTaxFormFilingInstance() {
    var params = new BulkTaxFilingGenerationParams();
    var bulkTaxFormFilingInstance = new BulkTaxFormFilingInstance(params);
    bulkTaxFormFilingInstance.setId(BULK_TAX_FILING_ID);
    return bulkTaxFormFilingInstance;
  }
}
