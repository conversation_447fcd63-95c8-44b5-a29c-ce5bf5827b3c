package com.everee.api.tax.filing.form.lookup;

import static com.everee.api.tax.filing.form.lookup.CompanyTaxFormFilingSummaryLookupType.FEDERAL;
import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.company.DetailedCompany;
import com.everee.api.company.DetailedCompanyRepository;
import com.everee.api.tax.FilingFrequency;
import com.everee.api.tax.TaxType;
import com.everee.api.tax.filing.form.CompanyTaxFormFilingInstance;
import com.everee.api.tax.filing.form.CompanyTaxFormFilingInstanceRepository;
import com.everee.api.tax.filing.job.CompanyTaxFormFilingGenerationJobService;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionRepository;
import com.everee.api.taxauthority.jurisdiction.TaxJurisdiction;
import com.everee.api.util.BooleanApiParam;
import com.everee.api.util.DbUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@AutoConfigureEmbeddedDatabase
@ActiveProfiles("integration-test")
@Disabled
public class CompanyTaxFormFilingSummaryServiceIntegrationTests {

  @Autowired private DbUtil dbUtil;

  @Autowired
  private CompanyTaxFormFilingSummaryLookupService companyTaxFormFilingSummaryLookupService;

  @Autowired private CompanyTaxFormFilingInstanceRepository companyTaxFormFilingInstanceRepository;
  @Autowired private CompanyTaxJurisdictionRepository companyTaxJurisdictionRepository;

  @Autowired
  private CompanyTaxFormFilingGenerationJobService companyTaxFormFilingGenerationJobService;

  @Autowired private CompanyTaxFormFilingSummaryService companyTaxFormFilingSummaryService;

  private final TaxJurisdiction FEDERAL_TAX_JURISDICTION1 =
      new TaxJurisdiction()
          .setId("00-000-0000-FIT-000")
          .setTaxType(TaxType.FIT)
          .setName("Federal Income Tax");
  private final TaxJurisdiction FEDERAL_TAX_JURISDICTION2 =
      new TaxJurisdiction()
          .setId("00-000-0000-FICA-000")
          .setTaxType(TaxType.FICA)
          .setName("Social Security");
  @Autowired private DetailedCompanyRepository detailedCompanyRepository;

  @BeforeEach
  public void setup() {
    companyTaxFormFilingInstanceRepository.deleteAll();
    companyTaxJurisdictionRepository.deleteAll();
  }

  @Test
  @Transactional
  public void setInstancesNotTemporary() {
    LocalDate filingPeriodStartDate = LocalDate.of(2022, 4, 1);
    var company1 = dbUtil.createCompany();

    var companyTaxJurisdiction =
        createCompanyTaxJurisdiction(
            company1, FEDERAL_TAX_JURISDICTION1, FilingFrequency.QUARTERLY, true, true);

    var instance =
        createCompanyTaxFormFilingInstance(
            company1,
            filingPeriodStartDate,
            FilingFrequency.QUARTERLY,
            true,
            true,
            companyTaxJurisdiction);

    var summaries =
        companyTaxFormFilingSummaryLookupService.listAll(
            new CompanyTaxFormFilingSummaryLookup()
                .setFilingPeriodStartDate(filingPeriodStartDate)
                .setStateOrFederal(FEDERAL)
                .setCompanyIds(Set.of(company1.getId()))
                .setComplete(BooleanApiParam.INCLUDE_ONLY),
            Pageable.unpaged());

    assertThat(summaries.getSize()).isEqualTo(1);
    var summary = summaries.getContent().get(0);

    companyTaxFormFilingSummaryService.setInstancesNotTemporary(summary.getId());

    instance = companyTaxFormFilingInstanceRepository.getOne(instance.getId());

    assertThat(instance).isNotNull();
    assertThat(instance.getIsTemporary()).isFalse();
  }

  @Test
  @Transactional
  public void companyEndDate() {
    LocalDate filingPeriodStartDateJanuary = LocalDate.of(2022, 1, 1);
    LocalDate filingPeriodStartDateApril = LocalDate.of(2022, 4, 1);
    LocalDate filingPeriodStartDateJuly = LocalDate.of(2022, 7, 1);
    var company1 = dbUtil.createCompany();

    var companyTaxJurisdiction =
        createCompanyTaxJurisdiction(
            company1, FEDERAL_TAX_JURISDICTION1, FilingFrequency.QUARTERLY, true, true);
    createCompanyTaxFormFilingInstance(
        company1,
        filingPeriodStartDateJanuary,
        FilingFrequency.QUARTERLY,
        true,
        true,
        companyTaxJurisdiction);
    createCompanyTaxFormFilingInstance(
        company1,
        filingPeriodStartDateApril,
        FilingFrequency.QUARTERLY,
        true,
        true,
        companyTaxJurisdiction);
    createCompanyTaxFormFilingInstance(
        company1,
        filingPeriodStartDateJuly,
        FilingFrequency.QUARTERLY,
        true,
        true,
        companyTaxJurisdiction);

    company1.setEndDate(LocalDate.of(2022, 6, 30));
    detailedCompanyRepository.saveAndFlush(company1);

    var summaries =
        companyTaxFormFilingSummaryLookupService.listAll(
            new CompanyTaxFormFilingSummaryLookup()
                .setStateOrFederal(FEDERAL)
                .setCompanyIds(Set.of(company1.getId()))
                .setComplete(BooleanApiParam.INCLUDE_ONLY),
            Pageable.unpaged());

    assertThat(summaries.getSize()).isEqualTo(3);
    for (var summary : summaries) {
      if (summary.getFilingPeriodStartDate().equals(filingPeriodStartDateJanuary)
          || summary.getFilingPeriodStartDate().equals(filingPeriodStartDateApril)) {
        assertThat(summary.getCompanyEndedBeforeFilingPeriod()).isFalse();
      } else {
        assertThat(summary.getCompanyEndedBeforeFilingPeriod()).isTrue();
      }
    }
  }

  private CompanyTaxJurisdiction createCompanyTaxJurisdiction(
      DetailedCompany company,
      TaxJurisdiction taxJurisdiction,
      FilingFrequency filingFrequency,
      boolean filedAt,
      boolean temporary) {
    dbUtil.findOrCreateTaxJurisdictions(List.of(taxJurisdiction));
    return companyTaxJurisdictionRepository.save(
        new CompanyTaxJurisdiction()
            .setCompanyId(company.getId())
            .setTaxJurisdiction(taxJurisdiction)
            .setFilingFrequencies(Set.of(filingFrequency)));
  }

  private CompanyTaxFormFilingInstance createCompanyTaxFormFilingInstance(
      DetailedCompany company,
      LocalDate filingPeriodStartDate,
      FilingFrequency filingFrequency,
      boolean filedAt,
      boolean temporary,
      CompanyTaxJurisdiction companyTaxJurisdiction) {
    return companyTaxFormFilingInstanceRepository.save(
        new CompanyTaxFormFilingInstance()
            .setCompanyId(company.getId())
            .setFilingPeriodStartDate(filingPeriodStartDate)
            .setFilingFrequency(filingFrequency)
            .setCompanyTaxJurisdictionId(companyTaxJurisdiction.getId())
            .setFiledAt(filedAt ? LocalDateTime.now() : null)
            .setIsTemporary(temporary));
  }
}
