package com.everee.api.tax.filing.bulk;

import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.PageRequest;
import org.junit.jupiter.api.Disabled;

@DataJpaTest
@AutoConfigureEmbeddedDatabase
@Disabled
class BulkTaxFormFilingInstanceRepositoryIntegrationTest {

  @Autowired private BulkTaxFormFilingInstanceRepository bulkTaxFormFilingInstanceRepository;

  @Test
  void testFindAll() {
    // The purpose of this test is to ensure that the @Formulas are correct on the
    // BulkTaxFormFilingInstance entity. If the formulas are incorrect, this test will fail.
    bulkTaxFormFilingInstanceRepository.findAll(PageRequest.of(0, 1));
  }
}
