package com.everee.api.tax.filing.contractor.fire.firerecord;

import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.tax.filing.contractor.FilingStatus;
import com.everee.api.tax.filing.contractor.fire.firefile.FireFileSubmissionStatus;
import com.everee.api.util.BaseTests;
import com.everee.api.util.DbUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.util.stream.Collectors;
import javax.transaction.Transactional;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

@DataJpaTest
@Transactional
@AutoConfigureEmbeddedDatabase
@ImportAutoConfiguration(DbUtil.class)
@Disabled
public class AFireRecordRepositoryIntegrationTests extends BaseTests {
  @Autowired AFireRecordRepository repository;
  @Autowired private DbUtil dbUtil;

  @Test
  public void streamFireFileReportData() {
    var company = dbUtil.createCompany();

    var employee1 = dbUtil.createEmployee(company);
    var employee2 = dbUtil.createEmployee(company);
    var employee3 = dbUtil.createEmployee(company);
    var employee4 = dbUtil.createEmployee(company);
    var employee5 = dbUtil.createEmployee(company);

    var ffs1 =
        dbUtil.createFireFileSubmission(
            ffs -> ffs.setSubmissionStatus(FireFileSubmissionStatus.GOOD).setFilename("TestFile1"));
    var ffs2 =
        dbUtil.createFireFileSubmission(
            ffs -> ffs.setSubmissionStatus(FireFileSubmissionStatus.BAD).setFilename("TestFile2"));
    var ffs3 =
        dbUtil.createFireFileSubmission(
            ffs ->
                ffs.setSubmissionStatus(FireFileSubmissionStatus.PROCESSING)
                    .setFilename("TestFile3"));
    var ffs4 =
        dbUtil.createFireFileSubmission(
            ffs -> ffs.setSubmissionStatus(FireFileSubmissionStatus.GOOD).setFilename("TestFile4"));
    var ffs5 = dbUtil.createFireFileSubmission(ffs -> ffs.setFilename("TestFile5"));

    var ctfs1 =
        dbUtil.createContractorTaxFilingStatus(
            2022, FilingStatus.FILED, FilingStatus.UNFILED, employee1);
    var ctfs2 =
        dbUtil.createContractorTaxFilingStatus(
            2022, FilingStatus.PROCESSING, FilingStatus.UNFILED, employee2);
    var ctfs3 =
        dbUtil.createContractorTaxFilingStatus(
            2022, FilingStatus.PROCESSING, FilingStatus.PROCESSING, employee3);
    var ctfs4 =
        dbUtil.createContractorTaxFilingStatus(
            2022, FilingStatus.FILED, FilingStatus.FILED, employee4);
    var ctfs5 =
        dbUtil.createContractorTaxFilingStatus(
            2022, FilingStatus.UNFILED, FilingStatus.UNFILED, employee5);

    dbUtil.createFireJoin(ctfs1, ffs1);
    dbUtil.createFireJoin(ctfs1, ffs4);
    dbUtil.createFireJoin(ctfs1, ffs5);
    dbUtil.createFireJoin(ctfs2, ffs2);
    dbUtil.createFireJoin(ctfs3, ffs3);
    dbUtil.createFireJoin(ctfs3, ffs4);
    dbUtil.createFireJoin(ctfs4, ffs1);
    dbUtil.createFireJoin(ctfs5, ffs5);

    var aRecord1 = dbUtil.createAFireRecord(company.getId(), ffs4.getId());
    var aRecord2 = dbUtil.createAFireRecord(company.getId(), ffs4.getId());

    dbUtil.createBFireRecord(1L, employee1.getId(), 601L, ffs1.getId());
    dbUtil.createBFireRecord(2L, employee1.getId(), 999L, ffs1.getId());
    var bRecord1 =
        dbUtil.createBFireRecord(aRecord1.getId(), employee1.getId(), 765L, ffs4.getId());
    var bRecord2 = dbUtil.createBFireRecord(aRecord2.getId(), employee1.getId(), 0L, ffs4.getId());
    dbUtil.createBFireRecord(5L, employee2.getId(), 612L, ffs2.getId());
    dbUtil.createBFireRecord(6L, employee3.getId(), 888L, ffs3.getId());
    dbUtil.createBFireRecord(7L, employee3.getId(), 777L, ffs4.getId());
    dbUtil.createBFireRecord(8L, employee4.getId(), 605L, ffs1.getId());
    dbUtil.createBFireRecord(9L, employee5.getId(), 876L, ffs5.getId());
    dbUtil.createBFireRecord(10L, employee1.getId(), 1000L, ffs5.getId());

    // only necessary in integration test context
    bRecord1.setAFireRecord(aRecord1);
    bRecord2.setAFireRecord(aRecord2);

    var response = repository.streamFireFileReportData(ffs4.getId()).collect(Collectors.toList());

    assertThat(response.size()).isEqualTo(4);
    assertThat(response.get(0).getFirstIssuerName()).isEqualTo("Pay Your Way Co");
    assertThat(response.get(0).getFirstPayeeName()).isEqualTo("Marys Flowers LLC");
  }
}
