package com.everee.api.tax.filing.form;

import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.company.DetailedCompany;
import com.everee.api.tax.FilingFrequency;
import com.everee.api.util.DbUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import javax.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@Transactional
@SpringBootTest
@AutoConfigureEmbeddedDatabase
@ActiveProfiles("integration-test")
class CompanyTaxFormFilingInstanceServiceIntegrationTest {

  @Autowired private DbUtil dbUtil;
  @Autowired private CompanyTaxFormFilingInstanceRepository companyTaxFormFilingInstanceRepository;
  @Autowired private TaxFilingFormDefinitionRepository taxFilingFormDefinitionRepository;
  @Autowired private CompanyTaxFormFilingInstanceService companyTaxFormFilingInstanceService;

  @Test
  void cleanupCompanyTaxFormFilingInstancesBeforeDate() {
    var date = LocalDate.of(2022, 9, 20);
    var company1 = dbUtil.createCompany();
    var company2 = dbUtil.createCompany();

    // shouldn't get deleted because of the company filter
    var instance1 = buildInstance(company2, FilingFrequency.MONTHLY, date.minusMonths(1));

    // Shouldn't get deleted because the date is in the current range
    var instance2 = buildInstance(company1, FilingFrequency.MONTHLY, date);

    // Shouldn't get deleted because it has been filed
    var instance3 =
        buildInstance(company1, FilingFrequency.MONTHLY, date.minusMonths(1), LocalDateTime.now());

    // Should get deleted
    var instance4 = buildInstance(company1, FilingFrequency.MONTHLY, date.minusMonths(1));
    var instance5 = buildInstance(company1, FilingFrequency.SEMI_MONTHLY, date.minusMonths(1));
    var instance6 = buildInstance(company1, FilingFrequency.QUARTERLY, date.minusMonths(4));
    var instance7 = buildInstance(company1, FilingFrequency.ANNUAL, date.minusYears(1));

    companyTaxFormFilingInstanceService.cleanupCompanyTaxFormFilingInstancesBeforeDate(
        company1.getId(), date);

    var shouldContainItems =
        companyTaxFormFilingInstanceRepository.findAllById(
            List.of(instance1.getId(), instance2.getId(), instance3.getId()));

    var shouldBeEmpty =
        companyTaxFormFilingInstanceRepository.findAllById(
            List.of(instance4.getId(), instance5.getId(), instance6.getId(), instance7.getId()));

    assertThat(shouldContainItems).size().isEqualTo(3);
    assertThat(shouldBeEmpty).isEmpty();
  }

  private CompanyTaxFormFilingInstance buildInstance(
      DetailedCompany company, FilingFrequency frequency, LocalDate date) {
    return buildInstance(company, frequency, date, null);
  }

  private CompanyTaxFormFilingInstance buildInstance(
      DetailedCompany company, FilingFrequency frequency, LocalDate date, LocalDateTime filedAt) {
    var dateRange = frequency.getDateRange(date);
    var definition =
        taxFilingFormDefinitionRepository.save(
            new TaxFilingFormDefinition()
                .setTaxFormName("Test Definition")
                .setStartDate(dateRange.getStartDate()));

    var instance =
        new CompanyTaxFormFilingInstance()
            .setCompanyId(company.getId())
            .setFilingPeriodStartDate(dateRange.getStartDate())
            .setFilingFrequency(frequency)
            .setTaxFilingFormDefinition(definition)
            .setTaxFilingFormDefinitionId(definition.getId());

    if (filedAt != null) {
      instance.setFiledAt(filedAt);
    }

    return companyTaxFormFilingInstanceRepository.save(instance);
  }
}
