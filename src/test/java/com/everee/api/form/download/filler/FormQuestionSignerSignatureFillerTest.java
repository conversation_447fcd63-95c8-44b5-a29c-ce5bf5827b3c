package com.everee.api.form.download.filler;

import static com.everee.api.form.download.filler.TestFillerUtils.context;
import static com.everee.api.form.download.filler.TestFillerUtils.mapping;
import static org.assertj.core.api.Assertions.assertThat;

import com.everee.api.form.download.PdfFillerFieldDataType;
import org.junit.jupiter.api.Test;

public class FormQuestionSignerSignatureFillerTest {
  FormQuestionSignerSignatureFiller filler = new FormQuestionSignerSignatureFiller();

  @Test
  public void getFieldType() {
    assertThat(filler.getFieldType())
        .isEqualTo(PdfFillerFieldDataType.FORM_QUESTION_SIGNATURE_SIGNATURE);
  }

  @Test
  public void getValue() {
    assertThat(filler.getValue(mapping(), context())).isEqualTo(null);
  }
}
