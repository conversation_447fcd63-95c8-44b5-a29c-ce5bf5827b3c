package com.everee.api.report;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.Map;
import java.util.function.Consumer;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.util.Pair;

/** http://makeseleniumeasy.com/2020/05/17/compare-two-excel-workbooks-using-apache-poi/ */
@Slf4j
public class XlsxCompareUtils {

  public static void verifyDataInExcelBookAllSheets(Workbook expected, Workbook actual) {
    verifyDataInExcelBookAllSheets(expected, actual, Map.of());
  }

  /**
   * @param expected
   * @param actual
   * @param validatorsByColumn - allows validating specific columns different ways. Should be used
   *     in rare cases
   */
  public static void verifyDataInExcelBookAllSheets(
      @NotNull Workbook expected,
      @NotNull Workbook actual,
      @NotNull Map<Integer, Consumer<Pair<Cell, Cell>>> overrideValidatorsByColumn) {
    log.info("Verifying if both work books have same data.............");

    // Since we have already verified that both work books have same number of sheets so iteration
    // can be done against any workbook sheet count
    var sheetCounts = expected.getNumberOfSheets();

    // So we will iterate through sheet by sheet
    for (var i = 0; i < sheetCounts; i++) {
      // Get sheet at same index of both work books
      var expectedSheet = expected.getSheetAt(i);
      var actualSheet = actual.getSheetAt(i);
      log.info("*********** Sheet Name : " + expectedSheet.getSheetName() + "*************");

      // Iterating through each row
      var rowCounts = expectedSheet.getPhysicalNumberOfRows();
      for (var rowIdx = 0; rowIdx < rowCounts; rowIdx++) {
        // Iterating through each cell
        var expectedRow = expectedSheet.getRow(rowIdx);
        var actualRow = actualSheet.getRow(rowIdx);

        var expectedRowIsEmpty = expectedRow == null || expectedRow.getPhysicalNumberOfCells() == 0;
        var actualRowIsEmpty = actualRow == null || actualRow.getPhysicalNumberOfCells() == 0;

        if (expectedRowIsEmpty && actualRowIsEmpty) {
          continue;
        }

        if (expectedRowIsEmpty) {
          fail(
              String.format(
                  "Sheet %s at row %s: expected is empty but found is not empty.",
                  expectedSheet.getSheetName(), rowIdx));
        } else if (actualRowIsEmpty) {
          fail(
              String.format(
                  "Sheet %s at row %s: expected is not empty but found is empty.",
                  expectedSheet.getSheetName(), rowIdx));
        }

        var cellCounts = expectedRow.getPhysicalNumberOfCells();
        for (var colIdx = 0; colIdx < cellCounts; colIdx++) {
          // Getting individual cell
          var expectedCell = expectedRow.getCell(colIdx);
          var actualCell = actualRow.getCell(colIdx);

          var validator =
              overrideValidatorsByColumn.getOrDefault(colIdx, XlsxCompareUtils::defaultValidator);
          validator.accept(Pair.of(expectedCell, actualCell));
        }
      }
    }
  }

  private static void defaultValidator(Pair<Cell, Cell> cells) {
    var expectedCell = cells.getFirst();
    var actualCell = cells.getSecond();

    var rowIdx = expectedCell.getRowIndex();
    var colIdx = expectedCell.getColumnIndex();

    // Since cell have types and need to use different methods
    if (expectedCell.getCellType().equals(actualCell.getCellType())) {

      if (expectedCell.getCellType() == CellType.STRING) {
        var expectedVal = expectedCell.getStringCellValue();
        var actualVal = actualCell.getStringCellValue();
        assertEquals(
            expectedVal,
            actualVal,
            "Cell values are different at row " + rowIdx + " and col " + colIdx);
      } else if (expectedCell.getCellType() == CellType.NUMERIC) {
        // If cell type is numeric, we need to check if data is of Date type
        if (DateUtil.isCellDateFormatted(expectedCell) | DateUtil.isCellDateFormatted(actualCell)) {
          // Need to use DataFormatter to get data in given style otherwise it will come as
          // time stamp
          DataFormatter df = new DataFormatter();
          var expectedVal = df.formatCellValue(expectedCell);
          var actualVal = df.formatCellValue(actualCell);
          assertEquals(
              expectedVal,
              actualVal,
              "Cell values are different at row " + rowIdx + " and col " + colIdx);
        } else {
          var expectedVal = expectedCell.getNumericCellValue();
          var actualVal = actualCell.getNumericCellValue();
          assertEquals(
              expectedVal,
              actualVal,
              "Cell values are different at row " + rowIdx + " and col " + colIdx);
        }
      } else if (expectedCell.getCellType() == CellType.BOOLEAN) {
        var expectedVal = expectedCell.getBooleanCellValue();
        var actualVal = actualCell.getBooleanCellValue();
        assertEquals(
            expectedVal,
            actualVal,
            "Cell values are different at row " + rowIdx + " and col " + colIdx);
      } else if (expectedCell.getCellType() == CellType.BLANK) {
        // both cells are blank, so this is good
      } else {
        fail(
            "Don't know how to compare cell type "
                + expectedCell.getCellType()
                + " at row "
                + rowIdx
                + " and col "
                + colIdx);
      }
    } else {
      // If cell types are not same, exit comparison
      fail("Non matching cell type at row " + rowIdx + " and col " + colIdx);
    }
  }
}
