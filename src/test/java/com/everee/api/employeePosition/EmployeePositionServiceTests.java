package com.everee.api.employeePosition;

import static com.everee.api.util.DateUtil.UTC_ZONE_ID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.everee.api.employee.DetailedEmployee;
import com.everee.api.employee.EmployeeWasTerminatedBeforeEffectiveDateException;
import com.everee.api.i18n.MessageSourceHolder;
import com.everee.api.labor.workedshift.WorkedShiftLookupService;
import com.everee.api.model.EmploymentType;
import com.everee.api.model.PayType;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentRepository;
import com.everee.api.phase.exception.PhasedDataStartsInInvalidRangeException;
import com.everee.api.time.WorkerLocalTimeService;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import com.everee.api.worker.updateparams.WorkerPositionsParamsForUpdate;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Import;

@SpringBootTest(classes = {MessageSource.class, MessageSourceHolder.class})
@Import(MessageSourceAutoConfiguration.class)
public class EmployeePositionServiceTests {
  private static final Long companyId = 1L;
  private static final Long employeeId = 1L;

  @Mock private PaymentRepository paymentRepository;
  @Mock private WorkerLocalTimeService workerLocalTimeService;
  @Mock private WorkedShiftLookupService workedShiftLookupService;
  @Mock private ApplicationEventPublisher eventPublisher;

  @InjectMocks private EmployeePositionService employeePositionService;

  @Test
  public void testGetEarliestExpiryDate() {
    var startDate = LocalDate.of(2021, 4, 11);
    var paymentDate = startDate.plusDays(10);
    var employee = mockEmployee(startDate);

    when(paymentRepository.findMostRecentPaymentDate(eq(employeeId), any(), any()))
        .thenReturn(Optional.of(paymentDate))
        .thenReturn(Optional.empty());

    var response = employeePositionService.getEarliestExpiryDate(employee);

    assertThat(response).as("Existing payment date should be returned").isEqualTo(paymentDate);

    response = employeePositionService.getEarliestExpiryDate(employee);

    assertThat(response)
        .as("Missing payment should return day before hire date")
        .isEqualTo(employee.getStartDate().minusDays(1));
  }

  @Test
  public void testGetEarliestExpiryDate_workedShifts() {
    var startDate = LocalDate.of(2021, 4, 11);
    var workedShiftDate = startDate.plusDays(10);
    var employee = mockEmployee(startDate);

    when(workedShiftLookupService.findMostRecentWorkedShiftEndDate(any()))
        .thenReturn(workedShiftDate)
        .thenReturn(null);

    var response = employeePositionService.getEarliestExpiryDate(employee);

    assertThat(response)
        .as("Existing worked shift date should be returned")
        .isEqualTo(workedShiftDate);

    response = employeePositionService.getEarliestExpiryDate(employee);

    assertThat(response)
        .as("Missing shifts and payments should return day before hire date")
        .isEqualTo(employee.getStartDate().minusDays(1));
  }

  @Test
  public void testValidateEffectiveDate() {
    var startDate = LocalDate.of(2021, 4, 11);
    var endDate = startDate.plusDays(20);
    var paymentDate = startDate.plusDays(10);
    var employee = mockEmployee(startDate).setEndDate(endDate);

    when(paymentRepository.findMostRecentPaymentDate(eq(employeeId), any(), any()))
        .thenReturn(Optional.of(paymentDate));

    assertThatThrownBy(
            () -> employeePositionService.validateEffectiveDate(endDate.plusDays(5), employee))
        .as("Dates after an employee ends aren't valid")
        .isInstanceOf(EmployeeWasTerminatedBeforeEffectiveDateException.class);
    assertThatThrownBy(
            () -> employeePositionService.validateEffectiveDate(paymentDate.minusDays(5), employee))
        .as("Dates before most recent payment date aren't valid")
        .isInstanceOf(PhasedDataStartsInInvalidRangeException.class);

    employeePositionService.validateEffectiveDate(paymentDate.plusDays(5), employee);
    // last call shouldn't throw
  }

    @Test
    public void testUpdateEmployeePositions()
    {
        // Arrange
        var startDate = LocalDate.of(2024, 1, 10);
        var earliestExpiryDate = startDate.minusDays(50);
        var newPosition = mockEmployeePosition(startDate.plusDays(10));
        var employee = mockEmployee(startDate);
        var params = List.of(new WorkerPositionsParamsForUpdate());

        employee.setPositions(new HashSet<>(Set.of(mockEmployeePosition(startDate.plusDays(5)))));
        params.get(0).setPayRate(newPosition.getPayRate());
        params.get(0).setTitle(newPosition.getTitle());
        params.get(0).setPayType(newPosition.getPayType());
        params.get(0).setEffectiveDate(newPosition.getStartDate());
        params.get(0).setEndDate(newPosition.getEndDate());
        params.get(0).setExpectedWeeklyHours(40);
        params.get(0).setEligibleForOvertime(true);

        when(paymentRepository.findMostRecentPaymentDate(eq(employeeId), any(), any()))
                .thenReturn(Optional.of(earliestExpiryDate));

        when(workerLocalTimeService.zonedTimestamp(eq(employee), any(ZonedDateTime.class)))
                .thenReturn(earliestExpiryDate.atStartOfDay().atZone(UTC_ZONE_ID));

        // Act
        var response = employeePositionService.updateEmployeePositions(employee, params);

        // Assert
        assertThat(response).as("Response should not be null").isNotNull();
    }

  @Test
  public void testIsInitialRecord() {
    var startDate = LocalDate.of(2021, 4, 11);
    var employee = mockEmployee(startDate);
    var scheduledRecord =
        mockEmployeePosition(startDate.plusDays(10)).setEmployeeId(employee.getId());
    var initialRecord =
        mockEmployeePosition(startDate)
            .setEmployeeId(employee.getId())
            .setEndDate(scheduledRecord.getStartDate().minusDays(1));
    var records = new HashSet<>(Set.of(initialRecord, scheduledRecord));
    employee.setPositions(records);

    var initialResponse = employeePositionService.isInitialRecord(initialRecord, employee);
    var scheduledResponse = employeePositionService.isInitialRecord(scheduledRecord, employee);

    assertThat(initialResponse).as("Initial record should return true").isTrue();
    assertThat(scheduledResponse).as("Upcoming record should return false").isFalse();
  }

  @Test
  public void testSetInitialRecordAndStartDate() {
    var startDate = LocalDate.of(2021, 4, 11);
    var employee = mockEmployee(startDate);
    var initialRecord =
        mockEmployeePosition(startDate.minusDays(17)).setEmployeeId(employee.getId());
    var records = new HashSet<>(Set.of(initialRecord));
    employee.setPositions(records);

    employeePositionService.setInitialRecordAndStartDate(initialRecord, employee);

    assertThat(initialRecord.getStartDate())
        .as("Initial record startDate should match employee startDate")
        .isEqualTo(employee.getStartDate());
  }

  @Test
  public void testUpdateExpiryDate() {
    var startDate = LocalDate.of(2021, 4, 11);
    var endDate = startDate.plusDays(15);
    var employee = mockEmployee(startDate);
    var initialRecord = mockEmployeePosition(startDate).setEmployeeId(employee.getId());
    var records = new HashSet<>(Set.of(initialRecord));
    employee.setPositions(records);

    employeePositionService.updateExpiryDate(initialRecord, endDate);

    assertThat(initialRecord.getEndDate()).as("Record's endDate gets updated").isEqualTo(endDate);

    employeePositionService.updateExpiryDate(initialRecord, null);

    assertThat(initialRecord.getEndDate()).as("Record's endDate gets updated to null").isNull();
  }

  @Test
  public void testGetNowDate() {
    var startDate = LocalDate.of(2021, 4, 11);
    var nowDate = startDate.plusDays(35).atTime(5, 30).atZone(UTC_ZONE_ID);
    var employee = mockEmployee(startDate);

    when(workerLocalTimeService.zonedTimestamp(eq(employee), any(ZonedDateTime.class)))
        .thenReturn(nowDate);

    var response = employeePositionService.getNowDate(employee);

    assertThat(response)
        .as("Get now date should appropriately map the nowDate for the employee")
        .isEqualTo(nowDate.toLocalDate());
  }

  private DetailedEmployee mockEmployee(LocalDate startDate) {
    var employee = new DetailedEmployee();
    employee.setId(employeeId);
    employee.setStartDate(startDate);
    employee.setEmploymentType(EmploymentType.EMPLOYEE);
    employee.setCompanyId(companyId);

    return employee;
  }

  private EmployeePosition mockEmployeePosition(LocalDate startDate) {
    var employeePosition = new EmployeePosition();
    employeePosition.setId(1L);
    employeePosition.setPayRate(Money.ZERO);
    employeePosition.setTitle("Title");
    employeePosition.setStartDate(startDate);
    employeePosition.setPayType(PayType.SALARY);
    employeePosition.setEmployeeId(employeeId);
    employeePosition.setCompanyId(companyId);

    return employeePosition;
  }
}
