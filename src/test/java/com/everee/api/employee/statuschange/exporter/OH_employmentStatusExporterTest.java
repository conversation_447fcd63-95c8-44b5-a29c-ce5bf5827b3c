package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.EmploymentStatusExporterTestUtils.buildRecord;
import static com.everee.api.employee.statuschange.exporter.EmploymentStatusExporterTestUtils.compareOutputToExpectedExportData;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChangeType;
import com.everee.api.model.DateRange;
import com.everee.api.storage.StorageAccess;
import com.everee.api.storage.StorageService;
import com.everee.api.storage.StoredFileLink;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdiction;
import com.everee.api.tax.jurisdictions.CompanyTaxJurisdictionService;
import com.everee.api.tax.state.State;
import com.everee.api.user.address.HomeAddress;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OH_employmentStatusExporterTest {
  @Mock StorageService storageService;
  @Mock CompanyService companyService;
  @Mock EmployeeService employeeService;
  @Mock CompanyTaxJurisdictionService companyTaxJurisdictionService;
  @Mock Clock clock;

  @InjectMocks OH_employmentStatusExporter exporter;

  @BeforeEach
  public void beforeEach() {
    lenient()
        .when(companyService.getCompany(eq(1L)))
        .thenReturn(
            (DetailedCompany)
                new DetailedCompany()
                    .setDisplayName("Fake Company")
                    .setFederalEin("841-35-5722")
                    .setAddresses(
                        Set.of(
                            new CompanyAddress()
                                .setStartDate(LocalDate.MIN)
                                .setLine1("12 High Street")
                                .setCity("Columbus")
                                .setState(State.OH)
                                .setPostalCode("80202")))
                    .setId(1L));
    lenient()
        .when(companyTaxJurisdictionService.getStateSitRecord(eq(1L), eq(State.OH)))
        .thenReturn(Optional.of(new CompanyTaxJurisdiction().setAccountNumber("***********")));

    // tell your tests to return the specified LOCAL_DATE when calling LocalDate.now(clock)
    var fixedClock =
        Clock.fixed(
            LocalDate.of(2021, 8, 19).atStartOfDay(ZoneId.systemDefault()).toInstant(),
            ZoneId.systemDefault());
    lenient().doReturn(fixedClock.instant()).when(clock).instant();
    lenient().doReturn(fixedClock.getZone()).when(clock).getZone();
  }

  @Test
  public void getFileName() {
    assertThat(
            exporter.getFileName(
                List.of(), DateRange.of(LocalDate.of(2021, 7, 4), LocalDate.of(2021, 7, 8))))
        .isEqualTo("everee.OH.NEWHIRE.070821.txt");
    assertThat(
            exporter.getFileName(
                List.of(), DateRange.of(LocalDate.of(2021, 7, 4), LocalDate.of(2021, 7, 4))))
        .isEqualTo("everee.OH.NEWHIRE.070421.txt");
  }

  @Test
  public void getState() {
    assertThat(exporter.getState()).isEqualTo(State.OH);
  }

  @Test
  public void builExport() throws MalformedURLException {
    lenient()
        .when(
            storageService.storeFile(
                any(byte[].class),
                eq("OH_NewHires_20210704.txt"),
                eq(StorageAccess.EVEREE_INTERNAL),
                any(List.class)))
        .thenReturn("/some/path/OH_NewHires_20210704.txt");
    lenient()
        .when(
            storageService.getStoredFileLink(eq(StorageAccess.EVEREE_INTERNAL), any(String.class)))
        .thenReturn(new StoredFileLink(new URL("http://www.google.com"), Instant.MAX));

    exporter.buildExport(
        List.of(
            buildRecord(
                1L,
                1L,
                "Mark",
                "West",
                "Spears",
                LocalDate.of(2019, 01, 01),
                new HomeAddress()
                    .setLine1("123 Sample Ln.")
                    .setCity("Boise")
                    .setState(State.ID)
                    .setPostalCode("83704-0001"),
                EmploymentStatusChangeType.HIRE,
                State.OH,
                employeeService)));
  }

  @Test
  public void generateExportData() throws IOException {

    var optionalBaos =
        exporter.generateExportData(
            List.of(
                buildRecord(
                        1L,
                        1L,
                        "George",
                        null,
                        "Washington",
                        LocalDate.of(2020, 8, 10),
                        new HomeAddress()
                            .setLine1("203 White House Dr.")
                            .setCity("Denver")
                            .setState(State.CO)
                            .setPostalCode("80202"),
                        EmploymentStatusChangeType.HIRE,
                        State.OH,
                        employeeService)
                    .setTaxpayerIdentifier("521-09-1234")
                    .setDateOfBirth(LocalDate.of(1952, 11, 2)),
                buildRecord(
                        100L,
                        1L,
                        "John",
                        "Quincy",
                        "Adams",
                        LocalDate.of(2020, 8, 10),
                        new HomeAddress()
                            .setLine1("204 White House Dr")
                            .setCity("Denver")
                            .setState(State.CO)
                            .setPostalCode("80202"),
                        EmploymentStatusChangeType.HIRE,
                        State.OH,
                        employeeService)
                    .setTaxpayerIdentifier("601-02-1382")
                    .setDateOfBirth(LocalDate.of(1986, 2, 20)),
                buildRecord(
                        101L,
                        1L,
                        "Thomas",
                        null,
                        "Jefferson",
                        LocalDate.of(2020, 8, 10),
                        new HomeAddress()
                            .setLine1("205 White House Dr")
                            .setCity("Denver")
                            .setState(State.CO)
                            .setPostalCode("80202"),
                        EmploymentStatusChangeType.HIRE,
                        State.OH,
                        employeeService)
                    .setTaxpayerIdentifier("521-04-6207")
                    .setDateOfBirth(LocalDate.of(1967, 10, 2)),
                buildRecord(
                        102L,
                        1L,
                        "Abraham",
                        null,
                        "Lincoln",
                        LocalDate.of(2020, 8, 10),
                        new HomeAddress()
                            .setLine1("206 White House Dr")
                            .setCity("Denver")
                            .setState(State.CO)
                            .setPostalCode("80202"),
                        EmploymentStatusChangeType.HIRE,
                        State.OH,
                        employeeService)
                    .setTaxpayerIdentifier("536-05-0680")
                    .setDateOfBirth(LocalDate.of(1983, 9, 9)),
                buildRecord(
                        103L,
                        1L,
                        "Harry",
                        "s",
                        "Truman",
                        LocalDate.of(2020, 8, 10),
                        new HomeAddress()
                            .setLine1("207 White House Dr")
                            .setCity("Denver")
                            .setState(State.CO)
                            .setPostalCode("80202"),
                        EmploymentStatusChangeType.HIRE,
                        State.OH,
                        employeeService)
                    .setTaxpayerIdentifier("541-05-9660")
                    .setDateOfBirth(LocalDate.of(1969, 6, 28))));

    compareOutputToExpectedExportData(
        this.getClass().getResourceAsStream("OH_NewHires_20210704-20210706.txt"), optionalBaos);
  }
}
