package com.everee.api.employee.statuschange.exporter;

import static com.everee.api.employee.statuschange.exporter.EmploymentStatusExporterTestUtils.buildRecord;
import static com.everee.api.employee.statuschange.exporter.EmploymentStatusExporterTestUtils.compareOutputToExpectedExportData;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;

import com.everee.api.company.CompanyAddress;
import com.everee.api.company.CompanyService;
import com.everee.api.company.DetailedCompany;
import com.everee.api.employee.EmployeeService;
import com.everee.api.employee.statuschange.EmploymentStatusChangeType;
import com.everee.api.storage.StorageService;
import com.everee.api.tax.state.State;
import com.everee.api.user.address.HomeAddress;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class WV_employmentStatusExporterTest {
  @Mock StorageService storageService;
  @Mock CompanyService companyService;
  @Mock EmployeeService employeeService;

  @InjectMocks WV_employmentStatusExporter exporter;

  @BeforeEach
  public void beforeEach() {
    lenient()
        .when(companyService.getCompany(eq(1L)))
        .thenReturn(
            (DetailedCompany)
                new DetailedCompany()
                    .setDisplayName("THE ACME CORPORATION")
                    .setFederalEin("82288-8888")
                    .setAddresses(
                        Set.of(
                            new CompanyAddress()
                                .setStartDate(LocalDate.MIN)
                                .setLine1("123456 Fake ADDRESS LN")
                                .setCity("Tallahassee")
                                .setState(State.WV)
                                .setPostalCode("83720-0000")))
                    .setId(1L));

    lenient()
        .when(companyService.getCompany(eq(2L)))
        .thenReturn(
            (DetailedCompany)
                new DetailedCompany()
                    .setDisplayName("Spacely Sprockets")
                    .setFederalEin("82999-9999")
                    .setAddresses(
                        Set.of(
                            new CompanyAddress()
                                .setStartDate(LocalDate.MIN)
                                .setLine1("1060 WEST ADDISON")
                                .setCity("Chicago")
                                .setState(State.IL)
                                .setPostalCode("60610")))
                    .setId(2L));
  }

  @Test
  public void getState() {
    assertThat(exporter.getState()).isEqualTo(State.WV);
  }

  @Test
  public void generateExportData() throws IOException {

    var optionalBaos =
        exporter.generateExportData(
            List.of(
                buildRecord(
                    1L,
                    1L,
                    "Bert",
                    "Xavier",
                    "Sample",
                    LocalDate.of(2019, 01, 01),
                    new HomeAddress()
                        .setLine1("123 Sample Ln.")
                        .setCity("Boise")
                        .setState(State.ID)
                        .setPostalCode("83704-0001"),
                    EmploymentStatusChangeType.HIRE,
                    State.WV,
                    employeeService),
                buildRecord(
                    100L,
                    1L,
                    "John",
                    null,
                    "Example",
                    LocalDate.of(2019, 01, 01),
                    new HomeAddress()
                        .setLine1("9999 Anyplace Rd.")
                        .setCity("anytown")
                        .setState(State.ID)
                        .setPostalCode("83642"),
                    EmploymentStatusChangeType.HIRE,
                    State.WV,
                    employeeService),
                buildRecord(
                    101L,
                    1L,
                    "Glen",
                    "allan",
                    "Faker",
                    LocalDate.of(2019, 01, 01),
                    new HomeAddress()
                        .setLine1("55 Bogus Rd.")
                        .setCity("Ontario")
                        .setState(State.OR)
                        .setPostalCode("97914"),
                    EmploymentStatusChangeType.HIRE,
                    State.WV,
                    employeeService),
                buildRecord(
                    200L,
                    2L,
                    "Joe",
                    "quentin",
                    "public",
                    LocalDate.of(2019, 01, 01),
                    new HomeAddress()
                        .setLine1("456 Fake example st")
                        .setCity("Tacoma")
                        .setState(State.WA)
                        .setPostalCode("98406"),
                    EmploymentStatusChangeType.HIRE,
                    State.WV,
                    employeeService),
                buildRecord(
                    201L,
                    2L,
                    "Harry",
                    null,
                    "Anybody",
                    LocalDate.of(2019, 01, 01),
                    new HomeAddress()
                        .setLine1("111 just another st")
                        .setCity("boise")
                        .setState(State.ID)
                        .setPostalCode("83704"),
                    EmploymentStatusChangeType.HIRE,
                    State.WV,
                    employeeService)));

    compareOutputToExpectedExportData(
        this.getClass().getResourceAsStream("WV_NewHires_20210704-20210706.txt"), optionalBaos);
  }
}
