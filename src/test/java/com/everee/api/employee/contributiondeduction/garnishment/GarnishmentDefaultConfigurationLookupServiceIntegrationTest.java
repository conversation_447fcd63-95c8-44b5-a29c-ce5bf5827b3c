package com.everee.api.employee.contributiondeduction.garnishment;

import com.everee.api.employee.contributiondeduction.EmployeeContributionDeductionType;
import com.everee.api.employee.contributiondeduction.garnishment.lookup.GarnishmentDefaultConfigurationLookup;
import com.everee.api.employee.contributiondeduction.garnishment.lookup.GarnishmentDefaultConfigurationLookupService;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import java.util.Set;
import org.junit.jupiter.api.Test;

import javax.transaction.Transactional;
import static org.assertj.core.api.Assertions.assertThat;

@Transactional
@SpringBootTest
@AutoConfigureEmbeddedDatabase
@ActiveProfiles("integration-test")
public class GarnishmentDefaultConfigurationLookupServiceIntegrationTest {

  @Autowired
  private GarnishmentDefaultConfigurationLookupService defaultConfigurationLookupService;

  @Test
  public void findAll_withoutPassingTypes_returnsAllDefaultConfigurations() {

    var result = defaultConfigurationLookupService.findAll(
      new GarnishmentDefaultConfigurationLookup());

    assertThat(result).isNotNull();
    assertThat(result.size()).isEqualTo(11);
  }

  @Test
  public void findAll_whenPassingTypes_returnsConfigurationsOfPassedTypes() {

    var lookup = new GarnishmentDefaultConfigurationLookup()
      .setTypes(Set.of(EmployeeContributionDeductionType.GARNISHMENT_CHILD_SUPPORT));

    var result = defaultConfigurationLookupService.findAll(lookup);

    assertThat(result).isNotNull();
    assertThat(result)
      .extracting(GarnishmentDefaultConfiguration::getType)
      .containsOnly(EmployeeContributionDeductionType.GARNISHMENT_CHILD_SUPPORT);
  }

}
