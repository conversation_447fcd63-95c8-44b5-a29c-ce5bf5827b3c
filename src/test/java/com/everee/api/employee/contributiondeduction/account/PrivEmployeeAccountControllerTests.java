package com.everee.api.employee.contributiondeduction.account;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.everee.api.employee.contributiondeduction.account.balance.EmployeeAccountBalanceLookupService;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionLookup;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionLookupService;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionRepository;
import com.everee.api.employee.contributiondeduction.account.transaction.EmployeeAccountTransactionService;
import com.everee.api.metrics.DatabaseMetricsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(PrivEmployeeAccountController.class)
public class PrivEmployeeAccountControllerTests {
  private static final String BASE_PATH = "/api-private/v1/employee-accounts";

  @Autowired private MockMvc mockMvc;
  @MockBean private EmployeeAccountTransactionRepository employeeAccountTransactionRepository;
  @MockBean private EmployeeAccountTransactionService employeeAccountTransactionService;
  @MockBean private EmployeeAccountBalanceLookupService employeeAccountBalanceLookupService;
  @MockBean private EmployeeAccountTransactionLookupService employeeAccountTransactionLookupService;
  @MockBean private DatabaseMetricsService databaseMetricsService;

  @Test
  public void listUnfundedEmployeeAccountTransactionsShouldSucceed() throws Exception {
    when(employeeAccountTransactionLookupService.listAll(
            any(EmployeeAccountTransactionLookup.class), any(Pageable.class)))
        .thenReturn(Page.empty());
    mockMvc
        .perform(
            get(BASE_PATH + "/transactions")
                .param("company-id", "1")
                .param("employee-id", "1")
                .param("funded", "false"))
        .andExpect(status().isOk());
    verify(employeeAccountTransactionLookupService)
        .listAll(any(EmployeeAccountTransactionLookup.class), any(Pageable.class));
  }

  @Test
  public void updateAchFileIdShouldSucceed() throws Exception {
    var achFileId = 1L;
    mockMvc
        .perform(
            put(BASE_PATH + "/transactions")
                .param("id", "1,2,3")
                .param("ach-file-id", "87")
                .param("new-ach-file-id", String.valueOf(achFileId)))
        .andExpect(status().isOk());
    verify(employeeAccountTransactionLookupService)
        .updateAchFileId(any(EmployeeAccountTransactionLookup.class), eq(achFileId));
  }
}
