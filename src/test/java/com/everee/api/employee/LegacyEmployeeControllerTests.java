package com.everee.api.employee;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.everee.api.auth.authserver.CustomUserDetailsService;
import com.everee.api.company.DetailedCompanyRepository;
import com.everee.api.employee.contributiondeduction.*;
import com.everee.api.employee.lookup.DetailedEmployeeLookupService;
import com.everee.api.employeeWithholdings.EmployeeWithholdings;
import com.everee.api.employeeWithholdings.WorkerWithholdingSettingsService;
import com.everee.api.metrics.DatabaseMetricsService;
import com.everee.api.model.MaritalStatus;
import com.everee.api.money.Money;
import com.everee.api.phase.PhaseLookup;
import com.everee.api.phase.PhaseValidatorService;
import com.everee.api.service.FinopsSlackNotifierService;
import com.everee.api.storage.StorageService;
import com.everee.api.user.DetailedUserRepository;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@WebMvcTest(LegacyEmployeeController.class)
public class LegacyEmployeeControllerTests {

  private final String BASE_URL = "/api/v1";
  Long companyId;
  Long employeeId;
  Long payPeriodId;

  @Autowired private MockMvc mvc;

  @MockBean private DetailedCompanyRepository detailedCompanyRepository;
  @MockBean private EmployeeContributionDeductionRepository contributionDeductionRepository;
  @MockBean private EmployeeDocumentRepository documentRepository;
  @MockBean private DetailedEmployeeRepository detailedEmployeeRepository;
  @MockBean private DetailedEmployeeLookupService detailedEmployeeLookupService;
  @MockBean private DetailedUserRepository detailedUserRepository;
  @MockBean private EmployeeService employeeService;
  @MockBean private StorageService storageService;
  @MockBean private CustomUserDetailsService userDetailsService;
  @MockBean private FinopsSlackNotifierService finOpsSlackNotifierService;
  @MockBean private WorkerWithholdingSettingsService workerWithholdingSettingsService;
  @MockBean private PhaseValidatorService phaseValidator;
  @MockBean private DatabaseMetricsService databaseMetricsService;

  @BeforeEach
  public void initialize() {
    this.companyId = 1l;
    this.employeeId = 1l;
    this.payPeriodId = 3l;
  }

  //////////////////////
  // FIXED EmployeeContributionDeduction
  //////////////////////

  @Test
  public void givenFixedContributionDeduction_whenGetEmployeeId_thenReturnJson() throws Exception {
    var cd = createFixedContributionDeduction(employeeId);
    var contributionsDeductions = List.of(cd);

    given(
            employeeService.listContributionDeductions(
                anyLong(), any(), any(PhaseLookup.class), any(Pageable.class)))
        .willReturn(contributionsDeductions);

    mvc.perform(
            get(
                    BASE_URL + "/companies/{companyId}/employees/{id}/contributiondeductions",
                    companyId,
                    employeeId)
                .param("phase", "ACTIVE")
                .param("for-date", LocalDate.now().toString())
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(1)))
        .andExpect(
            jsonPath(
                "$[0].amountEE.amount",
                is(contributionsDeductions.get(0).getAmountEE().getAmount().toString())))
        .andExpect(jsonPath("$[0].type", is(contributionsDeductions.get(0).getType().name())));
  }

  @Test
  public void addFixedContributionDeduction_ReturnSuccess() throws Exception {
    EmployeeContributionDeduction contributionDeduction =
        createFixedContributionDeduction(employeeId);

    given(contributionDeductionRepository.save(contributionDeduction))
        .willReturn(contributionDeduction);
    given(detailedCompanyRepository.existsById(employeeId)).willReturn(true);
    given(detailedEmployeeRepository.existsById(employeeId)).willReturn(true);

    MvcResult result =
        mvc.perform(
                post(BASE_URL + "/companies/1/employees/1/contributiondeductions")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(convertObjectToJsonBytes(contributionDeduction)))
            .andReturn();

    MockHttpServletResponse response = result.getResponse();
    assertEquals(HttpStatus.CREATED.value(), response.getStatus());
    assertEquals(
        "http://localhost" + BASE_URL + "/companies/1/employees/1/contributiondeductions/1",
        response.getHeader(HttpHeaders.LOCATION));
  }

  //////////////////////
  // PERCENT EmployeeContributionDeduction
  //////////////////////

  @Test
  public void givenPercentContributionDeduction_whenGetEmployeeId_thenReturnJson()
      throws Exception {
    var cd = createPercentContributionDeduction(employeeId);
    var contributionsDeductions = List.of(cd);

    given(
            employeeService.listContributionDeductions(
                anyLong(), any(), any(PhaseLookup.class), any(Pageable.class)))
        .willReturn(contributionsDeductions);

    mvc.perform(
            get(
                    BASE_URL + "/companies/{companyId}/employees/{id}/contributiondeductions",
                    companyId,
                    employeeId)
                .param("phase", "ACTIVE")
                .param("for-date", LocalDate.now().toString())
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(1)))
        .andExpect(
            jsonPath(
                "$[0].amountEE.amount",
                is(contributionsDeductions.get(0).getAmountEE().getAmount().toString())))
        .andExpect(jsonPath("$[0].type", is(contributionsDeductions.get(0).getType().name())));
  }

  @Test
  public void addPercentContributionDeduction_ReturnSuccess() throws Exception {
    EmployeeContributionDeduction contributionDeduction =
        createPercentContributionDeduction(employeeId);

    given(contributionDeductionRepository.save(contributionDeduction))
        .willReturn(contributionDeduction);
    given(detailedCompanyRepository.existsById(employeeId)).willReturn(true);
    given(detailedEmployeeRepository.existsById(employeeId)).willReturn(true);

    MvcResult result =
        mvc.perform(
                post(BASE_URL + "/companies/1/employees/1/contributiondeductions")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(convertObjectToJsonBytes(contributionDeduction)))
            .andReturn();

    MockHttpServletResponse response = result.getResponse();
    assertEquals(HttpStatus.CREATED.value(), response.getStatus());
    assertEquals(
        "http://localhost" + BASE_URL + "/companies/1/employees/1/contributiondeductions/1",
        response.getHeader(HttpHeaders.LOCATION));
  }

  @Test
  public void givenEmployeeDocuments_whenFindAll_thenReturnJsonArray() throws Exception {
    EmployeeDocument employeeDocument1 = createEmployeeDocument(employeeId);
    EmployeeDocument employeeDocument2 = createEmployeeDocument(employeeId);

    List<EmployeeDocument> allEmployees = Arrays.asList(employeeDocument1, employeeDocument2);

    given(documentRepository.findByEmployeeIdAndDeletedAtIsNull(employeeId))
        .willReturn(allEmployees);
    given(detailedCompanyRepository.existsById(employeeId)).willReturn(true);
    given(detailedEmployeeRepository.existsById(employeeId)).willReturn(true);

    mvc.perform(
            get(BASE_URL + "/companies/1/employees/1/documents")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].fileName", is(employeeDocument1.getFileName())));
  }

  private EmployeeContributionDeduction createFixedContributionDeduction(Long employeeId) {
    EmployeeContributionDeduction employeeContributionDeduction =
        new EmployeeContributionDeduction();
    employeeContributionDeduction.setEmployeeId(employeeId);
    employeeContributionDeduction.setCompanyId(companyId);
    employeeContributionDeduction.setAmountType(EmployeeContributionDeductionAmountType.FIXED);
    employeeContributionDeduction.setPercentEE(BigDecimal.ZERO);
    employeeContributionDeduction.setPercentER(BigDecimal.ZERO);
    employeeContributionDeduction.setStartDate(LocalDate.now());
    employeeContributionDeduction.setType(EmployeeContributionDeductionType.MEDICAL_INSURANCE);
    employeeContributionDeduction.setTypeGuid(
        EmployeeContributionDeductionType.MEDICAL_INSURANCE.toString());
    employeeContributionDeduction.setDescription("HD Med Plan");
    employeeContributionDeduction.setAmountEE(Money.valueOf("325.00"));
    employeeContributionDeduction.setAmountER(Money.valueOf("200.00"));
    employeeContributionDeduction.setId(1L);
    return employeeContributionDeduction;
  }

  private EmployeeContributionDeduction createPercentContributionDeduction(Long employeeId) {
    EmployeeContributionDeduction employeeContributionDeduction =
        new EmployeeContributionDeduction();
    employeeContributionDeduction.setEmployeeId(employeeId);
    employeeContributionDeduction.setCompanyId(companyId);
    employeeContributionDeduction.setAmountType(EmployeeContributionDeductionAmountType.PERCENT);
    employeeContributionDeduction.setPercentEE(BigDecimal.valueOf(0.07));
    employeeContributionDeduction.setPercentER(BigDecimal.valueOf(0.035));
    employeeContributionDeduction.setStartDate(LocalDate.now());
    employeeContributionDeduction.setType(EmployeeContributionDeductionType.ERISA_401K);
    employeeContributionDeduction.setTypeGuid(
        EmployeeContributionDeductionType.ERISA_401K.toString());
    employeeContributionDeduction.setDescription("401(k)");
    employeeContributionDeduction.setAmountEE(Money.ZERO);
    employeeContributionDeduction.setAmountER(Money.ZERO);
    employeeContributionDeduction.setId(1L);
    return employeeContributionDeduction;
  }

  private EmployeeWithholdings createEmployeeWithholdings(Long employeeId) {
    EmployeeWithholdings withholdings = new EmployeeWithholdings();

    withholdings.setId(1l);
    withholdings.setEmployeeId(employeeId);
    withholdings.setMaritalStatus(MaritalStatus.MARRIED);
    withholdings.setFederalAllowances(4);
    withholdings.setStateIncomeTaxable(true);
    withholdings.setFederalIncomeTaxable(true);
    withholdings.setStartDate(LocalDate.now());

    return withholdings;
  }

  private EmployeeDocument createEmployeeDocument(Long employeeId) {
    EmployeeDocument doc = new EmployeeDocument();

    doc.setId(1l);
    doc.setEmployeeId(employeeId);
    doc.setFileName("somefilename.txt");
    doc.setLocation("1/employeedoc/1/somefilename.txt");

    return doc;
  }

  private byte[] convertObjectToJsonBytes(Object object) throws IOException {
    ObjectMapper mapper = new ObjectMapper();
    mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    return mapper.writeValueAsBytes(object);
  }
}
