package com.everee.api.fundingdistribution.batch;

import static com.everee.api.TestTags.TEST_GROUP_5;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import com.everee.api.bankaccount.BankAccountType;
import com.everee.api.company.DetailedCompany;
import com.everee.api.company.DistributionFlowType;
import com.everee.api.company.bankaccount.CompanyBankAccount;
import com.everee.api.company.bankaccount.CompanyBankAccountRepository;
import com.everee.api.distribution.CompanyDistribution;
import com.everee.api.distribution.CompanyDistributionRepository;
import com.everee.api.distribution.CompanyDistributionStatus;
import com.everee.api.funding.CompanyFunding;
import com.everee.api.funding.CompanyFundingConfig;
import com.everee.api.funding.CompanyFundingRepository;
import com.everee.api.funding.CompanyFundingStatus;
import com.everee.api.money.Money;
import com.everee.api.payment.PaymentFundingType;
import com.everee.api.payment.distribution.PaymentDistributionType;
import com.everee.api.util.DbUtil;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.LocalDate;
import java.util.Set;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ActiveProfiles;

@Tag(TEST_GROUP_5)
@SpringBootTest
@AutoConfigureEmbeddedDatabase
@ActiveProfiles("integration-test")
class CompanyFundingDistributionBatchQueryServiceIntegrationTests {
  @Autowired private DbUtil dbUtil;

  @Autowired private CompanyDistributionRepository companyDistributionRepository;
  @Autowired private CompanyBankAccountRepository companyBankAccountRepository;
  @Autowired private CompanyFundingRepository companyFundingRepository;

  @Autowired private EntityManager entityManager;

  @Autowired CompanyFundingDistributionBatchQueryService queryService;

  DetailedCompany companyX;
  DetailedCompany companyZW;
  LocalDate contextDate = LocalDate.parse("2022-08-30");

  @BeforeEach
  public void beforeEach() {
    companyDistributionRepository.deleteAll();
    companyFundingRepository.deleteAll();
    companyX =
        dbUtil.createCompany(c -> c.setDistributionFlowType(DistributionFlowType.MANUAL_APPROVAL));
    companyZW =
        dbUtil.createCompany(
            c ->
                c.setLegalEntityName("Company Z")
                    .setDisplayName("Company W")
                    .setDistributionFlowType(DistributionFlowType.MANUAL_APPROVAL));
    var companyXBankAccount = createBankAccount(companyX.getId());
    var companyZWBankAccount = createBankAccount(companyZW.getId());

    // context date batches
    // batch 1
    var completeDistribution_companyX_cd =
        cd(
            companyX,
            contextDate,
            PaymentDistributionType.ACH,
            CompanyDistributionStatus.DISTRIBUTED,
            Money.valueOf("500.00"));
    var completeFunding_companyX_cd =
        cf(
            companyX,
            companyXBankAccount,
            contextDate,
            PaymentFundingType.OPTIMISTIC_FUNDED,
            CompanyFundingStatus.FUNDED,
            Money.valueOf("500.00"));
    cf(
        companyX,
        companyXBankAccount,
        contextDate,
        PaymentFundingType.OPTIMISTIC_FUNDED,
        CompanyFundingStatus.FUNDED,
        Money.valueOf("500.00"));

    // batch 2
    var incompleteDistribution_companyX_cd =
        cd(
            companyX,
            contextDate,
            PaymentDistributionType.ACH,
            CompanyDistributionStatus.CALCULATED,
            Money.valueOf("200.00"));
    var incompleteFunding_companyX_cd =
        cf(
            companyX,
            companyXBankAccount,
            contextDate,
            PaymentFundingType.OPTIMISTIC_FUNDED,
            CompanyFundingStatus.CALCULATED,
            Money.valueOf("200.00"));

    // "yesterday" batches
    // batch 3
    var completeDistribution_companyX_pcd =
        cd(
            companyX,
            contextDate.minusDays(1),
            PaymentDistributionType.ACH,
            CompanyDistributionStatus.DISTRIBUTED,
            Money.valueOf("100.00"));
    var completeFunding_companyX_pcd =
        cf(
            companyX,
            companyXBankAccount,
            contextDate.minusDays(1),
            PaymentFundingType.OPTIMISTIC_FUNDED,
            CompanyFundingStatus.FUNDED,
            Money.valueOf("100.00"));

    // batch 4
    var incompleteDistribution_companyX_pcd =
        cd(
            companyX,
            contextDate.minusDays(1),
            PaymentDistributionType.ACH,
            CompanyDistributionStatus.CALCULATED,
            Money.valueOf("600.00"));
    var incompleteFunding_companyX_pcd =
        cf(
            companyX,
            companyXBankAccount,
            contextDate.minusDays(1),
            PaymentFundingType.OPTIMISTIC_FUNDED,
            CompanyFundingStatus.CALCULATED,
            Money.valueOf("400.00"));

    // batch 5 - CompanyZW
    var completeDistribution_companyZW_pcd =
        cd(
            companyZW,
            contextDate.minusDays(1),
            PaymentDistributionType.ACH,
            CompanyDistributionStatus.DISTRIBUTED,
            Money.valueOf("10.00"));
    var completeFunding_companyZW_pcd =
        cf(
            companyZW,
            companyZWBankAccount,
            contextDate.minusDays(1),
            PaymentFundingType.OPTIMISTIC_FUNDED,
            CompanyFundingStatus.FUNDED,
            Money.valueOf("10.00"));

    // batch 6 - CompanyZW
    var incompleteDistribution_companyZW_pcd =
        cd(
            companyZW,
            contextDate.minusDays(1),
            PaymentDistributionType.ACH,
            CompanyDistributionStatus.CALCULATED,
            Money.valueOf("60.00"));
    var incompleteFunding_companyZW_pcd =
        cf(
            companyZW,
            companyZWBankAccount,
            contextDate.minusDays(1),
            PaymentFundingType.OPTIMISTIC_FUNDED,
            CompanyFundingStatus.CALCULATED,
            Money.valueOf("40.00"));
  }

  @Test
  public void getBatches_companyId_filter() {
    var pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setCompanyId(companyX.getId()),
            Pageable.unpaged());

    assertThat(pageBatches.getContent()).isNotNull();
    var batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(4);
  }

  @Test
  public void getBatches_paging() {
    var pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setCompanyId(companyX.getId()),
            PageRequest.of(0, 1, Sort.by("totalfunding")));
    var batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(1);
    assertThat(batches.get(0).getTotalFunding()).isEqualTo(Money.valueOf("100"));
    assertThat(pageBatches.getTotalElements()).isEqualTo(4);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup(),
            PageRequest.of(0, 1, Sort.by("totalfunding")));
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(1);
    assertThat(batches.get(0).getTotalFunding()).isEqualTo(Money.valueOf("10"));
    assertThat(pageBatches.getTotalElements()).isEqualTo(6);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup(),
            PageRequest.of(2, 1, Sort.by("totalfunding")));
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(1);
    assertThat(batches.get(0).getTotalFunding()).isEqualTo(Money.valueOf("100"));
    assertThat(pageBatches.getTotalElements()).isEqualTo(6);
  }

  @Test
  public void getBatches_date_filter() {
    var pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setDate(contextDate), Pageable.unpaged());
    var batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(2);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setDate(contextDate.minusDays(1)),
            Pageable.unpaged());
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(4);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup()
                .setMaxDateInclusive(contextDate.minusDays(1)),
            Pageable.unpaged());
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(4);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup()
                .setMinDateInclusive(contextDate.minusDays(1))
                .setMaxDateInclusive(contextDate),
            Pageable.unpaged());
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(6);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setMinDateInclusive(contextDate),
            Pageable.unpaged());
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(2);
  }

  @Test
  public void getBatches_finalized_filter() {
    var pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setFinalized(Boolean.TRUE),
            Pageable.unpaged());
    var batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(3);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setFinalized(Boolean.FALSE),
            Pageable.unpaged());
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(3);
  }

  @Test
  public void getBatches() {
    var pageBatches =
        queryService.getBatches(new CompanyFundingDistributionBatchLookup(), Pageable.unpaged());
    var batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(6);

    pageBatches =
        queryService.getBatches(
            new CompanyFundingDistributionBatchLookup().setIds(Set.of(batches.get(0).getId())),
            Pageable.unpaged());
    batches = pageBatches.getContent();
    assertThat(batches.size()).isEqualTo(1);
  }

  private CompanyDistribution cd(
      DetailedCompany company,
      LocalDate distributionDate,
      PaymentDistributionType type,
      CompanyDistributionStatus status,
      Money amount) {
    return companyDistributionRepository.save(
        new CompanyDistribution()
            .setCompanyId(company.getId())
            .setDistributionDate(distributionDate)
            .setStatus(status)
            .setType(type)
            .setAmount(amount));
  }

  private CompanyFunding cf(
      DetailedCompany company,
      CompanyBankAccount companyBankAccount,
      LocalDate fundingDate,
      PaymentFundingType type,
      CompanyFundingStatus status,
      Money amount) {
    return companyFundingRepository.save(
        new CompanyFunding()
            .setCompanyId(companyBankAccount.getCompanyId())
            .setFundingConfig(
                new CompanyFundingConfig()
                    .setCompanyBankAccountId(companyBankAccount.getId())
                    .setAccountNumber(companyBankAccount.getAccountNumber())
                    .setRoutingNumber(companyBankAccount.getRoutingNumber())
                    .setAccountName(companyBankAccount.getAccountName())
                    .setBankName(companyBankAccount.getBankName())
                    .setTargetAchBankAccountId(
                        company.getCompanyAchConfiguration().getPreFundedPaymentAccount().getId()))
            .setFundingDate(fundingDate)
            .setStatus(status)
            .setType(type)
            .setAmount(amount));
  }

  private CompanyBankAccount createBankAccount(Long companyId) {
    var bankAccount = new CompanyBankAccount();
    bankAccount.setCompanyId(companyId);
    bankAccount.setId(13L);
    bankAccount.setBankName("Big Bank");
    bankAccount.setAccountName("My Account");
    bankAccount.setAccountType(BankAccountType.SAVINGS);
    bankAccount.setRoutingNumber("*********");
    bankAccount.setAccountNumber("********");
    return companyBankAccountRepository.save(bankAccount);
  }
}
