<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.irs.gov/efile" xmlns="http://www.irs.gov/efile" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">

	<xsd:annotation>
		<xsd:documentation>
			<Description>IRS e-file Tax Exempt and Government Entities Schema - Binary Attachment</Description>
			<TaxYear>2020</TaxYear>
			<MaturityLevel>TY2020 941 Quarterly Family Form Drop 3 Final Schema Version</MaturityLevel>
			<ReleaseDate>July 23 2019</ReleaseDate>
		</xsd:documentation>
	</xsd:annotation>

	<xsd:include schemaLocation="efileTypes.xsd"/>

	<!-- ===================================================== -->
	<!-- ================= Binary Attachment ================= -->
	<!-- ===================================================== -->

	<xsd:element name="BinaryAttachment">
		<xsd:annotation>
			<xsd:documentation>Binary Attachment</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="BinaryAttachmentType">
					<xsd:attributeGroup ref="DocumentAttributes">
						<xsd:annotation>
							<xsd:documentation>Common return document attributes</xsd:documentation>
						</xsd:annotation>
					</xsd:attributeGroup>
					<xsd:attribute name="documentName" type="xsd:string" fixed="BinaryAttachment">
						<xsd:annotation>
							<xsd:documentation>IRS internal use only. To avoid error in the return, do not include the attribute name or value.</xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:complexType name="BinaryAttachmentType">
		<xsd:annotation>
			<xsd:documentation>Content model for Binary Attachment</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>

			<!-- Document Type (required) -->
			<xsd:element name="DocumentTypeCd">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Document Type</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="StringType">
						<xsd:enumeration value="PDF"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

			<!-- Description (required) -->
			<xsd:element name="Desc">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Description</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="128"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

			<!-- Attachment Location - short filename without path (required) -->
			<xsd:element name="AttachmentLocationTxt">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Attachment Location - short filename without path</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="64"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

		</xsd:sequence>
	</xsd:complexType>

</xsd:schema>
