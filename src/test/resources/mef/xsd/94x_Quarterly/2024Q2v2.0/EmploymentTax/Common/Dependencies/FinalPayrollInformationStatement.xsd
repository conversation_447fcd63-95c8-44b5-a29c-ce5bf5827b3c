<?xml version="1.0" encoding="UTF-8"?>

<xsd:schema targetNamespace="http://www.irs.gov/efile" xmlns="http://www.irs.gov/efile" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">



	<xsd:annotation>

		<xsd:documentation>

			<Description>IRS e-file Schema - Final Payroll Information Statement</Description>

			<Purpose>Business closed or stopped paying wages to employees -  include a statement showing the name of the person keeping the payroll records and the address where those records will be kept.</Purpose>

			<TaxYear>2024</TaxYear>

			<MaturityLevel>Final Schema Version RL107 MD2 941Quarterly Q2</MaturityLevel>

			<ReleaseDate>April 18 2024</ReleaseDate>

		</xsd:documentation>

	</xsd:annotation>



	<xsd:include schemaLocation="../../../Common/efileTypes.xsd" />



	<!-- ======================================================================== -->

	<!-- ================== Final Payroll Information Statement ================= -->

	<!-- ======================================================================== -->



	<xsd:element name="FinalPayrollInfoStatement">

		<xsd:annotation>

			<xsd:documentation>Final Payroll Information Statement</xsd:documentation>

		</xsd:annotation>

		<xsd:complexType>

			<xsd:complexContent>

				<xsd:extension base="FinalPayrollInfoStatementType">

					<xsd:attributeGroup ref="DocumentAttributes">

						<xsd:annotation>

							<xsd:documentation>Common return document attributes</xsd:documentation>

						</xsd:annotation>

					</xsd:attributeGroup>

					<xsd:attribute name="documentName" type="StringType" fixed="FinalPayrollInformationStatement">

						<xsd:annotation>

							<xsd:documentation>IRS internal use only. To avoid error in the return, do not include the attribute name or value.</xsd:documentation>

						</xsd:annotation>

					</xsd:attribute>

				</xsd:extension>

			</xsd:complexContent>

		</xsd:complexType>

	</xsd:element>



	<xsd:complexType name="FinalPayrollInfoStatementType">

		<xsd:annotation>

			<xsd:documentation>Content model for Final Payroll Information Statement</xsd:documentation>

		</xsd:annotation>

		<xsd:sequence>



			<!-- Person Name -->

			<xsd:element name="PersonNm" type="PersonNameType">

				<xsd:annotation>

					<xsd:documentation>

						<Description>Person Name</Description>

					</xsd:documentation>

				</xsd:annotation>

			</xsd:element>



			<!-- Address - choice between US Address or Foreign Address -->

			<xsd:choice>

				<!-- US Address -->

				<xsd:element name="USAddress" type="USAddressType">

					<xsd:annotation>

						<xsd:documentation>

							<Description>US Address</Description>

						</xsd:documentation>

					</xsd:annotation>

				</xsd:element>

				<!-- Foreign Address -->

				<xsd:element name="ForeignAddress" type="ForeignAddressType">

					<xsd:annotation>

						<xsd:documentation>

							<Description>Foreign Address</Description>

						</xsd:documentation>

					</xsd:annotation>

				</xsd:element>

			</xsd:choice>



		</xsd:sequence>

	</xsd:complexType>



</xsd:schema>

