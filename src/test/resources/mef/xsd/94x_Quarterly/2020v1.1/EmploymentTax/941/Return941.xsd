<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.irs.gov/efile" xmlns="http://www.irs.gov/efile" xmlns:efile="http://www.irs.gov/efile" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">

	<xsd:annotation>
		<xsd:documentation>
			<Description>IRS e-file Schema - Return Schema for 941 return</Description>
			<TaxYear>2020</TaxYear>
			<MaturityLevel>Final Schema Version TY2020 RL103 MD2 941 Quarterly</MaturityLevel>
			<ReleaseDate>June 04 2020</ReleaseDate>
		</xsd:documentation>
	</xsd:annotation>

	<xsd:include schemaLocation="../Common/ReturnHeader94x.xsd" />
	<xsd:include schemaLocation="ReturnData941.xsd" />

	<!--  Employer's QUARTERLY Federal Tax Return - 941 -->
	<xsd:element name="Return">
		<xsd:annotation>
			<xsd:documentation>941 Return - wraps around Return Header and Return Data</xsd:documentation>
		</xsd:annotation>

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element ref="ReturnHeader" />
				<xsd:element ref="ReturnData" />

			</xsd:sequence>

			<!-- Return Version -->
			<xsd:attribute name="returnVersion" type="StringType" use="required" fixed="2020v1.1" >
				<xsd:annotation>
					<xsd:documentation>Return Version</xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>

		</xsd:complexType>

		<!-- Uniqueness constraint for 'documentId' attribute of return documents -->
		<xsd:unique name="documentId">
			<xsd:selector xpath="efile:ReturnData/efile:*" />
			<xsd:field xpath="@documentId" />
		</xsd:unique>

		<!-- Uniqueness constraint for Binary Attachment -->
		<xsd:unique name="BinaryAttachment">
			<xsd:selector xpath="efile:ReturnData/efile:BinaryAttachment" />
			<xsd:field xpath="efile:AttachmentLocationTxt" />
		</xsd:unique>

	</xsd:element>

</xsd:schema>
