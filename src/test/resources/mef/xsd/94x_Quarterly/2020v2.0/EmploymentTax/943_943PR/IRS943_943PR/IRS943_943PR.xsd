<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns="http://www.irs.gov/efile" xmlns:efile="http://www.irs.gov/efile" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.irs.gov/efile" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">
	
	<xsd:annotation>
		<xsd:documentation>
			<Description>IRS e-file Schema - IRS Form 943 (943-PR) Employer's Annual Federal Tax Return for Agricultural Employees</Description>
			<TaxYear>2020</TaxYear>
			<MaturityLevel>Final Schema Version 94x Annual Family Form TY2020 RL104 DD4</MaturityLevel>
			<ReleaseDate>Nov 3 2020</ReleaseDate>
		</xsd:documentation>
	</xsd:annotation>

	<xsd:include schemaLocation="../../../Common/efileTypes.xsd"/>

	<!-- ======================================================================== -->
	<!-- ======================== IRS Form 943 (943-PR) ========================= -->
	<!-- ======================================================================== -->

	<xsd:element name="IRS943_943PR">
		<xsd:annotation>
			<xsd:documentation>IRS Form 943 (943-PR)</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="IRS943Type">

					<xsd:attributeGroup ref="DocumentAttributes">
						<xsd:annotation>
							<xsd:documentation>Common return document attributes</xsd:documentation>
						</xsd:annotation>
					</xsd:attributeGroup>
					<xsd:attribute name="documentName" type="xsd:string" fixed="IRS943_943PR">
						<xsd:annotation>
							<xsd:documentation>IRS internal use only. To avoid error in the return, do not include the attribute name or value.</xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="referenceDocumentId" type="IdListType">
						<xsd:annotation>
							<xsd:documentation>List of document ID's of forms, schedules, supporting info. etc. attached to this form</xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="referenceDocumentName" type="StringType" fixed="BinaryAttachment GeneralDependencySmall IRS943ScheduleR FinalPayrollInformationStatement TransferOfBusinessStatement">
					</xsd:attribute>

				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
		<!-- Uniqueness constraint for 'MonthCd' in IRS943_943PR -->
		<xsd:unique name="IRS943MonthCd">
			<xsd:selector xpath="efile:TaxLiabilityMonthlyDetailGrp" />
			<xsd:field xpath="efile:MonthCd" />
		</xsd:unique>	
	</xsd:element>

	<xsd:complexType name="IRS943Type">
		<xsd:annotation>
			<xsd:documentation>Content model for Form 943 (943-PR)</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			
			<!-- Prior Address Indicator -->
			<xsd:element name="PriorAddressInd" type="CheckboxType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Prior Address Indicator</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
	
			<!-- Future Filing Not Required Indicator -->
			<xsd:element name="FutureFilingNotRequiredInd" type="CheckboxType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Future Filing Not Required Indicator</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		
			<!-- Employee Count -->
			<xsd:element name="EmployeeCnt">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Employee Count</Description>
						<LineNumber>1</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="IntegerNNType">
						<xsd:totalDigits value="7" />
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

			<!-- Social Security Tax Cash Wages Amount -->
			<xsd:element name="SocialSecurityTaxCashWagesAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Social Security Tax Cash Wages Amount</Description>
						<LineNumber>2</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Social Security Qualified Paid Sick Leave Wage Amount -->
			<xsd:element name="SocialSecurityQlfyPdSLWageAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Social Security Qualified Paid Sick Leave Wage Amount</Description>
						<LineNumber>2a</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Social Security Qualified Paid Sick Leave Tax Amount -->
			<xsd:element name="SocialSecurityQlfyPdFMLWageAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Social Security Qualified Family Leave Wage Amount</Description>
						<LineNumber>2b</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Social Security Tax Amount -->
			<xsd:element name="SocialSecurityTaxAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Social Security Tax Amount</Description>
						<LineNumber>3</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Social Security Qualify Paid Family Leave Wage Amount -->
			<xsd:element name="SocialSecurityQlfyPdSLTaxAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Social Security Qualify Paid Sick Leave Tax Amount</Description>
						<LineNumber>3a</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Social Security Qualify Paid Family Leave Tax Amount -->
			<xsd:element name="SocialSecurityQlfyPdFMLTaxAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Social Security Qualify Paid Family Leave Tax Amount</Description>
						<LineNumber>3b</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Medicare Tax Cash Wages Amount -->
			<xsd:element name="MedicareTaxCashWagesAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Medicare Tax Cash Wages Amount</Description>
						<LineNumber>4</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Medicare Tax Withheld Amount -->
			<xsd:element name="MedicareTaxWithheldAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Medicare Tax Withheld Amount</Description>
						<LineNumber>5</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Total Wages Subject to Additional Medicare Tax Withholding -->
			<xsd:element name="TotMedcrTaxCashWagesAddnlWhAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Wages Subject to Additional Medicare Tax Withholding</Description>
						<LineNumber>6</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Additional Medicare Tax Withholding Amount -->
			<xsd:element name="AddnlMedicareTaxWithholdingAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Additional Medicare Tax Withholding Amount</Description>
						<LineNumber>7</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Federal Income Tax Withheld Amount -->
			<xsd:element name="FederalIncomeTaxWithheldAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Federal Income Tax Withheld Amount</Description>
						<LineNumber>8</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Total Tax Before Adjustment Amount -->
			<xsd:element name="TotalTaxBeforeAdjustmentAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Tax Before Adjustment Amount</Description>
						<LineNumber>9</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Current Year Adjustment Amount -->
			<xsd:element name="CurrentYearAdjustmentAmt" type="USDecimalAmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Current Year Adjustment Amount</Description>
						<LineNumber>10</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Total Tax After Adjustment Amount -->
			<xsd:element name="TotalTaxAfterAdjustmentAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Tax After Adjustment Amount</Description>
						<LineNumber>11</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Payroll Tax Credit Amount -->
			<xsd:element name="PayrollTaxCreditAmt" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Qualified small business payroll tax credit for increasing research activities</Description>
						<LineNumber>12a</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="USDecimalAmountNNType">
							<xsd:attribute name="referenceDocumentId" type="IdListType"/>
							<xsd:attribute name="referenceDocumentName" type="StringType" fixed="IRS8974"/>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>

			<!-- Nonrefundable Portion Credit Qualify Sick Leave and Family Leave Wages Amount -->
			<xsd:element name="NrfdblCrQlfySLFMLWagesAmt" type="USDecimalAmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Nonrefundable Portion Credit Qualify Sick Leave and Family Leave Wages Amount</Description>
						<LineNumber>12b</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>			
			
			<!-- Nonrefundable Portion Employee Retention Credit COVID Amount-->
			<xsd:element name="NrfdblEmplRtntnCrCOVIDAmt" type="USDecimalAmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Nonrefundable Portion Employee Retention Credit COVID Amount</Description>
						<LineNumber>12c</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>	
			
			<!-- Total Nonrefundable Credits Amount-->
			<xsd:element name="TotalNonrefundableCreditsAmt" type="USDecimalAmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Nonrefundable Credits Amount</Description>
						<LineNumber>12d</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Total Tax Amount -->
			<xsd:element name="TotalTaxAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Tax Amount</Description>
						<LineNumber>13</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Total Tax Deposit Amount -->
			<xsd:element name="TotalTaxDepositAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Tax Deposit Amount</Description>
						<LineNumber>14a</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Deferred Payment Employer Share Social Security Tax Amount -->
			<xsd:element name="DeferredPaymentEmplrShrSSTAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Deferred Payment Employer Share Social Security Tax Amount</Description>
						<LineNumber>14b</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Deferred Payment Employee Share Social Security Tax Amount -->
			<xsd:element name="DeferredPymtEmployeeShrSSTAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Deferred Payment Employee Share Social Security Tax Amount</Description>
						<LineNumber>14c</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Refundable Portion Credit Qualify Sick Leave And Family Leave Wages Amount -->
			<xsd:element name="RfdblCrQlfySLFMLWagesAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Refundable Portion Credit Qualify Sick Leave And Family Leave Wages Amount</Description>
						<LineNumber>14d</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Refundable Portion Employee Retention Credit COVID Amount -->
			<xsd:element name="RfdblEmplRtntnCrCOVIDAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Refundable Portion Employee Retention Credit COVID Amount</Description>
						<LineNumber>14e</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Total Payments Refundable Credits Amount-->
			<xsd:element name="TotalPaymentRefundableCrAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Payments Refundable Credits Amount</Description>
						<LineNumber>14f</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Total Advance Payments Employeer Credit Require Quarter Amount -->
			<xsd:element name="TotAdvncPymtEmplrCrReqQtrAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Advance Payments Employeer Credit Require Quarter Amount</Description>
						<LineNumber>14g</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Net Total Payments Refundable Credits Advances -->
			<xsd:element name="NetTotalPaymentRefundableCrAmt" type="USDecimalAmountType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Net Total Payments Refundable Credits Advances</Description>
						<LineNumber>14h</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>	

			<xsd:choice minOccurs="0">

				<!-- Balance Due Amount -->
				<xsd:element name="BalanceDueAmt" type="USDecimalAmountNNType">
					<xsd:annotation>
						<xsd:documentation>
							<Description>Balance Due Amount</Description>
							<LineNumber>15</LineNumber>
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

				<xsd:element name="OverpaymentGrp" minOccurs="0">
					<xsd:complexType>
						<xsd:sequence>

							<!-- Overpaid Amount -->
							<xsd:element name="OverpaidAmt" type="USDecimalAmountNNType">
								<xsd:annotation>
									<xsd:documentation>
										<Description>Overpaid Amount</Description>
										<LineNumber>16</LineNumber>
									</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
	
							<xsd:choice>

								<!-- Apply Overpayment Next Return Indicator -->
								<xsd:element name="ApplyOverpaymentNextReturnInd" type="CheckboxType">
									<xsd:annotation>
										<xsd:documentation>
											<Description>Apply Overpayment Next Return Indicator</Description>
											<LineNumber>16</LineNumber>
										</xsd:documentation>
									</xsd:annotation>
								</xsd:element>

								<!-- Refund Overpayment Indicator -->
								<xsd:element name="RefundOverpaymentInd" type="CheckboxType">
									<xsd:annotation>
										<xsd:documentation>
											<Description>Refund Overpayment Indicator</Description>
											<LineNumber>16</LineNumber>
										</xsd:documentation>
									</xsd:annotation>
								</xsd:element>

							</xsd:choice>

						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>

			</xsd:choice>

			<xsd:choice minOccurs="0">

				<!-- Semiweekly Schedule Depositor Indicator -->
				<xsd:element name="SemiweeklyScheduleDepositorInd">
					<xsd:annotation>
						<xsd:documentation>
							<Description>Semiweekly Schedule Depositor Indicator</Description>
							<LineNumber>16</LineNumber>
						</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="CheckboxType">
								<xsd:attribute name="referenceDocumentId" type="IdListType" />
								<xsd:attribute name="referenceDocumentName" type="StringType" fixed="IRS943A" />
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>

				<!-- Monthly Schedule Depositor Indicator -->
				<xsd:element name="MonthlyScheduleDepositorInd" type="CheckboxType">
					<xsd:annotation>
						<xsd:documentation>
							<Description>Monthly Schedule Depositor Indicator</Description>
							<LineNumber>16</LineNumber>
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

			</xsd:choice>

			<xsd:element name="TaxLiabilityMonthlyDetailGrp" minOccurs="0" maxOccurs="12">
				<xsd:complexType>
					<xsd:sequence>

						<!-- Month Code -->
						<xsd:element name="MonthCd">
							<xsd:annotation>
								<xsd:documentation>
									<Description>Month Code</Description>
									<LineNumber>17A to 17L</LineNumber>
								</xsd:documentation>
							</xsd:annotation>
							<xsd:simpleType>
								<xsd:restriction base="TextType">
									<xsd:enumeration value="JANUARY"/>
									<xsd:enumeration value="FEBRUARY"/>
									<xsd:enumeration value="MARCH"/>
									<xsd:enumeration value="APRIL"/>
									<xsd:enumeration value="MAY"/>
									<xsd:enumeration value="JUNE"/>
									<xsd:enumeration value="JULY"/>
									<xsd:enumeration value="AUGUST"/>
									<xsd:enumeration value="SEPTEMBER"/>
									<xsd:enumeration value="OCTOBER"/>
									<xsd:enumeration value="NOVEMBER"/>
									<xsd:enumeration value="DECEMBER"/>
								</xsd:restriction>
							</xsd:simpleType>
						</xsd:element>

						<!-- Tax Liability Amount -->
						<xsd:element name="TaxLiabilityAmt" type="USDecimalAmountNNType" minOccurs="0">
							<xsd:annotation>
								<xsd:documentation>
									<Description>Tax Liability Amount</Description>
									<LineNumber>17A to 17L</LineNumber>
								</xsd:documentation>
							</xsd:annotation>
						</xsd:element>

					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>

			<!-- Total Tax Liability Amount -->
			<xsd:element name="TotalTaxLiabilityAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Total Tax Liability Amount</Description>
						<LineNumber>17M</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Qualified Health Plan Expenses Social Security Qualify Paid Sick Leave Wages Amount -->
			<xsd:element name="QHPExpnssSSQlfyPdSLWageAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Qualified Health Plan Expenses Social Security Qualify Paid Sick Leave Wages Amount</Description>
						<LineNumber>18</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Qualified Health Plan Expenses Social Security Qualify Paid Family Leave Wages Amount-->
			<xsd:element name="QHPExpnssSSQlfyPdFMLWageAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Qualified Health Plan Expenses Social Security Qualify Paid Family Leave Wages Amount</Description>
						<LineNumber>19</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Qualified Wages Paid Retains Employees COVID Amount -->
			<xsd:element name="QlfyWgsPdRtnEmplCOVIDAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Qualified Wages Paid Retains Employees COVID Amount</Description>
						<LineNumber>20</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Qualified Health Plan Expenses Wages Paid Retains Employees COVID Amount -->
			<xsd:element name="QHPExpnssWgsPdRtnEmplCOVIDAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Qualified Health Plan Expenses Wages Paid Retains Employees COVID Amount</Description>
						<LineNumber>21</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		
			<!-- Work Opportunity Credit Amount -->
			<xsd:element name="WorkOpportunityCreditAmt" type="USDecimalAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Work Opportunity Credit Amount</Description>
						<LineNumber>22</LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
		</xsd:sequence>
	</xsd:complexType>
	
</xsd:schema>
