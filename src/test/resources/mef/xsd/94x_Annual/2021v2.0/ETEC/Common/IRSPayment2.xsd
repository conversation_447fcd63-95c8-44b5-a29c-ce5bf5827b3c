<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.irs.gov/efile" xmlns="http://www.irs.gov/efile" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">
	
	<xsd:annotation>
		<xsd:documentation>
			<Description>IRS e-file Heavy Highway Vehicle Return - IRS Payment </Description>
			<TaxYear>2021</TaxYear>
			<MaturityLevel>Final Schema Version RL105 Drop 4 TY2021 94x Annual Family Form</MaturityLevel>
			<ReleaseDate>Oct 14 2021</ReleaseDate>
		</xsd:documentation>
	</xsd:annotation>
	
	<xsd:include schemaLocation="../../Common/efileTypes.xsd"/>
	
	
	<!-- ===================================================== -->
	<!-- ===================== IRS Payment =================== -->
	<!-- ===================================================== -->
	
	
	<xsd:element name="IRSPayment2">
		<xsd:annotation>
			<xsd:documentation>IRS Payment</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="IRSPaymentType">
					<xsd:attributeGroup ref="DocumentAttributes">
						<xsd:annotation>
							<xsd:documentation>Common return document attributes</xsd:documentation>
						</xsd:annotation>
					</xsd:attributeGroup>
					<xsd:attribute name="documentName" type="StringType" fixed="IRSPayment2">
						<xsd:annotation>
							<xsd:documentation>IRS internal use only. To avoid error in the return, do not include the attribute name or value.</xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>
	<xsd:complexType name="IRSPaymentType">
		<xsd:annotation>
			<xsd:documentation>Content model for IRS Payment</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
		
			<!-- Routing Transit Number -->
			<xsd:element name="RoutingTransitNum" type="RoutingTransitNumberType">
				<xsd:annotation>
					<xsd:documentation>Routing Transit Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Bank Account Number -->
			<xsd:element name="BankAccountNum" type="BankAccountNumberType">
				<xsd:annotation>
					<xsd:documentation>Bank Account Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Type of Account -->
			<xsd:element name="BankAccountTypeCd" type="BankAccountType">
				<xsd:annotation>
					<xsd:documentation>Type of Account</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Payment Amount -->
			<xsd:element name="PaymentAmt" type="USDecimalAmountPosType">
				<xsd:annotation>
					<xsd:documentation>Payment Amount in Dollars and Cents</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Requested Payment Date -->
			<xsd:element name="RequestedPaymentDt" type="DateType">
				<xsd:annotation>
					<xsd:documentation>Requested Payment Date</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			
			<!-- Taxpayer Daytime Phone -->
			<xsd:element name="TaxpayerDaytimePhoneNum" type="PhoneNumberType">
				<xsd:annotation>
					<xsd:documentation>Taxpayer's Daytime Phone Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
