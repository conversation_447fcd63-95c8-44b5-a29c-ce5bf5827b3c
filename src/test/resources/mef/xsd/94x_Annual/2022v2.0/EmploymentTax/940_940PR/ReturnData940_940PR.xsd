<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.irs.gov/efile" xmlns="http://www.irs.gov/efile" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">

	<xsd:annotation>
		<xsd:documentation>
			<Description>IRS e-file Schema - Return data schema for 940 (940PR) return</Description>
			<TaxYear>2022</TaxYear>
			<MaturityLevel>Final Schema Version RL106 DD4 94x Annual</MaturityLevel>
			<ReleaseDate>11142022</ReleaseDate>
		</xsd:documentation>
	</xsd:annotation>

	<xsd:include schemaLocation="../../Common/efileTypes.xsd" />
	<xsd:include schemaLocation="../../Common/BinaryAttachment.xsd" />

	<xsd:include schemaLocation="IRS940_940PR/IRS940_940PR.xsd" />
	<xsd:include schemaLocation="IRS940ScheduleA/IRS940ScheduleA.xsd" />
	<xsd:include schemaLocation="IRS940ScheduleR/IRS940ScheduleR.xsd" />

	<!-- Common Dependencies (supporting info, schedules, computations, and such) -->	
	<xsd:include schemaLocation="../Common/Dependencies/FinalPayrollInformationStatement.xsd" />
	<xsd:include schemaLocation="../../CorporateIncomeTax/Common/Dependencies/GeneralDependencySmall.xsd" />
	<xsd:include schemaLocation="../../ETEC/Common/IRSPayment2.xsd" />

	<!-- Return 940 (940PR) -->
	<xsd:element name="ReturnData">
		<xsd:annotation>
			<xsd:documentation>940 (940PR) Return Contents</xsd:documentation>
		</xsd:annotation>

		<xsd:complexType>
			<xsd:sequence>

				<!-- Form 940 (940-PR) -->
				<xsd:element ref="IRS940_940PR">
					<xsd:annotation>
						<xsd:documentation>IRS Form 940 (940-PR) - Employer's Annual Federal Unemployment (FUTA) Tax Return</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

				<!-- Form 940 Schedule A -->
				<xsd:element ref="IRS940ScheduleA" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>IRS Form 940 Schedule A - Multi-State Employer and Credit Reduction Information</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

				<!-- Form 940 Schedule R -->
				<xsd:element ref="IRS940ScheduleR" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>IRS Form 940 Schedule R - Allocation Schedule for Aggregate Form 940 Filers</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

			<!-- List of Dependencies by form -->

				<!-- Final Payroll Information Statement (common) -->
				<xsd:element ref="FinalPayrollInfoStatement" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>Final Payroll Information Statement</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

				<!-- IRS Payment Record -->
				<xsd:element ref="IRSPayment2" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>IRS Payment Record</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

				<!-- General Dependency Small for any Form/Schedule -->
				<xsd:element ref="GeneralDependencySmall" minOccurs="0" maxOccurs="100">
					<xsd:annotation>
						<xsd:documentation>General Dependency Small (attachment not identified on the form or instructions)</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

				<!-- Binary Attachment -->
				<xsd:element ref="BinaryAttachment" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation>Binary Attachment</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

			</xsd:sequence>

			<xsd:attributeGroup ref="ReturnDataAttributes">
				<xsd:annotation>
					<xsd:documentation>Common return data attributes</xsd:documentation>
				</xsd:annotation>
			</xsd:attributeGroup>

		</xsd:complexType>

	</xsd:element>

</xsd:schema>
