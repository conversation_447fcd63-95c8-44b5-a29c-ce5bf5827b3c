<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.irs.gov/efile" xmlns="http://www.irs.gov/efile"
xmlns:xsd="http://www.w3.org/2001/XMLSchema"
elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">

	<xsd:annotation>
		<xsd:documentation>
			<Description>IRS e-file XML Schema - Base types commonly used across schema files</Description>
			<TaxYear>2024</TaxYear>
			<MaturityLevel>Final Schema Version RL108 Drop 4 94x Annual</MaturityLevel>
			<ReleaseDate>Oct 10 2024</ReleaseDate>
		</xsd:documentation>
	</xsd:annotation>

	<!-- Attributes for a Return Data -->
	<xsd:attributeGroup name="ReturnDataAttributes">
		<xsd:annotation>
			<xsd:documentation>Attributes to be applied to each return data group (a.k.a. return), which is a collection of return documents.</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="documentCnt" type="xsd:positiveInteger" use="required">
			<xsd:annotation>
				<xsd:documentation>The number of return documents in the return</xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:attributeGroup>

	<!-- Attributes for a Return Document -->
	<xsd:attributeGroup name="DocumentAttributes">
		<xsd:annotation>
			<xsd:documentation>Attribute to be applied for each return document</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="documentId" type="IdType" use="required">
			<xsd:annotation>
				<xsd:documentation>Must be unique within the return</xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="softwareId" type="SoftwareIdType">
			<xsd:annotation>
				<xsd:documentation>Software ID</xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="softwareVersionNum" type="SoftwareVersionType">
			<xsd:annotation>
				<xsd:documentation>Software Version</xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:attributeGroup>

	<!-- String Type -->
	<xsd:simpleType name="StringType">
		<xsd:annotation>
			<xsd:documentation>Base type for a string</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string" />
	</xsd:simpleType>

	<!-- URI Type -->
	<xsd:simpleType name="URIType">
		<xsd:annotation>
			<xsd:documentation>Base type for a URI</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:anyURI" />
	</xsd:simpleType>

	<!-- Date Type in the format of YYYY-MM-DD -->
	<xsd:simpleType name="DateType">
		<xsd:annotation>
			<xsd:documentation>Base type for a date</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:date">
			<xsd:pattern value="[1-9][0-9]{3}\-.*" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Year Month Type in the format of YYYY-MM -->
	<xsd:simpleType name="YearMonthType">
		<xsd:annotation>
			<xsd:documentation>Year and month type in the format of YYYY-MM</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="StringType">
			<xsd:pattern value="[1-9][0-9]{3}-((0[1-9])|(1[0-2]))" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Year Month Type in the format of YYMM -->
	<xsd:simpleType name="TaxYearEndMonthDtType">
		<xsd:annotation>
			<xsd:documentation>Year and month type in the format of YYMM</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:pattern value="[0-9][0-9](01|02|03|04|05|06|07|08|09|10|11|12)"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Month Type -->
	<xsd:simpleType name="MonthType">
		<xsd:annotation>
			<xsd:documentation>Month type in the format of --MM</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="StringType">
			<xsd:pattern value="--((0[1-9])|(1[0-2]))" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Month Day Type -->
	<xsd:simpleType name="MonthDayType">
		<xsd:annotation>
			<xsd:documentation>Month and day type in the format of --MM-DD</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="StringType">
			<xsd:pattern value="--((0[1-9])|(1[0-2]))-((0[1-9])|([12][0-9]))" />
			<xsd:pattern value="--((0[13-9])|(1[0-2]))-30" />
			<xsd:pattern value="--((0[13578])|(1[02]))-31" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Quarter End Date Type in the format of YYYY-MM-DD -->
	<xsd:simpleType name="QuarterEndDateType">
		<xsd:annotation>
			<xsd:documentation>The end date of a calendar quarter.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="StringType">
			<xsd:pattern value="[1-9][0-9]{3}\-((03\-31)|(06\-30)|(09\-30)|(12\-31))" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Timestamp Type - Timezone portion is required and fractional seconds are prohibited -->
	<xsd:simpleType name="TimestampType">
		<xsd:annotation>
			<xsd:documentation>Base type for a date and time stamp</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:dateTime">
			<xsd:pattern value="[1-9][0-9]{3}\-.+T[^\.]+(Z|[\+\-].+)" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Year Type -->
	<xsd:simpleType name="YearType">
		<xsd:annotation>
			<xsd:documentation>Base type for a 4-digit year</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="StringType">
			<xsd:pattern value="[1-9][0-9]{3}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Boolean Type - true or false, or 1 or 0 -->
	<xsd:simpleType name="BooleanType">
		<xsd:annotation>
			<xsd:documentation>Base type for a boolean. Typically used on an Yes or No field.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:boolean" />
	</xsd:simpleType>

	<!-- Checkbox Type -->
	<xsd:simpleType name="CheckboxType">
		<xsd:annotation>
			<xsd:documentation>Typically used by an optional checkbox.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="X" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Integer Type -->
	<xsd:simpleType name="IntegerType">
		<xsd:annotation>
			<xsd:documentation>Base type for an integer</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:integer">
			<xsd:totalDigits value="25"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Integer Type - Non-Negative -->
	<xsd:simpleType name="IntegerNNType">
		<xsd:annotation>
			<xsd:documentation>Base type for a non-negative integer</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:nonNegativeInteger">
			<xsd:totalDigits value="25"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Integer Type - Positive -->
	<xsd:simpleType name="IntegerPosType">
		<xsd:annotation>
			<xsd:documentation>Base type for a positive integer</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="25"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Long Integer Type -->
	<xsd:simpleType name="LongIntegerType">
		<xsd:annotation>
			<xsd:documentation>Base type for a long integer. Range of values is: -9,223,372,036,854,775,808 thru 9,223,372,036,854,775,807</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:long" />
	</xsd:simpleType>

	<!-- Decimal Type -->
	<xsd:simpleType name="DecimalType">
		<xsd:annotation>
			<xsd:documentation>2-digit decimal typically used by a decimal amount field.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:fractionDigits value="2" />
			<xsd:totalDigits value="25"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Decimal Type - Non-Negative -->
	<xsd:simpleType name="DecimalNNType">
		<xsd:annotation>
			<xsd:documentation>2-digit decimal typically used by a non-negative decimal amount field.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:fractionDigits value="2" />
			<xsd:minInclusive value="0.00" />
			<xsd:totalDigits value="25"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type -->
	<xsd:simpleType name="USAmountType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:integer">
			<xsd:totalDigits value="15"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type - Non-Negative -->
	<xsd:simpleType name="USAmountNNType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. non-negative integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:nonNegativeInteger">
			<xsd:totalDigits value="15"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type - Negative -->
	<xsd:simpleType name="USAmountNegType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. negative integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:negativeInteger">
			<xsd:totalDigits value="15"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type - Non-Positive -->
	<xsd:simpleType name="USAmountNonPosType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. non-positive integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:nonPositiveInteger">
			<xsd:totalDigits value="15"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type - Positive -->
	<xsd:simpleType name="USAmountPosType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. positive integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="15"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type  -->
	<xsd:simpleType name="USDecimalAmountType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. amount field with dollars and cents</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="DecimalType">
			<xsd:totalDigits value="17"/>
			<xsd:minInclusive value="-**********99999.99"/>
			<xsd:maxInclusive value="**********99999.99"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type - Non-Negative -->
	<xsd:simpleType name="USDecimalAmountNNType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. non-negative amount field with dollars and cents</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="DecimalNNType">
			<xsd:totalDigits value="17"/>
			<xsd:maxInclusive value="**********99999.99"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Amount Type - Positive -->
	<xsd:simpleType name="USDecimalAmountPosType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. positive amount field with dollars and cents</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="DecimalNNType">
			<xsd:totalDigits value="17"/>
			<xsd:minExclusive value="0"/>
			<xsd:maxInclusive value="**********99999.99"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. Large Decimal Amount Type  -->
	<xsd:simpleType name="USLargeDecimalAmountType">
		<xsd:annotation>
			<xsd:documentation>Type for a U.S. amount field with dollars and cents that allows 4 decimals</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="19"/>
			<xsd:fractionDigits value="4"/>
			<xsd:minInclusive value="-**********99999.9999"/>
			<xsd:maxInclusive value="**********99999.9999"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Foreign Amount Type -->
	<xsd:simpleType name="ForeignAmountType">
		<xsd:annotation>
			<xsd:documentation>Type for a foreign integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:integer">
			<xsd:totalDigits value="17"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Foreign Amount Type - Non-Negative -->
	<xsd:simpleType name="ForeignAmountNNType">
		<xsd:annotation>
			<xsd:documentation>Type for a foreign non-negative integer amount field</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:nonNegativeInteger">
			<xsd:totalDigits value="17"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Decimal 2 Ratio Type -->
	<xsd:simpleType name="Decimal2RatioType">
		<xsd:annotation>
			<xsd:documentation>A fraction between 0 and 1 that allows up to 2 decimal places</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="3" />
			<xsd:fractionDigits value="2" />
			<xsd:minInclusive value="0.00" />
			<xsd:maxInclusive value="1.00" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Decimal 4 Ratio Type -->
	<xsd:simpleType name="Decimal4RatioType">
		<xsd:annotation>
			<xsd:documentation>A fraction between 0 and 1 that requires 4 decimal places</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="5" />
			<xsd:fractionDigits value="4" />
			<xsd:minInclusive value="0.0000" />
			<xsd:maxInclusive value="1.0000" />
			<xsd:pattern value="[01].[0-9]{4}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Small Ratio Type -->
	<xsd:simpleType name="SmallRatioType">
		<xsd:annotation>
			<xsd:documentation>A fraction between 0 and .9999 that allows up to 4 decimal places</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="4" />
			<xsd:fractionDigits value="4" />
			<xsd:minInclusive value=".0000" />
			<xsd:maxInclusive value=".9999" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Ratio Type -->
	<xsd:simpleType name="RatioType">
		<xsd:annotation>
			<xsd:documentation>A fraction between 0 and 1 that allows up to 5 decimal places</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="6" />
			<xsd:fractionDigits value="5" />
			<xsd:minInclusive value="0.00000" />
			<xsd:maxInclusive value="1.00000" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Large Ratio Type -->
	<xsd:simpleType name="LargeRatioType">
		<xsd:annotation>
			<xsd:documentation>A non-negative decimal that allows up to 22 total digits and 12 fraction digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="22" />
			<xsd:fractionDigits value="12" />
			<xsd:minInclusive value="**********.**********00" />
			<xsd:maxInclusive value="**********.**********99" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Business Activity Code Type -->
	<xsd:simpleType name="BusinessActivityCodeType">
		<xsd:annotation>
			<xsd:documentation>Business Activity Code</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:integer">
			<xsd:minInclusive value="000001" />
			<xsd:maxInclusive value="999000" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Routing Transit Number Type -->
	<xsd:simpleType name="RoutingTransitNumberType">
		<xsd:annotation>
			<xsd:documentation>Routing Transit Number - 9 digits beginning with 01 through 12, or 21 through 32</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="(01|02|03|04|05|06|07|08|09|10|11|12|21|22|23|24|25|26|27|28|29|30|31|32)[0-9]{7}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Bank Account Number Type -->
	<xsd:simpleType name="BankAccountNumberType">
		<xsd:annotation>
			<xsd:documentation>Bank Account Number - 17 alphanumeric characters with hyphens</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="17" />
			<xsd:pattern value="[A-Za-z0-9\-]+" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Bank Account Type -->
	<xsd:simpleType name="BankAccountType">
		<xsd:annotation>
			<xsd:documentation>Type of Bank Account - 1 for Checking, 2 for Savings</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Checking</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Savings</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Social Security Number Type -->
	<xsd:simpleType name="SSNType">
		<xsd:annotation>
			<xsd:documentation>Type for Social Security No. - 9 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{9}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Employer Identification Number Type -->
	<xsd:simpleType name="EINType">
		<xsd:annotation>
			<xsd:documentation>Type for Employer Identification No. - 9 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{9}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Electronic Transmitter Identification Number Type -->
	<xsd:simpleType name="ETINType">
		<xsd:annotation>
			<xsd:documentation>Type for Electronic Transmitter Identification No. - 5 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{5}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Preparer Personal Identification Number Type -->
	<xsd:simpleType name="PTINType">
		<xsd:annotation>
			<xsd:documentation>Type for Preparer Personal Identification No. - P followed by 8 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="P[0-9]{8}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Electronic Filing Identification Number Type -->
	<xsd:simpleType name="EFINType">
		<xsd:annotation>
			<xsd:documentation>Type for Electronic Filing Identification No. - 6 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{6}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Practitioner PIN Type, Self-Select PIN Type and Third Party Designee Personal Identification Number Type -->
	<xsd:simpleType name="PINType">
		<xsd:annotation>
			<xsd:documentation>Type for Practitioner PIN, Self-Select PIN and Third Party Designee PIN</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{5}"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Intermediate Service Provider Number Type -->
	<xsd:simpleType name="ISPType">
		<xsd:annotation>
			<xsd:documentation>Type for Intermediate Service Provider No. - 6 uppercase alphanumeric characters</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Z0-9]{6}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Originator Type -->
	<xsd:simpleType name="OriginatorType">
		<xsd:annotation>
			<xsd:documentation>The type of originator</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="ERO" />
			<xsd:enumeration value="OnlineFiler" />
			<xsd:enumeration value="ReportingAgent" />
			<xsd:enumeration value="FinancialAgent" />
			<xsd:enumeration value="LargeTaxpayer" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Signature Type -->
	<xsd:simpleType name="SignatureType">
		<xsd:annotation>
			<xsd:documentation>Used for a PIN signature</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{10}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Software Id Type -->
	<xsd:simpleType name="SoftwareIdType">
		<xsd:annotation>
			<xsd:documentation>The Software ID - 8 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{8}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Software Version Type -->
	<xsd:simpleType name="SoftwareVersionType">
		<xsd:annotation>
			<xsd:documentation>The Software Version - 20 characters</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="20" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Id Type -->
	<xsd:simpleType name="IdType">
		<xsd:annotation>
			<xsd:documentation>Pattern used for generating TransmissionId, OriginId, ItemId, and AcknowledgementId. A timestamp may be used as an ID as long as it is unique within its parent element and within the filing season. Up to 4-decimal fractional digits may be used for the second in a timestamp to increase its uniqueness. Legal Characters: A-Z, a-z, 0-9, colon, period and hyphen.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Za-z0-9:\.\-]{1,30}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Id List Type -->
	<xsd:simpleType name="IdListType">
		<xsd:annotation>
			<xsd:documentation>Type for list of Ids</xsd:documentation>
		</xsd:annotation>
		<xsd:list itemType="IdType"/>
	</xsd:simpleType>

	<!-- Business Name Line 1 Type -->
	<xsd:simpleType name="BusinessNameLine1Type">
		<xsd:annotation>
			<xsd:documentation>Typically used for line 1 of a business name. Legal Characters: A-Z, a-z, 0-9, hash, hyphen, parentheses, ampersand, apostrophe and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="75" />
			<xsd:pattern value="(([A-Za-z0-9#\-\(\)]|&#x26;|&#x27;) ?)*([A-Za-z0-9#\-\(\)]|&#x26;|&#x27;)" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Business Name Line 2 Type -->
	<xsd:simpleType name="BusinessNameLine2Type">
		<xsd:annotation>
			<xsd:documentation>Typically used for line 2 of a business name. Legal Characters: A-Z, a-z, 0-9, hash, slash, percent, hyphen, parentheses, ampersand, apostrophe and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="75" />
			<xsd:pattern value="(([A-Za-z0-9#/%\-\(\)]|&#x26;|&#x27;) ?)*([A-Za-z0-9#/%\-\(\)]|&#x26;|&#x27;)" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- In Care of Name Type -->
	<xsd:simpleType name="InCareOfNameType">
		<xsd:annotation>
			<xsd:documentation>Typically used for line 3 of a business name or line 2 of an individual name. Element must begin with a percent followed by a space. Legal Characters: A-Z, a-z, 0-9, hash, slash, percent, hyphen, parentheses, ampersand, apostrophe and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35" />
			<xsd:pattern value="(% )(([A-Za-z0-9#/%\-\(\)]|&#x26;|&#x27;) ?)*([A-Za-z0-9#/%\-\(\)]|&#x26;|&#x27;)" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Name Line 1 Type -->
	<xsd:simpleType name="NameLine1Type">
		<xsd:annotation>
			<xsd:documentation>Typically used for line 1 of a name. Legal Characters: A-Z, a-z, hyphen, ampersand, less-than sign and single space. Illegal Character: leading space, trailing space, adjacent spaces, spaces surrounding the less-than sign and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35" />
				<xsd:pattern value="[A-Za-z]( |&lt;)?(([A-Za-z#\-]|&#x26;)( |&#x3c;)?)*([A-Za-z#\-]|&#x26;)" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Person Name Type -->
	<xsd:simpleType name="PersonNameType">
		<xsd:annotation>
			<xsd:documentation>Typically used for a person's name. Legal Characters: A-Z, a-z, 0-9, hyphen, apostrophe and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35" />
			<xsd:pattern value="([A-Za-z0-9'\-] ?)*[A-Za-z0-9'\-]" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Person Title Type -->
	<xsd:simpleType name="PersonTitleType">
		<xsd:annotation>
			<xsd:documentation>Typically used for a person's title. Legal Characters: printable characters from &#x21; to &#x7E; plus single space. Illegal Character: leading space, trailing space, adjacent spaces.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35" />
			<xsd:pattern value="([&#x0021;-&#x007E;] ?)*[&#x0021;-&#x007E;]" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Business Name Control Type -->
	<xsd:simpleType name="BusinessNameControlType">
		<xsd:annotation>
			<xsd:documentation>Used for a Name Control. Legal Characters: A-Z, 0-9, hyphen and ampersand. Illegal Character: spaces and symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([A-Z0-9\-]|&#x26;){1,4}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Check Digit Type -->
	<xsd:simpleType name="CheckDigitType">
		<xsd:annotation>
			<xsd:documentation>Used for a Check Digit field. 2 uppercase characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Z]{2}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Street Address Type -->
	<xsd:simpleType name="StreetAddressType">
		<xsd:annotation>
			<xsd:documentation>Used for a street address. Legal Characters: A-Z, a-z, 0-9, hyphen, slash and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35" />
			<xsd:pattern value="[A-Za-z0-9]( ?[A-Za-z0-9\-/])*" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- City Type -->
	<xsd:simpleType name="CityType">
		<xsd:annotation>
			<xsd:documentation>Used for a city. Legal Characters: A-Z, a-z, and single space. Illegal Character: leading space, trailing space, adjacent spaces, and symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="22" />
			<xsd:pattern value="([A-Za-z] ?)*[A-Za-z]" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. State Abbreviations Type -->
	<xsd:simpleType name="StateType">
		<xsd:annotation>
			<xsd:documentation>State abbreviations, a.k.a. state codes</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Alabama</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AK">
				<xsd:annotation>
					<xsd:documentation>Alaska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>American Samoa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZ">
				<xsd:annotation>
					<xsd:documentation>Arizona</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Arkansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>California</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>Colorado</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MP">
				<xsd:annotation>
					<xsd:documentation>Commonwealth of the Northern Mariana Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Connecticut</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Delaware</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>District of Columbia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Federated States of Micronesia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Florida</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Georgia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GU">
				<xsd:annotation>
					<xsd:documentation>Guam</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HI">
				<xsd:annotation>
					<xsd:documentation>Hawaii</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ID">
				<xsd:annotation>
					<xsd:documentation>Idaho</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IL">
				<xsd:annotation>
					<xsd:documentation>Illinois</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IN">
				<xsd:annotation>
					<xsd:documentation>Indiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IA">
				<xsd:annotation>
					<xsd:documentation>Iowa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KS">
				<xsd:annotation>
					<xsd:documentation>Kansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KY">
				<xsd:annotation>
					<xsd:documentation>Kentucky</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LA">
				<xsd:annotation>
					<xsd:documentation>Louisiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ME">
				<xsd:annotation>
					<xsd:documentation>Maine</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MH">
				<xsd:annotation>
					<xsd:documentation>Marshall Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Maryland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MA">
				<xsd:annotation>
					<xsd:documentation>Massachusetts</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MI">
				<xsd:annotation>
					<xsd:documentation>Michigan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MN">
				<xsd:annotation>
					<xsd:documentation>Minnesota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MS">
				<xsd:annotation>
					<xsd:documentation>Mississippi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MO">
				<xsd:annotation>
					<xsd:documentation>Missouri</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MT">
				<xsd:annotation>
					<xsd:documentation>Montana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NE">
				<xsd:annotation>
					<xsd:documentation>Nebraska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NV">
				<xsd:annotation>
					<xsd:documentation>Nevada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>New Hampshire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NJ">
				<xsd:annotation>
					<xsd:documentation>New Jersey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NM">
				<xsd:annotation>
					<xsd:documentation>New Mexico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NY">
				<xsd:annotation>
					<xsd:documentation>New York</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NC">
				<xsd:annotation>
					<xsd:documentation>North Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>North Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OH">
				<xsd:annotation>
					<xsd:documentation>Ohio</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OK">
				<xsd:annotation>
					<xsd:documentation>Oklahoma</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OR">
				<xsd:annotation>
					<xsd:documentation>Oregon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PW">
				<xsd:annotation>
					<xsd:documentation>Palau</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Pennsylvania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PR">
				<xsd:annotation>
					<xsd:documentation>Puerto Rico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RI">
				<xsd:annotation>
					<xsd:documentation>Rhode Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>South Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SD">
				<xsd:annotation>
					<xsd:documentation>South Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TN">
				<xsd:annotation>
					<xsd:documentation>Tennessee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TX">
				<xsd:annotation>
					<xsd:documentation>Texas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VI">
				<xsd:annotation>
					<xsd:documentation>U.S. Virgin Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UT">
				<xsd:annotation>
					<xsd:documentation>Utah</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VT">
				<xsd:annotation>
					<xsd:documentation>Vermont</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VA">
				<xsd:annotation>
					<xsd:documentation>Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WA">
				<xsd:annotation>
					<xsd:documentation>Washington</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WV">
				<xsd:annotation>
					<xsd:documentation>West Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WI">
				<xsd:annotation>
					<xsd:documentation>Wisconsin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WY">
				<xsd:annotation>
					<xsd:documentation>Wyoming</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Armed Forces the Americas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Europe</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Pacific</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Federal Unemployment Tax Act (FUTA) State Abbreviations Type -->
	<xsd:simpleType name="FUTAStateCdType">
		<xsd:annotation>
			<xsd:documentation>Federal Unemployment Tax Act (FUTA) state abbreviations</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Alabama</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AK">
				<xsd:annotation>
					<xsd:documentation>Alaska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZ">
				<xsd:annotation>
					<xsd:documentation>Arizona</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Arkansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>California</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>Colorado</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Connecticut</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Delaware</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>District of Columbia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Florida</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Georgia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HI">
				<xsd:annotation>
					<xsd:documentation>Hawaii</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ID">
				<xsd:annotation>
					<xsd:documentation>Idaho</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IL">
				<xsd:annotation>
					<xsd:documentation>Illinois</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IN">
				<xsd:annotation>
					<xsd:documentation>Indiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IA">
				<xsd:annotation>
					<xsd:documentation>Iowa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KS">
				<xsd:annotation>
					<xsd:documentation>Kansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KY">
				<xsd:annotation>
					<xsd:documentation>Kentucky</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LA">
				<xsd:annotation>
					<xsd:documentation>Louisiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ME">
				<xsd:annotation>
					<xsd:documentation>Maine</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Maryland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MA">
				<xsd:annotation>
					<xsd:documentation>Massachusetts</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MI">
				<xsd:annotation>
					<xsd:documentation>Michigan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MN">
				<xsd:annotation>
					<xsd:documentation>Minnesota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MS">
				<xsd:annotation>
					<xsd:documentation>Mississippi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MO">
				<xsd:annotation>
					<xsd:documentation>Missouri</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MT">
				<xsd:annotation>
					<xsd:documentation>Montana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NE">
				<xsd:annotation>
					<xsd:documentation>Nebraska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NV">
				<xsd:annotation>
					<xsd:documentation>Nevada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>New Hampshire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NJ">
				<xsd:annotation>
					<xsd:documentation>New Jersey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NM">
				<xsd:annotation>
					<xsd:documentation>New Mexico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NY">
				<xsd:annotation>
					<xsd:documentation>New York</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NC">
				<xsd:annotation>
					<xsd:documentation>North Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>North Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OH">
				<xsd:annotation>
					<xsd:documentation>Ohio</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OK">
				<xsd:annotation>
					<xsd:documentation>Oklahoma</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OR">
				<xsd:annotation>
					<xsd:documentation>Oregon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Pennsylvania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PR">
				<xsd:annotation>
					<xsd:documentation>Puerto Rico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RI">
				<xsd:annotation>
					<xsd:documentation>Rhode Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>South Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SD">
				<xsd:annotation>
					<xsd:documentation>South Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TN">
				<xsd:annotation>
					<xsd:documentation>Tennessee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TX">
				<xsd:annotation>
					<xsd:documentation>Texas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VI">
				<xsd:annotation>
					<xsd:documentation>U.S. Virgin Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UT">
				<xsd:annotation>
					<xsd:documentation>Utah</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VT">
				<xsd:annotation>
					<xsd:documentation>Vermont</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VA">
				<xsd:annotation>
					<xsd:documentation>Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WA">
				<xsd:annotation>
					<xsd:documentation>Washington</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WV">
				<xsd:annotation>
					<xsd:documentation>West Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WI">
				<xsd:annotation>
					<xsd:documentation>Wisconsin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WY">
				<xsd:annotation>
					<xsd:documentation>Wyoming</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- U.S. ZIP Code Type -->
	<xsd:simpleType name="ZIPCodeType">
		<xsd:annotation>
			<xsd:documentation>ZIP Code - 5 digits plus optional 4 or 7 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{5}(([0-9]{4})|([0-9]{7}))?" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Phone Number Type -->
	<xsd:simpleType name="PhoneNumberType">
		<xsd:annotation>
			<xsd:documentation>Used for a phone no. - 10 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{10}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Foreign Phone Number Type -->
	<xsd:simpleType name="ForeignPhoneNumberType">
		<xsd:annotation>
			<xsd:documentation>Used for a foreign phone no. - up to 30 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{1,30}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Email Address Type -->
	<xsd:simpleType name="EmailAddressType">
		<xsd:annotation>
		<xsd:documentation>Used for an email address. Symbol @ follows and is followed by at least one of the characters allowed by TextType - up to 75 characters in total.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="75"/>
			<xsd:pattern value=".+@.+"></xsd:pattern>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Text Type -->
	<xsd:simpleType name="TextType">
		<xsd:annotation>
			<xsd:documentation>Used for a text field. Legal Characters: printable characters from &#x21; to &#x7E; plus the [monetary] pound, section and multiplication symbols, plus Spanish characters and single space. Illegal Character: leading space, trailing space, adjacent spaces.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([&#x0021;-&#x007E;&#x00A3;&#x00A7;&#xC1;&#xC9;&#xCD;&#xD1;&#xD3;&#xD7;&#xDA;&#xDC;&#xE1;&#xE9;&#xED;&#xF1;&#xF3;&#xFA;&#xFC;] ?)*[&#x0021;-&#x007E;&#x00A3;&#x00A7;&#xC1;&#xC9;&#xCD;&#xD1;&#xD3;&#xD7;&#xDA;&#xDC;&#xE1;&#xE9;&#xED;&#xF1;&#xF3;&#xFA;&#xFC;]" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Regular-Length Explanation Type -->
	<xsd:simpleType name="ExplanationType">
		<xsd:annotation>
			<xsd:documentation>A typical explanation field that allows up to 9000 characters</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="9000" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- One-Line Explanation Type -->
	<xsd:simpleType name="LineExplanationType">
		<xsd:annotation>
			<xsd:documentation>A one-line explanation field that allows up to 100 characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="100" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Short-Length Explanation Type -->
	<xsd:simpleType name="ShortExplanationType">
		<xsd:annotation>
			<xsd:documentation>A short explanation field that allows up to 1000 characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="1000" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Short-Length Description Type -->
	<xsd:simpleType name="ShortDescriptionType">
		<xsd:annotation>
			<xsd:documentation>A short description field that allows up to 20 characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Medium-Length Explanation Type -->
	<xsd:simpleType name="MediumExplanationType">
		<xsd:annotation>
			<xsd:documentation>A medium explanation field that allows up to 100,000 characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="100000" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Long-Length Explanation Type -->
	<xsd:simpleType name="LongExplanationType">
		<xsd:annotation>
			<xsd:documentation>A long explanation field that allows up to 1,000,000 characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="1000000" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Country Type -->
	<xsd:simpleType name="CountryType">
		<xsd:annotation>
			<xsd:documentation>Country abbreviations, a.k.a. country codes</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Afghanistan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AX">
				<xsd:annotation>
					<xsd:documentation>Akrotiri</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Albania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AG">
				<xsd:annotation>
					<xsd:documentation>Algeria</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AQ">
				<xsd:annotation>
					<xsd:documentation>American Samoa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>Andorra</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AO">
				<xsd:annotation>
					<xsd:documentation>Angola</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AV">
				<xsd:annotation>
					<xsd:documentation>Anguilla</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AY">
				<xsd:annotation>
					<xsd:documentation>Antarctica</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Antigua and Barbuda</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Argentina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Armenia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Aruba</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AT">
				<xsd:annotation>
					<xsd:documentation>Ashmore and Cartier Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>Australia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AU">
				<xsd:annotation>
					<xsd:documentation>Austria</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AJ">
				<xsd:annotation>
					<xsd:documentation>Azerbaijan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BF">
				<xsd:annotation>
					<xsd:documentation>Bahamas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BA">
				<xsd:annotation>
					<xsd:documentation>Bahrain</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FQ">
				<xsd:annotation>
					<xsd:documentation>Baker Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BG">
				<xsd:annotation>
					<xsd:documentation>Bangladesh</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BB">
				<xsd:annotation>
					<xsd:documentation>Barbados</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BO">
				<xsd:annotation>
					<xsd:documentation>Belarus</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BE">
				<xsd:annotation>
					<xsd:documentation>Belgium</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BH">
				<xsd:annotation>
					<xsd:documentation>Belize</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BN">
				<xsd:annotation>
					<xsd:documentation>Benin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BD">
				<xsd:annotation>
					<xsd:documentation>Bermuda</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BT">
				<xsd:annotation>
					<xsd:documentation>Bhutan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BL">
				<xsd:annotation>
					<xsd:documentation>Bolivia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BK">
				<xsd:annotation>
					<xsd:documentation>Bosnia-Herzegovina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>Botswana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BV">
				<xsd:annotation>
					<xsd:documentation>Bouvet Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BR">
				<xsd:annotation>
					<xsd:documentation>Brazil</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IO">
				<xsd:annotation>
					<xsd:documentation>British Indian Ocean Territory</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VI">
				<xsd:annotation>
					<xsd:documentation>British Virgin Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BX">
				<xsd:annotation>
					<xsd:documentation>Brunei</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BU">
				<xsd:annotation>
					<xsd:documentation>Bulgaria</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UV">
				<xsd:annotation>
					<xsd:documentation>Burkina Faso</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BM">
				<xsd:annotation>
					<xsd:documentation>Burma</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Burundi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CB">
				<xsd:annotation>
					<xsd:documentation>Cambodia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CM">
				<xsd:annotation>
					<xsd:documentation>Cameroon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>Canada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CV">
				<xsd:annotation>
					<xsd:documentation>Cape Verde</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CJ">
				<xsd:annotation>
					<xsd:documentation>Cayman Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Central African Republic</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Chad</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CI">
				<xsd:annotation>
					<xsd:documentation>Chile</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CH">
				<xsd:annotation>
					<xsd:documentation>China</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KT">
				<xsd:annotation>
					<xsd:documentation>Christmas Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IP">
				<xsd:annotation>
					<xsd:documentation>Clipperton Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CK">
				<xsd:annotation>
					<xsd:documentation>Cocos (Keeling) Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>Colombia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CN">
				<xsd:annotation>
					<xsd:documentation>Comoros</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CF">
				<xsd:annotation>
					<xsd:documentation>Congo (Brazzaville)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CG">
				<xsd:annotation>
					<xsd:documentation>Congo (Kinshasa)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CW">
				<xsd:annotation>
					<xsd:documentation>Cook Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CR">
				<xsd:annotation>
					<xsd:documentation>Coral Sea Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Costa Rica</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IV">
				<xsd:annotation>
					<xsd:documentation>Cote D'Ivoire (Ivory Coast)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HR">
				<xsd:annotation>
					<xsd:documentation>Croatia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CU">
				<xsd:annotation>
					<xsd:documentation>Cuba</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UC">
				<xsd:annotation>
					<xsd:documentation>Curacao</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CY">
				<xsd:annotation>
					<xsd:documentation>Cyprus</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EZ">
				<xsd:annotation>
					<xsd:documentation>Czech Republic</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DA">
				<xsd:annotation>
					<xsd:documentation>Denmark</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>Dhekelia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DJ">
				<xsd:annotation>
					<xsd:documentation>Djibouti</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DO">
				<xsd:annotation>
					<xsd:documentation>Dominica</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DR">
				<xsd:annotation>
					<xsd:documentation>Dominican Republic</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TT">
				<xsd:annotation>
					<xsd:documentation>East Timor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EC">
				<xsd:annotation>
					<xsd:documentation>Ecuador</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EG">
				<xsd:annotation>
					<xsd:documentation>Egypt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ES">
				<xsd:annotation>
					<xsd:documentation>El Salvador</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EK">
				<xsd:annotation>
					<xsd:documentation>Equatorial Guinea</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ER">
				<xsd:annotation>
					<xsd:documentation>Eritrea</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EN">
				<xsd:annotation>
					<xsd:documentation>Estonia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ET">
				<xsd:annotation>
					<xsd:documentation>Ethiopia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FK">
				<xsd:annotation>
					<xsd:documentation>Falkland Islands (Islas Malvinas)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FO">
				<xsd:annotation>
					<xsd:documentation>Faroe Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Federated States of Micronesia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FJ">
				<xsd:annotation>
					<xsd:documentation>Fiji</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FI">
				<xsd:annotation>
					<xsd:documentation>Finland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FR">
				<xsd:annotation>
					<xsd:documentation>France</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FP">
				<xsd:annotation>
					<xsd:documentation>French Polynesia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FS">
				<xsd:annotation>
					<xsd:documentation>French Southern and Antarctic Lands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GB">
				<xsd:annotation>
					<xsd:documentation>Gabon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>The Gambia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GG">
				<xsd:annotation>
					<xsd:documentation>Georgia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GM">
				<xsd:annotation>
					<xsd:documentation>Germany</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GH">
				<xsd:annotation>
					<xsd:documentation>Ghana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GI">
				<xsd:annotation>
					<xsd:documentation>Gibraltar</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GR">
				<xsd:annotation>
					<xsd:documentation>Greece</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GL">
				<xsd:annotation>
					<xsd:documentation>Greenland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GJ">
				<xsd:annotation>
					<xsd:documentation>Grenada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GQ">
				<xsd:annotation>
					<xsd:documentation>Guam</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GT">
				<xsd:annotation>
					<xsd:documentation>Guatemala</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GK">
				<xsd:annotation>
					<xsd:documentation>Guernsey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GV">
				<xsd:annotation>
					<xsd:documentation>Guinea</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PU">
				<xsd:annotation>
					<xsd:documentation>Guinea-Bissau</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GY">
				<xsd:annotation>
					<xsd:documentation>Guyana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HA">
				<xsd:annotation>
					<xsd:documentation>Haiti</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HM">
				<xsd:annotation>
					<xsd:documentation>Heard Island and McDonald Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VT">
				<xsd:annotation>
					<xsd:documentation>Holy See</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HO">
				<xsd:annotation>
					<xsd:documentation>Honduras</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HK">
				<xsd:annotation>
					<xsd:documentation>Hong Kong</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HQ">
				<xsd:annotation>
					<xsd:documentation>Howland Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HU">
				<xsd:annotation>
					<xsd:documentation>Hungary</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IC">
				<xsd:annotation>
					<xsd:documentation>Iceland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IN">
				<xsd:annotation>
					<xsd:documentation>India</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ID">
				<xsd:annotation>
					<xsd:documentation>Indonesia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IR">
				<xsd:annotation>
					<xsd:documentation>Iran</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IZ">
				<xsd:annotation>
					<xsd:documentation>Iraq</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EI">
				<xsd:annotation>
					<xsd:documentation>Ireland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IS">
				<xsd:annotation>
					<xsd:documentation>Israel</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IT">
				<xsd:annotation>
					<xsd:documentation>Italy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JM">
				<xsd:annotation>
					<xsd:documentation>Jamaica</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JN">
				<xsd:annotation>
					<xsd:documentation>Jan Mayen</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JA">
				<xsd:annotation>
					<xsd:documentation>Japan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DQ">
				<xsd:annotation>
					<xsd:documentation>Jarvis Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JE">
				<xsd:annotation>
					<xsd:documentation>Jersey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JQ">
				<xsd:annotation>
					<xsd:documentation>Johnston Atoll</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JO">
				<xsd:annotation>
					<xsd:documentation>Jordan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KZ">
				<xsd:annotation>
					<xsd:documentation>Kazakhstan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KE">
				<xsd:annotation>
					<xsd:documentation>Kenya</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KQ">
				<xsd:annotation>
					<xsd:documentation>Kingman Reef</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KR">
				<xsd:annotation>
					<xsd:documentation>Kiribati</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KN">
				<xsd:annotation>
					<xsd:documentation>Korea, Democratic People's Republic of (North)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KS">
				<xsd:annotation>
					<xsd:documentation>Korea, Republic of (South)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KV">
				<xsd:annotation>
					<xsd:documentation>Kosovo</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KU">
				<xsd:annotation>
					<xsd:documentation>Kuwait</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KG">
				<xsd:annotation>
					<xsd:documentation>Kyrgyzstan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LA">
				<xsd:annotation>
					<xsd:documentation>Laos</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LG">
				<xsd:annotation>
					<xsd:documentation>Latvia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LE">
				<xsd:annotation>
					<xsd:documentation>Lebanon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LT">
				<xsd:annotation>
					<xsd:documentation>Lesotho</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LI">
				<xsd:annotation>
					<xsd:documentation>Liberia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LY">
				<xsd:annotation>
					<xsd:documentation>Libya</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LS">
				<xsd:annotation>
					<xsd:documentation>Liechtenstein</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LH">
				<xsd:annotation>
					<xsd:documentation>Lithuania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LU">
				<xsd:annotation>
					<xsd:documentation>Luxembourg</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MC">
				<xsd:annotation>
					<xsd:documentation>Macau</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MK">
				<xsd:annotation>
					<xsd:documentation>Macedonia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MA">
				<xsd:annotation>
					<xsd:documentation>Madagascar</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MI">
				<xsd:annotation>
					<xsd:documentation>Malawi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MY">
				<xsd:annotation>
					<xsd:documentation>Malaysia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MV">
				<xsd:annotation>
					<xsd:documentation>Maldives</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ML">
				<xsd:annotation>
					<xsd:documentation>Mali</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MT">
				<xsd:annotation>
					<xsd:documentation>Malta</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IM">
				<xsd:annotation>
					<xsd:documentation>Man, Isle of</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RM">
				<xsd:annotation>
					<xsd:documentation>Marshall Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MR">
				<xsd:annotation>
					<xsd:documentation>Mauritania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MP">
				<xsd:annotation>
					<xsd:documentation>Mauritius</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MX">
				<xsd:annotation>
					<xsd:documentation>Mexico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MQ">
				<xsd:annotation>
					<xsd:documentation>Midway Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Moldova</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MN">
				<xsd:annotation>
					<xsd:documentation>Monaco</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MG">
				<xsd:annotation>
					<xsd:documentation>Mongolia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MJ">
				<xsd:annotation>
					<xsd:documentation>Montenegro</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MH">
				<xsd:annotation>
					<xsd:documentation>Montserrat</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MO">
				<xsd:annotation>
					<xsd:documentation>Morocco</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MZ">
				<xsd:annotation>
					<xsd:documentation>Mozambique</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WA">
				<xsd:annotation>
					<xsd:documentation>Namibia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NR">
				<xsd:annotation>
					<xsd:documentation>Nauru</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BQ">
				<xsd:annotation>
					<xsd:documentation>Navassa Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NP">
				<xsd:annotation>
					<xsd:documentation>Nepal</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NL">
				<xsd:annotation>
					<xsd:documentation>Netherlands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NC">
				<xsd:annotation>
					<xsd:documentation>New Caledonia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NZ">
				<xsd:annotation>
					<xsd:documentation>New Zealand</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NU">
				<xsd:annotation>
					<xsd:documentation>Nicaragua</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NG">
				<xsd:annotation>
					<xsd:documentation>Niger</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NI">
				<xsd:annotation>
					<xsd:documentation>Nigeria</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NE">
				<xsd:annotation>
					<xsd:documentation>Niue</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NF">
				<xsd:annotation>
					<xsd:documentation>Norfolk Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CQ">
				<xsd:annotation>
					<xsd:documentation>Northern Mariana Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NO">
				<xsd:annotation>
					<xsd:documentation>Norway</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MU">
				<xsd:annotation>
					<xsd:documentation>Oman</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OC">
				<xsd:annotation>
					<xsd:documentation>Other Country</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PK">
				<xsd:annotation>
					<xsd:documentation>Pakistan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PS">
				<xsd:annotation>
					<xsd:documentation>Palau</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LQ">
				<xsd:annotation>
					<xsd:documentation>Palmyra Atoll</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PM">
				<xsd:annotation>
					<xsd:documentation>Panama</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PP">
				<xsd:annotation>
					<xsd:documentation>Papua-New Guinea</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PF">
				<xsd:annotation>
					<xsd:documentation>Paracel Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Paraguay</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PE">
				<xsd:annotation>
					<xsd:documentation>Peru</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RP">
				<xsd:annotation>
					<xsd:documentation>Philippines</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PC">
				<xsd:annotation>
					<xsd:documentation>Pitcairn Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PL">
				<xsd:annotation>
					<xsd:documentation>Poland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PO">
				<xsd:annotation>
					<xsd:documentation>Portugal</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RQ">
				<xsd:annotation>
					<xsd:documentation>Puerto Rico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="QA">
				<xsd:annotation>
					<xsd:documentation>Qatar</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RO">
				<xsd:annotation>
					<xsd:documentation>Romania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RS">
				<xsd:annotation>
					<xsd:documentation>Russia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RW">
				<xsd:annotation>
					<xsd:documentation>Rwanda</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TB">
				<xsd:annotation>
					<xsd:documentation>Saint Barthelemy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RN">
				<xsd:annotation>
					<xsd:documentation>Saint Martin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WS">
				<xsd:annotation>
					<xsd:documentation>Samoa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SM">
				<xsd:annotation>
					<xsd:documentation>San Marino</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TP">
				<xsd:annotation>
					<xsd:documentation>Sao Tome and Principe</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SA">
				<xsd:annotation>
					<xsd:documentation>Saudi Arabia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SG">
				<xsd:annotation>
					<xsd:documentation>Senegal</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RI">
				<xsd:annotation>
					<xsd:documentation>Serbia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SE">
				<xsd:annotation>
					<xsd:documentation>Seychelles</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SL">
				<xsd:annotation>
					<xsd:documentation>Sierra Leone</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SN">
				<xsd:annotation>
					<xsd:documentation>Singapore</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NN">
				<xsd:annotation>
					<xsd:documentation>Sint Maarten</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LO">
				<xsd:annotation>
					<xsd:documentation>Slovakia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SI">
				<xsd:annotation>
					<xsd:documentation>Slovenia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BP">
				<xsd:annotation>
					<xsd:documentation>Solomon Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SO">
				<xsd:annotation>
					<xsd:documentation>Somalia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SF">
				<xsd:annotation>
					<xsd:documentation>South Africa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SX">
				<xsd:annotation>
					<xsd:documentation>South Georgia and the South Sandwich Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OD">
				<xsd:annotation>
					<xsd:documentation>South Sudan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SP">
				<xsd:annotation>
					<xsd:documentation>Spain</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PG">
				<xsd:annotation>
					<xsd:documentation>Spratly Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CE">
				<xsd:annotation>
					<xsd:documentation>Sri Lanka</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SH">
				<xsd:annotation>
					<xsd:documentation>St. Helena</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>St. Kitts and Nevis</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ST">
				<xsd:annotation>
					<xsd:documentation>St. Lucia Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SB">
				<xsd:annotation>
					<xsd:documentation>St. Pierre and Miquelon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VC">
				<xsd:annotation>
					<xsd:documentation>St. Vincent and the Grenadines</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SU">
				<xsd:annotation>
					<xsd:documentation>Sudan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NS">
				<xsd:annotation>
					<xsd:documentation>Suriname</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SV">
				<xsd:annotation>
					<xsd:documentation>Svalbard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WZ">
				<xsd:annotation>
					<xsd:documentation>Swaziland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SW">
				<xsd:annotation>
					<xsd:documentation>Sweden</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SZ">
				<xsd:annotation>
					<xsd:documentation>Switzerland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SY">
				<xsd:annotation>
					<xsd:documentation>Syria</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TW">
				<xsd:annotation>
					<xsd:documentation>Taiwan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TI">
				<xsd:annotation>
					<xsd:documentation>Tajikistan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TZ">
				<xsd:annotation>
					<xsd:documentation>Tanzania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TH">
				<xsd:annotation>
					<xsd:documentation>Thailand</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TO">
				<xsd:annotation>
					<xsd:documentation>Togo</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TL">
				<xsd:annotation>
					<xsd:documentation>Tokelau</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TN">
				<xsd:annotation>
					<xsd:documentation>Tonga</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TD">
				<xsd:annotation>
					<xsd:documentation>Trinidad and Tobago</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TS">
				<xsd:annotation>
					<xsd:documentation>Tunisia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TU">
				<xsd:annotation>
					<xsd:documentation>Turkey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TX">
				<xsd:annotation>
					<xsd:documentation>Turkmenistan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TK">
				<xsd:annotation>
					<xsd:documentation>Turks and Caicos Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TV">
				<xsd:annotation>
					<xsd:documentation>Tuvalu</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UG">
				<xsd:annotation>
					<xsd:documentation>Uganda</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UP">
				<xsd:annotation>
					<xsd:documentation>Ukraine</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>United Arab Emirates</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UK">
				<xsd:annotation>
					<xsd:documentation>United Kingdom (England, Northern Ireland, Scotland, and Wales)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UY">
				<xsd:annotation>
					<xsd:documentation>Uruguay</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UZ">
				<xsd:annotation>
					<xsd:documentation>Uzbekistan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>Vanuatu</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VE">
				<xsd:annotation>
					<xsd:documentation>Venezuela</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VM">
				<xsd:annotation>
					<xsd:documentation>Vietnam</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VQ">
				<xsd:annotation>
					<xsd:documentation>Virgin Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WQ">
				<xsd:annotation>
					<xsd:documentation>Wake Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WF">
				<xsd:annotation>
					<xsd:documentation>Wallis and Futuna</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WI">
				<xsd:annotation>
					<xsd:documentation>Western Sahara</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="YM">
				<xsd:annotation>
					<xsd:documentation>Yemen (Aden)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZA">
				<xsd:annotation>
					<xsd:documentation>Zambia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZI">
				<xsd:annotation>
					<xsd:documentation>Zimbabwe</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Country Code Type Including 'US' -->
	<xsd:simpleType name="AllCountriesType">
		<xsd:annotation>
			<xsd:documentation>Country Code Type Including 'US'</xsd:documentation>
		</xsd:annotation>
		<xsd:union memberTypes="CountryType">
			<xsd:simpleType>
				<xsd:restriction base="StringType">
					<xsd:enumeration value="US"/>
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:union>
	</xsd:simpleType>

	<!-- Type for a U.S. Structured Itemized Entry -->
	<xsd:complexType name="USItemizedEntryType">
		<xsd:sequence>
			<xsd:element name="Desc" type="LineExplanationType" />
			<xsd:element name="Amt" type="USAmountType" />
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for a Foreign Structured Itemized Entry -->
	<xsd:complexType name="ForeignItemizedEntryType">
		<xsd:sequence>
			<xsd:element name="Desc" type="LineExplanationType" />
			<xsd:element name="ForeignAmt" type="ForeignAmountType" />
		</xsd:sequence>
	</xsd:complexType>

	<!-- Depreciation Convention Type -->
	<xsd:simpleType name="DepreciationConventionCodeType">
		<xsd:annotation>
			<xsd:documentation>Depreciation convention abbreviations</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="HY">
				<xsd:annotation>
					<xsd:documentation>Half-year convention</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MQ">
				<xsd:annotation>
					<xsd:documentation>Mid-quarter convention</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MM">
				<xsd:annotation>
					<xsd:documentation>Mid-month convention</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="S/L">
				<xsd:annotation>
					<xsd:documentation>Straight line convention</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Depreciation Convention Type -->
	<xsd:simpleType name="DepreciationConventionType">
		<xsd:annotation>
			<xsd:documentation>Depreciation convention abbreviations</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="DepreciationConventionCodeType">
		</xsd:restriction>
	</xsd:simpleType>


	<!-- Depreciation Method Type -->
	<xsd:simpleType name="DepreciationMethodCodeType">
		<xsd:annotation>
			<xsd:documentation>Depreciation method abbreviations</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="200 DB">
				<xsd:annotation>
					<xsd:documentation>200% declining balance method</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="150 DB">
				<xsd:annotation>
					<xsd:documentation>150% declining balance method</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DB">
				<xsd:annotation>
					<xsd:documentation>Declining balance method</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="S/L">
				<xsd:annotation>
					<xsd:documentation>Straight line method</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Various">
				<xsd:annotation>
					<xsd:documentation>Method</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Depreciation Method Type -->
	<xsd:simpleType name="DepreciationMethodType">
		<xsd:annotation>
			<xsd:documentation>Depreciation method abbreviations</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="DepreciationMethodCodeType">
		</xsd:restriction>
	</xsd:simpleType>


	<!-- Type for a U.S. Address -->
	<xsd:complexType name="USAddressType">
		<xsd:sequence>
			<xsd:element name="AddressLine1Txt" type="StreetAddressType">
				<xsd:annotation>
					<xsd:documentation>Address line 1</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddressLine2Txt" type="StreetAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 2</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CityNm" type="CityType">
				<xsd:annotation>
					<xsd:documentation>City</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="StateAbbreviationCd" type="StateType">
				<xsd:annotation>
					<xsd:documentation>State</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ZIPCd" type="ZIPCodeType">
				<xsd:annotation>
					<xsd:documentation>ZIP code</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for Other U.S. Address -->
	<xsd:complexType name="OtherUSAddressType">
		<xsd:sequence>
			<xsd:element name="AddressLine1Txt" type="StreetAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 1</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddressLine2Txt" type="StreetAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 2</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CityNm" type="CityType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>City</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="StateAbbreviationCd" type="StateType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>State</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ZIPCd" type="ZIPCodeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>ZIP code</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for a Foreign Address -->
	<xsd:complexType name="ForeignAddressType">
		<xsd:sequence>
			<xsd:element name="AddressLine1Txt" type="StreetAddressType">
				<xsd:annotation>
					<xsd:documentation>Address line 1</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddressLine2Txt" type="StreetAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 2</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CityNm" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>City</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="StringType">
						<xsd:pattern value="([A-Za-z] ?)*[A-Za-z]" />
						<xsd:maxLength value="50"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="ProvinceOrStateNm" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Province or state</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="17"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="CountryCd" type="CountryType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Country</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ForeignPostalCd" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Postal code</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="16"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for Other Foreign Address -->
	<xsd:complexType name="OtherForeignAddressType">
		<xsd:sequence>
			<xsd:element name="AddressLine1Txt" type="StreetAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 1</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddressLine2Txt" type="StreetAddressType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Address line 2</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CityNm" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>City</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="StringType">
						<xsd:pattern value="([A-Za-z] ?)*[A-Za-z]" />
						<xsd:maxLength value="50"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="ProvinceOrStateNm" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Province or state</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="17"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="CountryCd" type="CountryType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Country</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ForeignPostalCd" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Postal code</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="16"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for a Business Name -->
	<xsd:complexType name="BusinessNameType">
		<xsd:sequence>
			<xsd:element name="BusinessNameLine1Txt" type="BusinessNameLine1Type">
				<xsd:annotation>
					<xsd:documentation>Business name line 1</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="BusinessNameLine2Txt" type="BusinessNameLine2Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Business name line 2</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Name and Address Type -->
	<xsd:complexType name="NameAndAddressType">
		<xsd:annotation>
			<xsd:documentation>Recurring Name and Address Type</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:choice minOccurs="0">
				<xsd:element name="PersonNm" type="PersonNameType"/>
				<xsd:element name="BusinessName" type="BusinessNameType"/>
			</xsd:choice>
			<xsd:choice minOccurs="0">
				<xsd:element name="USAddress" type="USAddressType"/>
				<xsd:element name="ForeignAddress" type="ForeignAddressType"/>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for Building Identification Number (BIN) -->
	<xsd:simpleType name="BINType">
		<xsd:restriction base="TextType">
			<xsd:maxLength value="9"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for GTX key -->
	<xsd:simpleType name="GTXKeyType">
		<xsd:restriction base="TextType">
			<xsd:maxLength value="20" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for Method of Accounting -->
	<xsd:simpleType name="MethodOfAccountingType">
		<xsd:restriction base="StringType">
			<xsd:enumeration value="cash" />
			<xsd:enumeration value="accrual" />
			<xsd:enumeration value="hybrid" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for Numeric -->
	<xsd:simpleType name="NumericType">
		<xsd:restriction base="StringType">
			<xsd:pattern value="[0-9]*" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for Alpha-Numeric -->
	<xsd:simpleType name="AlphaNumericType">
		<xsd:restriction base="StringType">
			<xsd:pattern value="[A-Za-z0-9]*" />
			<xsd:maxLength value="150" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for Alpha-Numeric (plus Parentheses) -->
	<xsd:simpleType name="AlphaNumericAndParenthesesType">
		<xsd:restriction base="StringType">
			<xsd:pattern value="[A-Za-z0-9\(\)]*" />
			<xsd:maxLength value="250" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- IRS Service Center Type -->
	<xsd:simpleType name="IRSServiceCenterType">
		<xsd:restriction base="TextType">
			<xsd:maxLength value="12" />
			<xsd:pattern value="efile"/>
			<xsd:pattern value=".*"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- A string Type enumerated with "VARIOUS" literal -->
	<xsd:simpleType name="StringVARIOUSType">
		<xsd:restriction base="StringType">
			<xsd:enumeration value="VARIOUS"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for CUSIP Number -->
	<xsd:simpleType name="CUSIPNumberType">
		<xsd:restriction base="AlphaNumericType">
			<xsd:length value="9" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Fuel Gallons Type -->
	<xsd:simpleType name="FuelGallonsType">
		<xsd:restriction base="IntegerType">
			<xsd:totalDigits value="9" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Fuel Gallons Non-Negative Type -->
	<xsd:simpleType name="FuelGallonsNNType">
		<xsd:restriction base="IntegerNNType">
			<xsd:totalDigits value="9"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Miles Type -->
	<xsd:simpleType name="MilesType">
		<xsd:restriction base="IntegerType">
			<xsd:totalDigits value="12"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Miles Non-Negative Type -->
	<xsd:simpleType name="MilesNNType">
		<xsd:restriction base="IntegerNNType">
			<xsd:totalDigits value="12"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Government Code Type -->
	<xsd:simpleType name="GovernmentCodeType">
		<xsd:restriction base="StringType">

			<xsd:enumeration value="IRS">
				<xsd:annotation>
					<xsd:documentation>IRS</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ALST">
				<xsd:annotation>
					<xsd:documentation>Alabama</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="AKST">
				<xsd:annotation>
					<xsd:documentation>Alaska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="AZST">
				<xsd:annotation>
					<xsd:documentation>Arizona</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ARST">
				<xsd:annotation>
					<xsd:documentation>Arkansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="CAST">
				<xsd:annotation>
					<xsd:documentation>California</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="CNCT">
				<xsd:annotation>
					<xsd:documentation>Cincinnati</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="COCT">
				<xsd:annotation>
					<xsd:documentation>Columbus</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="COST">
				<xsd:annotation>
					<xsd:documentation>Colorado</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="CTST">
				<xsd:annotation>
					<xsd:documentation>Connecticut</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="DECT">
				<xsd:annotation>
					<xsd:documentation>Detroit</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="DEST">
				<xsd:annotation>
					<xsd:documentation>Delaware</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="DCST">
				<xsd:annotation>
					<xsd:documentation>District of Columbia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="FLST">
				<xsd:annotation>
					<xsd:documentation>Florida</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="GAST">
				<xsd:annotation>
					<xsd:documentation>Georgia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="HIST">
				<xsd:annotation>
					<xsd:documentation>Hawaii</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="IDST">
				<xsd:annotation>
					<xsd:documentation>Idaho</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ILST">
				<xsd:annotation>
					<xsd:documentation>Illinois</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="INST">
				<xsd:annotation>
					<xsd:documentation>Indiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="IAST">
				<xsd:annotation>
					<xsd:documentation>Iowa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="KACT">
				<xsd:annotation>
					<xsd:documentation>Kansas City</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="KSST">
				<xsd:annotation>
					<xsd:documentation>Kansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="KYST">
				<xsd:annotation>
					<xsd:documentation>Kentucky</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="LAST">
				<xsd:annotation>
					<xsd:documentation>Louisiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="LECT">
				<xsd:annotation>
					<xsd:documentation>Louisville</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="LXCT">
				<xsd:annotation>
					<xsd:documentation>Lexington</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MEST">
				<xsd:annotation>
					<xsd:documentation>Maine</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MDST">
				<xsd:annotation>
					<xsd:documentation>Maryland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MAST">
				<xsd:annotation>
					<xsd:documentation>Massachusetts</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MIST">
				<xsd:annotation>
					<xsd:documentation>Michigan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MNST">
				<xsd:annotation>
					<xsd:documentation>Minnesota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MSST">
				<xsd:annotation>
					<xsd:documentation>Mississippi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MOST">
				<xsd:annotation>
					<xsd:documentation>Missouri</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MTST">
				<xsd:annotation>
					<xsd:documentation>Montana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NEST">
				<xsd:annotation>
					<xsd:documentation>Nebraska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NVST">
				<xsd:annotation>
					<xsd:documentation>Nevada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NHST">
				<xsd:annotation>
					<xsd:documentation>New Hampshire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NJST">
				<xsd:annotation>
					<xsd:documentation>New Jersey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NMST">
				<xsd:annotation>
					<xsd:documentation>New Mexico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NYST">
				<xsd:annotation>
					<xsd:documentation>New York</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NCST">
				<xsd:annotation>
					<xsd:documentation>North Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NDST">
				<xsd:annotation>
					<xsd:documentation>North Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="OCCT">
				<xsd:annotation>
					<xsd:documentation>Ohio Central Collection Agency</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="OHST">
				<xsd:annotation>
					<xsd:documentation>Ohio</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="OKST">
				<xsd:annotation>
					<xsd:documentation>Oklahoma</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ORCT">
				<xsd:annotation>
					<xsd:documentation>Ohio Regional Income Tax Agency</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ORST">
				<xsd:annotation>
					<xsd:documentation>Oregon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="PAST">
				<xsd:annotation>
					<xsd:documentation>Pennsylvania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="POCT">
				<xsd:annotation>
					<xsd:documentation>Portland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="RIST">
				<xsd:annotation>
					<xsd:documentation>Rhode Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="SCST">
				<xsd:annotation>
					<xsd:documentation>South Carolina</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="SDST">
				<xsd:annotation>
					<xsd:documentation>South Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="SLCT">
				<xsd:annotation>
					<xsd:documentation>St Louis</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="TNST">
				<xsd:annotation>
					<xsd:documentation>Tennessee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="TOCT">
				<xsd:annotation>
					<xsd:documentation>Toledo</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="TXST">
				<xsd:annotation>
					<xsd:documentation>Texas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="UTST">
				<xsd:annotation>
					<xsd:documentation>Utah</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="VTST">
				<xsd:annotation>
					<xsd:documentation>Vermont</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="VAST">
				<xsd:annotation>
					<xsd:documentation>Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="WAST">
				<xsd:annotation>
					<xsd:documentation>Washington</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="WVST">
				<xsd:annotation>
					<xsd:documentation>West Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="WIST">
				<xsd:annotation>
					<xsd:documentation>Wisconsin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="WYST">
				<xsd:annotation>
					<xsd:documentation>Wyoming</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="NYCT">
				<xsd:annotation>
					<xsd:documentation>New York City</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="PHCT">
				<xsd:annotation>
					<xsd:documentation>Philadelphia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

		</xsd:restriction>
	</xsd:simpleType>

	<!-- Tax Shelter Registration Type -->
	<xsd:simpleType name="TaxShelterRegistrationType">
		<xsd:annotation>
			<xsd:documentation>Tax Shelter Registration Number - 1 to 22 positions in length with alphanumeric characters.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Za-z0-9]{1,22}"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Recurring Number of Shares Type -->
	<xsd:simpleType name="NumberOfSharesType">
		<xsd:annotation>
			<xsd:documentation>Recurring Number of Shares Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="IntegerType">
			<xsd:totalDigits value="20"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for VINs -->
	<xsd:simpleType name="VINType">
		<xsd:annotation>
			<xsd:documentation>Type for Vehicle Identification No. - 1 to 17 or 19 alphanumeric characters</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Z0-9]{1,17}"/>
			<xsd:pattern value="[A-Z0-9]{19}"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Number of Days Type -->
	<xsd:simpleType name="NumberOfDaysType">
		<xsd:annotation>
			<xsd:documentation>Number of days</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="IntegerType">
			<xsd:totalDigits value="3"/>
			<xsd:minInclusive value="000"/>
			<xsd:maxInclusive value="366"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- PIN Entered By Type Indicate Who Entered the PIN Taxpayer or ERO -->
	<xsd:simpleType name="PINEnteredByType">
		<xsd:annotation>
			<xsd:documentation>Used to indicate who entered the PIN (Personal Identification Number)</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Taxpayer" />
			<xsd:enumeration value="ERO" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- PIN Code Type -->
	<xsd:simpleType name="PINCodeType">
		<xsd:annotation>
			<xsd:documentation>Used to indicate the type of  PIN (Personal Identification Number)</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Practitioner" />
			<xsd:enumeration value="Self-Select Practitioner" />
			<xsd:enumeration value="Self-Select On-Line" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Consortium Type -->
	<xsd:simpleType name="ConsortiumType">
		<xsd:annotation>
			<xsd:documentation>Used to indicate if a return was filed using the English or Spanish Free-File Program or Free Fillable Forms</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="English Free-File" />
			<xsd:enumeration value="Spanish Free-File" />
			<xsd:enumeration value="Free Fillable Forms" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Partners Page Filing Type -->
	<xsd:simpleType name="PartnersPageFilingType">
		<xsd:annotation>
			<xsd:documentation>Used to indicate if a return was filed using the English or Spanish Partners Page (Affordable Filing Program)</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Partners Page English"/>
			<xsd:enumeration value="Partners Page Spanish"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Person Full Name Type -->
	<xsd:complexType name="PersonFullNameType">
		<xsd:sequence>
			<xsd:element name="PersonFirstNm" type="PersonFirstNameType">
				<xsd:annotation>
					<xsd:documentation>Person First Name</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PersonLastNm" type="PersonLastNameType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Person Last Name</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Person First Name Type -->
	<xsd:simpleType name="PersonFirstNameType">
		<xsd:annotation>
			<xsd:documentation>Typically used for a person's first name. Legal Characters: A-Z, a-z, hyphen and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20" />
			<xsd:pattern value="([A-Za-z\-] ?)*[A-Za-z\-]" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Person Last Name Type -->
	<xsd:simpleType name="PersonLastNameType">
		<xsd:annotation>
			<xsd:documentation>Typically used for a person's last name. Legal Characters: A-Z, a-z, hyphen and single space. Illegal Character: leading space, trailing space, adjacent spaces, and other symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20" />
			<xsd:pattern value="([A-Za-z\-] ?)*[A-Za-z\-]" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Person Name Control Type -->
	<xsd:simpleType name="PersonNameControlType">
		<xsd:annotation>
			<xsd:documentation>Used for a Name Control. Legal Characters: A-Z, hyphen and space. Illegal Character: numbers and symbols.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Z][A-Z\- ]{0,3}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- IP Address IPv4 Type -->
	<xsd:simpleType name="IPv4Type">
		<xsd:annotation>
			<xsd:documentation>Decimal format for IP Address</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- IP Address IPv6 Type -->
	<xsd:simpleType name="IPv6Type">
		<xsd:annotation>
			<xsd:documentation>Hexidecimal format for IP Address</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9A-F:]{1,39}"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- IP Address Type -->
	<xsd:complexType name="IPAddressType">
		<xsd:annotation>
			<xsd:documentation>IP address type to include either decimal or hexidecimal format</xsd:documentation>
		</xsd:annotation>
		<xsd:choice>
			<xsd:element name="IPv4AddressTxt" type="IPv4Type"/>
			<xsd:element name="IPv6AddressTxt" type="IPv6Type"/>
		</xsd:choice>
	</xsd:complexType>

	<!-- Time Type in the format of hh:mm:ss -->
	<xsd:simpleType name="TimeType">
		<xsd:annotation>
			<xsd:documentation>Base type for time</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:time">
			<xsd:pattern value="[0-9]{2}:[0-9]{2}:[0-9]{2}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Timezone Type -->
	<xsd:simpleType name="TimezoneType">
		<xsd:restriction base="StringType">

			<xsd:enumeration value="US">
				<xsd:annotation>
					<xsd:documentation>Universal Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ES">
				<xsd:annotation>
					<xsd:documentation>Eastern Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="ED">
				<xsd:annotation>
					<xsd:documentation>Eastern Daylight</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Central Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Central Daylight</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MS">
				<xsd:annotation>
					<xsd:documentation>Mountain Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Mountain Daylight</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="PS">
				<xsd:annotation>
					<xsd:documentation>Pacific Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="PD">
				<xsd:annotation>
					<xsd:documentation>Pacific Daylight</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>Alaskan Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="AD">
				<xsd:annotation>
					<xsd:documentation>Alaskan Daylight</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="HS">
				<xsd:annotation>
					<xsd:documentation>Hawaiian Standard</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>

			<xsd:enumeration value="HD">
				<xsd:annotation>
					<xsd:documentation>Hawaiian Daylight</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Preparer Personal Identification Number Type -->
	<xsd:simpleType name="STINType">
		<xsd:annotation>
			<xsd:documentation>Type for Preparer Personal Identification No. - S followed by 8 digits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="S[0-9]{8}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for Vehicle Description:  Year, Make, and Model of Vehicle -->
	<xsd:complexType name="VehicleDescriptionGrpType">
		<xsd:sequence>
			<!-- Year of Vehicle -->
			<xsd:element name="VehicleModelYr" type="YearType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Year of Vehicle</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Make of Vehicle -->
			<xsd:element name="VehicleMakeNameTxt" type="ShortDescriptionType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Make of Vehicle</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Model of Vehicle -->
			<xsd:element name="VehicleModelNameTxt" type="ShortDescriptionType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Model of Vehicle</Description>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for Registration No. -->
	<xsd:simpleType name="RegistrationNumType">
		<xsd:restriction base="StringType">
			<xsd:pattern value="[A-Z0-9]{1,20}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Type for Reference ID Number -->
	<xsd:complexType name="ForeignEntityIdentificationGrpType">
		<xsd:sequence>
			<xsd:element name="ForeignEntityReferenceIdNum">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Reference ID number</Description>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="AlphaNumericType">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="50"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!-- Type for Device Id -->
	<xsd:simpleType name="DeviceIdType">
		<xsd:restriction base="TextType">
			<xsd:pattern value="[A-F0-9]{40}" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Data Type for Name of Foreign Country or U.S. Possession - RIC -->
	<xsd:simpleType name="ForeignRegulatedInvestmtCompCdType">
		<xsd:restriction base="StringType">
			<xsd:enumeration value="RIC"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Submission ID Type - 20 characters (EFIN + ccyyddd + 7-character lower case alphanumeric) -->
	<xsd:simpleType name="SubmissionIdType">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{13}[a-z0-9]{7}"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Foreign Taxpayer Identification Number -->
	<xsd:simpleType name="FTINType">
		<xsd:restriction base="TextType">
			<xsd:maxLength value="50" />
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Currency Code for Country -->
	<xsd:simpleType name="CurrencyCodeType">
		<xsd:annotation>
			<xsd:documentation>Currency code for country of the corporation,  Please use International Organization for Standardization (ISO) 4217 three letter alphabetic code abbreviations</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:enumeration value="XUA">
				<xsd:annotation>
					<xsd:documentation>ADB Unit of Account - MEMBER COUNTRIES OF THE AFRICAN DEVELOPMENT BANK GROUP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AFN">
				<xsd:annotation>
					<xsd:documentation>Afghani - AFGHANISTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DZD">
				<xsd:annotation>
					<xsd:documentation>Algerian Dinar - ALGERIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ARS">
				<xsd:annotation>
					<xsd:documentation>Argentine Peso - ARGENTINA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AMD">
				<xsd:annotation>
					<xsd:documentation>Armenian Dram - ARMENIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AWG">
				<xsd:annotation>
					<xsd:documentation>Aruban Florin - ARUBA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AUD">
				<xsd:annotation>
					<xsd:documentation>Australian Dollar - AUSTRALIA, CHRISTMAS ISLAND, COCOS (KEELING) ISLANDS (THE), HEARD ISLAND AND McDONALD ISLANDS, KIRIBATI, NAURU, NORFOLK ISLAND, TUVALU</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZN">
				<xsd:annotation>
					<xsd:documentation>Azerbaijan Manat - AZERBAIJAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BSD">
				<xsd:annotation>
					<xsd:documentation>Bahamian Dollar - BAHAMAS (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BHD">
				<xsd:annotation>
					<xsd:documentation>Bahraini Dinar - BAHRAIN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="THB">
				<xsd:annotation>
					<xsd:documentation>Baht - THAILAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PAB">
				<xsd:annotation>
					<xsd:documentation>Balboa - PANAMA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BBD">
				<xsd:annotation>
					<xsd:documentation>Barbados Dollar - BARBADOS</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BYN">
				<xsd:annotation>
					<xsd:documentation>Belarusian Ruble - BELARUS</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BZD">
				<xsd:annotation>
					<xsd:documentation>Belize Dollar - BELIZE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BMD">
				<xsd:annotation>
					<xsd:documentation>Bermudian Dollar - BERMUDA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VES">
				<xsd:annotation>
					<xsd:documentation>Bolivar Soberano - VENEZUELA (BOLIVARIAN REPUBLIC OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BOB">
				<xsd:annotation>
					<xsd:documentation>Boliviano - BOLIVIA (PLURINATIONAL STATE OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BRL">
				<xsd:annotation>
					<xsd:documentation>Brazilian Real - BRAZIL</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BND">
				<xsd:annotation>
					<xsd:documentation>Brunei Dollar - BRUNEI DARUSSALAM</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BGN">
				<xsd:annotation>
					<xsd:documentation>Bulgarian Lev - BULGARIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BIF">
				<xsd:annotation>
					<xsd:documentation>Burundi Franc - BURUNDI</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CVE">
				<xsd:annotation>
					<xsd:documentation>Cabo Verde Escudo - CABO VERDE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CAD">
				<xsd:annotation>
					<xsd:documentation>Canadian Dollar - CANADA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KYD">
				<xsd:annotation>
					<xsd:documentation>Cayman Islands Dollar - CAYMAN ISLANDS (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="XOF">
				<xsd:annotation>
					<xsd:documentation>CFA Franc BCEAO - BENIN, BURKINA FASO, COTE D'IVOIRE, GUINEA-BISSAU, MALI, NIGER (THE), SENEGAL, TOGO</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="XAF">
				<xsd:annotation>
					<xsd:documentation>CFA Franc BEAC - CAMEROON, CENTRAL AFRICAN REPUBLIC (THE), CHAD, CONGO (THE), EQUATORIAL GUINEA, GABON</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="XPF">
				<xsd:annotation>
					<xsd:documentation>CFP Franc - FRENCH POLYNESIA, NEW CALEDONIA, WALLIS AND FUTUNA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CLP">
				<xsd:annotation>
					<xsd:documentation>Chilean Peso - CHILE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="COP">
				<xsd:annotation>
					<xsd:documentation>Colombian Peso - COLOMBIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KMF">
				<xsd:annotation>
					<xsd:documentation>Comorian Franc  - COMOROS (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CDF">
				<xsd:annotation>
					<xsd:documentation>Congolese Franc - CONGO (THE DEMOCRATIC REPUBLIC OF THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BAM">
				<xsd:annotation>
					<xsd:documentation>Convertible Mark - BOSNIA AND HERZEGOVINA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NIO">
				<xsd:annotation>
					<xsd:documentation>Cordoba Oro - NICARAGUA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CRC">
				<xsd:annotation>
					<xsd:documentation>Costa Rican Colon - COSTA RICA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CUP">
				<xsd:annotation>
					<xsd:documentation>Cuban Peso - CUBA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CZK">
				<xsd:annotation>
					<xsd:documentation>Czech Koruna - CZECHIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GMD">
				<xsd:annotation>
					<xsd:documentation>Dalasi - GAMBIA (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DKK">
				<xsd:annotation>
					<xsd:documentation>Danish Krone - DENMARK, FAROE ISLANDS (THE), GREENLAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MKD">
				<xsd:annotation>
					<xsd:documentation>Denar - MACEDONIA (THE FORMER YUGOSLAV REPUBLIC OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DJF">
				<xsd:annotation>
					<xsd:documentation>Djibouti Franc - DJIBOUTI</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="STN">
				<xsd:annotation>
					<xsd:documentation>Dobra - SAO TOME AND PRINCIPE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DOP">
				<xsd:annotation>
					<xsd:documentation>Dominican Peso - DOMINICAN REPUBLIC (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VND">
				<xsd:annotation>
					<xsd:documentation>Dong - VIET NAM</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="XCD">
				<xsd:annotation>
					<xsd:documentation>East Caribbean Dollar - ANGUILLA, ANTIGUA AND BARBUDA, DOMINICA, GRENADA, MONTSERRAT, SAINT KITTS AND NEVIS, SAINT LUCIA, SAINT VINCENT AND THE GRENADINES</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EGP">
				<xsd:annotation>
					<xsd:documentation>Egyptian Pound - EGYPT</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SVC">
				<xsd:annotation>
					<xsd:documentation>El Salvador Colon - EL SALVADOR</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ETB">
				<xsd:annotation>
					<xsd:documentation>Ethiopian Birr - ETHIOPIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EUR">
				<xsd:annotation>
					<xsd:documentation>Euro - ALAND ISLANDS, ANDORRA, AUSTRIA, BELGIUM, CYPRUS, ESTONIA, EUROPEAN UNION, FINLAND, FRANCE, FRENCH GUIANA, FRENCH SOUTHERN TERRITORIES (THE), GERMANY, GREECE, GUADELOUPE, HOLY SEE (THE), IRELAND, ITALY, LATVIA, LITHUANIA, LUXEMBOURG, MALTA, MARTINIQUE, MAYOTTE, MONACO, MONTENEGRO, NETHERLANDS (THE), PORTUGAL, REUNION, SAINT BARTHELEMY, SAINT MARTIN (FRENCH PART), SAINT PIERRE AND MIQUELON, SAN MARINO, SLOVAKIA, SLOVENIA, SPAIN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FKP">
				<xsd:annotation>
					<xsd:documentation>Falkland Islands Pound - FALKLAND ISLANDS (THE) [MALVINAS]</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FJD">
				<xsd:annotation>
					<xsd:documentation>Fiji Dollar - FIJI</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HUF">
				<xsd:annotation>
					<xsd:documentation>Forint - HUNGARY</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GHS">
				<xsd:annotation>
					<xsd:documentation>Ghana Cedi - GHANA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GIP">
				<xsd:annotation>
					<xsd:documentation>Gibraltar Pound - GIBRALTAR</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HTG">
				<xsd:annotation>
					<xsd:documentation>Gourde - HAITI</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PYG">
				<xsd:annotation>
					<xsd:documentation>Guarani - PARAGUAY</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GNF">
				<xsd:annotation>
					<xsd:documentation>Guinean Franc - GUINEA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GYD">
				<xsd:annotation>
					<xsd:documentation>Guyana Dollar - GUYANA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HKD">
				<xsd:annotation>
					<xsd:documentation>Hong Kong Dollar - HONG KONG</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UAH">
				<xsd:annotation>
					<xsd:documentation>Hryvnia - UKRAINE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ISK">
				<xsd:annotation>
					<xsd:documentation>Iceland Krona - ICELAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="INR">
				<xsd:annotation>
					<xsd:documentation>Indian Rupee - BHUTAN, INDIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IRR">
				<xsd:annotation>
					<xsd:documentation>Iranian Rial - IRAN (ISLAMIC REPUBLIC OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IQD">
				<xsd:annotation>
					<xsd:documentation>Iraqi Dinar - IRAQ</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JMD">
				<xsd:annotation>
					<xsd:documentation>Jamaican Dollar - JAMAICA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JOD">
				<xsd:annotation>
					<xsd:documentation>Jordanian Dinar - JORDAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KES">
				<xsd:annotation>
					<xsd:documentation>Kenyan Shilling - KENYA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PGK">
				<xsd:annotation>
					<xsd:documentation>Kina - PAPUA NEW GUINEA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HRK">
				<xsd:annotation>
					<xsd:documentation>Kuna - CROATIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KWD">
				<xsd:annotation>
					<xsd:documentation>Kuwaiti Dinar - KUWAIT</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AOA">
				<xsd:annotation>
					<xsd:documentation>Kwanza - ANGOLA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MMK">
				<xsd:annotation>
					<xsd:documentation>Kyat - MYANMAR</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LAK">
				<xsd:annotation>
					<xsd:documentation>Lao Kip - LAO PEOPLE’S DEMOCRATIC REPUBLIC (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GEL">
				<xsd:annotation>
					<xsd:documentation>Lari - GEORGIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LBP">
				<xsd:annotation>
					<xsd:documentation>Lebanese Pound - LEBANON</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ALL">
				<xsd:annotation>
					<xsd:documentation>Lek - ALBANIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HNL">
				<xsd:annotation>
					<xsd:documentation>Lempira - HONDURAS</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SLL">
				<xsd:annotation>
					<xsd:documentation>Leone - SIERRA LEONE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LRD">
				<xsd:annotation>
					<xsd:documentation>Liberian Dollar - LIBERIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LYD">
				<xsd:annotation>
					<xsd:documentation>Libyan Dinar - LIBYA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SZL">
				<xsd:annotation>
					<xsd:documentation>Lilangeni - ESWATINI</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LSL">
				<xsd:annotation>
					<xsd:documentation>Loti - LESOTHO</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MGA">
				<xsd:annotation>
					<xsd:documentation>Malagasy Ariary - MADAGASCAR</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MWK">
				<xsd:annotation>
					<xsd:documentation>Malawi Kwacha - MALAWI</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MYR">
				<xsd:annotation>
					<xsd:documentation>Malaysian Ringgit - MALAYSIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MUR">
				<xsd:annotation>
					<xsd:documentation>Mauritius Rupee - MAURITIUS</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MXN">
				<xsd:annotation>
					<xsd:documentation>Mexican Peso - MEXICO</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MXV">
				<xsd:annotation>
					<xsd:documentation>Mexican Unidad de Inversion (UDI) - MEXICO</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MDL">
				<xsd:annotation>
					<xsd:documentation>Moldovan Leu - MOLDOVA (THE REPUBLIC OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MAD">
				<xsd:annotation>
					<xsd:documentation>Moroccan Dirham - MOROCCO, WESTERN SAHARA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MZN">
				<xsd:annotation>
					<xsd:documentation>Mozambique Metical - MOZAMBIQUE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BOV">
				<xsd:annotation>
					<xsd:documentation>Mvdol - BOLIVIA (PLURINATIONAL STATE OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NGN">
				<xsd:annotation>
					<xsd:documentation>Naira - NIGERIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ERN">
				<xsd:annotation>
					<xsd:documentation>Nakfa - ERITREA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NAD">
				<xsd:annotation>
					<xsd:documentation>Namibia Dollar - NAMIBIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NPR">
				<xsd:annotation>
					<xsd:documentation>Nepalese Rupee - NEPAL</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ANG">
				<xsd:annotation>
					<xsd:documentation>Netherlands Antillean Guilder - CURACAO, SINT MAARTEN (DUTCH PART)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ILS">
				<xsd:annotation>
					<xsd:documentation>New Israeli Sheqel - ISRAEL</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TWD">
				<xsd:annotation>
					<xsd:documentation>New Taiwan Dollar - TAIWAN (PROVINCE OF CHINA)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NZD">
				<xsd:annotation>
					<xsd:documentation>New Zealand Dollar - COOK ISLANDS (THE), NEW ZEALAND ,NIUE ,PITCAIRN ,TOKELAU</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BTN">
				<xsd:annotation>
					<xsd:documentation>Ngultrum - BHUTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KPW">
				<xsd:annotation>
					<xsd:documentation>North Korean Won - KOREA (THE DEMOCRATIC PEOPLE’S REPUBLIC OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NOK">
				<xsd:annotation>
					<xsd:documentation>Norwegian Krone - BOUVET ISLAND, NORWAY, SVALBARD AND JAN MAYEN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MRU">
				<xsd:annotation>
					<xsd:documentation>Ouguiya - MAURITANIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TOP">
				<xsd:annotation>
					<xsd:documentation>Pa'anga - TONGA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PKR">
				<xsd:annotation>
					<xsd:documentation>Pakistan Rupee - PAKISTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MOP">
				<xsd:annotation>
					<xsd:documentation>Pataca - MACAO</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CUC">
				<xsd:annotation>
					<xsd:documentation>Peso Convertible - CUBA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UYU">
				<xsd:annotation>
					<xsd:documentation>Peso Uruguayo - URUGUAY</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PHP">
				<xsd:annotation>
					<xsd:documentation>Philippine Peso - PHILIPPINES (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GBP">
				<xsd:annotation>
					<xsd:documentation>Pound Sterling - GUERNSEY, ISLE OF MAN, JERSEY, UNITED KINGDOM OF GREAT BRITAIN AND NORTHERN IRELAND (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BWP">
				<xsd:annotation>
					<xsd:documentation>Pula - BOTSWANA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="QAR">
				<xsd:annotation>
					<xsd:documentation>Qatari Rial - QATAR</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GTQ">
				<xsd:annotation>
					<xsd:documentation>Quetzal - GUATEMALA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZAR">
				<xsd:annotation>
					<xsd:documentation>Rand - LESOTHO, NAMIBIA, SOUTH AFRICA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OMR">
				<xsd:annotation>
					<xsd:documentation>Rial Omani - OMAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KHR">
				<xsd:annotation>
					<xsd:documentation>Riel - CAMBODIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RON">
				<xsd:annotation>
					<xsd:documentation>Romanian Leu - ROMANIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MVR">
				<xsd:annotation>
					<xsd:documentation>Rufiyaa - MALDIVES</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IDR">
				<xsd:annotation>
					<xsd:documentation>Rupiah - INDONESIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RUB">
				<xsd:annotation>
					<xsd:documentation>Russian Ruble - RUSSIAN FEDERATION (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RWF">
				<xsd:annotation>
					<xsd:documentation>Rwanda Franc - RWANDA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SHP">
				<xsd:annotation>
					<xsd:documentation>Saint Helena Pound - SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SAR">
				<xsd:annotation>
					<xsd:documentation>Saudi Riyal - SAUDI ARABIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="XDR">
				<xsd:annotation>
					<xsd:documentation>SDR (Special Drawing Right) - INTERNATIONAL MONETARY FUND (IMF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RSD">
				<xsd:annotation>
					<xsd:documentation>Serbian Dinar - SERBIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SCR">
				<xsd:annotation>
					<xsd:documentation>Seychelles Rupee - SEYCHELLES</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SGD">
				<xsd:annotation>
					<xsd:documentation>Singapore Dollar - SINGAPORE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PEN">
				<xsd:annotation>
					<xsd:documentation>Sol - PERU</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SBD">
				<xsd:annotation>
					<xsd:documentation>Solomon Islands Dollar - SOLOMON ISLANDS</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KGS">
				<xsd:annotation>
					<xsd:documentation>Som - KYRGYZSTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SOS">
				<xsd:annotation>
					<xsd:documentation>Somali Shilling - SOMALIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TJS">
				<xsd:annotation>
					<xsd:documentation>Somoni - TAJIKISTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SSP">
				<xsd:annotation>
					<xsd:documentation>South Sudanese Pound - SOUTH SUDAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LKR">
				<xsd:annotation>
					<xsd:documentation>Sri Lanka Rupee - SRI LANKA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="XSU">
				<xsd:annotation>
					<xsd:documentation>Sucre - SISTEMA UNITARIO DE COMPENSACION REGIONAL DE PAGOS "SUCRE"</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SDG">
				<xsd:annotation>
					<xsd:documentation>Sudanese Pound - SUDAN (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SRD">
				<xsd:annotation>
					<xsd:documentation>Surinam Dollar - SURINAME</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SEK">
				<xsd:annotation>
					<xsd:documentation>Swedish Krona - SWEDEN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CHF">
				<xsd:annotation>
					<xsd:documentation>Swiss Franc - LIECHTENSTEIN, SWITZERLAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SYP">
				<xsd:annotation>
					<xsd:documentation>Syrian Pound - SYRIAN ARAB REPUBLIC</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BDT">
				<xsd:annotation>
					<xsd:documentation>Taka - BANGLADESH</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WST">
				<xsd:annotation>
					<xsd:documentation>Tala - SAMOA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TZS">
				<xsd:annotation>
					<xsd:documentation>Tanzanian Shilling - TANZANIA, UNITED REPUBLIC OF</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KZT">
				<xsd:annotation>
					<xsd:documentation>Tenge - KAZAKHSTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TTD">
				<xsd:annotation>
					<xsd:documentation>Trinidad and Tobago Dollar - TRINIDAD AND TOBAGO</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MNT">
				<xsd:annotation>
					<xsd:documentation>Tugrik - MONGOLIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TND">
				<xsd:annotation>
					<xsd:documentation>Tunisian Dinar - TUNISIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TRY">
				<xsd:annotation>
					<xsd:documentation>Turkish Lira - TURKEY</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TMT">
				<xsd:annotation>
					<xsd:documentation>Turkmenistan New Manat - TURKMENISTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AED">
				<xsd:annotation>
					<xsd:documentation>UAE Dirham - UNITED ARAB EMIRATES (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UGX">
				<xsd:annotation>
					<xsd:documentation>Uganda Shilling - UGANDA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CLF">
				<xsd:annotation>
					<xsd:documentation>Unidad de Fomento - CHILE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="COU">
				<xsd:annotation>
					<xsd:documentation>Unidad de Valor Real - COLOMBIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UYW">
				<xsd:annotation>
					<xsd:documentation>Unidad Previsional - URUGUAY</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UYI">
				<xsd:annotation>
					<xsd:documentation>Uruguay Peso en Unidades Indexadas (UI) - URUGUAY</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="USD">
				<xsd:annotation>
					<xsd:documentation>US Dollar - AMERICAN SAMOA, BONAIRE, SINT EUSTATIUS AND SABA, BRITISH INDIAN OCEAN TERRITORY (THE), ECUADOR, EL SALVADOR, GUAM, HAITI, MARSHALL ISLANDS (THE), MICRONESIA (FEDERATED STATES OF), NORTHERN MARIANA ISLANDS (THE), PALAU, PANAMA, PUERTO RICO, TIMOR-LESTE, TURKS AND CAICOS ISLANDS (THE), UNITED STATES MINOR OUTLYING ISLANDS (THE), UNITED STATES OF AMERICA (THE), VIRGIN ISLANDS (BRITISH), VIRGIN ISLANDS (U.S.)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="USN">
				<xsd:annotation>
					<xsd:documentation>US Dollar (Next day) - UNITED STATES OF AMERICA (THE)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UZS">
				<xsd:annotation>
					<xsd:documentation>Uzbekistan Sum - UZBEKISTAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VUV">
				<xsd:annotation>
					<xsd:documentation>Vatu - VANUATU</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CHE">
				<xsd:annotation>
					<xsd:documentation>WIR Euro - SWITZERLAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CHW">
				<xsd:annotation>
					<xsd:documentation>WIR Franc - SWITZERLAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KRW">
				<xsd:annotation>
					<xsd:documentation>Won - KOREA (THE REPUBLIC OF)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="YER">
				<xsd:annotation>
					<xsd:documentation>Yemeni Rial - YEMEN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="JPY">
				<xsd:annotation>
					<xsd:documentation>Yen - JAPAN</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CNY">
				<xsd:annotation>
					<xsd:documentation>Yuan Renminbi - CHINA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZMW">
				<xsd:annotation>
					<xsd:documentation>Zambian Kwacha - ZAMBIA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZWL">
				<xsd:annotation>
					<xsd:documentation>Zimbabwe Dollar - ZIMBABWE</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PLN">
				<xsd:annotation>
					<xsd:documentation>Zloty - POLAND</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- PTEP Group Code Type -->
	<xsd:simpleType name="PTEPGroupCodeType">
		<xsd:annotation>
			<xsd:documentation>PTEP Group Code Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:enumeration value="R965a">
				<xsd:annotation>
					<xsd:documentation>Reclassified section 965(a) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="R965b">
				<xsd:annotation>
					<xsd:documentation>Reclassified section 965(b) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="959c1">
				<xsd:annotation>
					<xsd:documentation>General section 959(c)(1) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="R951A">
				<xsd:annotation>
					<xsd:documentation>Reclassified section 951A PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="R245Ad">
				<xsd:annotation>
					<xsd:documentation>Reclassified section 245A(d) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="965a">
				<xsd:annotation>
					<xsd:documentation>Section 965(a) PTEP </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="965b">
				<xsd:annotation>
					<xsd:documentation>Section 965(b) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="951A">
				<xsd:annotation>
					<xsd:documentation>Section 951A PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="245Ad">
				<xsd:annotation>
					<xsd:documentation>Section 245A(d) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="951a1A">
				<xsd:annotation>
					<xsd:documentation>Section 951(a)(1)(A) PTEP</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!--  Stock Description Type -->
	<xsd:simpleType name="StockDescriptionType">
		<xsd:annotation>
			<xsd:documentation>Stock Description Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!--  Decimal 1 Ratio Type -->
	<xsd:simpleType name="Decimal1RatioType">
		<xsd:annotation>
			<xsd:documentation>Decimal 1 Ratio Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="3"/>
			<xsd:fractionDigits value="1"/>
			<xsd:minInclusive value="00.0"/>
			<xsd:maxInclusive value="99.9"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!--  Decimal 3 Ratio Type -->
	<xsd:simpleType name="Decimal3RatioType">
		<xsd:annotation>
			<xsd:documentation>Decimal 3 Ratio Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="4"/>
			<xsd:fractionDigits value="3"/>
			<xsd:minInclusive value="0.000"/>
			<xsd:maxInclusive value="1.000"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Alpha Row Id Type -->
	<xsd:simpleType name="AlphaRowIdType">
		<xsd:annotation>
			<xsd:documentation>RowID type in the format of A, B, C, D , ..., Z, AA, AB, AC, ..., or a, b, c, d, ..., z, aa, ab, ac, ...</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="4"/>
			<xsd:pattern value="[a-zA-Z]+"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Roman Row Id Type -->
	<xsd:simpleType name="RomanRowIdType">
		<xsd:annotation>
			<xsd:documentation>RowID type in the format of I, II, III, IV, V , ..., or i, ii, iii, iv, v, ...</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
			<xsd:maxLength value="10"/>
			<xsd:pattern value="[IVXLCDMivxlcdm]+"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Numeric Row Id Type -->
	<xsd:simpleType name="NumericRowIdType">
		<xsd:annotation>
			<xsd:documentation>RowID type in the format of 1, 2, 3, 4, 5, ...</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="IntegerPosType">
			<xsd:totalDigits value="4"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- Form 1099 State and Local Tax Type -->
	<xsd:complexType name="Form1099StateLocalTaxType">
		<xsd:annotation>
			<xsd:documentation>Information about state and local tax related to Form 1099</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>

			<!-- State abbreviation code -->
			<xsd:element name="StateAbbreviationCd" type="StateType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>State abbreviation code</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Payer/State identification number -->
			<xsd:element name="StateIdNum" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Payer/State identification number</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="25"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

			<!-- State tax withheld -->
			<xsd:element name="StateTaxWithheldAmt" type="USAmountNNType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>State tax withheld</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Local abbreviation code -->
			<xsd:element name="LocalAbbreviationCdTxt" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Local abbreviation code</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="10"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

			<!-- Locality name -->
			<xsd:element name="LocalityNm" type="ShortDescriptionType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Locality name</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Local tax withheld -->
			<xsd:element name="LocalTaxWithheldAmt" type="USAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Local tax withheld</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

		</xsd:sequence>
	</xsd:complexType>

	<!-- Form 1099 Other State and Local Tax Type -->
	<xsd:complexType name="Form1099OtherStateLocalTaxType">
		<xsd:annotation>
			<xsd:documentation>Information about other state and local tax related to Form 1099</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>

			<!-- State tax withheld -->
			<xsd:element name="StateTaxWithheldAmt" type="USAmountNNType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>State tax withheld</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- State abbreviation code -->
			<xsd:element name="StateAbbreviationCd" type="StateType">
				<xsd:annotation>
					<xsd:documentation>
						<Description>State abbreviation code</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Payer/State identification number -->
			<xsd:element name="StateIdNum" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Payer/State identification number</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="TextType">
						<xsd:maxLength value="25"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>

			<!-- State income -->
			<xsd:element name="StateIncomeAmt" type="USAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>State income</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Local tax withheld -->
			<xsd:element name="LocalTaxWithheldAmt" type="USAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Local tax withheld</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Locality name -->
			<xsd:element name="LocalityNm" type="ShortDescriptionType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Locality name</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

			<!-- Local income -->
			<xsd:element name="LocalIncomeAmt" type="USAmountNNType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<Description>Local income</Description>
						<LineNumber></LineNumber>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>

		</xsd:sequence>
	</xsd:complexType>
	
	<!--  AAR (Administrative Adjustment Request) Incoming/Outgoing Tracking Number Type -->
	<xsd:simpleType name="AARTrackingNumberType">
		<xsd:annotation>
			<xsd:documentation>Used to represent an element identified as a Administrative Adjustment Request Incoming/Outgoing Tracking Number. The pattern may be described as follows: The date in the format where the first 2 numeric digits are the last 2 digits of the year = Year (YY), the second 2 numeric digits = Month (MM), and then 4 numeric digits, followed by a dash followed by a 6 digit sequence in the range of 000001 to 999999.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="TextType">
		<xsd:pattern value="[1-9][0-9](0[1-9]|1[0-2])([0-9]{4})-([0-9]{5}[1-9]|[0-9]{4}[1-9]0|[0-9]{3}[1-9]00|[0-9]{2}[1-9]000|[0-9][1-9]0000|[1-9]00000)"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!-- IRS-issued registration number for the facility -->
	<xsd:simpleType name="FacilityIRSIssdRegistrationNumType">
		<xsd:restriction base="AlphaNumericType"> 
			<xsd:pattern value="[CPT][A-M][A-Za-z0-9]{3}[0-9]{2}[A-Za-z0-9]{5}"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!-- Location coordinates (Latitude) -->
	<xsd:simpleType name="LatitudeCoordinateType">
		<xsd:annotation>
			<xsd:documentation>
				Total of 9 boxes and a decimal point between the third and fourth box. 3 boxes will be to the left of the decimal and 6 boxes to the right of the decimal. For box 1, filers will either enter "+" (plus) or "-" (minus) symbol. For boxes 2-9, all characters will be numbers. For latitude, valid values range from +/- 90. 
			</xsd:documentation>
		</xsd:annotation>	
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="8"/>
			<xsd:fractionDigits value="6"/>
			<xsd:minInclusive value="-90.000000"/>
			<xsd:maxInclusive value="90.000000"/>
			<xsd:pattern value="[+-]?[0-9]{2}.[0-9]{6}"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!-- Location coordinates (Longitude) -->
	<xsd:simpleType name="LongitudeCoordinateType">
		<xsd:annotation>
			<xsd:documentation>
				Total of 10 boxes and a decimal point between the fourth and fifth box. 4 boxes will be to the left of the decimal and 6 boxes to the right of the decimal. For box 1, filers will either enter "+" (plus) or "-" (minus) symbol. For boxes 2-10, all characters will be number. For longitude, valid values range from +/- 180. 
			</xsd:documentation>
		</xsd:annotation>	
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="9"/>
			<xsd:fractionDigits value="6"/>
			<xsd:minInclusive value="-180.000000"/>
			<xsd:maxInclusive value="180.000000"/>
			<xsd:pattern value="[+-]?[0-9]{3}.[0-9]{6}"/>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
